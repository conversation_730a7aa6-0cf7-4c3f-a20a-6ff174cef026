# Details

Date : 2025-05-09 21:38:18

Directory d:\\Diplomkla_VS_Code\\Nash Git\\Marketplace

Total : 591 files,  44182 codes, 1089 comments, 8073 blanks, all 53344 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Marketplace.Application/ApplicationServiceExtensions.cs](/Marketplace.Application/ApplicationServiceExtensions.cs) | C# | 24 | 3 | 8 | 35 |
| [Marketplace.Application/Behaviors/ValidationBehavior.cs](/Marketplace.Application/Behaviors/ValidationBehavior.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Commands/Address/DeleteAddressCommand.cs](/Marketplace.Application/Commands/Address/DeleteAddressCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Address/DeleteAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/DeleteAddressCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Address/StoreAddressCommand.cs](/Marketplace.Application/Commands/Address/StoreAddressCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Address/StoreAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/StoreAddressCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Address/UpdateAddressCommand.cs](/Marketplace.Application/Commands/Address/UpdateAddressCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/Address/UpdateAddressCommandHandler.cs](/Marketplace.Application/Commands/Address/UpdateAddressCommandHandler.cs) | C# | 28 | 11 | 8 | 47 |
| [Marketplace.Application/Commands/Auth/ConfirmEmailCommand.cs](/Marketplace.Application/Commands/Auth/ConfirmEmailCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Auth/ConfirmEmailCommandHandler.cs](/Marketplace.Application/Commands/Auth/ConfirmEmailCommandHandler.cs) | C# | 24 | 0 | 6 | 30 |
| [Marketplace.Application/Commands/Auth/GoogleLoginCommand.cs](/Marketplace.Application/Commands/Auth/GoogleLoginCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Auth/GoogleLoginCommandHandler.cs](/Marketplace.Application/Commands/Auth/GoogleLoginCommandHandler.cs) | C# | 50 | 0 | 7 | 57 |
| [Marketplace.Application/Commands/Auth/LoginCommand.cs](/Marketplace.Application/Commands/Auth/LoginCommand.cs) | C# | 8 | 5 | 2 | 15 |
| [Marketplace.Application/Commands/Auth/LoginCommandHandler.cs](/Marketplace.Application/Commands/Auth/LoginCommandHandler.cs) | C# | 53 | 6 | 12 | 71 |
| [Marketplace.Application/Commands/Auth/RegisterCommand.cs](/Marketplace.Application/Commands/Auth/RegisterCommand.cs) | C# | 11 | 0 | 2 | 13 |
| [Marketplace.Application/Commands/Auth/RegisterCommandHandler.cs](/Marketplace.Application/Commands/Auth/RegisterCommandHandler.cs) | C# | 49 | 3 | 11 | 63 |
| [Marketplace.Application/Commands/Auth/RequestPasswordResetCommand.cs](/Marketplace.Application/Commands/Auth/RequestPasswordResetCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/Auth/RequestPasswordResetCommandHandler.cs](/Marketplace.Application/Commands/Auth/RequestPasswordResetCommandHandler.cs) | C# | 37 | 0 | 6 | 43 |
| [Marketplace.Application/Commands/Auth/ResetPasswordCommand.cs](/Marketplace.Application/Commands/Auth/ResetPasswordCommand.cs) | C# | 8 | 0 | 2 | 10 |
| [Marketplace.Application/Commands/Auth/ResetPasswordCommandHandler.cs](/Marketplace.Application/Commands/Auth/ResetPasswordCommandHandler.cs) | C# | 24 | 1 | 5 | 30 |
| [Marketplace.Application/Commands/Auth/UpdatePasswordCommand.cs](/Marketplace.Application/Commands/Auth/UpdatePasswordCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/Auth/UpdatePasswordCommandHandler.cs](/Marketplace.Application/Commands/Auth/UpdatePasswordCommandHandler.cs) | C# | 31 | 0 | 6 | 37 |
| [Marketplace.Application/Commands/Auth/UpdateProfileCommand.cs](/Marketplace.Application/Commands/Auth/UpdateProfileCommand.cs) | C# | 16 | 0 | 3 | 19 |
| [Marketplace.Application/Commands/Auth/UpdateProfileCommandHandler.cs](/Marketplace.Application/Commands/Auth/UpdateProfileCommandHandler.cs) | C# | 46 | 1 | 8 | 55 |
| [Marketplace.Application/Commands/Category/DeleteCategoryCommand.cs](/Marketplace.Application/Commands/Category/DeleteCategoryCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Category/DeleteCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/DeleteCategoryCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Category/StoreCategoryCommand.cs](/Marketplace.Application/Commands/Category/StoreCategoryCommand.cs) | C# | 12 | 0 | 4 | 16 |
| [Marketplace.Application/Commands/Category/StoreCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/StoreCategoryCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Category/UpdateCategoryCommand.cs](/Marketplace.Application/Commands/Category/UpdateCategoryCommand.cs) | C# | 13 | 0 | 4 | 17 |
| [Marketplace.Application/Commands/Category/UpdateCategoryCommandHandler.cs](/Marketplace.Application/Commands/Category/UpdateCategoryCommandHandler.cs) | C# | 33 | 1 | 8 | 42 |
| [Marketplace.Application/Commands/Chat/DeleteChatCommand.cs](/Marketplace.Application/Commands/Chat/DeleteChatCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Chat/DeleteChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/DeleteChatCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Chat/StoreChatCommand.cs](/Marketplace.Application/Commands/Chat/StoreChatCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Chat/StoreChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/StoreChatCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Chat/UpdateChatCommand.cs](/Marketplace.Application/Commands/Chat/UpdateChatCommand.cs) | C# | 8 | 0 | 2 | 10 |
| [Marketplace.Application/Commands/Chat/UpdateChatCommandHandler.cs](/Marketplace.Application/Commands/Chat/UpdateChatCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommand.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommandHandler.cs](/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommandHandler.cs) | C# | 28 | 11 | 9 | 48 |
| [Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommandHandler.cs) | C# | 15 | 5 | 8 | 28 |
| [Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommand.cs](/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommand.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommandHandler.cs](/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommandHandler.cs) | C# | 18 | 18 | 10 | 46 |
| [Marketplace.Application/Commands/Company/DeleteCompanyCommand.cs](/Marketplace.Application/Commands/Company/DeleteCompanyCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Company/DeleteCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/DeleteCompanyCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Company/StoreCompanyCommand.cs](/Marketplace.Application/Commands/Company/StoreCompanyCommand.cs) | C# | 17 | 0 | 4 | 21 |
| [Marketplace.Application/Commands/Company/StoreCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/StoreCompanyCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Company/UpdateCompanyCommand.cs](/Marketplace.Application/Commands/Company/UpdateCompanyCommand.cs) | C# | 18 | 0 | 4 | 22 |
| [Marketplace.Application/Commands/Company/UpdateCompanyCommandHandler.cs](/Marketplace.Application/Commands/Company/UpdateCompanyCommandHandler.cs) | C# | 35 | 0 | 8 | 43 |
| [Marketplace.Application/Commands/Coupon/DeleteCouponCommand.cs](/Marketplace.Application/Commands/Coupon/DeleteCouponCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Coupon/DeleteCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/DeleteCouponCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Coupon/StoreCouponCommand.cs](/Marketplace.Application/Commands/Coupon/StoreCouponCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Coupon/StoreCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/StoreCouponCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Coupon/UpdateCouponCommand.cs](/Marketplace.Application/Commands/Coupon/UpdateCouponCommand.cs) | C# | 11 | 0 | 3 | 14 |
| [Marketplace.Application/Commands/Coupon/UpdateCouponCommandHandler.cs](/Marketplace.Application/Commands/Coupon/UpdateCouponCommandHandler.cs) | C# | 29 | 11 | 9 | 49 |
| [Marketplace.Application/Commands/Favorite/DeleteFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Favorite/DeleteFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Favorite/StoreFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/StoreFavoriteCommand.cs) | C# | 9 | 0 | 3 | 12 |
| [Marketplace.Application/Commands/Favorite/StoreFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/StoreFavoriteCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Favorite/UpdateFavoriteCommand.cs](/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/Favorite/UpdateFavoriteCommandHandler.cs](/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommandHandler.cs) | C# | 28 | 0 | 9 | 37 |
| [Marketplace.Application/Commands/Message/DeleteMessageCommand.cs](/Marketplace.Application/Commands/Message/DeleteMessageCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Message/DeleteMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/DeleteMessageCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Message/StoreMessageCommand.cs](/Marketplace.Application/Commands/Message/StoreMessageCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Message/StoreMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/StoreMessageCommandHandler.cs) | C# | 36 | 8 | 10 | 54 |
| [Marketplace.Application/Commands/Message/UpdateMessageCommand.cs](/Marketplace.Application/Commands/Message/UpdateMessageCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Message/UpdateMessageCommandHandler.cs](/Marketplace.Application/Commands/Message/UpdateMessageCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/Notification/DeleteNotificationCommand.cs](/Marketplace.Application/Commands/Notification/DeleteNotificationCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Notification/DeleteNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/DeleteNotificationCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Notification/StoreNotificationCommand.cs](/Marketplace.Application/Commands/Notification/StoreNotificationCommand.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Application/Commands/Notification/StoreNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/StoreNotificationCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Notification/UpdateNotificationCommand.cs](/Marketplace.Application/Commands/Notification/UpdateNotificationCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Notification/UpdateNotificationCommandHandler.cs](/Marketplace.Application/Commands/Notification/UpdateNotificationCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommand.cs) | C# | 6 | 0 | 4 | 10 |
| [Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommand.cs](/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommandHandler.cs](/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/OrderItem/StoreOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/OrderItem/StoreOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommand.cs](/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommandHandler.cs](/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommandHandler.cs) | C# | 28 | 0 | 9 | 37 |
| [Marketplace.Application/Commands/Order/DeleteOrderCommand.cs](/Marketplace.Application/Commands/Order/DeleteOrderCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Order/DeleteOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/DeleteOrderCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Order/StoreOrderCommand.cs](/Marketplace.Application/Commands/Order/StoreOrderCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Order/StoreOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/StoreOrderCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Order/UpdateOrderCommand.cs](/Marketplace.Application/Commands/Order/UpdateOrderCommand.cs) | C# | 12 | 0 | 2 | 14 |
| [Marketplace.Application/Commands/Order/UpdateOrderCommandHandler.cs](/Marketplace.Application/Commands/Order/UpdateOrderCommandHandler.cs) | C# | 43 | 0 | 10 | 53 |
| [Marketplace.Application/Commands/Payment/DeletePaymentCommand.cs](/Marketplace.Application/Commands/Payment/DeletePaymentCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Payment/DeletePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/DeletePaymentCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Payment/StorePaymentCommand.cs](/Marketplace.Application/Commands/Payment/StorePaymentCommand.cs) | C# | 11 | 0 | 4 | 15 |
| [Marketplace.Application/Commands/Payment/StorePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/StorePaymentCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Payment/UpdatePaymentCommand.cs](/Marketplace.Application/Commands/Payment/UpdatePaymentCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/Payment/UpdatePaymentCommandHandler.cs](/Marketplace.Application/Commands/Payment/UpdatePaymentCommandHandler.cs) | C# | 29 | 0 | 9 | 38 |
| [Marketplace.Application/Commands/ProductImage/DeleteProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/ProductImage/DeleteProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/ProductImage/StoreProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/StoreProductImageCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/ProductImage/StoreProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/StoreProductImageCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/ProductImage/UpdateProductImageCommand.cs](/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommand.cs) | C# | 6 | 0 | 2 | 8 |
| [Marketplace.Application/Commands/ProductImage/UpdateProductImageCommandHandler.cs](/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommandHandler.cs) | C# | 26 | 0 | 9 | 35 |
| [Marketplace.Application/Commands/Product/DeleteProductCommand.cs](/Marketplace.Application/Commands/Product/DeleteProductCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Product/DeleteProductCommandHandler.cs](/Marketplace.Application/Commands/Product/DeleteProductCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Product/StoreProductCommand.cs](/Marketplace.Application/Commands/Product/StoreProductCommand.cs) | C# | 17 | 0 | 4 | 21 |
| [Marketplace.Application/Commands/Product/StoreProductCommandHandler.cs](/Marketplace.Application/Commands/Product/StoreProductCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Product/UpdateProductCommand.cs](/Marketplace.Application/Commands/Product/UpdateProductCommand.cs) | C# | 21 | 0 | 2 | 23 |
| [Marketplace.Application/Commands/Product/UpdateProductCommandHandler.cs](/Marketplace.Application/Commands/Product/UpdateProductCommandHandler.cs) | C# | 36 | 0 | 9 | 45 |
| [Marketplace.Application/Commands/Rating/DeleteRatingCommand.cs](/Marketplace.Application/Commands/Rating/DeleteRatingCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Rating/DeleteRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/DeleteRatingCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Rating/StoreRatingCommand.cs](/Marketplace.Application/Commands/Rating/StoreRatingCommand.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Commands/Rating/StoreRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/StoreRatingCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Rating/UpdateRatingCommand.cs](/Marketplace.Application/Commands/Rating/UpdateRatingCommand.cs) | C# | 9 | 0 | 2 | 11 |
| [Marketplace.Application/Commands/Rating/UpdateRatingCommandHandler.cs](/Marketplace.Application/Commands/Rating/UpdateRatingCommandHandler.cs) | C# | 28 | 0 | 9 | 37 |
| [Marketplace.Application/Commands/Review/DeleteReviewCommand.cs](/Marketplace.Application/Commands/Review/DeleteReviewCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Review/DeleteReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/DeleteReviewCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Review/StoreReviewCommand.cs](/Marketplace.Application/Commands/Review/StoreReviewCommand.cs) | C# | 9 | 0 | 4 | 13 |
| [Marketplace.Application/Commands/Review/StoreReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/StoreReviewCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Review/UpdateReviewCommand.cs](/Marketplace.Application/Commands/Review/UpdateReviewCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/Review/UpdateReviewCommandHandler.cs](/Marketplace.Application/Commands/Review/UpdateReviewCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommand.cs) | C# | 10 | 0 | 4 | 14 |
| [Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommand.cs](/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommand.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommandHandler.cs](/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommandHandler.cs) | C# | 29 | 0 | 9 | 38 |
| [Marketplace.Application/Commands/User/DeleteUserCommand.cs](/Marketplace.Application/Commands/User/DeleteUserCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/User/DeleteUserCommandHandler.cs](/Marketplace.Application/Commands/User/DeleteUserCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/User/StoreUserCommand.cs](/Marketplace.Application/Commands/User/StoreUserCommand.cs) | C# | 9 | 0 | 4 | 13 |
| [Marketplace.Application/Commands/User/StoreUserCommandHandler.cs](/Marketplace.Application/Commands/User/StoreUserCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/User/UpdateUserCommand.cs](/Marketplace.Application/Commands/User/UpdateUserCommand.cs) | C# | 18 | 0 | 2 | 20 |
| [Marketplace.Application/Commands/User/UpdateUserCommandHandler.cs](/Marketplace.Application/Commands/User/UpdateUserCommandHandler.cs) | C# | 34 | 0 | 9 | 43 |
| [Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommand.cs) | C# | 6 | 0 | 3 | 9 |
| [Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommand.cs](/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommand.cs) | C# | 7 | 0 | 2 | 9 |
| [Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommandHandler.cs](/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommandHandler.cs) | C# | 22 | 11 | 8 | 41 |
| [Marketplace.Application/Commands/Wishlist/DeleteWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommand.cs) | C# | 3 | 0 | 3 | 6 |
| [Marketplace.Application/Commands/Wishlist/DeleteWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommandHandler.cs) | C# | 19 | 0 | 8 | 27 |
| [Marketplace.Application/Commands/Wishlist/StoreWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/StoreWishlistCommand.cs) | C# | 6 | 0 | 4 | 10 |
| [Marketplace.Application/Commands/Wishlist/StoreWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/StoreWishlistCommandHandler.cs) | C# | 20 | 0 | 6 | 26 |
| [Marketplace.Application/Commands/Wishlist/UpdateWishlistCommand.cs](/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommand.cs) | C# | 6 | 0 | 2 | 8 |
| [Marketplace.Application/Commands/Wishlist/UpdateWishlistCommandHandler.cs](/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommandHandler.cs) | C# | 25 | 0 | 9 | 34 |
| [Marketplace.Application/Extensions/ValidationExtensions.cs](/Marketplace.Application/Extensions/ValidationExtensions.cs) | C# | 46 | 1 | 5 | 52 |
| [Marketplace.Application/Mappings/AddressMappingProfile.cs](/Marketplace.Application/Mappings/AddressMappingProfile.cs) | C# | 19 | 8 | 6 | 33 |
| [Marketplace.Application/Mappings/CategoryMappingProfile.cs](/Marketplace.Application/Mappings/CategoryMappingProfile.cs) | C# | 29 | 1 | 6 | 36 |
| [Marketplace.Application/Mappings/ChatMappingProfile.cs](/Marketplace.Application/Mappings/ChatMappingProfile.cs) | C# | 20 | 6 | 6 | 32 |
| [Marketplace.Application/Mappings/CompanyMappingProfile.cs](/Marketplace.Application/Mappings/CompanyMappingProfile.cs) | C# | 39 | 19 | 4 | 62 |
| [Marketplace.Application/Mappings/CompanyScheduleMappingProfile.cs](/Marketplace.Application/Mappings/CompanyScheduleMappingProfile.cs) | C# | 24 | 6 | 5 | 35 |
| [Marketplace.Application/Mappings/CompanyUserMappingProfile.cs](/Marketplace.Application/Mappings/CompanyUserMappingProfile.cs) | C# | 18 | 3 | 6 | 27 |
| [Marketplace.Application/Mappings/CouponMappingProfile.cs](/Marketplace.Application/Mappings/CouponMappingProfile.cs) | C# | 24 | 8 | 4 | 36 |
| [Marketplace.Application/Mappings/FavoriteMappingProfile.cs](/Marketplace.Application/Mappings/FavoriteMappingProfile.cs) | C# | 22 | 8 | 4 | 34 |
| [Marketplace.Application/Mappings/MessageMappingProfile.cs](/Marketplace.Application/Mappings/MessageMappingProfile.cs) | C# | 21 | 4 | 5 | 30 |
| [Marketplace.Application/Mappings/NotificationMappingProfile.cs](/Marketplace.Application/Mappings/NotificationMappingProfile.cs) | C# | 21 | 4 | 4 | 29 |
| [Marketplace.Application/Mappings/OrderCouponMappingProfile.cs](/Marketplace.Application/Mappings/OrderCouponMappingProfile.cs) | C# | 16 | 4 | 4 | 24 |
| [Marketplace.Application/Mappings/OrderItemMappingProfile.cs](/Marketplace.Application/Mappings/OrderItemMappingProfile.cs) | C# | 25 | 6 | 6 | 37 |
| [Marketplace.Application/Mappings/OrderMappingProfile.cs](/Marketplace.Application/Mappings/OrderMappingProfile.cs) | C# | 24 | 6 | 5 | 35 |
| [Marketplace.Application/Mappings/PaymentMappingProfile.cs](/Marketplace.Application/Mappings/PaymentMappingProfile.cs) | C# | 24 | 6 | 6 | 36 |
| [Marketplace.Application/Mappings/ProductImageMappingProfile.cs](/Marketplace.Application/Mappings/ProductImageMappingProfile.cs) | C# | 19 | 4 | 5 | 28 |
| [Marketplace.Application/Mappings/ProductMappingProfile.cs](/Marketplace.Application/Mappings/ProductMappingProfile.cs) | C# | 40 | 12 | 5 | 57 |
| [Marketplace.Application/Mappings/RatingMappingProfile.cs](/Marketplace.Application/Mappings/RatingMappingProfile.cs) | C# | 25 | 7 | 5 | 37 |
| [Marketplace.Application/Mappings/ReviewMappingProfile.cs](/Marketplace.Application/Mappings/ReviewMappingProfile.cs) | C# | 23 | 5 | 5 | 33 |
| [Marketplace.Application/Mappings/ShippingMethodMappingProfile.cs](/Marketplace.Application/Mappings/ShippingMethodMappingProfile.cs) | C# | 21 | 1 | 5 | 27 |
| [Marketplace.Application/Mappings/UserMappingProfile.cs](/Marketplace.Application/Mappings/UserMappingProfile.cs) | C# | 52 | 1 | 6 | 59 |
| [Marketplace.Application/Mappings/WishlistItemMappingProfile.cs](/Marketplace.Application/Mappings/WishlistItemMappingProfile.cs) | C# | 18 | 6 | 5 | 29 |
| [Marketplace.Application/Mappings/WishlistMappingProfile.cs](/Marketplace.Application/Mappings/WishlistMappingProfile.cs) | C# | 18 | 5 | 5 | 28 |
| [Marketplace.Application/Marketplace.Application.csproj](/Marketplace.Application/Marketplace.Application.csproj) | XML | 30 | 0 | 5 | 35 |
| [Marketplace.Application/Queries/Address/GetAddressQuery.cs](/Marketplace.Application/Queries/Address/GetAddressQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Address/GetAddressQueryHandler.cs](/Marketplace.Application/Queries/Address/GetAddressQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Address/GetAllAddressQuery.cs](/Marketplace.Application/Queries/Address/GetAllAddressQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Address/GetAllAddressQueryHandler.cs](/Marketplace.Application/Queries/Address/GetAllAddressQueryHandler.cs) | C# | 76 | 5 | 12 | 93 |
| [Marketplace.Application/Queries/Category/GetAllCategoryQuery.cs](/Marketplace.Application/Queries/Category/GetAllCategoryQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Category/GetAllCategoryQueryHandler.cs](/Marketplace.Application/Queries/Category/GetAllCategoryQueryHandler.cs) | C# | 74 | 5 | 12 | 91 |
| [Marketplace.Application/Queries/Category/GetCategoryBySlugQuery.cs](/Marketplace.Application/Queries/Category/GetCategoryBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Category/GetCategoryBySlugQueryHandler.cs](/Marketplace.Application/Queries/Category/GetCategoryBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Category/GetCategoryQuery.cs](/Marketplace.Application/Queries/Category/GetCategoryQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Category/GetCategoryQueryHandler.cs](/Marketplace.Application/Queries/Category/GetCategoryQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Chat/GetAllChatQuery.cs](/Marketplace.Application/Queries/Chat/GetAllChatQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Chat/GetAllChatQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetAllChatQueryHandler.cs) | C# | 73 | 5 | 12 | 90 |
| [Marketplace.Application/Queries/Chat/GetChatQuery.cs](/Marketplace.Application/Queries/Chat/GetChatQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Chat/GetChatQueryHandler.cs](/Marketplace.Application/Queries/Chat/GetChatQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQueryHandler.cs) | C# | 74 | 0 | 12 | 86 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQuery.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQueryHandler.cs](/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQueryHandler.cs) | C# | 26 | 47 | 11 | 84 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUserQuery.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/CompanyUser/GetCompanyUserQueryHandler.cs](/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQueryHandler.cs) | C# | 19 | 4 | 6 | 29 |
| [Marketplace.Application/Queries/Company/GetAllCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetAllCompanyQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Company/GetAllCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetAllCompanyQueryHandler.cs) | C# | 81 | 5 | 12 | 98 |
| [Marketplace.Application/Queries/Company/GetCategoryBySlugQuery.cs](/Marketplace.Application/Queries/Company/GetCategoryBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetCategoryBySlugQueryHandler.cs](/Marketplace.Application/Queries/Company/GetCategoryBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Company/GetCompanyQuery.cs](/Marketplace.Application/Queries/Company/GetCompanyQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Company/GetCompanyQueryHandler.cs](/Marketplace.Application/Queries/Company/GetCompanyQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Coupon/GetAllCouponQuery.cs](/Marketplace.Application/Queries/Coupon/GetAllCouponQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Coupon/GetAllCouponQueryHandler.cs](/Marketplace.Application/Queries/Coupon/GetAllCouponQueryHandler.cs) | C# | 74 | 0 | 12 | 86 |
| [Marketplace.Application/Queries/Coupon/GetCouponQuery.cs](/Marketplace.Application/Queries/Coupon/GetCouponQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Coupon/GetCouponQueryHandler.cs](/Marketplace.Application/Queries/Coupon/GetCouponQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Favorite/GetAllFavoriteQuery.cs](/Marketplace.Application/Queries/Favorite/GetAllFavoriteQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Favorite/GetAllFavoriteQueryHandler.cs](/Marketplace.Application/Queries/Favorite/GetAllFavoriteQueryHandler.cs) | C# | 73 | 0 | 12 | 85 |
| [Marketplace.Application/Queries/Favorite/GetFavoriteQuery.cs](/Marketplace.Application/Queries/Favorite/GetFavoriteQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Favorite/GetFavoriteQueryHandler.cs](/Marketplace.Application/Queries/Favorite/GetFavoriteQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Message/GetAllMessageQuery.cs](/Marketplace.Application/Queries/Message/GetAllMessageQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Message/GetAllMessageQueryHandler.cs](/Marketplace.Application/Queries/Message/GetAllMessageQueryHandler.cs) | C# | 73 | 0 | 12 | 85 |
| [Marketplace.Application/Queries/Message/GetMessageQuery.cs](/Marketplace.Application/Queries/Message/GetMessageQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Message/GetMessageQueryHandler.cs](/Marketplace.Application/Queries/Message/GetMessageQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Message/GetMessagesByChatQuery.cs](/Marketplace.Application/Queries/Message/GetMessagesByChatQuery.cs) | C# | 11 | 0 | 2 | 13 |
| [Marketplace.Application/Queries/Message/GetMessagesByChatQueryHandler.cs](/Marketplace.Application/Queries/Message/GetMessagesByChatQueryHandler.cs) | C# | 82 | 7 | 13 | 102 |
| [Marketplace.Application/Queries/Notification/GetAllNotificationQuery.cs](/Marketplace.Application/Queries/Notification/GetAllNotificationQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Notification/GetAllNotificationQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetAllNotificationQueryHandler.cs) | C# | 73 | 0 | 12 | 85 |
| [Marketplace.Application/Queries/Notification/GetNotificationQuery.cs](/Marketplace.Application/Queries/Notification/GetNotificationQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Notification/GetNotificationQueryHandler.cs](/Marketplace.Application/Queries/Notification/GetNotificationQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQuery.cs](/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQueryHandler.cs](/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQueryHandler.cs) | C# | 76 | 0 | 12 | 88 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQuery.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQueryHandler.cs](/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/OrderItem/GetAllOrderItemQuery.cs](/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/OrderItem/GetAllOrderItemQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQueryHandler.cs) | C# | 89 | 1 | 12 | 102 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemQuery.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/OrderItem/GetOrderItemQueryHandler.cs](/Marketplace.Application/Queries/OrderItem/GetOrderItemQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Order/GetAllOrderQuery.cs](/Marketplace.Application/Queries/Order/GetAllOrderQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Order/GetAllOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetAllOrderQueryHandler.cs) | C# | 74 | 0 | 12 | 86 |
| [Marketplace.Application/Queries/Order/GetOrderQuery.cs](/Marketplace.Application/Queries/Order/GetOrderQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Order/GetOrderQueryHandler.cs](/Marketplace.Application/Queries/Order/GetOrderQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Payment/GetAllPaymentQuery.cs](/Marketplace.Application/Queries/Payment/GetAllPaymentQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Payment/GetAllPaymentQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetAllPaymentQueryHandler.cs) | C# | 77 | 0 | 12 | 89 |
| [Marketplace.Application/Queries/Payment/GetPaymentQuery.cs](/Marketplace.Application/Queries/Payment/GetPaymentQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Payment/GetPaymentQueryHandler.cs](/Marketplace.Application/Queries/Payment/GetPaymentQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/ProductImage/GetAllProductImageQuery.cs](/Marketplace.Application/Queries/ProductImage/GetAllProductImageQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/ProductImage/GetAllProductImageQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetAllProductImageQueryHandler.cs) | C# | 87 | 1 | 12 | 100 |
| [Marketplace.Application/Queries/ProductImage/GetProductImageQuery.cs](/Marketplace.Application/Queries/ProductImage/GetProductImageQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/ProductImage/GetProductImageQueryHandler.cs](/Marketplace.Application/Queries/ProductImage/GetProductImageQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Product/GetAllProductQuery.cs](/Marketplace.Application/Queries/Product/GetAllProductQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Product/GetAllProductQueryHandler.cs](/Marketplace.Application/Queries/Product/GetAllProductQueryHandler.cs) | C# | 87 | 1 | 12 | 100 |
| [Marketplace.Application/Queries/Product/GetProductBySlugQuery.cs](/Marketplace.Application/Queries/Product/GetProductBySlugQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Product/GetProductBySlugQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductBySlugQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Product/GetProductQuery.cs](/Marketplace.Application/Queries/Product/GetProductQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Product/GetProductQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Product/GetProductsByCategoryQuery.cs](/Marketplace.Application/Queries/Product/GetProductsByCategoryQuery.cs) | C# | 11 | 0 | 2 | 13 |
| [Marketplace.Application/Queries/Product/GetProductsByCategoryQueryHandler.cs](/Marketplace.Application/Queries/Product/GetProductsByCategoryQueryHandler.cs) | C# | 88 | 8 | 14 | 110 |
| [Marketplace.Application/Queries/Rating/GetAllRatingQuery.cs](/Marketplace.Application/Queries/Rating/GetAllRatingQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Rating/GetAllRatingQueryHandler.cs](/Marketplace.Application/Queries/Rating/GetAllRatingQueryHandler.cs) | C# | 91 | 1 | 12 | 104 |
| [Marketplace.Application/Queries/Rating/GetRatingQuery.cs](/Marketplace.Application/Queries/Rating/GetRatingQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Rating/GetRatingQueryHandler.cs](/Marketplace.Application/Queries/Rating/GetRatingQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Review/GetAllReviewQuery.cs](/Marketplace.Application/Queries/Review/GetAllReviewQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Review/GetAllReviewQueryHandler.cs](/Marketplace.Application/Queries/Review/GetAllReviewQueryHandler.cs) | C# | 91 | 1 | 11 | 103 |
| [Marketplace.Application/Queries/Review/GetReviewQuery.cs](/Marketplace.Application/Queries/Review/GetReviewQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Review/GetReviewQueryHandler.cs](/Marketplace.Application/Queries/Review/GetReviewQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQuery.cs](/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQueryHandler.cs](/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQueryHandler.cs) | C# | 75 | 0 | 12 | 87 |
| [Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQuery.cs](/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQueryHandler.cs](/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetAllUserQuery.cs](/Marketplace.Application/Queries/User/GetAllUserQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/User/GetAllUserQueryHandler.cs](/Marketplace.Application/Queries/User/GetAllUserQueryHandler.cs) | C# | 74 | 0 | 11 | 85 |
| [Marketplace.Application/Queries/User/GetUserByEmailQuery.cs](/Marketplace.Application/Queries/User/GetUserByEmailQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserByEmailQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserByEmailQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetUserByUsernameQuery.cs](/Marketplace.Application/Queries/User/GetUserByUsernameQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserByUsernameQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserByUsernameQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/User/GetUserQuery.cs](/Marketplace.Application/Queries/User/GetUserQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/User/GetUserQueryHandler.cs](/Marketplace.Application/Queries/User/GetUserQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQuery.cs](/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQueryHandler.cs](/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQueryHandler.cs) | C# | 90 | 1 | 11 | 102 |
| [Marketplace.Application/Queries/WishlistItem/GetWishlistItemQuery.cs](/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/WishlistItem/GetWishlistItemQueryHandler.cs](/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Queries/Wishlist/GetAllWishlistQuery.cs](/Marketplace.Application/Queries/Wishlist/GetAllWishlistQuery.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Application/Queries/Wishlist/GetAllWishlistQueryHandler.cs](/Marketplace.Application/Queries/Wishlist/GetAllWishlistQueryHandler.cs) | C# | 76 | 0 | 12 | 88 |
| [Marketplace.Application/Queries/Wishlist/GetWishlistQuery.cs](/Marketplace.Application/Queries/Wishlist/GetWishlistQuery.cs) | C# | 4 | 0 | 3 | 7 |
| [Marketplace.Application/Queries/Wishlist/GetWishlistQueryHandler.cs](/Marketplace.Application/Queries/Wishlist/GetWishlistQueryHandler.cs) | C# | 22 | 0 | 6 | 28 |
| [Marketplace.Application/Responses/AddressResponse.cs](/Marketplace.Application/Responses/AddressResponse.cs) | C# | 18 | 0 | 3 | 21 |
| [Marketplace.Application/Responses/AuthResponse.cs](/Marketplace.Application/Responses/AuthResponse.cs) | C# | 12 | 0 | 2 | 14 |
| [Marketplace.Application/Responses/CategoryResponse.cs](/Marketplace.Application/Responses/CategoryResponse.cs) | C# | 26 | 0 | 5 | 31 |
| [Marketplace.Application/Responses/ChatResponse.cs](/Marketplace.Application/Responses/ChatResponse.cs) | C# | 16 | 0 | 3 | 19 |
| [Marketplace.Application/Responses/CompanyResponse.cs](/Marketplace.Application/Responses/CompanyResponse.cs) | C# | 51 | 0 | 4 | 55 |
| [Marketplace.Application/Responses/CompanyScheduleResponse.cs](/Marketplace.Application/Responses/CompanyScheduleResponse.cs) | C# | 21 | 0 | 3 | 24 |
| [Marketplace.Application/Responses/CompanyUserResponse.cs](/Marketplace.Application/Responses/CompanyUserResponse.cs) | C# | 16 | 0 | 3 | 19 |
| [Marketplace.Application/Responses/CouponResponse.cs](/Marketplace.Application/Responses/CouponResponse.cs) | C# | 21 | 0 | 3 | 24 |
| [Marketplace.Application/Responses/FavoriteResponse.cs](/Marketplace.Application/Responses/FavoriteResponse.cs) | C# | 20 | 0 | 1 | 21 |
| [Marketplace.Application/Responses/MessageResponse.cs](/Marketplace.Application/Responses/MessageResponse.cs) | C# | 18 | 0 | 1 | 19 |
| [Marketplace.Application/Responses/NotificationResponse.cs](/Marketplace.Application/Responses/NotificationResponse.cs) | C# | 18 | 0 | 2 | 20 |
| [Marketplace.Application/Responses/OrderCouponResponse.cs](/Marketplace.Application/Responses/OrderCouponResponse.cs) | C# | 14 | 0 | 1 | 15 |
| [Marketplace.Application/Responses/OrderItemResponse.cs](/Marketplace.Application/Responses/OrderItemResponse.cs) | C# | 20 | 0 | 1 | 21 |
| [Marketplace.Application/Responses/OrderResponse.cs](/Marketplace.Application/Responses/OrderResponse.cs) | C# | 23 | 0 | 1 | 24 |
| [Marketplace.Application/Responses/PaginatedResponse.cs](/Marketplace.Application/Responses/PaginatedResponse.cs) | C# | 16 | 0 | 0 | 16 |
| [Marketplace.Application/Responses/PaymentResponse.cs](/Marketplace.Application/Responses/PaymentResponse.cs) | C# | 19 | 0 | 1 | 20 |
| [Marketplace.Application/Responses/ProductImageResponse.cs](/Marketplace.Application/Responses/ProductImageResponse.cs) | C# | 12 | 0 | 1 | 13 |
| [Marketplace.Application/Responses/ProductResponse.cs](/Marketplace.Application/Responses/ProductResponse.cs) | C# | 51 | 0 | 1 | 52 |
| [Marketplace.Application/Responses/RatingResponse.cs](/Marketplace.Application/Responses/RatingResponse.cs) | C# | 18 | 0 | 1 | 19 |
| [Marketplace.Application/Responses/ReviewResponse.cs](/Marketplace.Application/Responses/ReviewResponse.cs) | C# | 14 | 0 | 1 | 15 |
| [Marketplace.Application/Responses/ShippingMethodResponse.cs](/Marketplace.Application/Responses/ShippingMethodResponse.cs) | C# | 19 | 0 | 1 | 20 |
| [Marketplace.Application/Responses/UserResponse.cs](/Marketplace.Application/Responses/UserResponse.cs) | C# | 35 | 0 | 2 | 37 |
| [Marketplace.Application/Responses/WishlistItemResponse.cs](/Marketplace.Application/Responses/WishlistItemResponse.cs) | C# | 14 | 0 | 1 | 15 |
| [Marketplace.Application/Responses/WishlistResponse.cs](/Marketplace.Application/Responses/WishlistResponse.cs) | C# | 12 | 0 | 1 | 13 |
| [Marketplace.Application/Services/Auth/EmailService.cs](/Marketplace.Application/Services/Auth/EmailService.cs) | C# | 48 | 0 | 9 | 57 |
| [Marketplace.Application/Services/Auth/JwtTokenService.cs](/Marketplace.Application/Services/Auth/JwtTokenService.cs) | C# | 55 | 3 | 12 | 70 |
| [Marketplace.Application/Services/Auth/TokenService.cs](/Marketplace.Application/Services/Auth/TokenService.cs) | C# | 15 | 0 | 3 | 18 |
| [Marketplace.Application/Validators/AddressValidator.cs](/Marketplace.Application/Validators/AddressValidator.cs) | C# | 42 | 1 | 5 | 48 |
| [Marketplace.Application/Validators/CategoryValidator.cs](/Marketplace.Application/Validators/CategoryValidator.cs) | C# | 65 | 0 | 16 | 81 |
| [Marketplace.Application/Validators/ChatValidator.cs](/Marketplace.Application/Validators/ChatValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/CompanyScheduleValidator.cs](/Marketplace.Application/Validators/CompanyScheduleValidator.cs) | C# | 38 | 0 | 11 | 49 |
| [Marketplace.Application/Validators/CompanyUserValidator.cs](/Marketplace.Application/Validators/CompanyUserValidator.cs) | C# | 25 | 0 | 6 | 31 |
| [Marketplace.Application/Validators/CompanyValidator.cs](/Marketplace.Application/Validators/CompanyValidator.cs) | C# | 89 | 0 | 20 | 109 |
| [Marketplace.Application/Validators/CouponValidator.cs](/Marketplace.Application/Validators/CouponValidator.cs) | C# | 40 | 6 | 12 | 58 |
| [Marketplace.Application/Validators/FavoriteValidator.cs](/Marketplace.Application/Validators/FavoriteValidator.cs) | C# | 45 | 0 | 12 | 57 |
| [Marketplace.Application/Validators/MessageValidator.cs](/Marketplace.Application/Validators/MessageValidator.cs) | C# | 34 | 0 | 10 | 44 |
| [Marketplace.Application/Validators/NotificationValidator.cs](/Marketplace.Application/Validators/NotificationValidator.cs) | C# | 29 | 0 | 8 | 37 |
| [Marketplace.Application/Validators/OrderCouponValidator.cs](/Marketplace.Application/Validators/OrderCouponValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/OrderItemValidator.cs](/Marketplace.Application/Validators/OrderItemValidator.cs) | C# | 36 | 5 | 12 | 53 |
| [Marketplace.Application/Validators/OrderValidator.cs](/Marketplace.Application/Validators/OrderValidator.cs) | C# | 43 | 0 | 14 | 57 |
| [Marketplace.Application/Validators/PaymentValidator.cs](/Marketplace.Application/Validators/PaymentValidator.cs) | C# | 37 | 0 | 12 | 49 |
| [Marketplace.Application/Validators/ProductImageValidator.cs](/Marketplace.Application/Validators/ProductImageValidator.cs) | C# | 27 | 0 | 6 | 33 |
| [Marketplace.Application/Validators/ProductValidator.cs](/Marketplace.Application/Validators/ProductValidator.cs) | C# | 81 | 5 | 28 | 114 |
| [Marketplace.Application/Validators/RatingValidator.cs](/Marketplace.Application/Validators/RatingValidator.cs) | C# | 40 | 0 | 13 | 53 |
| [Marketplace.Application/Validators/ReviewValidator.cs](/Marketplace.Application/Validators/ReviewValidator.cs) | C# | 32 | 0 | 10 | 42 |
| [Marketplace.Application/Validators/ShippingMethodValidator.cs](/Marketplace.Application/Validators/ShippingMethodValidator.cs) | C# | 28 | 5 | 9 | 42 |
| [Marketplace.Application/Validators/UserValidator.cs](/Marketplace.Application/Validators/UserValidator.cs) | C# | 52 | 0 | 13 | 65 |
| [Marketplace.Application/Validators/WishlistItemValidator.cs](/Marketplace.Application/Validators/WishlistItemValidator.cs) | C# | 25 | 0 | 7 | 32 |
| [Marketplace.Application/Validators/WishlistValidator.cs](/Marketplace.Application/Validators/WishlistValidator.cs) | C# | 23 | 0 | 6 | 29 |
| [Marketplace.Domain/Entities/Address.cs](/Marketplace.Domain/Entities/Address.cs) | C# | 36 | 0 | 3 | 39 |
| [Marketplace.Domain/Entities/Category.cs](/Marketplace.Domain/Entities/Category.cs) | C# | 124 | 2 | 18 | 144 |
| [Marketplace.Domain/Entities/Chat.cs](/Marketplace.Domain/Entities/Chat.cs) | C# | 48 | 17 | 6 | 71 |
| [Marketplace.Domain/Entities/Company.cs](/Marketplace.Domain/Entities/Company.cs) | C# | 187 | 0 | 18 | 205 |
| [Marketplace.Domain/Entities/CompanySchedule.cs](/Marketplace.Domain/Entities/CompanySchedule.cs) | C# | 92 | 0 | 13 | 105 |
| [Marketplace.Domain/Entities/CompanyUser.cs](/Marketplace.Domain/Entities/CompanyUser.cs) | C# | 39 | 0 | 6 | 45 |
| [Marketplace.Domain/Entities/Coupon.cs](/Marketplace.Domain/Entities/Coupon.cs) | C# | 102 | 0 | 12 | 114 |
| [Marketplace.Domain/Entities/Favorite.cs](/Marketplace.Domain/Entities/Favorite.cs) | C# | 54 | 0 | 7 | 61 |
| [Marketplace.Domain/Entities/IEntity.cs](/Marketplace.Domain/Entities/IEntity.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Domain/Entities/Message.cs](/Marketplace.Domain/Entities/Message.cs) | C# | 58 | 0 | 9 | 67 |
| [Marketplace.Domain/Entities/Notification.cs](/Marketplace.Domain/Entities/Notification.cs) | C# | 65 | 0 | 10 | 75 |
| [Marketplace.Domain/Entities/Order.cs](/Marketplace.Domain/Entities/Order.cs) | C# | 105 | 0 | 16 | 121 |
| [Marketplace.Domain/Entities/OrderCoupon.cs](/Marketplace.Domain/Entities/OrderCoupon.cs) | C# | 27 | 18 | 9 | 54 |
| [Marketplace.Domain/Entities/OrderItem.cs](/Marketplace.Domain/Entities/OrderItem.cs) | C# | 74 | 0 | 11 | 85 |
| [Marketplace.Domain/Entities/Payment.cs](/Marketplace.Domain/Entities/Payment.cs) | C# | 83 | 0 | 13 | 96 |
| [Marketplace.Domain/Entities/Product.cs](/Marketplace.Domain/Entities/Product.cs) | C# | 186 | 1 | 20 | 207 |
| [Marketplace.Domain/Entities/ProductImage.cs](/Marketplace.Domain/Entities/ProductImage.cs) | C# | 41 | 0 | 10 | 51 |
| [Marketplace.Domain/Entities/Rating.cs](/Marketplace.Domain/Entities/Rating.cs) | C# | 104 | 0 | 13 | 117 |
| [Marketplace.Domain/Entities/Review.cs](/Marketplace.Domain/Entities/Review.cs) | C# | 63 | 0 | 8 | 71 |
| [Marketplace.Domain/Entities/ShippingMethod.cs](/Marketplace.Domain/Entities/ShippingMethod.cs) | C# | 75 | 0 | 14 | 89 |
| [Marketplace.Domain/Entities/User.cs](/Marketplace.Domain/Entities/User.cs) | C# | 104 | 0 | 9 | 113 |
| [Marketplace.Domain/Entities/Wishlist.cs](/Marketplace.Domain/Entities/Wishlist.cs) | C# | 50 | 0 | 6 | 56 |
| [Marketplace.Domain/Entities/WishlistItem.cs](/Marketplace.Domain/Entities/WishlistItem.cs) | C# | 27 | 19 | 6 | 52 |
| [Marketplace.Domain/Exceptions/DomainException.cs](/Marketplace.Domain/Exceptions/DomainException.cs) | C# | 15 | 1 | 5 | 21 |
| [Marketplace.Domain/Marketplace.Domain.csproj](/Marketplace.Domain/Marketplace.Domain.csproj) | XML | 22 | 0 | 4 | 26 |
| [Marketplace.Domain/Repositories/IAddressRepository.cs](/Marketplace.Domain/Repositories/IAddressRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/ICategoryRepository.cs](/Marketplace.Domain/Repositories/ICategoryRepository.cs) | C# | 10 | 1 | 4 | 15 |
| [Marketplace.Domain/Repositories/IChatRepository.cs](/Marketplace.Domain/Repositories/IChatRepository.cs) | C# | 5 | 0 | 3 | 8 |
| [Marketplace.Domain/Repositories/ICompanyRepository.cs](/Marketplace.Domain/Repositories/ICompanyRepository.cs) | C# | 7 | 0 | 3 | 10 |
| [Marketplace.Domain/Repositories/ICompanyScheduleRepository.cs](/Marketplace.Domain/Repositories/ICompanyScheduleRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/ICompanyUserRepository.cs](/Marketplace.Domain/Repositories/ICompanyUserRepository.cs) | C# | 7 | 4 | 3 | 14 |
| [Marketplace.Domain/Repositories/ICouponRepository.cs](/Marketplace.Domain/Repositories/ICouponRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IFavoriteRepository.cs](/Marketplace.Domain/Repositories/IFavoriteRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IMessageRepository.cs](/Marketplace.Domain/Repositories/IMessageRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/INotificationRepository.cs](/Marketplace.Domain/Repositories/INotificationRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IOrderCouponRepository.cs](/Marketplace.Domain/Repositories/IOrderCouponRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IOrderItemRepository.cs](/Marketplace.Domain/Repositories/IOrderItemRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IOrderRepository.cs](/Marketplace.Domain/Repositories/IOrderRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IPaymentRepository.cs](/Marketplace.Domain/Repositories/IPaymentRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IProductImageRepository.cs](/Marketplace.Domain/Repositories/IProductImageRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IProductRepository.cs](/Marketplace.Domain/Repositories/IProductRepository.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Domain/Repositories/IRatingRepository.cs](/Marketplace.Domain/Repositories/IRatingRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IRepository.cs](/Marketplace.Domain/Repositories/IRepository.cs) | C# | 15 | 0 | 5 | 20 |
| [Marketplace.Domain/Repositories/IReviewRepository.cs](/Marketplace.Domain/Repositories/IReviewRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IShippingMethodRepository.cs](/Marketplace.Domain/Repositories/IShippingMethodRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IUserRepository.cs](/Marketplace.Domain/Repositories/IUserRepository.cs) | C# | 8 | 0 | 3 | 11 |
| [Marketplace.Domain/Repositories/IWishlistItemRepository.cs](/Marketplace.Domain/Repositories/IWishlistItemRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/Repositories/IWishlistRepository.cs](/Marketplace.Domain/Repositories/IWishlistRepository.cs) | C# | 5 | 0 | 4 | 9 |
| [Marketplace.Domain/ValueObjects/AddressVO.cs](/Marketplace.Domain/ValueObjects/AddressVO.cs) | C# | 39 | 1 | 6 | 46 |
| [Marketplace.Domain/ValueObjects/Email.cs](/Marketplace.Domain/ValueObjects/Email.cs) | C# | 27 | 0 | 7 | 34 |
| [Marketplace.Domain/ValueObjects/Meta.cs](/Marketplace.Domain/ValueObjects/Meta.cs) | C# | 19 | 0 | 6 | 25 |
| [Marketplace.Domain/ValueObjects/Money.cs](/Marketplace.Domain/ValueObjects/Money.cs) | C# | 29 | 1 | 6 | 36 |
| [Marketplace.Domain/ValueObjects/Password.cs](/Marketplace.Domain/ValueObjects/Password.cs) | C# | 28 | 0 | 5 | 33 |
| [Marketplace.Domain/ValueObjects/Phone.cs](/Marketplace.Domain/ValueObjects/Phone.cs) | C# | 21 | 0 | 6 | 27 |
| [Marketplace.Domain/ValueObjects/Slug.cs](/Marketplace.Domain/ValueObjects/Slug.cs) | C# | 19 | 0 | 6 | 25 |
| [Marketplace.Domain/ValueObjects/Url.cs](/Marketplace.Domain/ValueObjects/Url.cs) | C# | 16 | 0 | 5 | 21 |
| [Marketplace.Infrastructure/DatabaseSeeder/DatabaseSeeder.cs](/Marketplace.Infrastructure/DatabaseSeeder/DatabaseSeeder.cs) | C# | 64 | 7 | 14 | 85 |
| [Marketplace.Infrastructure/InfrastructureServiceExtensions.cs](/Marketplace.Infrastructure/InfrastructureServiceExtensions.cs) | C# | 46 | 8 | 9 | 63 |
| [Marketplace.Infrastructure/Marketplace.Infrastructure.csproj](/Marketplace.Infrastructure/Marketplace.Infrastructure.csproj) | XML | 27 | 0 | 6 | 33 |
| [Marketplace.Infrastructure/Migrations/20250412102541\_InitialCreate.Designer.cs](/Marketplace.Infrastructure/Migrations/20250412102541_InitialCreate.Designer.cs) | C# | 1,236 | 2 | 444 | 1,682 |
| [Marketplace.Infrastructure/Migrations/20250412102541\_InitialCreate.cs](/Marketplace.Infrastructure/Migrations/20250412102541_InitialCreate.cs) | C# | 912 | 3 | 98 | 1,013 |
| [Marketplace.Infrastructure/Migrations/20250420180614\_UserUpdate.Designer.cs](/Marketplace.Infrastructure/Migrations/20250420180614_UserUpdate.Designer.cs) | C# | 1,244 | 2 | 448 | 1,694 |
| [Marketplace.Infrastructure/Migrations/20250420180614\_UserUpdate.cs](/Marketplace.Infrastructure/Migrations/20250420180614_UserUpdate.cs) | C# | 47 | 3 | 10 | 60 |
| [Marketplace.Infrastructure/Migrations/20250506200445\_RemovePhoneColumn.Designer.cs](/Marketplace.Infrastructure/Migrations/20250506200445_RemovePhoneColumn.Designer.cs) | C# | 1,244 | 2 | 448 | 1,694 |
| [Marketplace.Infrastructure/Migrations/20250506200445\_RemovePhoneColumn.cs](/Marketplace.Infrastructure/Migrations/20250506200445_RemovePhoneColumn.cs) | C# | 14 | 3 | 6 | 23 |
| [Marketplace.Infrastructure/Migrations/MarketplaceDbContextModelSnapshot.cs](/Marketplace.Infrastructure/Migrations/MarketplaceDbContextModelSnapshot.cs) | C# | 1,242 | 1 | 448 | 1,691 |
| [Marketplace.Infrastructure/Persistence/Configurations/AddressConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/AddressConfiguration.cs) | C# | 36 | 0 | 6 | 42 |
| [Marketplace.Infrastructure/Persistence/Configurations/CategoryConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CategoryConfiguration.cs) | C# | 60 | 0 | 12 | 72 |
| [Marketplace.Infrastructure/Persistence/Configurations/ChatConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ChatConfiguration.cs) | C# | 31 | 7 | 9 | 47 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyConfiguration.cs) | C# | 78 | 1 | 19 | 98 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyScheduleConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyScheduleConfiguration.cs) | C# | 29 | 0 | 7 | 36 |
| [Marketplace.Infrastructure/Persistence/Configurations/CompanyUserConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CompanyUserConfiguration.cs) | C# | 26 | 4 | 7 | 37 |
| [Marketplace.Infrastructure/Persistence/Configurations/CouponConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/CouponConfiguration.cs) | C# | 36 | 0 | 11 | 47 |
| [Marketplace.Infrastructure/Persistence/Configurations/FavoriteConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/FavoriteConfiguration.cs) | C# | 46 | 2 | 11 | 59 |
| [Marketplace.Infrastructure/Persistence/Configurations/MessageConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/MessageConfiguration.cs) | C# | 33 | 8 | 9 | 50 |
| [Marketplace.Infrastructure/Persistence/Configurations/NotificationConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/NotificationConfiguration.cs) | C# | 28 | 2 | 7 | 37 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderConfiguration.cs) | C# | 48 | 2 | 15 | 65 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderCouponConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderCouponConfiguration.cs) | C# | 29 | 2 | 7 | 38 |
| [Marketplace.Infrastructure/Persistence/Configurations/OrderItemConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/OrderItemConfiguration.cs) | C# | 43 | 2 | 12 | 57 |
| [Marketplace.Infrastructure/Persistence/Configurations/PaymentConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/PaymentConfiguration.cs) | C# | 41 | 2 | 11 | 54 |
| [Marketplace.Infrastructure/Persistence/Configurations/ProductConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ProductConfiguration.cs) | C# | 78 | 2 | 22 | 102 |
| [Marketplace.Infrastructure/Persistence/Configurations/ProductImageConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ProductImageConfiguration.cs) | C# | 27 | 0 | 5 | 32 |
| [Marketplace.Infrastructure/Persistence/Configurations/RatingConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/RatingConfiguration.cs) | C# | 41 | 0 | 12 | 53 |
| [Marketplace.Infrastructure/Persistence/Configurations/ReviewConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ReviewConfiguration.cs) | C# | 41 | 0 | 12 | 53 |
| [Marketplace.Infrastructure/Persistence/Configurations/ShippingMethodConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/ShippingMethodConfiguration.cs) | C# | 32 | 0 | 6 | 38 |
| [Marketplace.Infrastructure/Persistence/Configurations/UserConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/UserConfiguration.cs) | C# | 36 | 0 | 12 | 48 |
| [Marketplace.Infrastructure/Persistence/Configurations/WishlistConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/WishlistConfiguration.cs) | C# | 30 | 0 | 7 | 37 |
| [Marketplace.Infrastructure/Persistence/Configurations/WishlistItemConfiguration.cs](/Marketplace.Infrastructure/Persistence/Configurations/WishlistItemConfiguration.cs) | C# | 29 | 0 | 5 | 34 |
| [Marketplace.Infrastructure/Persistence/Implementation/AddressRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/AddressRepository.cs) | C# | 10 | 0 | 3 | 13 |
| [Marketplace.Infrastructure/Persistence/Implementation/CategoryRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CategoryRepository.cs) | C# | 58 | 7 | 16 | 81 |
| [Marketplace.Infrastructure/Persistence/Implementation/ChatRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ChatRepository.cs) | C# | 15 | 0 | 3 | 18 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyRepository.cs) | C# | 24 | 0 | 6 | 30 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyScheduleRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyScheduleRepository.cs) | C# | 15 | 0 | 2 | 17 |
| [Marketplace.Infrastructure/Persistence/Implementation/CompanyUserRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CompanyUserRepository.cs) | C# | 18 | 0 | 4 | 22 |
| [Marketplace.Infrastructure/Persistence/Implementation/CouponRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/CouponRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/FavoriteRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/FavoriteRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/MessageRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/MessageRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/NotificationRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/NotificationRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderCouponRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderCouponRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderItemRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderItemRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/OrderRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/OrderRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/PaymentRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/PaymentRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/ProductImageRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ProductImageRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/ProductRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ProductRepository.cs) | C# | 33 | 0 | 6 | 39 |
| [Marketplace.Infrastructure/Persistence/Implementation/RatingRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/RatingRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/Repository.cs](/Marketplace.Infrastructure/Persistence/Implementation/Repository.cs) | C# | 76 | 0 | 14 | 90 |
| [Marketplace.Infrastructure/Persistence/Implementation/ReviewRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ReviewRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/ShippingMethodRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/ShippingMethodRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/UserRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/UserRepository.cs) | C# | 49 | 4 | 8 | 61 |
| [Marketplace.Infrastructure/Persistence/Implementation/WishlistItemRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/WishlistItemRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/Implementation/WishlistRepository.cs](/Marketplace.Infrastructure/Persistence/Implementation/WishlistRepository.cs) | C# | 10 | 0 | 2 | 12 |
| [Marketplace.Infrastructure/Persistence/MarketplaceDbContext.cs](/Marketplace.Infrastructure/Persistence/MarketplaceDbContext.cs) | C# | 37 | 0 | 3 | 40 |
| [Marketplace.Presentation/Controllers/AddressController.cs](/Marketplace.Presentation/Controllers/AddressController.cs) | C# | 53 | 0 | 14 | 67 |
| [Marketplace.Presentation/Controllers/AdminCategoryController.cs](/Marketplace.Presentation/Controllers/AdminCategoryController.cs) | C# | 158 | 10 | 33 | 201 |
| [Marketplace.Presentation/Controllers/AuthController.cs](/Marketplace.Presentation/Controllers/AuthController.cs) | C# | 97 | 14 | 21 | 132 |
| [Marketplace.Presentation/Controllers/BasicApiController.cs](/Marketplace.Presentation/Controllers/BasicApiController.cs) | C# | 15 | 0 | 3 | 18 |
| [Marketplace.Presentation/Controllers/CategoryController.cs](/Marketplace.Presentation/Controllers/CategoryController.cs) | C# | 61 | 0 | 17 | 78 |
| [Marketplace.Presentation/Controllers/ChatController.cs](/Marketplace.Presentation/Controllers/ChatController.cs) | C# | 69 | 0 | 17 | 86 |
| [Marketplace.Presentation/Controllers/CompanyController.cs](/Marketplace.Presentation/Controllers/CompanyController.cs) | C# | 61 | 0 | 17 | 78 |
| [Marketplace.Presentation/Controllers/CompanyScheduleController.cs](/Marketplace.Presentation/Controllers/CompanyScheduleController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/CompanyUserController.cs](/Marketplace.Presentation/Controllers/CompanyUserController.cs) | C# | 55 | 0 | 15 | 70 |
| [Marketplace.Presentation/Controllers/CouponController.cs](/Marketplace.Presentation/Controllers/CouponController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/FavoriteController.cs](/Marketplace.Presentation/Controllers/FavoriteController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/MessageController.cs](/Marketplace.Presentation/Controllers/MessageController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/NotificationController.cs](/Marketplace.Presentation/Controllers/NotificationController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/OrderController.cs](/Marketplace.Presentation/Controllers/OrderController.cs) | C# | 54 | 0 | 14 | 68 |
| [Marketplace.Presentation/Controllers/OrderCouponController.cs](/Marketplace.Presentation/Controllers/OrderCouponController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/OrderItemController.cs](/Marketplace.Presentation/Controllers/OrderItemController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/PaymentController.cs](/Marketplace.Presentation/Controllers/PaymentController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/ProductController.cs](/Marketplace.Presentation/Controllers/ProductController.cs) | C# | 75 | 0 | 19 | 94 |
| [Marketplace.Presentation/Controllers/ProductImageController.cs](/Marketplace.Presentation/Controllers/ProductImageController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/RatingController.cs](/Marketplace.Presentation/Controllers/RatingController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/ReviewController.cs](/Marketplace.Presentation/Controllers/ReviewController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/ShippingMethodController.cs](/Marketplace.Presentation/Controllers/ShippingMethodController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/UserController.cs](/Marketplace.Presentation/Controllers/UserController.cs) | C# | 68 | 0 | 19 | 87 |
| [Marketplace.Presentation/Controllers/WishlistController.cs](/Marketplace.Presentation/Controllers/WishlistController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Controllers/WishlistItemController.cs](/Marketplace.Presentation/Controllers/WishlistItemController.cs) | C# | 54 | 0 | 15 | 69 |
| [Marketplace.Presentation/Marketplace.Presentation.csproj](/Marketplace.Presentation/Marketplace.Presentation.csproj) | XML | 24 | 0 | 5 | 29 |
| [Marketplace.Presentation/Pages/Error.cshtml](/Marketplace.Presentation/Pages/Error.cshtml) | ASP.NET Razor | 23 | 0 | 4 | 27 |
| [Marketplace.Presentation/Pages/Error.cshtml.cs](/Marketplace.Presentation/Pages/Error.cshtml.cs) | C# | 20 | 0 | 8 | 28 |
| [Marketplace.Presentation/Pages/MockAuthenticationHandler.cs](/Marketplace.Presentation/Pages/MockAuthenticationHandler.cs) | C# | 27 | 2 | 7 | 36 |
| [Marketplace.Presentation/Pages/Privacy.cshtml](/Marketplace.Presentation/Pages/Privacy.cshtml) | ASP.NET Razor | 7 | 0 | 2 | 9 |
| [Marketplace.Presentation/Pages/Privacy.cshtml.cs](/Marketplace.Presentation/Pages/Privacy.cshtml.cs) | C# | 14 | 0 | 6 | 20 |
| [Marketplace.Presentation/Pages/Shared/\_Layout.cshtml](/Marketplace.Presentation/Pages/Shared/_Layout.cshtml) | ASP.NET Razor | 49 | 0 | 4 | 53 |
| [Marketplace.Presentation/Pages/Shared/\_Layout.cshtml.css](/Marketplace.Presentation/Pages/Shared/_Layout.cshtml.css) | CSS | 38 | 2 | 9 | 49 |
| [Marketplace.Presentation/Pages/Shared/\_ValidationScriptsPartial.cshtml](/Marketplace.Presentation/Pages/Shared/_ValidationScriptsPartial.cshtml) | ASP.NET Razor | 2 | 0 | 1 | 3 |
| [Marketplace.Presentation/Pages/\_ViewImports.cshtml](/Marketplace.Presentation/Pages/_ViewImports.cshtml) | ASP.NET Razor | 5 | 0 | 2 | 7 |
| [Marketplace.Presentation/Pages/\_ViewStart.cshtml](/Marketplace.Presentation/Pages/_ViewStart.cshtml) | ASP.NET Razor | 3 | 0 | 1 | 4 |
| [Marketplace.Presentation/Program.cs](/Marketplace.Presentation/Program.cs) | C# | 112 | 3 | 21 | 136 |
| [Marketplace.Presentation/Properties/launchSettings.json](/Marketplace.Presentation/Properties/launchSettings.json) | JSON | 23 | 0 | 1 | 24 |
| [Marketplace.Presentation/appsettings.Development.json](/Marketplace.Presentation/appsettings.Development.json) | JSON | 8 | 0 | 1 | 9 |
| [Marketplace.Presentation/appsettings.json](/Marketplace.Presentation/appsettings.json) | JSON | 35 | 0 | 1 | 36 |
| [README-GOOGLE-AUTH.md](/README-GOOGLE-AUTH.md) | Markdown | 76 | 0 | 27 | 103 |
| [Routes.md](/Routes.md) | Markdown | 170 | 0 | 21 | 191 |
| [frontend/.env](/frontend/.env) | Properties | 1 | 0 | 1 | 2 |
| [frontend/README.md](/frontend/README.md) | Markdown | 36 | 0 | 15 | 51 |
| [frontend/index.html](/frontend/index.html) | HTML | 152 | 2 | 26 | 180 |
| [frontend/package-lock.json](/frontend/package-lock.json) | JSON | 914 | 0 | 1 | 915 |
| [frontend/package.json](/frontend/package.json) | JSON | 29 | 0 | 1 | 30 |
| [frontend/src/App.vue](/frontend/src/App.vue) | vue | 329 | 1 | 58 | 388 |
| [frontend/src/TestComponent.vue](/frontend/src/TestComponent.vue) | vue | 17 | 0 | 4 | 21 |
| [frontend/src/admin/README.md](/frontend/src/admin/README.md) | Markdown | 74 | 0 | 17 | 91 |
| [frontend/src/admin/api.js](/frontend/src/admin/api.js) | JavaScript | 2 | 2 | 3 | 7 |
| [frontend/src/admin/components/Header.vue](/frontend/src/admin/components/Header.vue) | vue | 306 | 3 | 54 | 363 |
| [frontend/src/admin/components/Sidebar.vue](/frontend/src/admin/components/Sidebar.vue) | vue | 257 | 1 | 41 | 299 |
| [frontend/src/admin/components/categories/CategoryFormModal.vue](/frontend/src/admin/components/categories/CategoryFormModal.vue) | vue | 209 | 4 | 24 | 237 |
| [frontend/src/admin/components/categories/CategorySkeleton.vue](/frontend/src/admin/components/categories/CategorySkeleton.vue) | vue | 162 | 2 | 20 | 184 |
| [frontend/src/admin/components/categories/CategoryTable.vue](/frontend/src/admin/components/categories/CategoryTable.vue) | vue | 86 | 0 | 6 | 92 |
| [frontend/src/admin/components/categories/CategoryTreeNode.vue](/frontend/src/admin/components/categories/CategoryTreeNode.vue) | vue | 141 | 0 | 19 | 160 |
| [frontend/src/admin/components/common/AdminStatCard.vue](/frontend/src/admin/components/common/AdminStatCard.vue) | vue | 89 | 0 | 13 | 102 |
| [frontend/src/admin/components/common/ConfirmDialog.vue](/frontend/src/admin/components/common/ConfirmDialog.vue) | vue | 102 | 0 | 16 | 118 |
| [frontend/src/admin/components/common/DataTable.vue](/frontend/src/admin/components/common/DataTable.vue) | vue | 403 | 5 | 56 | 464 |
| [frontend/src/admin/components/common/EmptyState.vue](/frontend/src/admin/components/common/EmptyState.vue) | vue | 62 | 0 | 7 | 69 |
| [frontend/src/admin/components/common/FilterPanel.vue](/frontend/src/admin/components/common/FilterPanel.vue) | vue | 98 | 0 | 16 | 114 |
| [frontend/src/admin/components/common/LoadingIndicator.vue](/frontend/src/admin/components/common/LoadingIndicator.vue) | vue | 58 | 0 | 7 | 65 |
| [frontend/src/admin/components/common/Pagination.vue](/frontend/src/admin/components/common/Pagination.vue) | vue | 134 | 5 | 22 | 161 |
| [frontend/src/admin/components/common/StatusBadge.vue](/frontend/src/admin/components/common/StatusBadge.vue) | vue | 171 | 0 | 25 | 196 |
| [frontend/src/admin/components/dashboard/OrdersByStatusChart.vue](/frontend/src/admin/components/dashboard/OrdersByStatusChart.vue) | vue | 200 | 0 | 26 | 226 |
| [frontend/src/admin/components/dashboard/PendingSellerRequests.vue](/frontend/src/admin/components/dashboard/PendingSellerRequests.vue) | vue | 191 | 0 | 30 | 221 |
| [frontend/src/admin/components/dashboard/RecentOrders.vue](/frontend/src/admin/components/dashboard/RecentOrders.vue) | vue | 160 | 0 | 23 | 183 |
| [frontend/src/admin/components/dashboard/SalesChart.vue](/frontend/src/admin/components/dashboard/SalesChart.vue) | vue | 205 | 0 | 23 | 228 |
| [frontend/src/admin/components/orders/OrderDetailsModal.vue](/frontend/src/admin/components/orders/OrderDetailsModal.vue) | vue | 244 | 4 | 22 | 270 |
| [frontend/src/admin/components/orders/OrderFilters.vue](/frontend/src/admin/components/orders/OrderFilters.vue) | vue | 140 | 0 | 15 | 155 |
| [frontend/src/admin/components/orders/OrderTable.vue](/frontend/src/admin/components/orders/OrderTable.vue) | vue | 110 | 0 | 8 | 118 |
| [frontend/src/admin/components/products/ProductFilters.vue](/frontend/src/admin/components/products/ProductFilters.vue) | vue | 147 | 0 | 18 | 165 |
| [frontend/src/admin/components/products/ProductFormModal.vue](/frontend/src/admin/components/products/ProductFormModal.vue) | vue | 263 | 4 | 23 | 290 |
| [frontend/src/admin/components/products/ProductTable.vue](/frontend/src/admin/components/products/ProductTable.vue) | vue | 107 | 0 | 8 | 115 |
| [frontend/src/admin/components/seller-requests/SellerRequestDetailsModal.vue](/frontend/src/admin/components/seller-requests/SellerRequestDetailsModal.vue) | vue | 194 | 5 | 23 | 222 |
| [frontend/src/admin/components/seller-requests/SellerRequestFilters.vue](/frontend/src/admin/components/seller-requests/SellerRequestFilters.vue) | vue | 113 | 0 | 14 | 127 |
| [frontend/src/admin/components/seller-requests/SellerRequestTable.vue](/frontend/src/admin/components/seller-requests/SellerRequestTable.vue) | vue | 102 | 0 | 7 | 109 |
| [frontend/src/admin/components/users/UserFilters.vue](/frontend/src/admin/components/users/UserFilters.vue) | vue | 109 | 0 | 14 | 123 |
| [frontend/src/admin/components/users/UserFormModal.vue](/frontend/src/admin/components/users/UserFormModal.vue) | vue | 223 | 4 | 20 | 247 |
| [frontend/src/admin/components/users/UserTable.vue](/frontend/src/admin/components/users/UserTable.vue) | vue | 143 | 0 | 12 | 155 |
| [frontend/src/admin/layouts/AdminLayout.vue](/frontend/src/admin/layouts/AdminLayout.vue) | vue | 112 | 3 | 21 | 136 |
| [frontend/src/admin/services/categories.js](/frontend/src/admin/services/categories.js) | JavaScript | 228 | 36 | 51 | 315 |
| [frontend/src/admin/services/dashboard.js](/frontend/src/admin/services/dashboard.js) | JavaScript | 53 | 5 | 11 | 69 |
| [frontend/src/admin/services/orders.js](/frontend/src/admin/services/orders.js) | JavaScript | 334 | 12 | 14 | 360 |
| [frontend/src/admin/services/products.js](/frontend/src/admin/services/products.js) | JavaScript | 240 | 10 | 12 | 262 |
| [frontend/src/admin/services/reports.js](/frontend/src/admin/services/reports.js) | JavaScript | 210 | 6 | 7 | 223 |
| [frontend/src/admin/services/seller-requests.js](/frontend/src/admin/services/seller-requests.js) | JavaScript | 189 | 5 | 6 | 200 |
| [frontend/src/admin/services/sellerRequests.js](/frontend/src/admin/services/sellerRequests.js) | JavaScript | 221 | 7 | 8 | 236 |
| [frontend/src/admin/services/settings.js](/frontend/src/admin/services/settings.js) | JavaScript | 259 | 10 | 11 | 280 |
| [frontend/src/admin/services/users.js](/frontend/src/admin/services/users.js) | JavaScript | 366 | 15 | 17 | 398 |
| [frontend/src/admin/views/Categories.vue](/frontend/src/admin/views/Categories.vue) | vue | 119 | 3 | 16 | 138 |
| [frontend/src/admin/views/Dashboard.vue](/frontend/src/admin/views/Dashboard.vue) | vue | 250 | 6 | 33 | 289 |
| [frontend/src/admin/views/Orders.vue](/frontend/src/admin/views/Orders.vue) | vue | 119 | 4 | 18 | 141 |
| [frontend/src/admin/views/Products.vue](/frontend/src/admin/views/Products.vue) | vue | 151 | 5 | 21 | 177 |
| [frontend/src/admin/views/Reports.vue](/frontend/src/admin/views/Reports.vue) | vue | 332 | 6 | 34 | 372 |
| [frontend/src/admin/views/SellerRequests.vue](/frontend/src/admin/views/SellerRequests.vue) | vue | 136 | 4 | 19 | 159 |
| [frontend/src/admin/views/Settings.vue](/frontend/src/admin/views/Settings.vue) | vue | 331 | 6 | 42 | 379 |
| [frontend/src/admin/views/Users.vue](/frontend/src/admin/views/Users.vue) | vue | 178 | 5 | 24 | 207 |
| [frontend/src/admin/views/categories/CategoryDetail.vue](/frontend/src/admin/views/categories/CategoryDetail.vue) | vue | 535 | 6 | 65 | 606 |
| [frontend/src/admin/views/categories/CategoryForm.vue](/frontend/src/admin/views/categories/CategoryForm.vue) | vue | 454 | 6 | 64 | 524 |
| [frontend/src/admin/views/categories/CategoryList.vue](/frontend/src/admin/views/categories/CategoryList.vue) | vue | 555 | 10 | 73 | 638 |
| [frontend/src/admin/views/orders/OrderDetail.vue](/frontend/src/admin/views/orders/OrderDetail.vue) | vue | 701 | 6 | 88 | 795 |
| [frontend/src/admin/views/orders/OrderList.vue](/frontend/src/admin/views/orders/OrderList.vue) | vue | 528 | 4 | 44 | 576 |
| [frontend/src/admin/views/products/ProductDetail.vue](/frontend/src/admin/views/products/ProductDetail.vue) | vue | 453 | 5 | 52 | 510 |
| [frontend/src/admin/views/products/ProductForm.vue](/frontend/src/admin/views/products/ProductForm.vue) | vue | 661 | 7 | 70 | 738 |
| [frontend/src/admin/views/products/ProductList.vue](/frontend/src/admin/views/products/ProductList.vue) | vue | 397 | 4 | 37 | 438 |
| [frontend/src/admin/views/seller-requests/SellerRequestDetail.vue](/frontend/src/admin/views/seller-requests/SellerRequestDetail.vue) | vue | 551 | 5 | 76 | 632 |
| [frontend/src/admin/views/seller-requests/SellerRequestList.vue](/frontend/src/admin/views/seller-requests/SellerRequestList.vue) | vue | 420 | 3 | 60 | 483 |
| [frontend/src/admin/views/users/UserDetail.vue](/frontend/src/admin/views/users/UserDetail.vue) | vue | 803 | 8 | 97 | 908 |
| [frontend/src/admin/views/users/UserList.vue](/frontend/src/admin/views/users/UserList.vue) | vue | 617 | 7 | 72 | 696 |
| [frontend/src/assets/css/admin.css](/frontend/src/assets/css/admin.css) | CSS | 184 | 12 | 38 | 234 |
| [frontend/src/assets/css/style.css](/frontend/src/assets/css/style.css) | CSS | 46 | 1 | 10 | 57 |
| [frontend/src/assets/images/apple-icon.svg](/frontend/src/assets/images/apple-icon.svg) | XML | 3 | 0 | 1 | 4 |
| [frontend/src/assets/images/google-icon.svg](/frontend/src/assets/images/google-icon.svg) | XML | 6 | 0 | 1 | 7 |
| [frontend/src/assets/images/klondike-logo.svg](/frontend/src/assets/images/klondike-logo.svg) | XML | 8 | 0 | 1 | 9 |
| [frontend/src/components/ErrorBoundary.vue](/frontend/src/components/ErrorBoundary.vue) | vue | 173 | 0 | 33 | 206 |
| [frontend/src/components/GlobalLoading.vue](/frontend/src/components/GlobalLoading.vue) | vue | 72 | 0 | 10 | 82 |
| [frontend/src/components/admin/common/StatusBadge.vue](/frontend/src/components/admin/common/StatusBadge.vue) | vue | 81 | 0 | 9 | 90 |
| [frontend/src/config/google-auth.js](/frontend/src/config/google-auth.js) | JavaScript | 110 | 23 | 16 | 149 |
| [frontend/src/layouts/AdminLayout.vue](/frontend/src/layouts/AdminLayout.vue) | vue | 494 | 3 | 69 | 566 |
| [frontend/src/main.js](/frontend/src/main.js) | JavaScript | 14 | 1 | 6 | 21 |
| [frontend/src/router/index.js](/frontend/src/router/index.js) | JavaScript | 227 | 22 | 22 | 271 |
| [frontend/src/services/api.js](/frontend/src/services/api.js) | JavaScript | 193 | 43 | 48 | 284 |
| [frontend/src/services/api.service.js](/frontend/src/services/api.service.js) | JavaScript | 48 | 10 | 12 | 70 |
| [frontend/src/services/auth.service.js](/frontend/src/services/auth.service.js) | JavaScript | 103 | 21 | 32 | 156 |
| [frontend/src/services/category.service.js](/frontend/src/services/category.service.js) | JavaScript | 76 | 12 | 20 | 108 |
| [frontend/src/services/dashboard.service.js](/frontend/src/services/dashboard.service.js) | JavaScript | 85 | 10 | 7 | 102 |
| [frontend/src/services/order.service.js](/frontend/src/services/order.service.js) | JavaScript | 25 | 7 | 9 | 41 |
| [frontend/src/services/product.service.js](/frontend/src/services/product.service.js) | JavaScript | 32 | 8 | 10 | 50 |
| [frontend/src/services/request-manager.js](/frontend/src/services/request-manager.js) | JavaScript | 47 | 28 | 13 | 88 |
| [frontend/src/services/seller-request.service.js](/frontend/src/services/seller-request.service.js) | JavaScript | 22 | 6 | 8 | 36 |
| [frontend/src/services/user.service.js](/frontend/src/services/user.service.js) | JavaScript | 25 | 7 | 9 | 41 |
| [frontend/src/store/index.js](/frontend/src/store/index.js) | JavaScript | 11 | 0 | 2 | 13 |
| [frontend/src/store/modules/auth.js](/frontend/src/store/modules/auth.js) | JavaScript | 124 | 20 | 29 | 173 |
| [frontend/src/store/modules/categories.js](/frontend/src/store/modules/categories.js) | JavaScript | 181 | 7 | 39 | 227 |
| [frontend/src/store/modules/loading.js](/frontend/src/store/modules/loading.js) | JavaScript | 70 | 1 | 17 | 88 |
| [frontend/src/utils/slugify.js](/frontend/src/utils/slugify.js) | JavaScript | 18 | 12 | 4 | 34 |
| [frontend/src/views/AdminDashboard.vue](/frontend/src/views/AdminDashboard.vue) | vue | 150 | 0 | 9 | 159 |
| [frontend/src/views/Cart.vue](/frontend/src/views/Cart.vue) | vue | 706 | 0 | 74 | 780 |
| [frontend/src/views/Dashboard.vue](/frontend/src/views/Dashboard.vue) | vue | 71 | 0 | 10 | 81 |
| [frontend/src/views/Home.vue](/frontend/src/views/Home.vue) | vue | 55 | 0 | 4 | 59 |
| [frontend/src/views/Login.vue](/frontend/src/views/Login.vue) | vue | 647 | 2 | 113 | 762 |
| [frontend/src/views/NotFound.vue](/frontend/src/views/NotFound.vue) | vue | 19 | 0 | 3 | 22 |
| [frontend/src/views/Profile.vue](/frontend/src/views/Profile.vue) | vue | 139 | 0 | 20 | 159 |
| [frontend/src/views/Register.vue](/frontend/src/views/Register.vue) | vue | 707 | 2 | 119 | 828 |
| [frontend/src/views/admin/Categories.vue](/frontend/src/views/admin/Categories.vue) | vue | 421 | 5 | 62 | 488 |
| [frontend/src/views/admin/Dashboard.vue](/frontend/src/views/admin/Dashboard.vue) | vue | 839 | 8 | 101 | 948 |
| [frontend/src/views/admin/Orders.vue](/frontend/src/views/admin/Orders.vue) | vue | 567 | 5 | 50 | 622 |
| [frontend/src/views/admin/Products.vue](/frontend/src/views/admin/Products.vue) | vue | 434 | 5 | 53 | 492 |
| [frontend/src/views/admin/SellerRequests.vue](/frontend/src/views/admin/SellerRequests.vue) | vue | 469 | 5 | 49 | 523 |
| [frontend/src/views/admin/Users.vue](/frontend/src/views/admin/Users.vue) | vue | 412 | 5 | 45 | 462 |
| [frontend/vite.config.js](/frontend/vite.config.js) | JavaScript | 20 | 0 | 2 | 22 |
| [start-dev.bat](/start-dev.bat) | Batch | 9 | 0 | 4 | 13 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)