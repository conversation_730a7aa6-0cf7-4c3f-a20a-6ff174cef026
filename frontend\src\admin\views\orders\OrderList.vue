<template>
  <div class="order-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Orders</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            class="button is-primary" 
            @click="exportOrders"
            :class="{ 'is-loading': exporting }">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
            <span>Export Orders</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-content">
        <div class="columns is-multiline">
          <div class="column is-3">
            <div class="field">
              <label class="label">Search</label>
              <div class="control has-icons-left">
                <input
                  class="input"
                  type="text"
                  placeholder="Order ID, customer name, email..."
                  v-model="filters.search"
                  @input="debouncedSearch">
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.status">
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Payment Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.paymentStatus">
                    <option value="">All Payment Statuses</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Date Range</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.dateRange">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="last7days">Last 7 Days</option>
                    <option value="last30days">Last 30 Days</option>
                    <option value="thisMonth">This Month</option>
                    <option value="lastMonth">Last Month</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div v-if="filters.dateRange === 'custom'" class="column is-6">
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">From</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="date" 
                      v-model="filters.dateFrom">
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">To</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="date" 
                      v-model="filters.dateTo">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-12">
            <div class="field is-grouped is-grouped-right">
              <div class="control">
                <button 
                  class="button is-light" 
                  @click="resetFilters">
                  Reset
                </button>
              </div>
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="fetchOrders"
                  :class="{ 'is-loading': loading }">
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading && !orders.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading orders...</p>
        </div>
        <div v-else-if="!orders.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-shopping-cart fa-2x"></i>
          </span>
          <p class="mt-2">No orders found</p>
          <p class="mt-2">Try adjusting your filters</p>
        </div>
        <div v-else>
          <div class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Order ID</th>
                  <th>Customer</th>
                  <th>Date</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Payment</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="order in orders" :key="order.id">
                  <td>
                    <code class="has-text-info">{{ order.id }}</code>
                  </td>
                  <td>
                    <div>
                      <strong>{{ order.customerName || order.userName || 'N/A' }}</strong>
                      <br>
                      <small class="has-text-grey">{{ order.customerEmail || 'No email' }}</small>
                    </div>
                  </td>
                  <td>{{ formatDate(order.createdAt) }}</td>
                  <td>
                    <strong class="has-text-success">{{ formatCurrency(order.total || order.totalPriceAmount) }}</strong>
                  </td>
                  <td>
                    <span class="tag" :class="getOrderStatusClass(order.status)">
                      {{ getOrderStatusText(order.status) }}
                    </span>
                  </td>
                  <td>
                    <span class="tag" :class="getPaymentStatusClass(order.paymentStatusText || order.paymentStatus)">
                      {{ getPaymentStatusText(order.paymentStatusText || order.paymentStatus) }}
                    </span>
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <router-link
                        :to="`/admin/orders/${order.id}`"
                        class="button is-info"
                        title="View">
                        <span class="icon is-small">
                          <i class="fas fa-eye"></i>
                        </span>
                      </router-link>
                      <button
                        class="button is-primary"
                        @click="openStatusModal(order)"
                        title="Update Status">
                        <span class="icon is-small">
                          <i class="fas fa-edit"></i>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <pagination 
            :current-page="currentPage" 
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal" :class="{ 'is-active': showStatusModal }">
      <div class="modal-background" @click="closeStatusModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Update Order Status</p>
          <button class="delete" aria-label="close" @click="closeStatusModal"></button>
        </header>
        <section class="modal-card-body">
          <div v-if="selectedOrder" class="content">
            <p><strong>Order ID:</strong> {{ selectedOrder.id }}</p>
            <p><strong>Customer:</strong> {{ selectedOrder.userName }}</p>
            <p><strong>Current Status:</strong> 
              <status-badge 
                :status="selectedOrder.status" 
                type="order" />
            </p>
            
            <div class="field">
              <label class="label">New Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newStatus">
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Payment Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newPaymentStatus">
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Note (Optional)</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  v-model="statusNote" 
                  placeholder="Add a note about this status change">
                </textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-primary" 
            @click="updateOrderStatus"
            :class="{ 'is-loading': updatingStatus }">
            Update Status
          </button>
          <button class="button" @click="closeStatusModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ordersService } from '@/admin/services/orders';
import { exportService } from '@/admin/services/exportService';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';

// State
const orders = ref([]);
const loading = ref(false);
const exporting = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);
const showStatusModal = ref(false);
const selectedOrder = ref(null);
const newStatus = ref('');
const newPaymentStatus = ref('');
const statusNote = ref('');
const updatingStatus = ref(false);
const searchTimeout = ref(null);

// Filters
const filters = reactive({
  search: '',
  status: '',
  paymentStatus: '',
  dateRange: '',
  dateFrom: '',
  dateTo: ''
});



// Fetch orders
const fetchOrders = async (page = 1) => {
  loading.value = true;
  currentPage.value = page;

  try {
    // Оптимізовані параметри для швидкості
    const optimizedParams = {
      page: currentPage.value,
      pageSize: Math.min(itemsPerPage.value, 10), // Максимум 10 записів
      limit: Math.min(itemsPerPage.value, 10),
      search: filters.search,
      status: filters.status,
      paymentStatus: filters.paymentStatus
    };

    // Process date range filter only if needed
    if (filters.dateRange) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      switch (filters.dateRange) {
        case 'today':
          dateFilters.dateFrom = today.toISOString().split('T')[0];
          break;
        case 'yesterday':
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          dateFilters.dateFrom = yesterday.toISOString().split('T')[0];
          dateFilters.dateTo = today.toISOString().split('T')[0];
          break;
        case 'last7days':
          const last7days = new Date(today);
          last7days.setDate(last7days.getDate() - 7);
          dateFilters.dateFrom = last7days.toISOString().split('T')[0];
          break;
        case 'last30days':
          const last30days = new Date(today);
          last30days.setDate(last30days.getDate() - 30);
          dateFilters.dateFrom = last30days.toISOString().split('T')[0];
          break;
        case 'thisMonth':
          const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
          dateFilters.dateFrom = thisMonth.toISOString().split('T')[0];
          break;
        case 'lastMonth':
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
          dateFilters.dateFrom = lastMonth.toISOString().split('T')[0];
          dateFilters.dateTo = endOfLastMonth.toISOString().split('T')[0];
          break;
        case 'custom':
          if (filters.dateFrom) optimizedParams.dateFrom = filters.dateFrom;
          if (filters.dateTo) optimizedParams.dateTo = filters.dateTo;
          break;
      }
    }

    const response = await ordersService.getAll(optimizedParams);

    console.log('Orders API response:', response);

    if (response.orders) {
      orders.value = response.orders;
      console.log('Orders loaded:', orders.value.length);
    } else if (response.data) {
      orders.value = response.data;
      console.log('Orders loaded (data field):', orders.value.length);
    } else {
      orders.value = [];
      console.log('No orders found in response');
    }

    if (response.pagination) {
      totalPages.value = response.pagination.totalPages;
      totalItems.value = response.pagination.total;
      currentPage.value = response.pagination.page;
      console.log('Pagination:', response.pagination);
    }
  } catch (error) {
    console.error('Error fetching orders:', error);
  } finally {
    loading.value = false;
  }
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.status = '';
  filters.paymentStatus = '';
  filters.dateRange = '';
  filters.dateFrom = '';
  filters.dateTo = '';
  fetchOrders(1);
};

// Handle page change
const handlePageChange = (page) => {
  fetchOrders(page);
};

// Debounced search
const debouncedSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  searchTimeout.value = setTimeout(() => {
    fetchOrders(1);
    searchTimeout.value = null;
  }, 500); // 500ms debounce
};

// Export orders
const exportOrders = async () => {
  try {
    exporting.value = true;

    // Prepare export filters
    const exportFilters = {
      search: filters.search,
      status: filters.status,
      paymentStatus: filters.paymentStatus,
      dateRange: filters.dateRange,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo
    };

    // Remove empty filters
    Object.keys(exportFilters).forEach(key => {
      if (!exportFilters[key]) {
        delete exportFilters[key];
      }
    });

    const result = await exportService.exportOrdersToExcel(exportFilters, 'orders_export');

    // Show success notification
    console.log('Export successful:', result);
    alert(`Successfully exported orders to ${result.filename}`);

  } catch (error) {
    console.error('Export failed:', error);
    alert(`Export failed: ${error.message}`);
  } finally {
    exporting.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency
const formatCurrency = (value) => {
  if (!value) return '₴0.00';

  // Округлюємо до 2 знаків після коми
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};

// Order status functions
const getOrderStatusText = (status) => {
  const statusMap = {
    'Pending': 'Pending',
    'Paid': 'Paid',
    'Shipped': 'Shipped',
    'Delivered': 'Delivered',
    'Cancelled': 'Cancelled',
    0: 'Pending',
    1: 'Paid',
    2: 'Shipped',
    3: 'Delivered',
    4: 'Cancelled'
  };
  return statusMap[status] || status || 'Unknown';
};

const getOrderStatusClass = (status) => {
  const classMap = {
    'Pending': 'is-warning',
    'Paid': 'is-info',
    'Shipped': 'is-primary',
    'Delivered': 'is-success',
    'Cancelled': 'is-danger',
    0: 'is-warning',
    1: 'is-info',
    2: 'is-primary',
    3: 'is-success',
    4: 'is-danger'
  };
  return classMap[status] || 'is-light';
};

// Payment status functions
const getPaymentStatusText = (status) => {
  const statusMap = {
    'Pending': 'Pending',
    'Completed': 'Completed',
    'Failed': 'Failed',
    0: 'Pending',
    1: 'Completed',
    2: 'Failed'
  };
  return statusMap[status] || status || 'Unknown';
};

const getPaymentStatusClass = (status) => {
  const classMap = {
    'Pending': 'is-warning',
    'Completed': 'is-success',
    'Failed': 'is-danger',
    0: 'is-warning',
    1: 'is-success',
    2: 'is-danger'
  };
  return classMap[status] || 'is-light';
};



// Open status modal
const openStatusModal = (order) => {
  selectedOrder.value = order;
  newStatus.value = order.status;
  newPaymentStatus.value = order.paymentStatus;
  statusNote.value = '';
  showStatusModal.value = true;
};

// Close status modal
const closeStatusModal = () => {
  showStatusModal.value = false;
  selectedOrder.value = null;
};

// Update order status
const updateOrderStatus = async () => {
  if (!selectedOrder.value) return;
  
  updatingStatus.value = true;
  
  try {
    // Update order status
    if (newStatus.value !== selectedOrder.value.status) {
      await ordersService.updateOrderStatus(selectedOrder.value.id, newStatus.value);
    }
    
    // Update payment status
    if (newPaymentStatus.value !== selectedOrder.value.paymentStatus) {
      await ordersService.updatePaymentStatus(selectedOrder.value.id, newPaymentStatus.value);
    }
    
    // Add note if provided
    if (statusNote.value.trim()) {
      await ordersService.addOrderNote(selectedOrder.value.id, statusNote.value);
    }
    
    // Update the order in the list
    const index = orders.value.findIndex(o => o.id === selectedOrder.value.id);
    if (index !== -1) {
      orders.value[index].status = newStatus.value;
      orders.value[index].paymentStatus = newPaymentStatus.value;
    }
    
    // Close modal
    closeStatusModal();
  } catch (error) {
    console.error('Error updating order status:', error);
  } finally {
    updatingStatus.value = false;
  }
};





// Lifecycle hooks
onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
.order-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f5;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}
</style>
