/**
 * Orders API Testing Suite
 * Run these tests in browser console to verify Orders functionality
 */

class OrdersTestSuite {
  constructor() {
    this.baseUrl = '/api/admin/orders';
    this.testResults = [];
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting Orders API Tests...\n');
    
    try {
      await this.testGetAllOrders();
      await this.testSearchOrders();
      await this.testFilterByStatus();
      await this.testFilterByPaymentStatus();
      await this.testFilterByDateRange();
      await this.testPagination();
      await this.testSorting();
      await this.testCombinedFilters();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Test basic orders retrieval
   */
  async testGetAllOrders() {
    console.log('📋 Testing: Get All Orders');
    
    try {
      const response = await fetch(this.baseUrl);
      const data = await response.json();
      
      if (response.ok && data.success) {
        this.logSuccess('Get All Orders', `Retrieved ${data.data?.length || 0} orders`);
        console.log('Sample order:', data.data?.[0]);
      } else {
        this.logError('Get All Orders', data.message || 'Failed to fetch orders');
      }
    } catch (error) {
      this.logError('Get All Orders', error.message);
    }
  }

  /**
   * Test search functionality
   */
  async testSearchOrders() {
    console.log('\n🔍 Testing: Search Orders');
    
    const searchTerms = [
      'test',
      'john',
      'pending',
      '123'
    ];

    for (const term of searchTerms) {
      try {
        const url = `${this.baseUrl}?filter=${encodeURIComponent(term)}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(`Search: "${term}"`, `Found ${data.data?.length || 0} results`);
        } else {
          this.logError(`Search: "${term}"`, data.message || 'Search failed');
        }
      } catch (error) {
        this.logError(`Search: "${term}"`, error.message);
      }
    }
  }

  /**
   * Test order status filtering
   */
  async testFilterByStatus() {
    console.log('\n📊 Testing: Filter by Order Status');
    
    const statuses = ['Pending', 'Paid', 'Shipped', 'Delivered', 'Cancelled'];

    for (const status of statuses) {
      try {
        const url = `${this.baseUrl}?filter=${encodeURIComponent(status)}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(`Status Filter: ${status}`, `Found ${data.data?.length || 0} orders`);
        } else {
          this.logError(`Status Filter: ${status}`, data.message || 'Filter failed');
        }
      } catch (error) {
        this.logError(`Status Filter: ${status}`, error.message);
      }
    }
  }

  /**
   * Test payment status filtering
   */
  async testFilterByPaymentStatus() {
    console.log('\n💳 Testing: Filter by Payment Status');
    
    const paymentStatuses = ['Paid', 'Pending', 'Failed', 'Refunded'];

    for (const status of paymentStatuses) {
      try {
        const url = `${this.baseUrl}?paymentStatus=${encodeURIComponent(status)}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(`Payment Filter: ${status}`, `Found ${data.data?.length || 0} orders`);
        } else {
          this.logError(`Payment Filter: ${status}`, data.message || 'Filter failed');
        }
      } catch (error) {
        this.logError(`Payment Filter: ${status}`, error.message);
      }
    }
  }

  /**
   * Test date range filtering
   */
  async testFilterByDateRange() {
    console.log('\n📅 Testing: Filter by Date Range');
    
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const dateTests = [
      {
        name: 'Last 7 days',
        dateFrom: lastWeek.toISOString().split('T')[0],
        dateTo: today.toISOString().split('T')[0]
      },
      {
        name: 'Today only',
        dateFrom: today.toISOString().split('T')[0],
        dateTo: today.toISOString().split('T')[0]
      }
    ];

    for (const test of dateTests) {
      try {
        const url = `${this.baseUrl}?dateFrom=${test.dateFrom}&dateTo=${test.dateTo}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(`Date Filter: ${test.name}`, `Found ${data.data?.length || 0} orders`);
        } else {
          this.logError(`Date Filter: ${test.name}`, data.message || 'Filter failed');
        }
      } catch (error) {
        this.logError(`Date Filter: ${test.name}`, error.message);
      }
    }
  }

  /**
   * Test pagination
   */
  async testPagination() {
    console.log('\n📄 Testing: Pagination');
    
    const paginationTests = [
      { page: 1, pageSize: 5 },
      { page: 2, pageSize: 10 },
      { page: 1, pageSize: 20 }
    ];

    for (const test of paginationTests) {
      try {
        const url = `${this.baseUrl}?page=${test.page}&pageSize=${test.pageSize}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(
            `Pagination: Page ${test.page}, Size ${test.pageSize}`, 
            `Retrieved ${data.data?.length || 0} orders, Total: ${data.totalItems || 0}`
          );
        } else {
          this.logError(`Pagination: Page ${test.page}`, data.message || 'Pagination failed');
        }
      } catch (error) {
        this.logError(`Pagination: Page ${test.page}`, error.message);
      }
    }
  }

  /**
   * Test sorting
   */
  async testSorting() {
    console.log('\n🔄 Testing: Sorting');
    
    const sortTests = [
      { orderBy: 'CreatedAt', descending: true },
      { orderBy: 'CreatedAt', descending: false },
      { orderBy: 'TotalPrice', descending: true },
      { orderBy: 'Status', descending: false }
    ];

    for (const test of sortTests) {
      try {
        const url = `${this.baseUrl}?orderBy=${test.orderBy}&descending=${test.descending}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(
            `Sort: ${test.orderBy} ${test.descending ? 'DESC' : 'ASC'}`, 
            `Retrieved ${data.data?.length || 0} orders`
          );
        } else {
          this.logError(`Sort: ${test.orderBy}`, data.message || 'Sorting failed');
        }
      } catch (error) {
        this.logError(`Sort: ${test.orderBy}`, error.message);
      }
    }
  }

  /**
   * Test combined filters
   */
  async testCombinedFilters() {
    console.log('\n🔗 Testing: Combined Filters');
    
    const combinedTests = [
      {
        name: 'Search + Status',
        params: 'filter=test&orderBy=CreatedAt&descending=true'
      },
      {
        name: 'Status + Payment + Pagination',
        params: 'filter=Pending&page=1&pageSize=5'
      },
      {
        name: 'Date + Sort',
        params: `dateFrom=${new Date().toISOString().split('T')[0]}&orderBy=TotalPrice&descending=true`
      }
    ];

    for (const test of combinedTests) {
      try {
        const url = `${this.baseUrl}?${test.params}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
          this.logSuccess(`Combined: ${test.name}`, `Found ${data.data?.length || 0} orders`);
        } else {
          this.logError(`Combined: ${test.name}`, data.message || 'Combined filter failed');
        }
      } catch (error) {
        this.logError(`Combined: ${test.name}`, error.message);
      }
    }
  }

  /**
   * Log successful test
   */
  logSuccess(testName, message) {
    console.log(`✅ ${testName}: ${message}`);
    this.testResults.push({ test: testName, status: 'PASS', message });
  }

  /**
   * Log failed test
   */
  logError(testName, message) {
    console.log(`❌ ${testName}: ${message}`);
    this.testResults.push({ test: testName, status: 'FAIL', message });
  }

  /**
   * Print test results summary
   */
  printResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📊 Total: ${this.testResults.length}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    console.log('\n🎯 Test Results Object:');
    console.table(this.testResults);
  }
}

// Export for use in console
window.OrdersTestSuite = OrdersTestSuite;

// Instructions for running tests
console.log(`
🧪 Orders API Test Suite Loaded!

To run tests, execute in console:
const tests = new OrdersTestSuite();
tests.runAllTests();

Or run individual tests:
tests.testGetAllOrders();
tests.testSearchOrders();
tests.testFilterByStatus();
tests.testPagination();
`);
