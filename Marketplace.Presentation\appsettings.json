{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=MarketplaceDB;Username=postgres;Password="}, "Authentication": {"Google": {"ClientId": "", "ClientSecret": ""}}, "Frontend": {"BaseUrl": "http://localhost:5296"}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "lsbo plpq nuna ldia", "SenderEmail": "<EMAIL>", "SenderName": "KlonDike"}, "Jwt": {"Issuer": "MarketplaceIssuer", "Audience": "MarketplaceAudience", "Key": "YourSuperSecretKey1234567890!@#$%^&*()", "ExpiryInDays": 7}, "FileService": {"StoragePath": "wwwroot/uploads", "BaseUrl": "uploads", "MaxFileSize": 10485760, "AllowedFileTypes": ["image/jpeg", "image/png", "image/gif", "image/webp", "image/jfif", "application/octet-stream", "application/pdf"]}}