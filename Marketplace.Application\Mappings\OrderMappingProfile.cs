﻿using AutoMapper;
using Marketplace.Application.Commands.Order;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Mappings;

public class OrderMappingProfile : Profile
{
    public OrderMappingProfile()
    {
        // Мапінг для створення замовлення (StoreOrderCommand -> Order)
        CreateMap<StoreOrderCommand, Order>()
            .ForMember(dest => dest.CustomerId, opt => opt.MapFrom(src => src.CustomerId))
            .ForMember(dest => dest.TotalPrice, opt => opt.MapFrom(src => new Money(src.TotalPriceAmount, src.TotalPriceCurrency)))
            .ForMember(dest => dest.ShippingAddressId, opt => opt.MapFrom(src => src.ShippingAddressId))
            .ForMember(dest => dest.ShippingMethodId, opt => opt.MapFrom(src => src.ShippingMethodId))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        CreateMap<Order, OrderResponse>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.CustomerId, opt => opt.MapFrom(src => src.CustomerId))
            .ForMember(dest => dest.CustomerName, opt => opt.MapFrom(src => src.Customer != null ? src.Customer.Username : "N/A"))
            .ForMember(dest => dest.CustomerEmail, opt => opt.MapFrom(src => src.Customer != null ? src.Customer.Email.Value : "N/A"))
            .ForMember(dest => dest.TotalPrice, opt => opt.MapFrom(src => src.TotalPrice))
            .ForMember(dest => dest.TotalPriceAmount, opt => opt.MapFrom(src => Math.Round(src.TotalPrice.Amount, 2))) // Округлюємо до 2 знаків
            .ForMember(dest => dest.TotalPriceCurrency, opt => opt.MapFrom(src => src.TotalPrice.Currency.ToString()))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.ShippingAddressId, opt => opt.MapFrom(src => src.ShippingAddressId))
            .ForMember(dest => dest.ShippingMethodId, opt => opt.MapFrom(src => src.ShippingMethodId))
            .ForMember(dest => dest.ShippingMethodName, opt => opt.MapFrom(src => "Standard")) // Тимчасово для швидкості
            .ForMember(dest => dest.ShippingAddressLine, opt => opt.MapFrom(src => "N/A")) // Буде встановлено в handler
            .ForMember(dest => dest.ShippingCity, opt => opt.MapFrom(src => "N/A")) // Буде встановлено в handler
            .ForMember(dest => dest.ShippingCountry, opt => opt.MapFrom(src => "N/A")) // Буде встановлено в handler
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            // Payment information - will be set manually in query handler
            .ForMember(dest => dest.PaymentStatus, opt => opt.Ignore())
            .ForMember(dest => dest.PaymentMethod, opt => opt.Ignore())
            .ForMember(dest => dest.PaymentStatusText, opt => opt.Ignore())
            .ForMember(dest => dest.PaymentMethodText, opt => opt.Ignore())
            .ForMember(dest => dest.ItemsCount, opt => opt.Ignore());

        // Мапінг для оновлення замовлення (UpdateOrderCommand -> Order)
        //CreateMap<UpdateOrderCommand, Order>()
        //    .ForMember(dest => dest.TotalPrice, opt => opt.MapFrom(src => new Money(src.TotalPriceAmount, src.TotalPriceCurrency)))
        //    .ForMember(dest => dest.ShippingAddressId, opt => opt.MapFrom(src => src.ShippingAddressId))
        //    .ForMember(dest => dest.ShippingMethodId, opt => opt.MapFrom(src => src.ShippingMethodId));
    }
}
