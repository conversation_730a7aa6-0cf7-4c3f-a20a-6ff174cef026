import{_ as tt,g as n,z as st,i as et,c as p,a as t,n as f,k as V,A as u,B as D,C as h,H as C,b as R,F as at,p as ot,t as c,w as lt,r as nt,d as A,o as v}from"./index-C1YYMYJd.js";import{o as S}from"./orders-CY0AS2VW.js";import{S as it}from"./StatusBadge-7HpbPXqn.js";import{P as dt}from"./Pagination-uai-tBBw.js";/* empty css                                                                    */const rt={class:"order-list"},ut={class:"level"},ct={class:"level-right"},pt={class:"level-item"},vt={class:"card mb-4"},mt={class:"card-content"},gt={class:"columns is-multiline"},ft={class:"column is-3"},bt={class:"field"},yt={class:"control has-icons-left"},ht={class:"column is-3"},St={class:"field"},_t={class:"control"},wt={class:"select is-fullwidth"},Ft={class:"column is-3"},Pt={class:"field"},Dt={class:"control"},Ct={class:"select is-fullwidth"},Tt={class:"column is-3"},Ot={class:"field"},kt={class:"control"},Mt={class:"select is-fullwidth"},xt={key:0,class:"column is-6"},Ut={class:"columns"},Rt={class:"column is-6"},It={class:"field"},Nt={class:"control"},Vt={class:"column is-6"},At={class:"field"},Lt={class:"control"},Et={class:"column is-12"},Bt={class:"field is-grouped is-grouped-right"},Yt={class:"control"},zt={class:"card"},jt={class:"card-content"},$t={key:0,class:"has-text-centered py-6"},Ht={key:1,class:"has-text-centered py-6"},qt={key:2},Gt={class:"table-container"},Jt={class:"table is-fullwidth is-hoverable"},Kt={class:"has-text-info"},Qt={class:"has-text-grey"},Wt={class:"has-text-success"},Xt={class:"buttons are-small"},Zt=["onClick"],ts={class:"modal-card"},ss={class:"modal-card-body"},es={key:0,class:"content"},as={class:"field"},os={class:"control"},ls={class:"select is-fullwidth"},ns={class:"field"},is={class:"control"},ds={class:"select is-fullwidth"},rs={class:"field"},us={class:"control"},cs={class:"modal-card-foot"},ps={__name:"OrderList",setup(vs){const r=n([]),_=n(!1),T=n(!1),w=n(1),I=n(1),L=n(0),N=n(10),O=n(!1),i=n(null),m=n(""),g=n(""),b=n(""),k=n(!1),F=n(null),o=st({search:"",status:"",paymentStatus:"",dateRange:"",dateFrom:"",dateTo:""}),y=async(a=1)=>{_.value=!0,w.value=a;try{const s={page:w.value,pageSize:Math.min(N.value,10),limit:Math.min(N.value,10),search:o.search,status:o.status,paymentStatus:o.paymentStatus};if(o.dateRange){const e=new Date,d=new Date(e.getFullYear(),e.getMonth(),e.getDate());switch(o.dateRange){case"today":dateFilters.dateFrom=d.toISOString().split("T")[0];break;case"yesterday":const M=new Date(d);M.setDate(M.getDate()-1),dateFilters.dateFrom=M.toISOString().split("T")[0],dateFilters.dateTo=d.toISOString().split("T")[0];break;case"last7days":const x=new Date(d);x.setDate(x.getDate()-7),dateFilters.dateFrom=x.toISOString().split("T")[0];break;case"last30days":const U=new Date(d);U.setDate(U.getDate()-30),dateFilters.dateFrom=U.toISOString().split("T")[0];break;case"thisMonth":const W=new Date(d.getFullYear(),d.getMonth(),1);dateFilters.dateFrom=W.toISOString().split("T")[0];break;case"lastMonth":const X=new Date(d.getFullYear(),d.getMonth()-1,1),Z=new Date(d.getFullYear(),d.getMonth(),0);dateFilters.dateFrom=X.toISOString().split("T")[0],dateFilters.dateTo=Z.toISOString().split("T")[0];break;case"custom":o.dateFrom&&(s.dateFrom=o.dateFrom),o.dateTo&&(s.dateTo=o.dateTo);break}}const l=await S.getOrders(s);console.log("Orders API response:",l),l.orders?(r.value=l.orders,console.log("Orders loaded:",r.value.length)):l.data?(r.value=l.data,console.log("Orders loaded (data field):",r.value.length)):(r.value=[],console.log("No orders found in response")),l.pagination&&(I.value=l.pagination.totalPages,L.value=l.pagination.total,w.value=l.pagination.page,console.log("Pagination:",l.pagination))}catch(s){console.error("Error fetching orders:",s)}finally{_.value=!1}},E=()=>{o.search="",o.status="",o.paymentStatus="",o.dateRange="",o.dateFrom="",o.dateTo="",y(1)},B=a=>{y(a)},Y=()=>{F.value&&clearTimeout(F.value),F.value=setTimeout(()=>{y(1),F.value=null},500)},z=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(a)):"",j=a=>{if(!a)return"₴0.00";const s=typeof a=="string"?parseFloat(a):a;return new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2,maximumFractionDigits:2}).format(s)},$=a=>({Pending:"Pending",Paid:"Paid",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled",0:"Pending",1:"Paid",2:"Shipped",3:"Delivered",4:"Cancelled"})[a]||a||"Unknown",H=a=>({Pending:"is-warning",Paid:"is-info",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger",0:"is-warning",1:"is-info",2:"is-primary",3:"is-success",4:"is-danger"})[a]||"is-light",q=a=>({Pending:"Pending",Completed:"Completed",Failed:"Failed",0:"Pending",1:"Completed",2:"Failed"})[a]||a||"Unknown",G=a=>({Pending:"is-warning",Completed:"is-success",Failed:"is-danger",0:"is-warning",1:"is-success",2:"is-danger"})[a]||"is-light",J=async()=>{T.value=!0;try{const a={};o.dateRange&&o.dateRange==="custom"&&(o.dateFrom&&(a.dateFrom=o.dateFrom),o.dateTo&&(a.dateTo=o.dateTo));const s=await S.exportOrders({search:o.search,status:o.status,paymentStatus:o.paymentStatus,...a}),l=window.URL.createObjectURL(s),e=document.createElement("a");e.style.display="none",e.href=l,e.download=`orders-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(e),e.click(),window.URL.revokeObjectURL(l)}catch(a){console.error("Error exporting orders:",a)}finally{T.value=!1}},K=a=>{i.value=a,m.value=a.status,g.value=a.paymentStatus,b.value="",O.value=!0},P=()=>{O.value=!1,i.value=null},Q=async()=>{if(i.value){k.value=!0;try{m.value!==i.value.status&&await S.updateOrderStatus(i.value.id,m.value),g.value!==i.value.paymentStatus&&await S.updatePaymentStatus(i.value.id,g.value),b.value.trim()&&await S.addOrderNote(i.value.id,b.value);const a=r.value.findIndex(s=>s.id===i.value.id);a!==-1&&(r.value[a].status=m.value,r.value[a].paymentStatus=g.value),P()}catch(a){console.error("Error updating order status:",a)}finally{k.value=!1}}};return et(()=>{y()}),(a,s)=>{const l=nt("router-link");return v(),p("div",rt,[t("div",ut,[s[10]||(s[10]=t("div",{class:"level-left"},[t("div",{class:"level-item"},[t("h1",{class:"title"},"Orders")])],-1)),t("div",ct,[t("div",pt,[t("button",{class:f(["button is-primary",{"is-loading":T.value}]),onClick:J},s[9]||(s[9]=[t("span",{class:"icon"},[t("i",{class:"fas fa-download"})],-1),t("span",null,"Export Orders",-1)]),2)])])]),t("div",vt,[t("div",mt,[t("div",gt,[t("div",ft,[t("div",bt,[s[12]||(s[12]=t("label",{class:"label"},"Search",-1)),t("div",yt,[u(t("input",{class:"input",type:"text",placeholder:"Order ID, customer name, email...","onUpdate:modelValue":s[0]||(s[0]=e=>o.search=e),onInput:Y},null,544),[[D,o.search]]),s[11]||(s[11]=t("span",{class:"icon is-small is-left"},[t("i",{class:"fas fa-search"})],-1))])])]),t("div",ht,[t("div",St,[s[14]||(s[14]=t("label",{class:"label"},"Status",-1)),t("div",_t,[t("div",wt,[u(t("select",{"onUpdate:modelValue":s[1]||(s[1]=e=>o.status=e)},s[13]||(s[13]=[C('<option value="" data-v-72d29fb1>All Statuses</option><option value="pending" data-v-72d29fb1>Pending</option><option value="processing" data-v-72d29fb1>Processing</option><option value="shipped" data-v-72d29fb1>Shipped</option><option value="delivered" data-v-72d29fb1>Delivered</option><option value="cancelled" data-v-72d29fb1>Cancelled</option><option value="refunded" data-v-72d29fb1>Refunded</option>',7)]),512),[[h,o.status]])])])])]),t("div",Ft,[t("div",Pt,[s[16]||(s[16]=t("label",{class:"label"},"Payment Status",-1)),t("div",Dt,[t("div",Ct,[u(t("select",{"onUpdate:modelValue":s[2]||(s[2]=e=>o.paymentStatus=e)},s[15]||(s[15]=[C('<option value="" data-v-72d29fb1>All Payment Statuses</option><option value="paid" data-v-72d29fb1>Paid</option><option value="pending" data-v-72d29fb1>Pending</option><option value="failed" data-v-72d29fb1>Failed</option><option value="refunded" data-v-72d29fb1>Refunded</option>',5)]),512),[[h,o.paymentStatus]])])])])]),t("div",Tt,[t("div",Ot,[s[18]||(s[18]=t("label",{class:"label"},"Date Range",-1)),t("div",kt,[t("div",Mt,[u(t("select",{"onUpdate:modelValue":s[3]||(s[3]=e=>o.dateRange=e)},s[17]||(s[17]=[C('<option value="" data-v-72d29fb1>All Time</option><option value="today" data-v-72d29fb1>Today</option><option value="yesterday" data-v-72d29fb1>Yesterday</option><option value="last7days" data-v-72d29fb1>Last 7 Days</option><option value="last30days" data-v-72d29fb1>Last 30 Days</option><option value="thisMonth" data-v-72d29fb1>This Month</option><option value="lastMonth" data-v-72d29fb1>Last Month</option><option value="custom" data-v-72d29fb1>Custom Range</option>',8)]),512),[[h,o.dateRange]])])])])]),o.dateRange==="custom"?(v(),p("div",xt,[t("div",Ut,[t("div",Rt,[t("div",It,[s[19]||(s[19]=t("label",{class:"label"},"From",-1)),t("div",Nt,[u(t("input",{class:"input",type:"date","onUpdate:modelValue":s[4]||(s[4]=e=>o.dateFrom=e)},null,512),[[D,o.dateFrom]])])])]),t("div",Vt,[t("div",At,[s[20]||(s[20]=t("label",{class:"label"},"To",-1)),t("div",Lt,[u(t("input",{class:"input",type:"date","onUpdate:modelValue":s[5]||(s[5]=e=>o.dateTo=e)},null,512),[[D,o.dateTo]])])])])])])):V("",!0),t("div",Et,[t("div",Bt,[t("div",{class:"control"},[t("button",{class:"button is-light",onClick:E}," Reset ")]),t("div",Yt,[t("button",{class:f(["button is-primary",{"is-loading":_.value}]),onClick:y}," Apply Filters ",2)])])])])])]),t("div",zt,[t("div",jt,[_.value&&!r.value.length?(v(),p("div",$t,s[21]||(s[21]=[t("span",{class:"icon is-large"},[t("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),t("p",{class:"mt-2"},"Loading orders...",-1)]))):r.value.length?(v(),p("div",qt,[t("div",Gt,[t("table",Jt,[s[26]||(s[26]=t("thead",null,[t("tr",null,[t("th",null,"Order ID"),t("th",null,"Customer"),t("th",null,"Date"),t("th",null,"Total"),t("th",null,"Status"),t("th",null,"Payment"),t("th",null,"Actions")])],-1)),t("tbody",null,[(v(!0),p(at,null,ot(r.value,e=>(v(),p("tr",{key:e.id},[t("td",null,[t("code",Kt,c(e.id),1)]),t("td",null,[t("div",null,[t("strong",null,c(e.customerName||e.userName||"N/A"),1),s[23]||(s[23]=t("br",null,null,-1)),t("small",Qt,c(e.customerEmail||"No email"),1)])]),t("td",null,c(z(e.createdAt)),1),t("td",null,[t("strong",Wt,c(j(e.total||e.totalPriceAmount)),1)]),t("td",null,[t("span",{class:f(["tag",H(e.status)])},c($(e.status)),3)]),t("td",null,[t("span",{class:f(["tag",G(e.paymentStatus)])},c(q(e.paymentStatus)),3)]),t("td",null,[t("div",Xt,[R(l,{to:`/admin/orders/${e.id}`,class:"button is-info",title:"View"},{default:lt(()=>s[24]||(s[24]=[t("span",{class:"icon is-small"},[t("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),t("button",{class:"button is-primary",onClick:d=>K(e),title:"Update Status"},s[25]||(s[25]=[t("span",{class:"icon is-small"},[t("i",{class:"fas fa-edit"})],-1)]),8,Zt)])])]))),128))])])]),R(dt,{"current-page":w.value,"total-pages":I.value,onPageChanged:B},null,8,["current-page","total-pages"])])):(v(),p("div",Ht,s[22]||(s[22]=[t("span",{class:"icon is-large"},[t("i",{class:"fas fa-shopping-cart fa-2x"})],-1),t("p",{class:"mt-2"},"No orders found",-1),t("p",{class:"mt-2"},"Try adjusting your filters",-1)])))])]),t("div",{class:f(["modal",{"is-active":O.value}])},[t("div",{class:"modal-background",onClick:P}),t("div",ts,[t("header",{class:"modal-card-head"},[s[27]||(s[27]=t("p",{class:"modal-card-title"},"Update Order Status",-1)),t("button",{class:"delete","aria-label":"close",onClick:P})]),t("section",ss,[i.value?(v(),p("div",es,[t("p",null,[s[28]||(s[28]=t("strong",null,"Order ID:",-1)),A(" "+c(i.value.id),1)]),t("p",null,[s[29]||(s[29]=t("strong",null,"Customer:",-1)),A(" "+c(i.value.userName),1)]),t("p",null,[s[30]||(s[30]=t("strong",null,"Current Status:",-1)),R(it,{status:i.value.status,type:"order"},null,8,["status"])]),t("div",as,[s[32]||(s[32]=t("label",{class:"label"},"New Status",-1)),t("div",os,[t("div",ls,[u(t("select",{"onUpdate:modelValue":s[6]||(s[6]=e=>m.value=e)},s[31]||(s[31]=[C('<option value="pending" data-v-72d29fb1>Pending</option><option value="processing" data-v-72d29fb1>Processing</option><option value="shipped" data-v-72d29fb1>Shipped</option><option value="delivered" data-v-72d29fb1>Delivered</option><option value="cancelled" data-v-72d29fb1>Cancelled</option><option value="refunded" data-v-72d29fb1>Refunded</option>',6)]),512),[[h,m.value]])])])]),t("div",ns,[s[34]||(s[34]=t("label",{class:"label"},"Payment Status",-1)),t("div",is,[t("div",ds,[u(t("select",{"onUpdate:modelValue":s[7]||(s[7]=e=>g.value=e)},s[33]||(s[33]=[t("option",{value:"pending"},"Pending",-1),t("option",{value:"paid"},"Paid",-1),t("option",{value:"failed"},"Failed",-1),t("option",{value:"refunded"},"Refunded",-1)]),512),[[h,g.value]])])])]),t("div",rs,[s[35]||(s[35]=t("label",{class:"label"},"Note (Optional)",-1)),t("div",us,[u(t("textarea",{class:"textarea","onUpdate:modelValue":s[8]||(s[8]=e=>b.value=e),placeholder:"Add a note about this status change"},"                ",512),[[D,b.value]])])])])):V("",!0)]),t("footer",cs,[t("button",{class:f(["button is-primary",{"is-loading":k.value}]),onClick:Q}," Update Status ",2),t("button",{class:"button",onClick:P},"Cancel")])])],2)])}}},hs=tt(ps,[["__scopeId","data-v-72d29fb1"]]);export{hs as default};
