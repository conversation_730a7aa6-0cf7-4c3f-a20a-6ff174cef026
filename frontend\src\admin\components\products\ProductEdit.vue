<template>
  <div class="product-edit">
    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader is-loading"></div>
      <p class="mt-4">Loading product data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <strong>Error:</strong> {{ error }}
    </div>

    <!-- Edit Form -->
    <form v-else @submit.prevent="handleSubmit" class="product-form">
      <!-- Header -->
      <div class="level mb-5">
        <div class="level-left">
          <div class="level-item">
            <button class="button" @click="goBack">
              <span class="icon">
                <i class="fas fa-arrow-left"></i>
              </span>
              <span>Back to Products</span>
            </button>
          </div>
          <div class="level-item">
            <div>
              <h1 class="title is-3">{{ isCreate ? 'Create Product' : 'Edit Product' }}</h1>
              <p v-if="!isCreate" class="subtitle is-6 has-text-grey">{{ form.id }}</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <button type="button" class="button" @click="$emit('cancel')">
                Cancel
              </button>
              <button type="submit" class="button is-primary" :class="{ 'is-loading': saving }">
                {{ isCreate ? 'Create Product' : 'Save Changes' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Content -->
      <div class="columns">
        <!-- Left Column -->
        <div class="column is-8">
          <!-- Basic Information -->
          <div class="box">
            <h2 class="title is-5 mb-4">📋 Basic Information</h2>
            <div class="columns is-multiline">
              <div class="column is-6">
                <CompanySelect
                  v-model="form.companyId"
                  label="Company"
                  placeholder="Search and select company..."
                  :required="true"
                  @change="onCompanyChange"
                />
              </div>
              <div class="column is-6">
                <CategorySelect
                  v-model="form.categoryId"
                  label="Category"
                  placeholder="Search and select category..."
                  :required="true"
                  @change="onCategoryChange"
                />
              </div>
              <div class="column is-12">
                <div class="field">
                  <label class="label">Product Name *</label>
                  <div class="control">
                    <input v-model="form.name" class="input" type="text" 
                           placeholder="Enter product name" required>
                  </div>
                </div>
              </div>
              <div class="column is-12">
                <div class="field">
                  <label class="label">Slug</label>
                  <div class="control">
                    <input v-model="form.slug" class="input" type="text" 
                           placeholder="Auto-generated from name">
                  </div>
                  <p class="help">Leave empty to auto-generate from product name</p>
                </div>
              </div>
              <div class="column is-12">
                <div class="field">
                  <label class="label">Description</label>
                  <div class="control">
                    <textarea v-model="form.description" class="textarea" 
                              placeholder="Enter product description" rows="4"></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pricing & Inventory -->
          <div class="box">
            <h2 class="title is-5 mb-4">💰 Pricing & Inventory</h2>
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">Price *</label>
                  <div class="control">
                    <input v-model.number="form.priceAmount" class="input" type="number" 
                           step="0.01" placeholder="0.00" required>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">Stock *</label>
                  <div class="control">
                    <input v-model.number="form.stock" class="input" type="number" 
                           min="0" placeholder="0" required>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Attributes -->
          <div class="box">
            <h2 class="title is-5 mb-4">🏷️ Product Attributes</h2>
            
            <!-- Add New Attribute -->
            <div class="field is-grouped">
              <div class="control is-expanded">
                <input v-model="newAttribute.key" class="input" type="text" 
                       placeholder="Attribute name (e.g., Color, Size)">
              </div>
              <div class="control is-expanded">
                <input v-model="newAttribute.value" class="input" type="text" 
                       placeholder="Attribute value (e.g., Red, Large)">
              </div>
              <div class="control">
                <button type="button" class="button is-primary" @click="addAttribute">
                  <span class="icon">
                    <i class="fas fa-plus"></i>
                  </span>
                  <span>Add</span>
                </button>
              </div>
            </div>

            <!-- Attributes Table -->
            <div v-if="Object.keys(form.attributes).length > 0" class="table-container">
              <table class="table is-fullwidth is-striped">
                <thead>
                  <tr>
                    <th>Attribute</th>
                    <th>Values</th>
                    <th width="100">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(values, key) in form.attributes" :key="key">
                    <td><strong>{{ key }}</strong></td>
                    <td>
                      <span class="tags">
                        <span v-for="(value, index) in (Array.isArray(values) ? values : [values])" 
                              :key="index" class="tag is-light">
                          {{ value }}
                          <button type="button" class="delete is-small" 
                                  @click="removeAttributeValue(key, index)"></button>
                        </span>
                      </span>
                    </td>
                    <td>
                      <button type="button" class="button is-small is-danger" 
                              @click="removeAttribute(key)">
                        <span class="icon">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="has-text-grey has-text-centered py-4">
              No attributes added yet. Use the form above to add product attributes.
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="column is-4">
          <!-- Status & Approval -->
          <div class="box">
            <h2 class="title is-5 mb-4">📊 Status & Approval</h2>
            <div class="field">
              <label class="label">Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="form.status">
                    <option value="0">Pending</option>
                    <option value="1">Approved</option>
                    <option value="2">Rejected</option>
                  </select>
                </div>
              </div>
            </div>

          </div>

          <!-- SEO Meta -->
          <div class="box">
            <h2 class="title is-5 mb-4">🔍 SEO Meta</h2>
            <div class="field">
              <label class="label">Meta Title</label>
              <div class="control">
                <input v-model="form.metaTitle" class="input" type="text" 
                       placeholder="SEO title for search engines">
              </div>
            </div>
            <div class="field">
              <label class="label">Meta Description</label>
              <div class="control">
                <textarea v-model="form.metaDescription" class="textarea" 
                          placeholder="SEO description for search engines" rows="3"></textarea>
              </div>
            </div>
            <div class="field">
              <label class="label">Meta Image</label>
              <div class="control">
                <div class="file has-name is-fullwidth">
                  <label class="file-label">
                    <input class="file-input" type="file" accept="image/*" @change="handleImageUpload" :disabled="uploadingImage">
                    <span class="file-cta" :class="{ 'is-loading': uploadingImage }">
                      <span class="file-icon" v-if="!uploadingImage">
                        <i class="fas fa-upload"></i>
                      </span>
                      <span class="file-label">{{ uploadingImage ? 'Uploading...' : 'Choose image...' }}</span>
                    </span>
                    <span class="file-name">{{ imageFileName || 'No file selected' }}</span>
                  </label>
                </div>
              </div>
              <div v-if="form.metaImage" class="mt-3">
                <figure class="image is-128x128">
                  <img :src="form.metaImage" :alt="form.name" class="is-rounded">
                </figure>
              </div>
            </div>
          </div>

          <!-- Product Images Manager -->
          <div class="box">
            <ProductImageManager
              ref="imageManager"
              :product-id="currentProductId"
              :images="productImages"
              :is-create="props.isCreate"
              @images-updated="loadProductImages"
              @main-image-changed="handleMainImageChanged"
              @image-uploaded="handleImageUploaded"
              @image-deleted="handleImageDeleted"
              @pending-images-changed="handlePendingImagesChanged"
            />
          </div>

          <!-- Timestamps (Read-only for edit) -->
          <div v-if="!isCreate" class="box">
            <h2 class="title is-5 mb-4">⏰ Timestamps</h2>
            <div class="field">
              <label class="label">Created At</label>
              <div class="control">
                <input class="input" type="text" :value="formatDate(form.createdAt)" readonly>
              </div>
            </div>
            <div v-if="form.updatedAt" class="field">
              <label class="label">Updated At</label>
              <div class="control">
                <input class="input" type="text" :value="formatDate(form.updatedAt)" readonly>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { productsService } from '@/admin/services/products';
import CategorySelect from '@/admin/components/common/CategorySelect.vue';
import CompanySelect from '@/admin/components/common/CompanySelect.vue';
import ProductImageManager from './ProductImageManager.vue';
import api from '@/services/api';

// Props
const props = defineProps({
  productId: {
    type: String,
    required: false
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['save', 'cancel']);

// Route
const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const uploadingImage = ref(false);
const error = ref(null);
const imageFileName = ref('');
const productImages = ref([]);
const imageManager = ref(null); // Reference to ProductImageManager component

// Form data
const form = ref({
  id: '',
  companyId: '',
  name: '',
  slug: '',
  description: '',
  priceAmount: 0,
  priceCurrency: 0, // 0 = UAH, 1 = USD, 2 = EUR
  stock: 0,
  status: '0', // 0 = Pending, 1 = Approved, 2 = Rejected
  categoryId: '',
  attributes: {},
  metaTitle: '',
  metaDescription: '',
  metaImage: '',
  createdAt: null,
  updatedAt: null
});

// New attribute form
const newAttribute = ref({
  key: '',
  value: ''
});

// Get product ID from props or route
const currentProductId = computed(() => {
  return props.productId || route.params.id;
});



// Methods
const addAttribute = () => {
  if (!newAttribute.value.key.trim() || !newAttribute.value.value.trim()) {
    return;
  }
  
  const key = newAttribute.value.key.trim();
  const value = newAttribute.value.value.trim();
  
  if (form.value.attributes[key]) {
    // If attribute exists, add to array or convert to array
    if (Array.isArray(form.value.attributes[key])) {
      form.value.attributes[key].push(value);
    } else {
      form.value.attributes[key] = [form.value.attributes[key], value];
    }
  } else {
    // New attribute
    form.value.attributes[key] = value;
  }
  
  // Clear form
  newAttribute.value.key = '';
  newAttribute.value.value = '';
};

const removeAttribute = (key) => {
  delete form.value.attributes[key];
};

const removeAttributeValue = (key, index) => {
  const values = form.value.attributes[key];
  if (Array.isArray(values)) {
    if (values.length === 1) {
      delete form.value.attributes[key];
    } else {
      values.splice(index, 1);
    }
  } else {
    delete form.value.attributes[key];
  }
};

const handleImageUpload = async (event) => {
  const file = event.target.files[0];
  if (file) {
    imageFileName.value = file.name;
    try {
        const reader = new FileReader();
        reader.onload = (e) => {
          form.value.metaImage = e.target.result;
        };
        reader.readAsDataURL(file);
    } catch (err) {
      console.error('Error uploading image:', err);
      error.value = 'Failed to upload image. Please try again.';
    } finally {
      uploadingImage.value = false;
    }
  }
};

// Event handlers for ProductImageManager
const handleMainImageChanged = (imageId) => {
  console.log('Main image changed:', imageId);
  // The component will handle the API call
};

const handleImageUploaded = (uploadResult) => {
  console.log('Image uploaded:', uploadResult);
  // Reload images to show the new one
  loadProductImages();
};

const handleImageDeleted = (imageId) => {
  console.log('Image deleted:', imageId);
  // The component will handle the API call and emit this event
};

const handlePendingImagesChanged = (pendingImages) => {
  console.log('Pending images changed:', pendingImages.length);
  // Store reference for save operation
};

const loadProductImages = async () => {
  if (props.isCreate || !currentProductId.value) return;

  try {
    const response = await api.get(`/api/admin/products/${currentProductId.value}/with-images`);

    if (response.data && response.data.success && response.data.data) {
      productImages.value = response.data.data.images || [];
    }
  } catch (err) {
    console.error('Error loading product images:', err);
    productImages.value = [];
  }
};



const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    // Handle backend date format: "2025-06-02 20:35:40.231835+03"
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return dateString;
  }
};

const onCategoryChange = (category) => {
  console.log('Category changed:', category);
};

const onCompanyChange = (company) => {
  console.log('Company changed:', company);
};

const goBack = () => {
  router.push('/admin/products');
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    error.value = null;

    // Validate required fields
    if (!form.value.companyId) {
      error.value = 'Company is required';
      saving.value = false;
      return;
    }

    if (!form.value.name || !form.value.name.trim()) {
      error.value = 'Product name is required';
      saving.value = false;
      return;
    }

    if (!form.value.description || !form.value.description.trim()) {
      error.value = 'Product description is required';
      saving.value = false;
      return;
    }

    if (!form.value.categoryId) {
      error.value = 'Category is required';
      saving.value = false;
      return;
    }

    if (!form.value.priceAmount || form.value.priceAmount <= 0) {
      error.value = 'Price must be greater than 0';
      saving.value = false;
      return;
    }

    // Generate slug if empty
    const generatedSlug = form.value.slug || form.value.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');

    // Prepare data for API - must match StoreProductCommand exactly
    const productData = {
      companyId: form.value.companyId,
      name: form.value.name.trim(),
      slug: generatedSlug,
      description: form.value.description.trim(),
      priceCurrency: 0, // 0 = UAH, 1 = USD, 2 = EUR (send as enum number)
      priceAmount: parseFloat(form.value.priceAmount) || 0,
      stock: parseInt(form.value.stock) || 0,
      categoryId: form.value.categoryId,
      status: parseInt(form.value.status) || 0, // 0=Pending, 1=Approved, 2=Rejected
      attributes: form.value.attributes || null, // Can be null according to command
      metaTitle: form.value.metaTitle || form.value.name || '',
      metaDescription: form.value.metaDescription || form.value.description || '',
      metaImage: form.value.metaImage && form.value.metaImage.trim() && form.value.metaImage.trim() !== '' && !form.value.metaImage.startsWith('data:')
        ? form.value.metaImage.trim()
        : 'https://via.placeholder.com/300x200.png?text=Product+Image' // Always provide valid URL, skip data URLs
    };

    console.log('📤 Sending product data:', productData);

    let productId;
    if (props.isCreate) {
      const result = await productsService.createProduct(productData);
      console.log('✅ Product created:', result);
      productId = result.data || result;
    } else {
      const result = await productsService.updateProduct(currentProductId.value, productData);
      console.log('✅ Product updated:', result);
      productId = currentProductId.value;
    }

    // Завантажуємо тимчасові зображення після збереження продукту
    if (imageManager.value && productId) {
      const pendingImages = imageManager.value.getPendingImages();
      if (pendingImages.length > 0) {
        console.log(`🔄 Uploading ${pendingImages.length} pending images...`);
        await imageManager.value.uploadPendingImages(productId);
      }
    }

    emit('save', productData);
  } catch (err) {
    console.error('❌ Error saving product:', err);

    // Extract detailed error message
    let errorMessage = 'Failed to save product';
    if (err.response && err.response.data) {
      if (err.response.data.message) {
        errorMessage = err.response.data.message;
      } else if (err.response.data.errors) {
        errorMessage = Object.values(err.response.data.errors).flat().join(', ');
      } else if (typeof err.response.data === 'string') {
        errorMessage = err.response.data;
      }
    } else if (err.message) {
      errorMessage = err.message;
    }

    error.value = errorMessage;
  } finally {
    saving.value = false;
  }
};

// Auto-generate slug from name
watch(() => form.value.name, (newName) => {
  if (newName && !form.value.slug) {
    form.value.slug = newName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
});

// Load data
const loadProduct = async () => {
  if (props.isCreate) return;
  
  try {
    loading.value = true;
    error.value = null;
    
    const response = await productsService.getProductById(currentProductId.value);
    const product = response.data || response;
    
    // Populate form
    Object.keys(form.value).forEach(key => {
      if (product[key] !== undefined) {
        form.value[key] = product[key];
      }
    });
    
    // Parse attributes
    if (product.attributes) {
      try {
        form.value.attributes = typeof product.attributes === 'string' 
          ? JSON.parse(product.attributes) 
          : product.attributes;
      } catch (e) {
        console.error('Error parsing attributes:', e);
        form.value.attributes = {};
      }
    }
    
    console.log('Product loaded for editing:', form.value);

    // Load product images
    await loadProductImages();
  } catch (err) {
    console.error('Error loading product:', err);
    error.value = err.message || 'Failed to load product';
  } finally {
    loading.value = false;
  }
};





// Initialize
onMounted(async () => {
  await loadProduct();
});
</script>

<style scoped>
.product-edit {
  padding: 1rem;
}

.loader {
  width: 3rem;
  height: 3rem;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.box {
  margin-bottom: 1.5rem;
}

.tags .tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.image img {
  object-fit: cover;
}
</style>
