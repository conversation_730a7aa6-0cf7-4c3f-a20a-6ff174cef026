<template>
  <div class="card admin-stat-card">
    <div class="card-content" :class="color">
      <div class="level is-mobile">
        <div class="level-left">
          <div class="level-item">
            <div>
              <p class="heading">{{ title }}</p>
              <p class="title">{{ value }}</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <span class="icon is-large">
              <i :class="['fas', 'fa-' + icon, 'fa-2x']"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'is-primary'
  }
});
</script>

<style scoped>
.admin-stat-card {
  border-radius: 6px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.admin-stat-card:hover {
  transform: translateY(-5px);
}

.card-content {
  padding: 1.5rem;
  border-radius: 6px;
}

.card-content.is-primary {
  background: linear-gradient(135deg, #00d1b2, #009e86);
  color: white;
}

.card-content.is-info {
  background: linear-gradient(135deg, #3e8ed0, #2160a5);
  color: white;
}

.card-content.is-success {
  background: linear-gradient(135deg, #48c78e, #2fa866);
  color: white;
}

.card-content.is-warning {
  background: linear-gradient(135deg, #ffe08a, #ffd04d);
  color: rgba(0, 0, 0, 0.7);
}

.card-content.is-danger {
  background: linear-gradient(135deg, #f14668, #cc0f35);
  color: white;
}

.heading {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
}

.icon {
  opacity: 0.8;
}
</style>
