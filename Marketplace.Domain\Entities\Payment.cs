using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public enum PaymentMethod
{
    Card,
    PayPal,
    BankTransfer
}

public enum PaymentStatus
{
    Pending,
    Completed,
    Refunded,
    Failed
}

public class Payment : IEntity
{
    private Money _amount;
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Order Order { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public Money Amount
    {
        get => _amount;
        set => UpdateAmount(value);
    }
    public PaymentStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private Payment() { }

    public Payment(
        Guid orderId,
        PaymentMethod paymentMethod,
        Money amount,
        PaymentStatus paymentStatus
    )
    {
        ValidateCreation(orderId, amount);

        Id = Guid.NewGuid();
        OrderId = orderId;
        PaymentMethod = paymentMethod;
        Amount = amount;
        Status = paymentStatus;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = null;
    }

    public void Update(
        Currency? paymentAmountCurrency = null,
        decimal? paymentAmountAmount = null,
        PaymentMethod? paymentMethod = null,
        PaymentStatus? status = null
    )
    {
        if (paymentAmountCurrency != null || paymentAmountAmount != null)
        {
            UpdateAmount(new Money(
                paymentAmountAmount ?? Amount.Amount,
                paymentAmountCurrency ?? Amount.Currency
            ));
        }

        if (status != null) UpdateStatus(status.Value);
        if (paymentMethod != null) UpdateMethod(paymentMethod.Value);
    }

    public void UpdateAmount(Money amount)
    {
        _amount = amount ?? throw new ArgumentNullException(nameof(amount));
    }

    public void UpdateStatus(PaymentStatus status)
    {
        Status = status;
    }
    public void UpdateMethod(PaymentMethod method)
    {
        PaymentMethod = method;
    }

    private void ValidateCreation(Guid orderId, Money amount)
    {
        if (orderId == Guid.Empty)
            throw new ArgumentNullException(nameof(orderId), "Order ID cannot be empty.");
        if (amount == null || amount.Amount <= 0)
            throw new ArgumentNullException(nameof(amount), "Amount must be a valid and positive value.");
    }
}
