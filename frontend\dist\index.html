<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <title>Klondike</title>
  <style>
    :root {
      --dark-bg: #111827;
      --darker-bg: #0f172a;
      --card-bg: #1e293b;
      --text-primary: #f3f4f6;
      --text-secondary: #9ca3af;
      --accent-color: #3b82f6;
      --accent-color-dark: #2563eb;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --border-color: #374151;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--dark-bg);
      color: var(--text-primary);
    }

    /* Fix for Font Awesome icons */
    .fas, .far, .fab, .fa {
      font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands' !important;
      font-weight: 900;
    }

    /* Global styles */
    .button {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;
      background-color: var(--card-bg);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
    }

    .button:hover {
      background-color: var(--darker-bg);
      color: var(--text-primary);
    }

    .card {
      border-radius: 8px;
      overflow: hidden;
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .box {
      border-radius: 8px;
      background-color: var(--card-bg);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
    }

    .title, .subtitle {
      color: var(--text-primary);
    }

    .table {
      background-color: var(--card-bg);
      color: var(--text-primary);
    }

    .table th {
      color: var(--text-secondary);
    }

    .table td {
      border-color: var(--border-color);
    }

    /* Improve scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--darker-bg);
    }

    ::-webkit-scrollbar-thumb {
      background: #4b5563;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #6b7280;
    }

    /* Filament-style stat cards */
    .stat-card {
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .stat-card .stat-title {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin-bottom: 0.5rem;
    }

    .stat-card .stat-value {
      font-size: 1.875rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .stat-card .stat-trend {
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
    }

    .stat-card .trend-up {
      color: var(--success-color);
    }

    .stat-card .trend-down {
      color: var(--danger-color);
    }

    /* Chart styles */
    .chart-container {
      width: 100%;
      height: 200px;
      margin-top: auto;
    }

    /* Date picker styles */
    .date-picker {
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
      color: var(--text-primary);
      padding: 0.5rem 1rem;
      border-radius: 6px;
    }

    /* Dropdown styles */
    .dropdown-content {
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
    }

    .dropdown-item {
      color: var(--text-primary);
    }

    .dropdown-item:hover {
      background-color: var(--darker-bg);
      color: var(--text-primary);
    }
  </style>
  <script type="module" crossorigin src="/assets/index-DMg5qKr1.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-C988bJkl.css">
</head>
<body>
  <div id="app"></div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Ensure Font Awesome is properly loaded -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
  <!-- Add Chart.js for graphs -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>
