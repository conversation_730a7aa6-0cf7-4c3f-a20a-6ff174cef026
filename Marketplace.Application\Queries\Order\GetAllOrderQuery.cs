﻿using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;

namespace Marketplace.Application.Queries.Order;

public record GetAllOrderQuery(
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null,
    OrderStatus? Status = null,
    PaymentStatus? PaymentStatus = null
    ) : PaginatedQuery<OrderResponse>(Filter, OrderBy, Descending, Page, PageSize);
