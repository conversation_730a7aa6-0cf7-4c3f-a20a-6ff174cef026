using Marketplace.Domain.Services;
using Marketplace.Infrastructure.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Marketplace.Presentation.Middleware;

/// <summary>
/// Middleware для обслуговування статичних файлів
/// </summary>
public class FileMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<FileMiddleware> _logger;
    private readonly string _baseUrl;
    private readonly string _storagePath;

    public FileMiddleware(
        RequestDelegate next,
        IOptions<Infrastructure.Services.FileServiceOptions> options,
        ILogger<FileMiddleware> logger)
    {
        _next = next;
        _logger = logger;
        _baseUrl = options.Value.BaseUrl.TrimStart('/').TrimEnd('/');
        _storagePath = options.Value.StoragePath;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value;
        _logger.LogInformation("FileMiddleware: Processing request for path: {Path}", path);
        _logger.LogInformation("FileMiddleware: BaseUrl: {BaseUrl}, StoragePath: {StoragePath}", _baseUrl, _storagePath);

        // Перевіряємо, чи запит стосується файлу
        if (path.StartsWith($"/{_baseUrl}", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogInformation("FileMiddleware: Path matches base URL pattern");

            // Отримуємо відносний шлях до файлу
            var relativePath = path.Substring($"/{_baseUrl}".Length).TrimStart('/');
            _logger.LogInformation("FileMiddleware: Relative path: {RelativePath}", relativePath);

            // Формуємо повний шлях до файлу
            var filePath = Path.Combine(_storagePath, relativePath);
            _logger.LogInformation("FileMiddleware: Full file path: {FilePath}", filePath);

            // Перевіряємо, чи файл існує
            if (File.Exists(filePath))
            {
                _logger.LogInformation("FileMiddleware: File exists, serving file");
            }
            else
            {
                _logger.LogWarning("FileMiddleware: File not found: {FilePath}", filePath);
            }

            if (File.Exists(filePath))
            {
                try
                {
                    // Визначаємо MIME-тип файлу
                    var contentType = GetContentTypeFromFileName(filePath);

                    // Встановлюємо заголовки відповіді
                    context.Response.ContentType = contentType;
                    context.Response.Headers.Add("Cache-Control", "public, max-age=86400"); // Кешування на 1 день

                    // Відправляємо файл
                    await context.Response.SendFileAsync(filePath);
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Помилка при обслуговуванні файлу: {FilePath}", filePath);
                }
            }
        }

        // Якщо запит не стосується файлу або виникла помилка, передаємо запит далі
        await _next(context);
    }

    private string GetContentTypeFromFileName(string filePath)
    {
        // Отримуємо розширення файлу
        var extension = Path.GetExtension(filePath).ToLowerInvariant();

        // Визначаємо MIME-тип за розширенням
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".webp" => "image/webp",
            ".pdf" => "application/pdf",
            _ => "application/octet-stream"
        };
    }
}
