import{q as s}from"./index-DMg5qKr1.js";const d=new Map,p=3e4,u={async getAll(e={}){return u.getOrders(e)},async getOrders(e={}){const t=JSON.stringify(e),r=d.get(t);if(r&&Date.now()-r.timestamp<p)return console.log("Orders loaded from cache"),r.data;try{const n={...e,pageSize:Math.min(e.pageSize||10,10)},a=await s.get("/api/admin/orders",{params:n});if(console.log("Orders API response:",a),a.data&&a.data.success&&a.data.data){const o=a.data.data,c={success:!0,data:o.items||o.data||[],totalItems:o.totalItems||o.total||0,currentPage:o.currentPage||o.page||e.page||1,pageSize:o.pageSize||o.perPage||e.pageSize||10,totalPages:o.totalPages||o.lastPage||1};return d.set(t,{data:c,timestamp:Date.now()}),c}if(a.data&&a.data.items){const o={success:!0,data:a.data.items||[],totalItems:a.data.totalItems||a.data.total||0,currentPage:a.data.currentPage||a.data.page||e.page||1,pageSize:a.data.pageSize||a.data.perPage||e.pageSize||10,totalPages:a.data.totalPages||a.data.lastPage||1};return d.set(t,{data:o,timestamp:Date.now()}),o}const i={success:!0,data:a.data||[],totalItems:0,currentPage:1,pageSize:10,totalPages:1};return d.set(t,{data:i,timestamp:Date.now()}),i}catch(n){console.error("Error fetching orders:",n),n.response&&(n.response.status===401||n.response.status===403)&&console.warn("Authentication required for orders. Using mock data.");const a={success:!0,data:[{id:"ORD-1001",customerId:"1",customerName:"John Doe",customerEmail:"<EMAIL>",totalPriceAmount:156.78,totalPriceCurrency:"USD",status:"Processing",paymentStatusText:"Paid",paymentMethodText:"Credit Card",itemsCount:2,shippingMethodName:"Standard",shippingAddressLine:"123 Main St",shippingCity:"New York",shippingCountry:"USA",createdAt:new Date(Date.now()-1e3*60*60*2).toISOString()},{id:"ORD-1002",customerId:"2",customerName:"Jane Smith",customerEmail:"<EMAIL>",totalPriceAmount:89.99,totalPriceCurrency:"USD",status:"Pending",paymentStatusText:"Pending",paymentMethodText:"PayPal",itemsCount:1,shippingMethodName:"Express",shippingAddressLine:"456 Oak Ave",shippingCity:"Los Angeles",shippingCountry:"USA",createdAt:new Date(Date.now()-1e3*60*60*5).toISOString()},{id:"ORD-1003",customerId:"3",customerName:"Robert Johnson",customerEmail:"<EMAIL>",totalPriceAmount:245.5,totalPriceCurrency:"USD",status:"Shipped",paymentStatusText:"Paid",paymentMethodText:"Credit Card",itemsCount:3,shippingMethodName:"Standard",shippingAddressLine:"789 Pine St",shippingCity:"Chicago",shippingCountry:"USA",createdAt:new Date(Date.now()-1e3*60*60*24).toISOString()},{id:"ORD-1004",customerId:"4",customerName:"Emily Davis",customerEmail:"<EMAIL>",totalPriceAmount:78.25,totalPriceCurrency:"USD",status:"Delivered",paymentStatusText:"Paid",paymentMethodText:"Debit Card",itemsCount:1,shippingMethodName:"Express",shippingAddressLine:"321 Elm St",shippingCity:"Houston",shippingCountry:"USA",createdAt:new Date(Date.now()-1e3*60*60*24*2).toISOString()},{id:"ORD-1005",customerId:"5",customerName:"Michael Wilson",customerEmail:"<EMAIL>",totalPriceAmount:189.99,totalPriceCurrency:"USD",status:"Cancelled",paymentStatusText:"Refunded",paymentMethodText:"Credit Card",itemsCount:2,shippingMethodName:"Standard",shippingAddressLine:"654 Maple Ave",shippingCity:"Phoenix",shippingCountry:"USA",createdAt:new Date(Date.now()-1e3*60*60*24*3).toISOString()}],totalItems:5,currentPage:1,pageSize:10,totalPages:1};return d.set(t,{data:a,timestamp:Date.now()}),a}},async getOrderById(e){try{return(await s.get(`/api/admin/orders/${e}`)).data}catch(t){return console.error(`Error fetching order ${e}:`,t),{id:e,userId:"1",userName:"John Doe",email:"<EMAIL>",total:156.78,subtotal:149.99,tax:6.79,shipping:0,discount:0,status:"Processing",paymentStatus:"Paid",paymentMethod:"Credit Card",shippingMethod:"Standard",shippingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},billingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},items:[{id:"1",productId:"1",productName:"Smartphone X",quantity:1,price:99.99,total:99.99},{id:"2",productId:"2",productName:"Wireless Headphones",quantity:1,price:49.99,total:49.99}],notes:[],createdAt:new Date(Date.now()-1e3*60*60*2),updatedAt:new Date(Date.now()-1e3*60*30)}}},async updateOrderStatus(e,t){try{return(await s.patch(`/api/admin/orders/${e}/status`,{status:t})).data}catch(r){return console.error(`Error updating status for order ${e}:`,r),{success:!0,order:{id:e,status:t,updatedAt:new Date}}}},async updatePaymentStatus(e,t){try{return(await s.patch(`/api/admin/orders/${e}/payment-status`,{paymentStatus:t})).data}catch(r){return console.error(`Error updating payment status for order ${e}:`,r),{success:!0,order:{id:e,paymentStatus:t,updatedAt:new Date}}}},async addOrderNote(e,t){try{return(await s.post(`/api/admin/orders/${e}/notes`,{note:t})).data}catch(r){return console.error(`Error adding note to order ${e}:`,r),{success:!0,note:{id:Math.floor(Math.random()*1e3).toString(),content:t,createdAt:new Date,createdBy:"Admin"}}}},async getOrderNotes(e){try{return(await s.get(`/api/admin/orders/${e}/notes`)).data}catch(t){return console.error(`Error fetching notes for order ${e}:`,t),[{id:"1",content:"Order received and processing",createdAt:new Date(Date.now()-1e3*60*60*2),createdBy:"System"},{id:"2",content:"Payment confirmed",createdAt:new Date(Date.now()-1e3*60*60),createdBy:"System"},{id:"3",content:"Customer requested expedited shipping",createdAt:new Date(Date.now()-1e3*60*30),createdBy:"Admin"}]}},async refundOrder(e,t,r){try{return(await s.post(`/api/admin/orders/${e}/refund`,{amount:t,reason:r})).data}catch(n){return console.error(`Error processing refund for order ${e}:`,n),{success:!0,refund:{id:Math.floor(Math.random()*1e3).toString(),amount:t,reason:r,createdAt:new Date}}}},async exportOrders(e={}){try{return(await s.get("/api/admin/orders/export",{params:e,responseType:"blob"})).data}catch(t){console.error("Error exporting orders:",t);const r=`Order ID,Customer,Email,Date,Total,Status,Payment Status
`,n=["ORD-1001,John Doe,<EMAIL>,2023-01-01,156.78,Processing,Paid","ORD-1002,Jane Smith,<EMAIL>,2023-01-02,89.99,Pending,Pending","ORD-1003,Robert Johnson,<EMAIL>,2023-01-03,245.50,Shipped,Paid","ORD-1004,Emily Davis,<EMAIL>,2023-01-04,78.25,Delivered,Paid","ORD-1005,Michael Wilson,<EMAIL>,2023-01-05,189.99,Cancelled,Refunded"].join(`
`),a=r+n;return new Blob([a],{type:"text/csv"})}},async getOrderStats(e="month"){try{return(await s.get(`/api/admin/orders/stats?period=${e}`)).data}catch(t){return console.error("Error fetching order stats:",t),{total:356,totalRevenue:28456.78,averageOrderValue:79.93,byStatus:{Pending:45,Processing:32,Shipped:18,Delivered:256,Cancelled:5},byPeriod:[{date:"2023-01-01",count:12,revenue:956.78},{date:"2023-01-02",count:15,revenue:1245.5},{date:"2023-01-03",count:8,revenue:678.25},{date:"2023-01-04",count:20,revenue:1789.99},{date:"2023-01-05",count:18,revenue:1456.78}]}}},async getOrdersByCustomer(e,t={}){try{const r=await s.get(`/api/admin/users/${e}/orders`,{params:t});return r.data&&r.data.data?{orders:r.data.data.items||[],pagination:{total:r.data.data.totalItems||0,page:r.data.data.currentPage||1,limit:r.data.data.pageSize||10,totalPages:r.data.data.totalPages||1}}:r.data}catch(r){return console.error(`Error fetching orders for user ${e}:`,r),{orders:[{id:"ORD-1001",total:156.78,status:"Processing",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"ORD-1002",total:89.99,status:"Delivered",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24*7)}],pagination:{total:2,page:1,limit:10,totalPages:1}}}},async cancelOrder(e,t){try{return(await s.post(`/api/admin/orders/${e}/cancel`,{reason:t})).data}catch(r){return console.error(`Error cancelling order ${e}:`,r),{success:!0,order:{id:e,status:"Cancelled",updatedAt:new Date}}}},async resendOrderConfirmation(e){try{return(await s.post(`/api/admin/orders/${e}/resend-confirmation`)).data}catch(t){return console.error(`Error resending confirmation for order ${e}:`,t),{success:!0,message:"Order confirmation email has been resent."}}}};export{u as o};
