import{M as zt,_ as os,g as er,z as ls,i as cs,c as Pr,a as B,n as nt,k as w0,A as Fr,B as $t,C as Et,H as Kt,b as kn,F as hs,p as us,t as yr,w as xs,r as ds,d as S0,o as Lr}from"./index-BtyG65bR.js";import{o as St}from"./orders-D4s5ZKaF.js";import{S as ps}from"./StatusBadge-Tzu5Tszv.js";import{P as vs}from"./Pagination-BX7rerWh.js";/* empty css                                                                    *//*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var an={};an.version="0.18.5";var ua=1252,ms=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],xa=function(e){ms.indexOf(e)!=-1&&(ua=e)};function gs(){xa(1252)}var It=function(e){xa(e)};function _s(){It(1200),gs()}var Yt=function(t){return String.fromCharCode(t)},A0=function(t){return String.fromCharCode(t)},F0,Br="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function kt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,l=0,o=0;o<e.length;)r=e.charCodeAt(o++),i=r>>2,n=e.charCodeAt(o++),s=(r&3)<<4|n>>4,a=e.charCodeAt(o++),f=(n&15)<<2|a>>6,l=a&63,isNaN(n)?f=l=64:isNaN(a)&&(l=64),t+=Br.charAt(i)+Br.charAt(s)+Br.charAt(f)+Br.charAt(l);return t}function Ir(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)i=Br.indexOf(e.charAt(o++)),s=Br.indexOf(e.charAt(o++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=Br.indexOf(e.charAt(o++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),l=Br.indexOf(e.charAt(o++)),a=(f&3)<<6|l,l!==64&&(t+=String.fromCharCode(a));return t}var ve=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Nr=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function Kr(e){return ve?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function y0(e){return ve?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var pr=function(t){return ve?Nr(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function _n(e){if(typeof ArrayBuffer>"u")return pr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function Ut(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Ts(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}var Ve=ve?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Nr(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function Es(e){for(var t=[],r=0,n=e.length+250,a=Kr(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=Kr(65535),n=65530)}return t.push(a.slice(0,r)),Ve(t)}var Ft=/\u0000/g,Jt=/[\u0001-\u0006]/g;function lt(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function vr(e,t){var r=""+e;return r.length>=t?r:Re("0",t-r.length)+r}function jn(e,t){var r=""+e;return r.length>=t?r:Re(" ",t-r.length)+r}function sn(e,t){var r=""+e;return r.length>=t?r:r+Re(" ",t-r.length)}function ws(e,t){var r=""+Math.round(e);return r.length>=t?r:Re("0",t-r.length)+r}function Ss(e,t){var r=""+e;return r.length>=t?r:Re("0",t-r.length)+r}var C0=Math.pow(2,32);function at(e,t){if(e>C0||e<-C0)return ws(e,t);var r=Math.round(e);return Ss(r,t)}function fn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var O0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Nn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function As(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Ie={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},D0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Fs={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function on(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,l=1,o=0,c=0,d=Math.floor(a);o<t&&(d=Math.floor(a),f=d*s+i,c=d*o+l,!(a-d<5e-8));)a=1/(a-d),i=s,s=f,l=o,o=c;if(c>t&&(o>t?(c=l,f=i):(c=o,f=s)),!r)return[0,n*f,c];var u=Math.floor(n*f/c);return[u,n*f-u*c,c]}function Zt(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var l=new Date(1900,0,1);l.setDate(l.getDate()+n-1),s=[l.getFullYear(),l.getMonth()+1,l.getDate()],i=l.getDay(),n<60&&(i=(i+6)%7),r&&(i=ks(l,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var da=new Date(1899,11,31,0,0,0),ys=da.getTime(),Cs=new Date(1900,2,1,0,0,0);function pa(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Cs&&(r+=24*60*60*1e3),(r-(ys+(e.getTimezoneOffset()-da.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function zn(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Os(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Ds(e){var t=e<0?12:11,r=zn(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Rs(e){var t=zn(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Is(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Ds(e):t===10?r=e.toFixed(10).substr(0,12):r=Rs(e),zn(Os(r.toUpperCase()))}function Hn(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Is(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return br(14,pa(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function ks(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Ns(e,t,r,n){var a="",i=0,s=0,f=r.y,l,o=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:l=f%100,o=2;break;default:l=f%1e4,o=4;break}break;case 109:switch(t.length){case 1:case 2:l=r.m,o=t.length;break;case 3:return Nn[r.m-1][1];case 5:return Nn[r.m-1][0];default:return Nn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:l=r.d,o=t.length;break;case 3:return O0[r.q][0];default:return O0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:l=1+(r.H+11)%12,o=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:l=r.H,o=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:l=r.M,o=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?vr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=vr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":l=r.D*24+r.H;break;case"[m]":case"[mm]":l=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":l=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}o=t.length===3?1:2;break;case 101:l=f,o=1;break}var c=o>0?vr(l,o):"";return c}function Ur(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var va=/%/g;function Ps(e,t,r){var n=t.replace(va,""),a=t.length-n.length;return Or(e,n,r*Math.pow(10,2*a))+Re("%",a)}function Ls(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Or(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function ma(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+ma(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,l,o,c){return l+o+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ga=/# (\?+)( ?)\/( ?)(\d+)/;function Ms(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?Re(" ",e[1].length+1+e[4].length):jn(s,e[1].length)+e[2]+"/"+e[3]+vr(f,e[4].length))}function Bs(e,t,r){return r+(t===0?"":""+t)+Re(" ",e[1].length+2+e[4].length)}var _a=/^#*0*\.([0#]+)/,Ta=/\).*[0#]/,Ea=/\(###\) ###\\?-####/;function Ze(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function R0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function I0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Us(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function bs(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function cr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ta)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?cr("n",n,r):"("+cr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Ls(e,t,r);if(t.indexOf("%")!==-1)return Ps(e,t,r);if(t.indexOf("E")!==-1)return ma(t,r);if(t.charCodeAt(0)===36)return"$"+cr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,l=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+at(l,t.length);if(t.match(/^[#?]+$/))return a=at(r,0),a==="0"&&(a=""),a.length>t.length?a:Ze(t.substr(0,t.length-a.length))+a;if(i=t.match(ga))return Ms(i,l,o);if(t.match(/^#+0+$/))return o+at(l,t.length-t.indexOf("0"));if(i=t.match(_a))return a=R0(r,i[1].length).replace(/^([^\.]+)$/,"$1."+Ze(i[1])).replace(/\.$/,"."+Ze(i[1])).replace(/\.(\d*)$/,function(_,h){return"."+h+Re("0",Ze(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+R0(l,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Ur(at(l,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+cr(e,t,-r):Ur(""+(Math.floor(r)+Us(r,i[1].length)))+"."+vr(I0(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return cr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=lt(cr(e,t.replace(/[\\-]/g,""),r)),s=0,lt(lt(t.replace(/\\/g,"")).replace(/[0#]/g,function(_){return s<a.length?a.charAt(s++):_==="0"?"0":""}));if(t.match(Ea))return a=cr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=on(l,Math.pow(10,s)-1,!1),a=""+o,c=Or("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=sn(f[2],s),c.length<i[4].length&&(c=Ze(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=on(l,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?jn(f[1],s)+i[2]+"/"+i[3]+sn(f[2],s):Re(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=at(r,0),t.length<=a.length?a:Ze(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var d=t.indexOf(".")-s,u=t.length-a.length-d;return Ze(t.substr(0,d)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=I0(r,i[1].length),r<0?"-"+cr(e,t,-r):Ur(bs(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(_){return"00,"+(_.length<3?vr(0,3-_.length):"")+_})+"."+vr(s,i[1].length);switch(t){case"###,##0.00":return cr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=Ur(at(l,0));return p!=="0"?o+p:"";case"###,###.00":return cr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return cr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Ws(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Or(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Hs(e,t,r){var n=t.replace(va,""),a=t.length-n.length;return Or(e,n,r*Math.pow(10,2*a))+Re("%",a)}function wa(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+wa(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,l,o,c){return l+o+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function _r(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Ta)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?_r("n",n,r):"("+_r("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Ws(e,t,r);if(t.indexOf("%")!==-1)return Hs(e,t,r);if(t.indexOf("E")!==-1)return wa(t,r);if(t.charCodeAt(0)===36)return"$"+_r(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,l=Math.abs(r),o=r<0?"-":"";if(t.match(/^00+$/))return o+vr(l,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:Ze(t.substr(0,t.length-a.length))+a;if(i=t.match(ga))return Bs(i,l,o);if(t.match(/^#+0+$/))return o+vr(l,t.length-t.indexOf("0"));if(i=t.match(_a))return a=(""+r).replace(/^([^\.]+)$/,"$1."+Ze(i[1])).replace(/\.$/,"."+Ze(i[1])),a=a.replace(/\.(\d*)$/,function(_,h){return"."+h+Re("0",Ze(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return o+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return o+Ur(""+l);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+_r(e,t,-r):Ur(""+r)+"."+Re("0",i[1].length);if(i=t.match(/^#,#*,#0/))return _r(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=lt(_r(e,t.replace(/[\\-]/g,""),r)),s=0,lt(lt(t.replace(/\\/g,"")).replace(/[0#]/g,function(_){return s<a.length?a.charAt(s++):_==="0"?"0":""}));if(t.match(Ea))return a=_r(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=on(l,Math.pow(10,s)-1,!1),a=""+o,c=Or("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=sn(f[2],s),c.length<i[4].length&&(c=Ze(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=on(l,Math.pow(10,s)-1,!0),o+(f[0]||(f[1]?"":"0"))+" "+(f[1]?jn(f[1],s)+i[2]+"/"+i[3]+sn(f[2],s):Re(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:Ze(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var d=t.indexOf(".")-s,u=t.length-a.length-d;return Ze(t.substr(0,d)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+_r(e,t,-r):Ur(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(_){return"00,"+(_.length<3?vr(0,3-_.length):"")+_})+"."+vr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=Ur(""+l);return p!=="0"?o+p:"";default:if(t.match(/\.[0#?]*$/))return _r(e,t.slice(0,t.lastIndexOf(".")),r)+Ze(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Or(e,t,r){return(r|0)===r?_r(e,t,r):cr(e,t,r)}function Vs(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Sa=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Aa(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":fn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(Sa))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function Gs(e,t,r,n){for(var a=[],i="",s=0,f="",l="t",o,c,d,u="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!fn(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(d=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(d);a[a.length]={t:"t",v:i},++s;break;case"\\":var p=e.charAt(++s),_=p==="("||p===")"?p:"t";a[a.length]={t:_,v:p},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(o==null&&(o=Zt(t,r,e.charAt(s+1)==="2"),o==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},l=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||o==null&&(o=Zt(t,r),o==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&l.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=u),a[a.length]={t:f,v:i},l=f;break;case"A":case"a":case"上":var h={t:f,v:f};if(o==null&&(o=Zt(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(o!=null&&(h.v=o.H>=12?"P":"A"),h.t="T",u="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(o!=null&&(h.v=o.H>=12?"PM":"AM"),h.t="T",s+=5,u="h"):e.substr(s,5).toUpperCase()==="上午/下午"?(o!=null&&(h.v=o.H>=12?"下午":"上午"),h.t="T",s+=5,u="h"):(h.t="t",++s),o==null&&h.t==="T")return"";a[a.length]=h,l=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Sa)){if(o==null&&(o=Zt(t,r),o==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},l=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Aa(e)||(a[a.length]={t:"t",v:i}));break;case".":if(o!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},l=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var g=0,C=0,O;for(s=a.length-1,l="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=u,l="h",g<1&&(g=1);break;case"s":(O=a[s].v.match(/\.0+$/))&&(C=Math.max(C,O[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":l=a[s].t;break;case"m":l==="s"&&(a[s].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&a[s].v.match(/[Hh]/)&&(g=1),g<2&&a[s].v.match(/[Mm]/)&&(g=2),g<3&&a[s].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M),o.M>=60&&(o.M=0,++o.H);break;case 2:o.u>=.5&&(o.u=0,++o.S),o.S>=60&&(o.S=0,++o.M);break}var F="",U;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=Ns(a[s].t.charCodeAt(0),a[s].v,o,C),a[s].t="t";break;case"n":case"?":for(U=s+1;a[U]!=null&&((f=a[U].t)==="?"||f==="D"||(f===" "||f==="t")&&a[U+1]!=null&&(a[U+1].t==="?"||a[U+1].t==="t"&&a[U+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[U].v==="/"||a[U].v===" "&&a[U+1]!=null&&a[U+1].t=="?"));)a[s].v+=a[U].v,a[U]={v:"",t:";"},++U;F+=a[s].v,s=U-1;break;case"G":a[s].t="t",a[s].v=Hn(t,r);break}var J="",re,D;if(F.length>0){F.charCodeAt(0)==40?(re=t<0&&F.charCodeAt(0)===45?-t:t,D=Or("n",F,re)):(re=t<0&&n>1?-t:t,D=Or("n",F,re),re<0&&a[0]&&a[0].t=="t"&&(D=D.substr(1),a[0].v="-"+a[0].v)),U=D.length-1;var H=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){H=s;break}var L=a.length;if(H===a.length&&D.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(U>=a[s].v.length-1?(U-=a[s].v.length,a[s].v=D.substr(U+1,a[s].v.length)):U<0?a[s].v="":(a[s].v=D.substr(0,U+1),U=-1),a[s].t="t",L=s);U>=0&&L<a.length&&(a[L].v=D.substr(0,U+1)+a[L].v)}else if(H!==a.length&&D.indexOf("E")===-1){for(U=D.indexOf(".")-1,s=H;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(c=a[s].v.indexOf(".")>-1&&s===H?a[s].v.indexOf(".")-1:a[s].v.length-1,J=a[s].v.substr(c+1);c>=0;--c)U>=0&&(a[s].v.charAt(c)==="0"||a[s].v.charAt(c)==="#")&&(J=D.charAt(U--)+J);a[s].v=J,a[s].t="t",L=s}for(U>=0&&L<a.length&&(a[L].v=D.substr(0,U+1)+a[L].v),U=D.indexOf(".")+1,s=H;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==H)){for(c=a[s].v.indexOf(".")>-1&&s===H?a[s].v.indexOf(".")+1:0,J=a[s].v.substr(0,c);c<a[s].v.length;++c)U<D.length&&(J+=D.charAt(U++));a[s].v=J,a[s].t="t",L=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(re=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Or(a[s].t,a[s].v,re),a[s].t="t");var X="";for(s=0;s!==a.length;++s)a[s]!=null&&(X+=a[s].v);return X}var k0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function N0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Xs(e,t){var r=Vs(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(k0),f=r[1].match(k0);return N0(t,s)?[n,r[0]]:N0(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function br(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Ie)[e],n==null&&(n=r.table&&r.table[D0[e]]||Ie[D0[e]]),n==null&&(n=Fs[e]||"General");break}if(fn(n,0))return Hn(t,r);t instanceof Date&&(t=pa(t,r.date1904));var a=Xs(n,t);if(fn(a[1]))return Hn(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return Gs(a[1],t,r,a[0])}function Fa(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Ie[r]==null){t<0&&(t=r);continue}if(Ie[r]==e){t=r;break}}t<0&&(t=391)}return Ie[t]=e,t}function Tn(e){for(var t=0;t!=392;++t)e[t]!==void 0&&Fa(e[t],t)}function En(){Ie=As()}var ya=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function js(e){var t=typeof e=="number"?Ie[e]:e;return t=t.replace(ya,"(\\d+)"),new RegExp("^"+t+"$")}function zs(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,l=-1;(t.match(ya)||[]).forEach(function(d,u){var p=parseInt(r[u+1],10);switch(d.toLowerCase().charAt(0)){case"y":n=p;break;case"d":i=p;break;case"h":s=p;break;case"s":l=p;break;case"m":s>=0?f=p:a=p;break}}),l>=0&&f==-1&&a>=0&&(f=a,a=-1);var o=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);o.length==7&&(o="0"+o),o.length==8&&(o="20"+o);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);return s==-1&&f==-1&&l==-1?o:n==-1&&a==-1&&i==-1?c:o+"T"+c}var $s=function(){var e={};e.version="1.2.0";function t(){for(var D=0,H=new Array(256),L=0;L!=256;++L)D=L,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,H[L]=D;return typeof Int32Array<"u"?new Int32Array(H):H}var r=t();function n(D){var H=0,L=0,X=0,j=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(X=0;X!=256;++X)j[X]=D[X];for(X=0;X!=256;++X)for(L=D[X],H=256+X;H<4096;H+=256)L=j[H]=L>>>8^D[L&255];var z=[];for(X=1;X!=16;++X)z[X-1]=typeof Int32Array<"u"?j.subarray(X*256,X*256+256):j.slice(X*256,X*256+256);return z}var a=n(r),i=a[0],s=a[1],f=a[2],l=a[3],o=a[4],c=a[5],d=a[6],u=a[7],p=a[8],_=a[9],h=a[10],g=a[11],C=a[12],O=a[13],F=a[14];function U(D,H){for(var L=H^-1,X=0,j=D.length;X<j;)L=L>>>8^r[(L^D.charCodeAt(X++))&255];return~L}function J(D,H){for(var L=H^-1,X=D.length-15,j=0;j<X;)L=F[D[j++]^L&255]^O[D[j++]^L>>8&255]^C[D[j++]^L>>16&255]^g[D[j++]^L>>>24]^h[D[j++]]^_[D[j++]]^p[D[j++]]^u[D[j++]]^d[D[j++]]^c[D[j++]]^o[D[j++]]^l[D[j++]]^f[D[j++]]^s[D[j++]]^i[D[j++]]^r[D[j++]];for(X+=15;j<X;)L=L>>>8^r[(L^D[j++])&255];return~L}function re(D,H){for(var L=H^-1,X=0,j=D.length,z=0,te=0;X<j;)z=D.charCodeAt(X++),z<128?L=L>>>8^r[(L^z)&255]:z<2048?(L=L>>>8^r[(L^(192|z>>6&31))&255],L=L>>>8^r[(L^(128|z&63))&255]):z>=55296&&z<57344?(z=(z&1023)+64,te=D.charCodeAt(X++)&1023,L=L>>>8^r[(L^(240|z>>8&7))&255],L=L>>>8^r[(L^(128|z>>2&63))&255],L=L>>>8^r[(L^(128|te>>6&15|(z&3)<<4))&255],L=L>>>8^r[(L^(128|te&63))&255]):(L=L>>>8^r[(L^(224|z>>12&15))&255],L=L>>>8^r[(L^(128|z>>6&63))&255],L=L>>>8^r[(L^(128|z&63))&255]);return~L}return e.table=r,e.bstr=U,e.buf=J,e.str=re,e}(),we=function(){var t={};t.version="1.2.1";function r(x,T){for(var v=x.split("/"),m=T.split("/"),E=0,w=0,I=Math.min(v.length,m.length);E<I;++E){if(w=v[E].length-m[E].length)return w;if(v[E]!=m[E])return v[E]<m[E]?-1:1}return v.length-m.length}function n(x){if(x.charAt(x.length-1)=="/")return x.slice(0,-1).indexOf("/")===-1?x:n(x.slice(0,-1));var T=x.lastIndexOf("/");return T===-1?x:x.slice(0,T+1)}function a(x){if(x.charAt(x.length-1)=="/")return a(x.slice(0,-1));var T=x.lastIndexOf("/");return T===-1?x:x.slice(T+1)}function i(x,T){typeof T=="string"&&(T=new Date(T));var v=T.getHours();v=v<<6|T.getMinutes(),v=v<<5|T.getSeconds()>>>1,x.write_shift(2,v);var m=T.getFullYear()-1980;m=m<<4|T.getMonth()+1,m=m<<5|T.getDate(),x.write_shift(2,m)}function s(x){var T=x.read_shift(2)&65535,v=x.read_shift(2)&65535,m=new Date,E=v&31;v>>>=5;var w=v&15;v>>>=4,m.setMilliseconds(0),m.setFullYear(v+1980),m.setMonth(w-1),m.setDate(E);var I=T&31;T>>>=5;var W=T&63;return T>>>=6,m.setHours(T),m.setMinutes(W),m.setSeconds(I<<1),m}function f(x){ir(x,0);for(var T={},v=0;x.l<=x.length-4;){var m=x.read_shift(2),E=x.read_shift(2),w=x.l+E,I={};switch(m){case 21589:v=x.read_shift(1),v&1&&(I.mtime=x.read_shift(4)),E>5&&(v&2&&(I.atime=x.read_shift(4)),v&4&&(I.ctime=x.read_shift(4))),I.mtime&&(I.mt=new Date(I.mtime*1e3));break}x.l=w,T[m]=I}return T}var l;function o(){return l||(l={})}function c(x,T){if(x[0]==80&&x[1]==75)return E0(x,T);if((x[0]|32)==109&&(x[1]|32)==105)return ts(x,T);if(x.length<512)throw new Error("CFB file size "+x.length+" < 512");var v=3,m=512,E=0,w=0,I=0,W=0,R=0,k=[],N=x.slice(0,512);ir(N,0);var K=d(N);switch(v=K[0],v){case 3:m=512;break;case 4:m=4096;break;case 0:if(K[1]==0)return E0(x,T);default:throw new Error("Major Version: Expected 3 or 4 saw "+v)}m!==512&&(N=x.slice(0,m),ir(N,28));var Q=x.slice(0,m);u(N,v);var ie=N.read_shift(4,"i");if(v===3&&ie!==0)throw new Error("# Directory Sectors: Expected 0 saw "+ie);N.l+=4,I=N.read_shift(4,"i"),N.l+=4,N.chk("00100000","Mini Stream Cutoff Size: "),W=N.read_shift(4,"i"),E=N.read_shift(4,"i"),R=N.read_shift(4,"i"),w=N.read_shift(4,"i");for(var Y=-1,ne=0;ne<109&&(Y=N.read_shift(4,"i"),!(Y<0));++ne)k[ne]=Y;var he=p(x,m);g(R,w,he,m,k);var ye=O(he,I,k,m);ye[I].name="!Directory",E>0&&W!==te&&(ye[W].name="!MiniFAT"),ye[k[0]].name="!FAT",ye.fat_addrs=k,ye.ssz=m;var Ce={},$e=[],gt=[],_t=[];F(I,ye,he,$e,E,Ce,gt,W),_(gt,_t,$e),$e.shift();var Tt={FileIndex:gt,FullPaths:_t};return T&&T.raw&&(Tt.raw={header:Q,sectors:he}),Tt}function d(x){if(x[x.l]==80&&x[x.l+1]==75)return[0,0];x.chk(ae,"Header Signature: "),x.l+=16;var T=x.read_shift(2,"u");return[x.read_shift(2,"u"),T]}function u(x,T){var v=9;switch(x.l+=2,v=x.read_shift(2)){case 9:if(T!=3)throw new Error("Sector Shift: Expected 9 saw "+v);break;case 12:if(T!=4)throw new Error("Sector Shift: Expected 12 saw "+v);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+v)}x.chk("0600","Mini Sector Shift: "),x.chk("000000000000","Reserved: ")}function p(x,T){for(var v=Math.ceil(x.length/T)-1,m=[],E=1;E<v;++E)m[E-1]=x.slice(E*T,(E+1)*T);return m[v-1]=x.slice(v*T),m}function _(x,T,v){for(var m=0,E=0,w=0,I=0,W=0,R=v.length,k=[],N=[];m<R;++m)k[m]=N[m]=m,T[m]=v[m];for(;W<N.length;++W)m=N[W],E=x[m].L,w=x[m].R,I=x[m].C,k[m]===m&&(E!==-1&&k[E]!==E&&(k[m]=k[E]),w!==-1&&k[w]!==w&&(k[m]=k[w])),I!==-1&&(k[I]=m),E!==-1&&m!=k[m]&&(k[E]=k[m],N.lastIndexOf(E)<W&&N.push(E)),w!==-1&&m!=k[m]&&(k[w]=k[m],N.lastIndexOf(w)<W&&N.push(w));for(m=1;m<R;++m)k[m]===m&&(w!==-1&&k[w]!==w?k[m]=k[w]:E!==-1&&k[E]!==E&&(k[m]=k[E]));for(m=1;m<R;++m)if(x[m].type!==0){if(W=m,W!=k[W])do W=k[W],T[m]=T[W]+"/"+T[m];while(W!==0&&k[W]!==-1&&W!=k[W]);k[m]=-1}for(T[0]+="/",m=1;m<R;++m)x[m].type!==2&&(T[m]+="/")}function h(x,T,v){for(var m=x.start,E=x.size,w=[],I=m;v&&E>0&&I>=0;)w.push(T.slice(I*z,I*z+z)),E-=z,I=zr(v,I*4);return w.length===0?b(0):Ve(w).slice(0,x.size)}function g(x,T,v,m,E){var w=te;if(x===te){if(T!==0)throw new Error("DIFAT chain shorter than expected")}else if(x!==-1){var I=v[x],W=(m>>>2)-1;if(!I)return;for(var R=0;R<W&&(w=zr(I,R*4))!==te;++R)E.push(w);g(zr(I,m-4),T-1,v,m,E)}}function C(x,T,v,m,E){var w=[],I=[];E||(E=[]);var W=m-1,R=0,k=0;for(R=T;R>=0;){E[R]=!0,w[w.length]=R,I.push(x[R]);var N=v[Math.floor(R*4/m)];if(k=R*4&W,m<4+k)throw new Error("FAT boundary crossed: "+R+" 4 "+m);if(!x[N])break;R=zr(x[N],k)}return{nodes:w,data:H0([I])}}function O(x,T,v,m){var E=x.length,w=[],I=[],W=[],R=[],k=m-1,N=0,K=0,Q=0,ie=0;for(N=0;N<E;++N)if(W=[],Q=N+T,Q>=E&&(Q-=E),!I[Q]){R=[];var Y=[];for(K=Q;K>=0;){Y[K]=!0,I[K]=!0,W[W.length]=K,R.push(x[K]);var ne=v[Math.floor(K*4/m)];if(ie=K*4&k,m<4+ie)throw new Error("FAT boundary crossed: "+K+" 4 "+m);if(!x[ne]||(K=zr(x[ne],ie),Y[K]))break}w[Q]={nodes:W,data:H0([R])}}return w}function F(x,T,v,m,E,w,I,W){for(var R=0,k=m.length?2:0,N=T[x].data,K=0,Q=0,ie;K<N.length;K+=128){var Y=N.slice(K,K+128);ir(Y,64),Q=Y.read_shift(2),ie=Zn(Y,0,Q-k),m.push(ie);var ne={name:ie,type:Y.read_shift(1),color:Y.read_shift(1),L:Y.read_shift(4,"i"),R:Y.read_shift(4,"i"),C:Y.read_shift(4,"i"),clsid:Y.read_shift(16),state:Y.read_shift(4,"i"),start:0,size:0},he=Y.read_shift(2)+Y.read_shift(2)+Y.read_shift(2)+Y.read_shift(2);he!==0&&(ne.ct=U(Y,Y.l-8));var ye=Y.read_shift(2)+Y.read_shift(2)+Y.read_shift(2)+Y.read_shift(2);ye!==0&&(ne.mt=U(Y,Y.l-8)),ne.start=Y.read_shift(4,"i"),ne.size=Y.read_shift(4,"i"),ne.size<0&&ne.start<0&&(ne.size=ne.type=0,ne.start=te,ne.name=""),ne.type===5?(R=ne.start,E>0&&R!==te&&(T[R].name="!StreamData")):ne.size>=4096?(ne.storage="fat",T[ne.start]===void 0&&(T[ne.start]=C(v,ne.start,T.fat_addrs,T.ssz)),T[ne.start].name=ne.name,ne.content=T[ne.start].data.slice(0,ne.size)):(ne.storage="minifat",ne.size<0?ne.size=0:R!==te&&ne.start!==te&&T[R]&&(ne.content=h(ne,T[R].data,(T[W]||{}).data))),ne.content&&ir(ne.content,0),w[ie]=ne,I.push(ne)}}function U(x,T){return new Date((fr(x,T+4)/1e7*Math.pow(2,32)+fr(x,T)/1e7-11644473600)*1e3)}function J(x,T){return o(),c(l.readFileSync(x),T)}function re(x,T){var v=T&&T.type;switch(v||ve&&Buffer.isBuffer(x)&&(v="buffer"),v||"base64"){case"file":return J(x,T);case"base64":return c(pr(Ir(x)),T);case"binary":return c(pr(x),T)}return c(x,T)}function D(x,T){var v=T||{},m=v.root||"Root Entry";if(x.FullPaths||(x.FullPaths=[]),x.FileIndex||(x.FileIndex=[]),x.FullPaths.length!==x.FileIndex.length)throw new Error("inconsistent CFB structure");x.FullPaths.length===0&&(x.FullPaths[0]=m+"/",x.FileIndex[0]={name:m,type:5}),v.CLSID&&(x.FileIndex[0].clsid=v.CLSID),H(x)}function H(x){var T="Sh33tJ5";if(!we.find(x,"/"+T)){var v=b(4);v[0]=55,v[1]=v[3]=50,v[2]=54,x.FileIndex.push({name:T,type:2,content:v,size:4,L:69,R:69,C:69}),x.FullPaths.push(x.FullPaths[0]+T),L(x)}}function L(x,T){D(x);for(var v=!1,m=!1,E=x.FullPaths.length-1;E>=0;--E){var w=x.FileIndex[E];switch(w.type){case 0:m?v=!0:(x.FileIndex.pop(),x.FullPaths.pop());break;case 1:case 2:case 5:m=!0,isNaN(w.R*w.L*w.C)&&(v=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(v=!0);break;default:v=!0;break}}if(!(!v&&!T)){var I=new Date(1987,1,19),W=0,R=Object.create?Object.create(null):{},k=[];for(E=0;E<x.FullPaths.length;++E)R[x.FullPaths[E]]=!0,x.FileIndex[E].type!==0&&k.push([x.FullPaths[E],x.FileIndex[E]]);for(E=0;E<k.length;++E){var N=n(k[E][0]);m=R[N],m||(k.push([N,{name:a(N).replace("/",""),type:1,clsid:xe,ct:I,mt:I,content:null}]),R[N]=!0)}for(k.sort(function(ie,Y){return r(ie[0],Y[0])}),x.FullPaths=[],x.FileIndex=[],E=0;E<k.length;++E)x.FullPaths[E]=k[E][0],x.FileIndex[E]=k[E][1];for(E=0;E<k.length;++E){var K=x.FileIndex[E],Q=x.FullPaths[E];if(K.name=a(Q).replace("/",""),K.L=K.R=K.C=-(K.color=1),K.size=K.content?K.content.length:0,K.start=0,K.clsid=K.clsid||xe,E===0)K.C=k.length>1?1:-1,K.size=0,K.type=5;else if(Q.slice(-1)=="/"){for(W=E+1;W<k.length&&n(x.FullPaths[W])!=Q;++W);for(K.C=W>=k.length?-1:W,W=E+1;W<k.length&&n(x.FullPaths[W])!=n(Q);++W);K.R=W>=k.length?-1:W,K.type=1}else n(x.FullPaths[E+1]||"")==n(Q)&&(K.R=E+1),K.type=2}}}function X(x,T){var v=T||{};if(v.fileType=="mad")return ns(x,v);switch(L(x),v.fileType){case"zip":return Ji(x,v)}var m=function(ie){for(var Y=0,ne=0,he=0;he<ie.FileIndex.length;++he){var ye=ie.FileIndex[he];if(ye.content){var Ce=ye.content.length;Ce>0&&(Ce<4096?Y+=Ce+63>>6:ne+=Ce+511>>9)}}for(var $e=ie.FullPaths.length+3>>2,gt=Y+7>>3,_t=Y+127>>7,Tt=gt+ne+$e+_t,jr=Tt+127>>7,In=jr<=109?0:Math.ceil((jr-109)/127);Tt+jr+In+127>>7>jr;)In=++jr<=109?0:Math.ceil((jr-109)/127);var Ar=[1,In,jr,_t,$e,ne,Y,0];return ie.FileIndex[0].size=Y<<6,Ar[7]=(ie.FileIndex[0].start=Ar[0]+Ar[1]+Ar[2]+Ar[3]+Ar[4]+Ar[5])+(Ar[6]+7>>3),Ar}(x),E=b(m[7]<<9),w=0,I=0;{for(w=0;w<8;++w)E.write_shift(1,M[w]);for(w=0;w<8;++w)E.write_shift(2,0);for(E.write_shift(2,62),E.write_shift(2,3),E.write_shift(2,65534),E.write_shift(2,9),E.write_shift(2,6),w=0;w<3;++w)E.write_shift(2,0);for(E.write_shift(4,0),E.write_shift(4,m[2]),E.write_shift(4,m[0]+m[1]+m[2]+m[3]-1),E.write_shift(4,0),E.write_shift(4,4096),E.write_shift(4,m[3]?m[0]+m[1]+m[2]-1:te),E.write_shift(4,m[3]),E.write_shift(-4,m[1]?m[0]-1:te),E.write_shift(4,m[1]),w=0;w<109;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1)}if(m[1])for(I=0;I<m[1];++I){for(;w<236+I*127;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1);E.write_shift(-4,I===m[1]-1?te:I+1)}var W=function(ie){for(I+=ie;w<I-1;++w)E.write_shift(-4,w+1);ie&&(++w,E.write_shift(-4,te))};for(I=w=0,I+=m[1];w<I;++w)E.write_shift(-4,oe.DIFSECT);for(I+=m[2];w<I;++w)E.write_shift(-4,oe.FATSECT);W(m[3]),W(m[4]);for(var R=0,k=0,N=x.FileIndex[0];R<x.FileIndex.length;++R)N=x.FileIndex[R],N.content&&(k=N.content.length,!(k<4096)&&(N.start=I,W(k+511>>9)));for(W(m[6]+7>>3);E.l&511;)E.write_shift(-4,oe.ENDOFCHAIN);for(I=w=0,R=0;R<x.FileIndex.length;++R)N=x.FileIndex[R],N.content&&(k=N.content.length,!(!k||k>=4096)&&(N.start=I,W(k+63>>6)));for(;E.l&511;)E.write_shift(-4,oe.ENDOFCHAIN);for(w=0;w<m[4]<<2;++w){var K=x.FullPaths[w];if(!K||K.length===0){for(R=0;R<17;++R)E.write_shift(4,0);for(R=0;R<3;++R)E.write_shift(4,-1);for(R=0;R<12;++R)E.write_shift(4,0);continue}N=x.FileIndex[w],w===0&&(N.start=N.size?N.start-1:te);var Q=w===0&&v.root||N.name;if(k=2*(Q.length+1),E.write_shift(64,Q,"utf16le"),E.write_shift(2,k),E.write_shift(1,N.type),E.write_shift(1,N.color),E.write_shift(-4,N.L),E.write_shift(-4,N.R),E.write_shift(-4,N.C),N.clsid)E.write_shift(16,N.clsid,"hex");else for(R=0;R<4;++R)E.write_shift(4,0);E.write_shift(4,N.state||0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,N.start),E.write_shift(4,N.size),E.write_shift(4,0)}for(w=1;w<x.FileIndex.length;++w)if(N=x.FileIndex[w],N.size>=4096)if(E.l=N.start+1<<9,ve&&Buffer.isBuffer(N.content))N.content.copy(E,E.l,0,N.size),E.l+=N.size+511&-512;else{for(R=0;R<N.size;++R)E.write_shift(1,N.content[R]);for(;R&511;++R)E.write_shift(1,0)}for(w=1;w<x.FileIndex.length;++w)if(N=x.FileIndex[w],N.size>0&&N.size<4096)if(ve&&Buffer.isBuffer(N.content))N.content.copy(E,E.l,0,N.size),E.l+=N.size+63&-64;else{for(R=0;R<N.size;++R)E.write_shift(1,N.content[R]);for(;R&63;++R)E.write_shift(1,0)}if(ve)E.l=E.length;else for(;E.l<E.length;)E.write_shift(1,0);return E}function j(x,T){var v=x.FullPaths.map(function(R){return R.toUpperCase()}),m=v.map(function(R){var k=R.split("/");return k[k.length-(R.slice(-1)=="/"?2:1)]}),E=!1;T.charCodeAt(0)===47?(E=!0,T=v[0].slice(0,-1)+T):E=T.indexOf("/")!==-1;var w=T.toUpperCase(),I=E===!0?v.indexOf(w):m.indexOf(w);if(I!==-1)return x.FileIndex[I];var W=!w.match(Jt);for(w=w.replace(Ft,""),W&&(w=w.replace(Jt,"!")),I=0;I<v.length;++I)if((W?v[I].replace(Jt,"!"):v[I]).replace(Ft,"")==w||(W?m[I].replace(Jt,"!"):m[I]).replace(Ft,"")==w)return x.FileIndex[I];return null}var z=64,te=-2,ae="d0cf11e0a1b11ae1",M=[208,207,17,224,161,177,26,225],xe="00000000000000000000000000000000",oe={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:te,FREESECT:-1,HEADER_SIGNATURE:ae,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:xe,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Fe(x,T,v){o();var m=X(x,v);l.writeFileSync(T,m)}function Oe(x){for(var T=new Array(x.length),v=0;v<x.length;++v)T[v]=String.fromCharCode(x[v]);return T.join("")}function ze(x,T){var v=X(x,T);switch(T&&T.type||"buffer"){case"file":return o(),l.writeFileSync(T.filename,v),v;case"binary":return typeof v=="string"?v:Oe(v);case"base64":return kt(typeof v=="string"?v:Oe(v));case"buffer":if(ve)return Buffer.isBuffer(v)?v:Nr(v);case"array":return typeof v=="string"?pr(v):v}return v}var be;function S(x){try{var T=x.InflateRaw,v=new T;if(v._processChunk(new Uint8Array([3,0]),v._finishFlushFlag),v.bytesRead)be=x;else throw new Error("zlib does not expose bytesRead")}catch(m){console.error("cannot use native zlib: "+(m.message||m))}}function P(x,T){if(!be)return _0(x,T);var v=be.InflateRaw,m=new v,E=m._processChunk(x.slice(x.l),m._finishFlushFlag);return x.l+=m.bytesRead,E}function y(x){return be?be.deflateRawSync(x):x0(x)}var A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],G=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],le=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ce(x){var T=(x<<1|x<<11)&139536|(x<<5|x<<15)&558144;return(T>>16|T>>8|T)&255}for(var fe=typeof Uint8Array<"u",ee=fe?new Uint8Array(256):[],Se=0;Se<256;++Se)ee[Se]=ce(Se);function de(x,T){var v=ee[x&255];return T<=8?v>>>8-T:(v=v<<8|ee[x>>8&255],T<=16?v>>>16-T:(v=v<<8|ee[x>>16&255],v>>>24-T))}function Je(x,T){var v=T&7,m=T>>>3;return(x[m]|(v<=6?0:x[m+1]<<8))>>>v&3}function me(x,T){var v=T&7,m=T>>>3;return(x[m]|(v<=5?0:x[m+1]<<8))>>>v&7}function wr(x,T){var v=T&7,m=T>>>3;return(x[m]|(v<=4?0:x[m+1]<<8))>>>v&15}function De(x,T){var v=T&7,m=T>>>3;return(x[m]|(v<=3?0:x[m+1]<<8))>>>v&31}function se(x,T){var v=T&7,m=T>>>3;return(x[m]|(v<=1?0:x[m+1]<<8))>>>v&127}function lr(x,T,v){var m=T&7,E=T>>>3,w=(1<<v)-1,I=x[E]>>>m;return v<8-m||(I|=x[E+1]<<8-m,v<16-m)||(I|=x[E+2]<<16-m,v<24-m)||(I|=x[E+3]<<24-m),I&w}function Sr(x,T,v){var m=T&7,E=T>>>3;return m<=5?x[E]|=(v&7)<<m:(x[E]|=v<<m&255,x[E+1]=(v&7)>>8-m),T+3}function Gr(x,T,v){var m=T&7,E=T>>>3;return v=(v&1)<<m,x[E]|=v,T+1}function tt(x,T,v){var m=T&7,E=T>>>3;return v<<=m,x[E]|=v&255,v>>>=8,x[E+1]=v,T+8}function u0(x,T,v){var m=T&7,E=T>>>3;return v<<=m,x[E]|=v&255,v>>>=8,x[E+1]=v&255,x[E+2]=v>>>8,T+16}function Cn(x,T){var v=x.length,m=2*v>T?2*v:T+5,E=0;if(v>=T)return x;if(ve){var w=y0(m);if(x.copy)x.copy(w);else for(;E<x.length;++E)w[E]=x[E];return w}else if(fe){var I=new Uint8Array(m);if(I.set)I.set(x);else for(;E<v;++E)I[E]=x[E];return I}return x.length=m,x}function gr(x){for(var T=new Array(x),v=0;v<x;++v)T[v]=0;return T}function Xt(x,T,v){var m=1,E=0,w=0,I=0,W=0,R=x.length,k=fe?new Uint16Array(32):gr(32);for(w=0;w<32;++w)k[w]=0;for(w=R;w<v;++w)x[w]=0;R=x.length;var N=fe?new Uint16Array(R):gr(R);for(w=0;w<R;++w)k[E=x[w]]++,m<E&&(m=E),N[w]=0;for(k[0]=0,w=1;w<=m;++w)k[w+16]=W=W+k[w-1]<<1;for(w=0;w<R;++w)W=x[w],W!=0&&(N[w]=k[W+16]++);var K=0;for(w=0;w<R;++w)if(K=x[w],K!=0)for(W=de(N[w],m)>>m-K,I=(1<<m+4-K)-1;I>=0;--I)T[W|I<<K]=K&15|w<<4;return m}var On=fe?new Uint16Array(512):gr(512),Dn=fe?new Uint16Array(32):gr(32);if(!fe){for(var Xr=0;Xr<512;++Xr)On[Xr]=0;for(Xr=0;Xr<32;++Xr)Dn[Xr]=0}(function(){for(var x=[],T=0;T<32;T++)x.push(5);Xt(x,Dn,32);var v=[];for(T=0;T<=143;T++)v.push(8);for(;T<=255;T++)v.push(9);for(;T<=279;T++)v.push(7);for(;T<=287;T++)v.push(8);Xt(v,On,288)})();var zi=function(){for(var T=fe?new Uint8Array(32768):[],v=0,m=0;v<le.length-1;++v)for(;m<le[v+1];++m)T[m]=v;for(;m<32768;++m)T[m]=29;var E=fe?new Uint8Array(259):[];for(v=0,m=0;v<G.length-1;++v)for(;m<G[v+1];++m)E[m]=v;function w(W,R){for(var k=0;k<W.length;){var N=Math.min(65535,W.length-k),K=k+N==W.length;for(R.write_shift(1,+K),R.write_shift(2,N),R.write_shift(2,~N&65535);N-- >0;)R[R.l++]=W[k++]}return R.l}function I(W,R){for(var k=0,N=0,K=fe?new Uint16Array(32768):[];N<W.length;){var Q=Math.min(65535,W.length-N);if(Q<10){for(k=Sr(R,k,+(N+Q==W.length)),k&7&&(k+=8-(k&7)),R.l=k/8|0,R.write_shift(2,Q),R.write_shift(2,~Q&65535);Q-- >0;)R[R.l++]=W[N++];k=R.l*8;continue}k=Sr(R,k,+(N+Q==W.length)+2);for(var ie=0;Q-- >0;){var Y=W[N];ie=(ie<<5^Y)&32767;var ne=-1,he=0;if((ne=K[ie])&&(ne|=N&-32768,ne>N&&(ne-=32768),ne<N))for(;W[ne+he]==W[N+he]&&he<250;)++he;if(he>2){Y=E[he],Y<=22?k=tt(R,k,ee[Y+1]>>1)-1:(tt(R,k,3),k+=5,tt(R,k,ee[Y-23]>>5),k+=3);var ye=Y<8?0:Y-4>>2;ye>0&&(u0(R,k,he-G[Y]),k+=ye),Y=T[N-ne],k=tt(R,k,ee[Y]>>3),k-=3;var Ce=Y<4?0:Y-2>>1;Ce>0&&(u0(R,k,N-ne-le[Y]),k+=Ce);for(var $e=0;$e<he;++$e)K[ie]=N&32767,ie=(ie<<5^W[N])&32767,++N;Q-=he-1}else Y<=143?Y=Y+48:k=Gr(R,k,1),k=tt(R,k,ee[Y]),K[ie]=N&32767,++N}k=tt(R,k,0)-1}return R.l=(k+7)/8|0,R.l}return function(R,k){return R.length<8?w(R,k):I(R,k)}}();function x0(x){var T=b(50+Math.floor(x.length*1.1)),v=zi(x,T);return T.slice(0,v)}var d0=fe?new Uint16Array(32768):gr(32768),p0=fe?new Uint16Array(32768):gr(32768),v0=fe?new Uint16Array(128):gr(128),m0=1,g0=1;function $i(x,T){var v=De(x,T)+257;T+=5;var m=De(x,T)+1;T+=5;var E=wr(x,T)+4;T+=4;for(var w=0,I=fe?new Uint8Array(19):gr(19),W=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],R=1,k=fe?new Uint8Array(8):gr(8),N=fe?new Uint8Array(8):gr(8),K=I.length,Q=0;Q<E;++Q)I[A[Q]]=w=me(x,T),R<w&&(R=w),k[w]++,T+=3;var ie=0;for(k[0]=0,Q=1;Q<=R;++Q)N[Q]=ie=ie+k[Q-1]<<1;for(Q=0;Q<K;++Q)(ie=I[Q])!=0&&(W[Q]=N[ie]++);var Y=0;for(Q=0;Q<K;++Q)if(Y=I[Q],Y!=0){ie=ee[W[Q]]>>8-Y;for(var ne=(1<<7-Y)-1;ne>=0;--ne)v0[ie|ne<<Y]=Y&7|Q<<3}var he=[];for(R=1;he.length<v+m;)switch(ie=v0[se(x,T)],T+=ie&7,ie>>>=3){case 16:for(w=3+Je(x,T),T+=2,ie=he[he.length-1];w-- >0;)he.push(ie);break;case 17:for(w=3+me(x,T),T+=3;w-- >0;)he.push(0);break;case 18:for(w=11+se(x,T),T+=7;w-- >0;)he.push(0);break;default:he.push(ie),R<ie&&(R=ie);break}var ye=he.slice(0,v),Ce=he.slice(v);for(Q=v;Q<286;++Q)ye[Q]=0;for(Q=m;Q<30;++Q)Ce[Q]=0;return m0=Xt(ye,d0,286),g0=Xt(Ce,p0,30),T}function Ki(x,T){if(x[0]==3&&!(x[1]&3))return[Kr(T),2];for(var v=0,m=0,E=y0(T||1<<18),w=0,I=E.length>>>0,W=0,R=0;(m&1)==0;){if(m=me(x,v),v+=3,m>>>1)m>>1==1?(W=9,R=5):(v=$i(x,v),W=m0,R=g0);else{v&7&&(v+=8-(v&7));var k=x[v>>>3]|x[(v>>>3)+1]<<8;if(v+=32,k>0)for(!T&&I<w+k&&(E=Cn(E,w+k),I=E.length);k-- >0;)E[w++]=x[v>>>3],v+=8;continue}for(;;){!T&&I<w+32767&&(E=Cn(E,w+32767),I=E.length);var N=lr(x,v,W),K=m>>>1==1?On[N]:d0[N];if(v+=K&15,K>>>=4,(K>>>8&255)===0)E[w++]=K;else{if(K==256)break;K-=257;var Q=K<8?0:K-4>>2;Q>5&&(Q=0);var ie=w+G[K];Q>0&&(ie+=lr(x,v,Q),v+=Q),N=lr(x,v,R),K=m>>>1==1?Dn[N]:p0[N],v+=K&15,K>>>=4;var Y=K<4?0:K-2>>1,ne=le[K];for(Y>0&&(ne+=lr(x,v,Y),v+=Y),!T&&I<ie&&(E=Cn(E,ie+100),I=E.length);w<ie;)E[w]=E[w-ne],++w}}}return T?[E,v+7>>>3]:[E.slice(0,w),v+7>>>3]}function _0(x,T){var v=x.slice(x.l||0),m=Ki(v,T);return x.l+=m[1],m[0]}function T0(x,T){if(x)typeof console<"u"&&console.error(T);else throw new Error(T)}function E0(x,T){var v=x;ir(v,0);var m=[],E=[],w={FileIndex:m,FullPaths:E};D(w,{root:T.root});for(var I=v.length-4;(v[I]!=80||v[I+1]!=75||v[I+2]!=5||v[I+3]!=6)&&I>=0;)--I;v.l=I+4,v.l+=4;var W=v.read_shift(2);v.l+=6;var R=v.read_shift(4);for(v.l=R,I=0;I<W;++I){v.l+=20;var k=v.read_shift(4),N=v.read_shift(4),K=v.read_shift(2),Q=v.read_shift(2),ie=v.read_shift(2);v.l+=8;var Y=v.read_shift(4),ne=f(v.slice(v.l+K,v.l+K+Q));v.l+=K+Q+ie;var he=v.l;v.l=Y+4,Yi(v,k,N,w,ne),v.l=he}return w}function Yi(x,T,v,m,E){x.l+=2;var w=x.read_shift(2),I=x.read_shift(2),W=s(x);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var R=x.read_shift(4),k=x.read_shift(4),N=x.read_shift(4),K=x.read_shift(2),Q=x.read_shift(2),ie="",Y=0;Y<K;++Y)ie+=String.fromCharCode(x[x.l++]);if(Q){var ne=f(x.slice(x.l,x.l+Q));(ne[21589]||{}).mt&&(W=ne[21589].mt),((E||{})[21589]||{}).mt&&(W=E[21589].mt)}x.l+=Q;var he=x.slice(x.l,x.l+k);switch(I){case 8:he=P(x,N);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+I)}var ye=!1;w&8&&(R=x.read_shift(4),R==134695760&&(R=x.read_shift(4),ye=!0),k=x.read_shift(4),N=x.read_shift(4)),k!=T&&T0(ye,"Bad compressed size: "+T+" != "+k),N!=v&&T0(ye,"Bad uncompressed size: "+v+" != "+N),Rn(m,ie,he,{unsafe:!0,mt:W})}function Ji(x,T){var v=T||{},m=[],E=[],w=b(1),I=v.compression?8:0,W=0,R=0,k=0,N=0,K=0,Q=x.FullPaths[0],ie=Q,Y=x.FileIndex[0],ne=[],he=0;for(R=1;R<x.FullPaths.length;++R)if(ie=x.FullPaths[R].slice(Q.length),Y=x.FileIndex[R],!(!Y.size||!Y.content||ie=="Sh33tJ5")){var ye=N,Ce=b(ie.length);for(k=0;k<ie.length;++k)Ce.write_shift(1,ie.charCodeAt(k)&127);Ce=Ce.slice(0,Ce.l),ne[K]=$s.buf(Y.content,0);var $e=Y.content;I==8&&($e=y($e)),w=b(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,W),w.write_shift(2,I),Y.mt?i(w,Y.mt):w.write_shift(4,0),w.write_shift(-4,ne[K]),w.write_shift(4,$e.length),w.write_shift(4,Y.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),N+=w.length,m.push(w),N+=Ce.length,m.push(Ce),N+=$e.length,m.push($e),w=b(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,W),w.write_shift(2,I),w.write_shift(4,0),w.write_shift(-4,ne[K]),w.write_shift(4,$e.length),w.write_shift(4,Y.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,ye),he+=w.l,E.push(w),he+=Ce.length,E.push(Ce),++K}return w=b(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,K),w.write_shift(2,K),w.write_shift(4,he),w.write_shift(4,N),w.write_shift(2,0),Ve([Ve(m),Ve(E),w])}var jt={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Zi(x,T){if(x.ctype)return x.ctype;var v=x.name||"",m=v.match(/\.([^\.]+)$/);return m&&jt[m[1]]||T&&(m=(v=T).match(/[\.\\]([^\.\\])+$/),m&&jt[m[1]])?jt[m[1]]:"application/octet-stream"}function qi(x){for(var T=kt(x),v=[],m=0;m<T.length;m+=76)v.push(T.slice(m,m+76));return v.join(`\r
`)+`\r
`}function Qi(x){var T=x.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(k){var N=k.charCodeAt(0).toString(16).toUpperCase();return"="+(N.length==1?"0"+N:N)});T=T.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),T.charAt(0)==`
`&&(T="=0D"+T.slice(1)),T=T.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var v=[],m=T.split(`\r
`),E=0;E<m.length;++E){var w=m[E];if(w.length==0){v.push("");continue}for(var I=0;I<w.length;){var W=76,R=w.slice(I,I+W);R.charAt(W-1)=="="?W--:R.charAt(W-2)=="="?W-=2:R.charAt(W-3)=="="&&(W-=3),R=w.slice(I,I+W),I+=W,I<w.length&&(R+="="),v.push(R)}}return v.join(`\r
`)}function es(x){for(var T=[],v=0;v<x.length;++v){for(var m=x[v];v<=x.length&&m.charAt(m.length-1)=="=";)m=m.slice(0,m.length-1)+x[++v];T.push(m)}for(var E=0;E<T.length;++E)T[E]=T[E].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return pr(T.join(`\r
`))}function rs(x,T,v){for(var m="",E="",w="",I,W=0;W<10;++W){var R=T[W];if(!R||R.match(/^\s*$/))break;var k=R.match(/^(.*?):\s*([^\s].*)$/);if(k)switch(k[1].toLowerCase()){case"content-location":m=k[2].trim();break;case"content-type":w=k[2].trim();break;case"content-transfer-encoding":E=k[2].trim();break}}switch(++W,E.toLowerCase()){case"base64":I=pr(Ir(T.slice(W).join("")));break;case"quoted-printable":I=es(T.slice(W));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+E)}var N=Rn(x,m.slice(v.length),I,{unsafe:!0});w&&(N.ctype=w)}function ts(x,T){if(Oe(x.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var v=T&&T.root||"",m=(ve&&Buffer.isBuffer(x)?x.toString("binary"):Oe(x)).split(`\r
`),E=0,w="";for(E=0;E<m.length;++E)if(w=m[E],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),v||(v=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,v.length)!=v))for(;v.length>0&&(v=v.slice(0,v.length-1),v=v.slice(0,v.lastIndexOf("/")+1),w.slice(0,v.length)!=v););var I=(m[1]||"").match(/boundary="(.*?)"/);if(!I)throw new Error("MAD cannot find boundary");var W="--"+(I[1]||""),R=[],k=[],N={FileIndex:R,FullPaths:k};D(N);var K,Q=0;for(E=0;E<m.length;++E){var ie=m[E];ie!==W&&ie!==W+"--"||(Q++&&rs(N,m.slice(K,E),v),K=E)}return N}function ns(x,T){var v=T||{},m=v.boundary||"SheetJS";m="------="+m;for(var E=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+m.slice(2)+'"',"","",""],w=x.FullPaths[0],I=w,W=x.FileIndex[0],R=1;R<x.FullPaths.length;++R)if(I=x.FullPaths[R].slice(w.length),W=x.FileIndex[R],!(!W.size||!W.content||I=="Sh33tJ5")){I=I.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(he){return"_x"+he.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(he){return"_u"+he.charCodeAt(0).toString(16)+"_"});for(var k=W.content,N=ve&&Buffer.isBuffer(k)?k.toString("binary"):Oe(k),K=0,Q=Math.min(1024,N.length),ie=0,Y=0;Y<=Q;++Y)(ie=N.charCodeAt(Y))>=32&&ie<128&&++K;var ne=K>=Q*4/5;E.push(m),E.push("Content-Location: "+(v.root||"file:///C:/SheetJS/")+I),E.push("Content-Transfer-Encoding: "+(ne?"quoted-printable":"base64")),E.push("Content-Type: "+Zi(W,I)),E.push(""),E.push(ne?Qi(N):qi(N))}return E.push(m+`--\r
`),E.join(`\r
`)}function as(x){var T={};return D(T,x),T}function Rn(x,T,v,m){var E=m&&m.unsafe;E||D(x);var w=!E&&we.find(x,T);if(!w){var I=x.FullPaths[0];T.slice(0,I.length)==I?I=T:(I.slice(-1)!="/"&&(I+="/"),I=(I+T).replace("//","/")),w={name:a(T),type:2},x.FileIndex.push(w),x.FullPaths.push(I),E||we.utils.cfb_gc(x)}return w.content=v,w.size=v?v.length:0,m&&(m.CLSID&&(w.clsid=m.CLSID),m.mt&&(w.mt=m.mt),m.ct&&(w.ct=m.ct)),w}function is(x,T){D(x);var v=we.find(x,T);if(v){for(var m=0;m<x.FileIndex.length;++m)if(x.FileIndex[m]==v)return x.FileIndex.splice(m,1),x.FullPaths.splice(m,1),!0}return!1}function ss(x,T,v){D(x);var m=we.find(x,T);if(m){for(var E=0;E<x.FileIndex.length;++E)if(x.FileIndex[E]==m)return x.FileIndex[E].name=a(v),x.FullPaths[E]=v,!0}return!1}function fs(x){L(x,!0)}return t.find=j,t.read=re,t.parse=c,t.write=ze,t.writeFile=Fe,t.utils={cfb_new:as,cfb_add:Rn,cfb_del:is,cfb_mov:ss,cfb_gc:fs,ReadShift:Ct,CheckField:Xa,prep_blob:ir,bconcat:Ve,use_zlib:S,_deflateRaw:x0,_inflateRaw:_0,consts:oe},t}();function Ks(e){return typeof e=="string"?_n(e):Array.isArray(e)?Ts(e):e}function bt(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=_n(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Pt(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(n,e);if(typeof Blob<"u"){var a=new Blob([Ks(n)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs<"u")return saveAs(a,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=Ut(t)),f.write(t),f.close(),t}catch(l){if(!l.message||!l.message.match(/onstruct/))throw l}throw new Error("cannot save file "+e)}function je(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function P0(e,t){for(var r=[],n=je(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function $n(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function wn(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function Ys(e){for(var t=[],r=je(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var ln=new Date(1899,11,30,0,0,0);function tr(e,t){var r=e.getTime(),n=ln.getTime()+(e.getTimezoneOffset()-ln.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var Ca=new Date,Js=ln.getTime()+(Ca.getTimezoneOffset()-ln.getTimezoneOffset())*6e4,L0=Ca.getTimezoneOffset();function Oa(e){var t=new Date;return t.setTime(e*24*60*60*1e3+Js),t.getTimezoneOffset()!==L0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-L0)*6e4),t}var M0=new Date("2017-02-19T19:06:09.000Z"),Da=isNaN(M0.getFullYear())?new Date("2/19/17"):M0,Zs=Da.getFullYear()==2017;function Qe(e,t){var r=new Date(e);if(Zs)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Da.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function Sn(e,t){if(ve&&Buffer.isBuffer(e))return e.toString("binary");if(typeof TextDecoder<"u")try{var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function nr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=nr(e[r]));return t}function Re(e,t){for(var r="";r.length<t;)r+=e;return r}function Dr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var qs=["january","february","march","april","may","june","july","august","september","october","november","december"];function Nt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&qs.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function ue(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return ve?n=Nr(r):n=Es(r),we.utils.cfb_add(e,t,n)}we.utils.cfb_add(e,t,r)}else e.file(t,r)}function Kn(){return we.utils.cfb_new()}var Pe=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Qs={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Yn=$n(Qs),Jn=/[&<>'"]/g,ef=/[\u0000-\u0008\u000b-\u001f]/g;function Te(e){var t=e+"";return t.replace(Jn,function(r){return Yn[r]}).replace(ef,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function B0(e){return Te(e).replace(/ /g,"_x0020_")}var Ra=/[\u0000-\u001f]/g;function rf(e){var t=e+"";return t.replace(Jn,function(r){return Yn[r]}).replace(/\n/g,"<br/>").replace(Ra,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function tf(e){var t=e+"";return t.replace(Jn,function(r){return Yn[r]}).replace(Ra,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function nf(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function af(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Pn(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function U0(e){var t=Kr(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function b0(e){return Nr(e,"binary").toString("utf8")}var qt="foo bar bazâð£",yt=ve&&(b0(qt)==Pn(qt)&&b0||U0(qt)==Pn(qt)&&U0)||Pn,Pt=ve?function(e){return Nr(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},sf=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),Ia=/(^\s|\s$|\n)/;function Ge(e,t){return"<"+e+(t.match(Ia)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Lt(e){return je(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function Z(e,t,r){return"<"+e+(r!=null?Lt(r):"")+(t!=null?(t.match(Ia)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Vn(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function ff(e,t){switch(typeof e){case"string":var r=Z("vt:lpwstr",Te(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return Z((e|0)==e?"vt:i4":"vt:r8",Te(String(e)));case"boolean":return Z("vt:bool",e?"true":"false")}if(e instanceof Date)return Z("vt:filetime",Vn(e));throw new Error("Unable to serialize "+e)}var Me={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},dt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],sr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function of(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function lf(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var W0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},H0=ve?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Nr(t)})):W0(e)}:W0,V0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(At(e,a)));return n.join("").replace(Ft,"")},Zn=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Ft,""):V0(e,t,r)}:V0,G0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},ka=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):G0(e,t,r)}:G0,X0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(ft(e,a)));return n.join("")},Wt=ve?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):X0(t,r,n)}:X0,Na=function(e,t){var r=fr(e,t);return r>0?Wt(e,t+4,t+4+r-1):""},Pa=Na,La=function(e,t){var r=fr(e,t);return r>0?Wt(e,t+4,t+4+r-1):""},Ma=La,Ba=function(e,t){var r=2*fr(e,t);return r>0?Wt(e,t+4,t+4+r-1):""},Ua=Ba,ba=function(t,r){var n=fr(t,r);return n>0?Zn(t,r+4,r+4+n):""},Wa=ba,Ha=function(e,t){var r=fr(e,t);return r>0?Wt(e,t+4,t+4+r):""},Va=Ha,Ga=function(e,t){return of(e,t)},cn=Ga,qn=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};ve&&(Pa=function(t,r){if(!Buffer.isBuffer(t))return Na(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ma=function(t,r){if(!Buffer.isBuffer(t))return La(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ua=function(t,r){if(!Buffer.isBuffer(t))return Ba(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Wa=function(t,r){if(!Buffer.isBuffer(t))return ba(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},Va=function(t,r){if(!Buffer.isBuffer(t))return Ha(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},cn=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Ga(t,r)},qn=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var ft=function(e,t){return e[t]},At=function(e,t){return e[t+1]*256+e[t]},cf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},fr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},zr=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},hf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Ct(e,t){var r="",n,a,i=[],s,f,l,o;switch(t){case"dbcs":if(o=this.l,ve&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(l=0;l<e;++l)r+=String.fromCharCode(At(this,o)),o+=2;e*=2;break;case"utf8":r=Wt(this,this.l,this.l+e);break;case"utf16le":e*=2,r=Zn(this,this.l,this.l+e);break;case"wstr":return Ct.call(this,e,"dbcs");case"lpstr-ansi":r=Pa(this,this.l),e=4+fr(this,this.l);break;case"lpstr-cp":r=Ma(this,this.l),e=4+fr(this,this.l);break;case"lpwstr":r=Ua(this,this.l),e=4+2*fr(this,this.l);break;case"lpp4":e=4+fr(this,this.l),r=Wa(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+fr(this,this.l),r=Va(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=ft(this,this.l+e++))!==0;)i.push(Yt(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=At(this,this.l+e))!==0;)i.push(Yt(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",o=this.l,l=0;l<e;++l){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ft(this,o),this.l=o+1,f=Ct.call(this,e-l,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Yt(At(this,o))),o+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",o=this.l,l=0;l!=e;++l){if(this.lens&&this.lens.indexOf(o)!==-1)return s=ft(this,o),this.l=o+1,f=Ct.call(this,e-l,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(Yt(ft(this,o))),o+=1}r=i.join("");break;default:switch(e){case 1:return n=ft(this,this.l),this.l++,n;case 2:return n=(t==="i"?cf:At)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?zr:hf)(this,this.l),this.l+=4,n):(a=fr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=cn(this,this.l):a=cn([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=ka(this,this.l,e);break}}return this.l+=e,r}var uf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},xf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},df=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function pf(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)df(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,uf(this,t,this.l);break;case 8:if(n=8,r==="f"){lf(this,t,this.l);break}case 16:break;case-4:n=4,xf(this,t,this.l);break}return this.l+=n,this}function Xa(e,t){var r=ka(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function ir(e,t){e.l=t,e.read_shift=Ct,e.chk=Xa,e.write_shift=pf}function Er(e,t){e.l+=t}function b(e){var t=Kr(e);return ir(t,0),t}function rr(){var e=[],t=ve?256:2048,r=function(o){var c=b(o);return ir(c,0),c},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(o){return n&&o<n.length-n.l?n:(a(),n=r(Math.max(o+1,t)))},s=function(){return a(),Ve(e)},f=function(o){a(),n=o,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function V(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=lu[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&qn(r)&&e.push(r)}}function Ot(e,t,r){var n=nr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function j0(e,t,r){var n=nr(e);return n.s=Ot(n.s,t.s,r),n.e=Ot(n.e,t.s,r),n}function Dt(e,t){if(e.cRel&&e.c<0)for(e=nr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=nr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ee(e);return!e.cRel&&e.cRel!=null&&(r=gf(r)),!e.rRel&&e.rRel!=null&&(r=vf(r)),r}function Ln(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+Ke(e.s.c)+":"+(e.e.cRel?"":"$")+Ke(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Xe(e.s.r)+":"+(e.e.rRel?"":"$")+Xe(e.e.r):Dt(e.s,t.biff)+":"+Dt(e.e,t.biff)}function Qn(e){return parseInt(mf(e),10)-1}function Xe(e){return""+(e+1)}function vf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function mf(e){return e.replace(/\$(\d+)$/,"$1")}function e0(e){for(var t=_f(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function Ke(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function gf(e){return e.replace(/^([A-Z])/,"$$$1")}function _f(e){return e.replace(/^\$([A-Z])/,"$1")}function Tf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Be(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Ee(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function or(e){var t=e.indexOf(":");return t==-1?{s:Be(e),e:Be(e)}:{s:Be(e.slice(0,t)),e:Be(e.slice(t+1))}}function Ne(e,t){return typeof t>"u"||typeof t=="number"?Ne(e.s,e.e):(typeof e!="string"&&(e=Ee(e)),typeof t!="string"&&(t=Ee(t)),e==t?e:e+":"+t)}function Ae(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function z0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=br(e.z,r?tr(t):t)}catch{}try{return e.w=br((e.XF||{}).numFmtId||(r?14:0),r?tr(t):t)}catch{return""+t}}function kr(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Ht[e.v]||e.v:t==null?z0(e,e.v):z0(e,t))}function Zr(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function ja(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var l=typeof n.origin=="string"?Be(n.origin):n.origin;s=l.r,f=l.c}i["!ref"]||(i["!ref"]="A1:A1")}var o={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=Ae(i["!ref"]);o.s.c=c.s.c,o.s.r=c.s.r,o.e.c=Math.max(o.e.c,c.e.c),o.e.r=Math.max(o.e.r,c.e.r),s==-1&&(o.e.r=s=c.e.r+1)}for(var d=0;d!=t.length;++d)if(t[d]){if(!Array.isArray(t[d]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[d].length;++u)if(!(typeof t[d][u]>"u")){var p={v:t[d][u]},_=s+d,h=f+u;if(o.s.r>_&&(o.s.r=_),o.s.c>h&&(o.s.c=h),o.e.r<_&&(o.e.r=_),o.e.c<h&&(o.e.c=h),t[d][u]&&typeof t[d][u]=="object"&&!Array.isArray(t[d][u])&&!(t[d][u]instanceof Date))p=t[d][u];else if(Array.isArray(p.v)&&(p.f=t[d][u][1],p.v=p.v[0]),p.v===null)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else if(n.sheetStubs)p.t="z";else continue;else typeof p.v=="number"?p.t="n":typeof p.v=="boolean"?p.t="b":p.v instanceof Date?(p.z=n.dateNF||Ie[14],n.cellDates?(p.t="d",p.w=br(p.z,tr(p.v))):(p.t="n",p.v=tr(p.v),p.w=br(p.z,p.v))):p.t="s";if(a)i[_]||(i[_]=[]),i[_][h]&&i[_][h].z&&(p.z=i[_][h].z),i[_][h]=p;else{var g=Ee({c:h,r:_});i[g]&&i[g].z&&(p.z=i[g].z),i[g]=p}}}return o.s.c<1e7&&(i["!ref"]=Ne(o)),i}function pt(e,t){return ja(null,e,t)}function Ef(e){return e.read_shift(4,"i")}function mr(e,t){return t||(t=b(4)),t.write_shift(4,e),t}function Ye(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function Ue(e,t){var r=!1;return t==null&&(r=!0,t=b(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function wf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Sf(e,t){return t||(t=b(4)),t.write_shift(2,0),t.write_shift(2,0),t}function r0(e,t){var r=e.l,n=e.read_shift(1),a=Ye(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),l=0;l!=f;++l)i.push(wf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Af(e,t){var r=!1;return t==null&&(r=!0,t=b(15+4*e.t.length)),t.write_shift(1,0),Ue(e.t,t),r?t.slice(0,t.l):t}var Ff=r0;function yf(e,t){var r=!1;return t==null&&(r=!0,t=b(23+4*e.t.length)),t.write_shift(1,1),Ue(e.t,t),t.write_shift(4,1),Sf({},t),r?t.slice(0,t.l):t}function ur(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function qr(e,t){return t==null&&(t=b(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Qr(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function et(e,t){return t==null&&(t=b(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Cf=Ye,za=Ue;function t0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function hn(e,t){var r=!1;return t==null&&(r=!0,t=b(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Of=Ye,Gn=t0,n0=hn;function $a(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?cn([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):zr(t,0)>>2;return r?a/100:a}function Ka(e,t){t==null&&(t=b(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?n=1:a==(a|0)&&a>=-536870912&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Ya(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Df(e,t){return t||(t=b(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var rt=Ya,vt=Df;function mt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Yr(e,t){return(t||b(8)).write_shift(8,e,"f")}function Rf(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),l=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var o=bf[a];o&&(t.rgb=na(o));break;case 2:t.rgb=na([s,f,l]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function un(e,t){if(t||(t=b(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function If(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function kf(e,t){t||(t=b(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Ja=2,ar=3,Qt=11,xn=19,en=64,Nf=65,Pf=71,Lf=4108,Mf=4126,He=80,$0={1:{n:"CodePage",t:Ja},2:{n:"Category",t:He},3:{n:"PresentationFormat",t:He},4:{n:"ByteCount",t:ar},5:{n:"LineCount",t:ar},6:{n:"ParagraphCount",t:ar},7:{n:"SlideCount",t:ar},8:{n:"NoteCount",t:ar},9:{n:"HiddenCount",t:ar},10:{n:"MultimediaClipCount",t:ar},11:{n:"ScaleCrop",t:Qt},12:{n:"HeadingPairs",t:Lf},13:{n:"TitlesOfParts",t:Mf},14:{n:"Manager",t:He},15:{n:"Company",t:He},16:{n:"LinksUpToDate",t:Qt},17:{n:"CharacterCount",t:ar},19:{n:"SharedDoc",t:Qt},22:{n:"HyperlinksChanged",t:Qt},23:{n:"AppVersion",t:ar,p:"version"},24:{n:"DigSig",t:Nf},26:{n:"ContentType",t:He},27:{n:"ContentStatus",t:He},28:{n:"Language",t:He},29:{n:"Version",t:He},255:{},2147483648:{n:"Locale",t:xn},2147483651:{n:"Behavior",t:xn},1919054434:{}},K0={1:{n:"CodePage",t:Ja},2:{n:"Title",t:He},3:{n:"Subject",t:He},4:{n:"Author",t:He},5:{n:"Keywords",t:He},6:{n:"Comments",t:He},7:{n:"Template",t:He},8:{n:"LastAuthor",t:He},9:{n:"RevNumber",t:He},10:{n:"EditTime",t:en},11:{n:"LastPrinted",t:en},12:{n:"CreatedDate",t:en},13:{n:"ModifiedDate",t:en},14:{n:"PageCount",t:ar},15:{n:"WordCount",t:ar},16:{n:"CharCount",t:ar},17:{n:"Thumbnail",t:Pf},18:{n:"Application",t:He},19:{n:"DocSecurity",t:ar},255:{},2147483648:{n:"Locale",t:xn},2147483651:{n:"Behavior",t:xn},1919054434:{}};function Bf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Uf=Bf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),bf=nr(Uf),Ht={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Wf={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Za(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function qa(e,t){var r=Ys(Wf),n=[],a;n[n.length]=Pe,n[n.length]=Z("Types",null,{xmlns:Me.CT,"xmlns:xsd":Me.xsd,"xmlns:xsi":Me.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(l){return Z("Default",null,{Extension:l[0],ContentType:l[1]})}));var i=function(l){e[l]&&e[l].length>0&&(a=e[l][0],n[n.length]=Z("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:rn[l][t.bookType]||rn[l].xlsx}))},s=function(l){(e[l]||[]).forEach(function(o){n[n.length]=Z("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:rn[l][t.bookType]||rn[l].xlsx})})},f=function(l){(e[l]||[]).forEach(function(o){n[n.length]=Z("Override",null,{PartName:(o[0]=="/"?"":"/")+o,ContentType:r[l][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var pe={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Qa(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ct(e){var t=[Pe,Z("Relationships",null,{xmlns:Me.RELS})];return je(e["!id"]).forEach(function(r){t[t.length]=Z("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function _e(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,[pe.HLINK,pe.XPATH,pe.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Hf(e){var t=[Pe];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function Y0(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function Vf(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function Gf(e){var t=[Pe];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(Y0(e[r][0],e[r][1])),t.push(Vf("",e[r][0]));return t.push(Y0("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ei(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+an.version+"</meta:generator></office:meta></office:document-meta>"}var $r=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function Mn(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=Te(t),n[n.length]=r?Z(e,t,r):Ge(e,t))}function ri(e,t){var r=t||{},n=[Pe,Z("cp:coreProperties",null,{"xmlns:cp":Me.CORE_PROPS,"xmlns:dc":Me.dc,"xmlns:dcterms":Me.dcterms,"xmlns:dcmitype":Me.dcmitype,"xmlns:xsi":Me.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&Mn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:Vn(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&Mn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:Vn(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=$r.length;++i){var s=$r[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&Mn(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var ht=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ti=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ni(e){var t=[],r=Z;return e||(e={}),e.Application="SheetJS",t[t.length]=Pe,t[t.length]=Z("Properties",null,{xmlns:Me.EXT_PROPS,"xmlns:vt":Me.vt}),ht.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=Te(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+Te(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function ai(e){var t=[Pe,Z("Properties",null,{xmlns:Me.CUST_PROPS,"xmlns:vt":Me.vt})];if(!e)return t.join("");var r=1;return je(e).forEach(function(a){++r,t[t.length]=Z("property",ff(e[a]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Te(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var J0={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Xf(e,t){var r=[];return je(J0).map(function(n){for(var a=0;a<$r.length;++a)if($r[a][1]==n)return $r[a];for(a=0;a<ht.length;++a)if(ht[a][1]==n)return ht[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Ge(J0[n[1]]||n[1],a))}}),Z("DocumentProperties",r.join(""),{xmlns:sr.o})}function jf(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&je(e).forEach(function(i){if(Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<$r.length;++s)if(i==$r[s][1])return;for(s=0;s<ht.length;++s)if(i==ht[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],l="string";typeof f=="number"?(l="float",f=String(f)):f===!0||f===!1?(l="boolean",f=f?"1":"0"):f=String(f),a.push(Z(B0(i),f,{"dt:dt":l}))}}),t&&je(t).forEach(function(i){if(Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(Z(B0(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+sr.o+'">'+a.join("")+"</"+n+">"}function zf(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=b(8);return s.write_shift(4,n),s.write_shift(4,a),s}function Z0(e,t){var r=b(4),n=b(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=b(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=zf(t);break;case 31:case 80:for(n=b(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return Ve([r,n])}var ii=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function $f(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function q0(e,t,r){var n=b(8),a=[],i=[],s=8,f=0,l=b(8),o=b(8);if(l.write_shift(4,2),l.write_shift(4,1200),o.write_shift(4,1),i.push(l),a.push(o),s+=8+l.length,!t){o=b(8),o.write_shift(4,0),a.unshift(o);var c=[b(4)];for(c[0].write_shift(4,e.length),f=0;f<e.length;++f){var d=e[f][0];for(l=b(8+2*(d.length+1)+(d.length%2?0:2)),l.write_shift(4,f+2),l.write_shift(4,d.length+1),l.write_shift(0,d,"dbcs");l.l!=l.length;)l.write_shift(1,0);c.push(l)}l=Ve(c),i.unshift(l),s+=8+l.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(ii.indexOf(e[f][0])>-1||ti.indexOf(e[f][0])>-1)&&e[f][1]!=null){var u=e[f][1],p=0;if(t){p=+t[e[f][0]];var _=r[p];if(_.p=="version"&&typeof u=="string"){var h=u.split(".");u=(+h[0]<<16)+(+h[1]||0)}l=Z0(_.t,u)}else{var g=$f(u);g==-1&&(g=31,u=String(u)),l=Z0(g,u)}i.push(l),o=b(8),o.write_shift(4,t?p:2+f),a.push(o),s+=8+l.length}var C=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,C),C+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),Ve([n].concat(a).concat(i))}function Q0(e,t,r,n,a,i){var s=b(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,we.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var l=q0(e,r,n);if(f.push(l),a){var o=q0(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+l.length),f.push(o)}return Ve(f)}function Kf(e,t){t||(t=b(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Yf(e,t){return e.read_shift(t)===1}function qe(e,t){return t||(t=b(2)),t.write_shift(2,+!!e),t}function si(e){return e.read_shift(2,"u")}function hr(e,t){return t||(t=b(2)),t.write_shift(2,e),t}function fi(e,t,r){return r||(r=b(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function oi(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function Jf(e){var t=e.t||"",r=b(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=b(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return Ve(a)}function Zf(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function qf(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):Zf(e,n,r)}function Qf(e,t,r){if(r.biff>5)return qf(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function li(e,t,r){return r||(r=b(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function ea(e,t){t||(t=b(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function eo(e){var t=b(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),ea(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&ea(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var l=0;n.slice(l*3,l*3+3)=="../"||n.slice(l*3,l*3+3)=="..\\";)++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,n.charCodeAt(r+3*l)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Jr(e,t,r,n){return n||(n=b(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function ro(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function to(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function ci(e,t){return t||(t=b(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function a0(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=b(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function no(e,t){var r=!t||t.biff==8,n=b(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function ao(e,t){var r=!t||t.biff>=8?2:1,n=b(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function io(e,t){var r=b(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=Jf(e[a]);var i=Ve([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function so(){var e=b(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function fo(e){var t=b(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function oo(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=b(a);return i.write_shift(2,e.sz*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function lo(e,t,r,n){var a=b(10);return Jr(e,t,n,a),a.write_shift(4,r),a}function co(e,t,r,n,a){var i=!a||a.biff==8,s=b(8+ +i+(1+i)*r.length);return Jr(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function ho(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function uo(e,t){var r=t.biff==8||!t.biff?4:2,n=b(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function ra(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function xo(e){var t=b(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function po(e,t,r,n,a,i){var s=b(8);return Jr(e,t,n,s),fi(r,i,s),s}function vo(e,t,r,n){var a=b(14);return Jr(e,t,n,a),Yr(r,a),a}function mo(e,t,r){if(r.biff<8)return go(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(ro(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function go(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=oi(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function _o(e){var t=b(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)ci(e[r],t);return t}function To(e){var t=b(24),r=Be(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return Ve([t,eo(e[1])])}function Eo(e){var t=e[1].Tooltip,r=b(10+2*(t.length+1));r.write_shift(2,2048);var n=Be(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function wo(e){return e||(e=b(4)),e.write_shift(2,1),e.write_shift(2,1),e}function So(e,t,r){if(!r.cellStyles)return Er(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),l=e.read_shift(2);n==2&&(e.l+=2);var o={s:a,e:i,w:s,ixfe:f,flags:l};return(r.biff>=5||!r.biff)&&(o.level=l>>8&7),o}function Ao(e,t){var r=b(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Fo(e){for(var t=b(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function yo(e,t,r){var n=b(15);return Gt(n,e,t),n.write_shift(8,r,"f"),n}function Co(e,t,r){var n=b(9);return Gt(n,e,t),n.write_shift(2,r),n}var Oo=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=$n({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,l){var o=[],c=Kr(1);switch(l.type){case"base64":c=pr(Ir(f));break;case"binary":c=pr(f);break;case"buffer":case"array":c=f;break}ir(c,0);var d=c.read_shift(1),u=!!(d&136),p=!1,_=!1;switch(d){case 2:break;case 3:break;case 48:p=!0,u=!0;break;case 49:p=!0,u=!0;break;case 131:break;case 139:break;case 140:_=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+d.toString(16))}var h=0,g=521;d==2&&(h=c.read_shift(2)),c.l+=3,d!=2&&(h=c.read_shift(4)),h>1048576&&(h=1e6),d!=2&&(g=c.read_shift(2));var C=c.read_shift(2),O=l.codepage||1252;d!=2&&(c.l+=16,c.read_shift(1),c[c.l]!==0&&(O=e[c[c.l]]),c.l+=1,c.l+=2),_&&(c.l+=36);for(var F=[],U={},J=Math.min(c.length,d==2?521:g-10-(p?264:0)),re=_?32:11;c.l<J&&c[c.l]!=13;)switch(U={},U.name=F0.utils.decode(O,c.slice(c.l,c.l+re)).replace(/[\u0000\r\n].*$/g,""),c.l+=re,U.type=String.fromCharCode(c.read_shift(1)),d!=2&&!_&&(U.offset=c.read_shift(4)),U.len=c.read_shift(1),d==2&&(U.offset=c.read_shift(2)),U.dec=c.read_shift(1),U.name.length&&F.push(U),d!=2&&(c.l+=_?13:14),U.type){case"B":(!p||U.len!=8)&&l.WTF&&console.log("Skipping "+U.name+":"+U.type);break;case"G":case"P":l.WTF&&console.log("Skipping "+U.name+":"+U.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+U.type)}if(c[c.l]!==13&&(c.l=g-1),c.read_shift(1)!==13)throw new Error("DBF Terminator not found "+c.l+" "+c[c.l]);c.l=g;var D=0,H=0;for(o[0]=[],H=0;H!=F.length;++H)o[0][H]=F[H].name;for(;h-- >0;){if(c[c.l]===42){c.l+=C;continue}for(++c.l,o[++D]=[],H=0,H=0;H!=F.length;++H){var L=c.slice(c.l,c.l+F[H].len);c.l+=F[H].len,ir(L,0);var X=F0.utils.decode(O,L);switch(F[H].type){case"C":X.trim().length&&(o[D][H]=X.replace(/\s+$/,""));break;case"D":X.length===8?o[D][H]=new Date(+X.slice(0,4),+X.slice(4,6)-1,+X.slice(6,8)):o[D][H]=X;break;case"F":o[D][H]=parseFloat(X.trim());break;case"+":case"I":o[D][H]=_?L.read_shift(-4,"i")^2147483648:L.read_shift(4,"i");break;case"L":switch(X.trim().toUpperCase()){case"Y":case"T":o[D][H]=!0;break;case"N":case"F":o[D][H]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+X+"|")}break;case"M":if(!u)throw new Error("DBF Unexpected MEMO for type "+d.toString(16));o[D][H]="##MEMO##"+(_?parseInt(X.trim(),10):L.read_shift(4));break;case"N":X=X.replace(/\u0000/g,"").trim(),X&&X!="."&&(o[D][H]=+X||0);break;case"@":o[D][H]=new Date(L.read_shift(-8,"f")-621356832e5);break;case"T":o[D][H]=new Date((L.read_shift(4)-2440588)*864e5+L.read_shift(4));break;case"Y":o[D][H]=L.read_shift(4,"i")/1e4+L.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":o[D][H]=-L.read_shift(-8,"f");break;case"B":if(p&&F[H].len==8){o[D][H]=L.read_shift(8,"f");break}case"G":case"P":L.l+=F[H].len;break;case"0":if(F[H].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+F[H].type)}}}if(d!=2&&c.l<c.length&&c[c.l++]!=26)throw new Error("DBF EOF Marker missing "+(c.l-1)+" of "+c.length+" "+c[c.l-1].toString(16));return l&&l.sheetRows&&(o=o.slice(0,l.sheetRows)),l.DBF=F,o}function n(f,l){var o=l||{};o.dateNF||(o.dateNF="yyyymmdd");var c=pt(r(f,o),o);return c["!cols"]=o.DBF.map(function(d){return{wch:d.len,DBF:d}}),delete o.DBF,c}function a(f,l){try{return Zr(n(f,l),l)}catch(o){if(l&&l.WTF)throw o}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,l){var o=l||{};if(+o.codepage>=0&&It(+o.codepage),o.type=="string")throw new Error("Cannot write DBF to JS string");var c=rr(),d=gn(f,{header:1,raw:!0,cellDates:!0}),u=d[0],p=d.slice(1),_=f["!cols"]||[],h=0,g=0,C=0,O=1;for(h=0;h<u.length;++h){if(((_[h]||{}).DBF||{}).name){u[h]=_[h].DBF.name,++C;continue}if(u[h]!=null){if(++C,typeof u[h]=="number"&&(u[h]=u[h].toString(10)),typeof u[h]!="string")throw new Error("DBF Invalid column name "+u[h]+" |"+typeof u[h]+"|");if(u.indexOf(u[h])!==h){for(g=0;g<1024;++g)if(u.indexOf(u[h]+"_"+g)==-1){u[h]+="_"+g;break}}}}var F=Ae(f["!ref"]),U=[],J=[],re=[];for(h=0;h<=F.e.c-F.s.c;++h){var D="",H="",L=0,X=[];for(g=0;g<p.length;++g)p[g][h]!=null&&X.push(p[g][h]);if(X.length==0||u[h]==null){U[h]="?";continue}for(g=0;g<X.length;++g){switch(typeof X[g]){case"number":H="B";break;case"string":H="C";break;case"boolean":H="L";break;case"object":H=X[g]instanceof Date?"D":"C";break;default:H="C"}L=Math.max(L,String(X[g]).length),D=D&&D!=H?"C":H}L>250&&(L=250),H=((_[h]||{}).DBF||{}).type,H=="C"&&_[h].DBF.len>L&&(L=_[h].DBF.len),D=="B"&&H=="N"&&(D="N",re[h]=_[h].DBF.dec,L=_[h].DBF.len),J[h]=D=="C"||H=="N"?L:i[D]||0,O+=J[h],U[h]=D}var j=c.next(32);for(j.write_shift(4,318902576),j.write_shift(4,p.length),j.write_shift(2,296+32*C),j.write_shift(2,O),h=0;h<4;++h)j.write_shift(4,0);for(j.write_shift(4,0|(+t[ua]||3)<<8),h=0,g=0;h<u.length;++h)if(u[h]!=null){var z=c.next(32),te=(u[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);z.write_shift(1,te,"sbcs"),z.write_shift(1,U[h]=="?"?"C":U[h],"sbcs"),z.write_shift(4,g),z.write_shift(1,J[h]||i[U[h]]||0),z.write_shift(1,re[h]||0),z.write_shift(1,2),z.write_shift(4,0),z.write_shift(1,0),z.write_shift(4,0),z.write_shift(4,0),g+=J[h]||i[U[h]]||0}var ae=c.next(264);for(ae.write_shift(4,13),h=0;h<65;++h)ae.write_shift(4,0);for(h=0;h<p.length;++h){var M=c.next(O);for(M.write_shift(1,0),g=0;g<u.length;++g)if(u[g]!=null)switch(U[g]){case"L":M.write_shift(1,p[h][g]==null?63:p[h][g]?84:70);break;case"B":M.write_shift(8,p[h][g]||0,"f");break;case"N":var xe="0";for(typeof p[h][g]=="number"&&(xe=p[h][g].toFixed(re[g]||0)),C=0;C<J[g]-xe.length;++C)M.write_shift(1,32);M.write_shift(1,xe,"sbcs");break;case"D":p[h][g]?(M.write_shift(4,("0000"+p[h][g].getFullYear()).slice(-4),"sbcs"),M.write_shift(2,("00"+(p[h][g].getMonth()+1)).slice(-2),"sbcs"),M.write_shift(2,("00"+p[h][g].getDate()).slice(-2),"sbcs")):M.write_shift(8,"00000000","sbcs");break;case"C":var oe=String(p[h][g]!=null?p[h][g]:"").slice(0,J[g]);for(M.write_shift(1,oe,"sbcs"),C=0;C<J[g]-oe.length;++C)M.write_shift(1,32);break}}return c.next(1).write_shift(1,26),c.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),Do=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+je(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(u,p){var _=e[p];return typeof _=="number"?A0(_):_},n=function(u,p,_){var h=p.charCodeAt(0)-32<<4|_.charCodeAt(0)-48;return h==59?u:A0(h)};e["|"]=254;function a(u,p){switch(p.type){case"base64":return i(Ir(u),p);case"binary":return i(u,p);case"buffer":return i(ve&&Buffer.isBuffer(u)?u.toString("binary"):Ut(u),p);case"array":return i(Sn(u),p)}throw new Error("Unrecognized type "+p.type)}function i(u,p){var _=u.split(/[\n\r]+/),h=-1,g=-1,C=0,O=0,F=[],U=[],J=null,re={},D=[],H=[],L=[],X=0,j;for(+p.codepage>=0&&It(+p.codepage);C!==_.length;++C){X=0;var z=_[C].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),te=z.replace(/;;/g,"\0").split(";").map(function(A){return A.replace(/\u0000/g,";")}),ae=te[0],M;if(z.length>0)switch(ae){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":te[1].charAt(0)=="P"&&U.push(z.slice(3).replace(/;;/g,";"));break;case"C":var xe=!1,oe=!1,Fe=!1,Oe=!1,ze=-1,be=-1;for(O=1;O<te.length;++O)switch(te[O].charAt(0)){case"A":break;case"X":g=parseInt(te[O].slice(1))-1,oe=!0;break;case"Y":for(h=parseInt(te[O].slice(1))-1,oe||(g=0),j=F.length;j<=h;++j)F[j]=[];break;case"K":M=te[O].slice(1),M.charAt(0)==='"'?M=M.slice(1,M.length-1):M==="TRUE"?M=!0:M==="FALSE"?M=!1:isNaN(Dr(M))?isNaN(Nt(M).getDate())||(M=Qe(M)):(M=Dr(M),J!==null&&Aa(J)&&(M=Oa(M))),xe=!0;break;case"E":Oe=!0;var S=Cl(te[O].slice(1),{r:h,c:g});F[h][g]=[F[h][g],S];break;case"S":Fe=!0,F[h][g]=[F[h][g],"S5S"];break;case"G":break;case"R":ze=parseInt(te[O].slice(1))-1;break;case"C":be=parseInt(te[O].slice(1))-1;break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+z)}if(xe&&(F[h][g]&&F[h][g].length==2?F[h][g][0]=M:F[h][g]=M,J=null),Fe){if(Oe)throw new Error("SYLK shared formula cannot have own formula");var P=ze>-1&&F[ze][be];if(!P||!P[1])throw new Error("SYLK shared formula cannot find base");F[h][g][1]=Ol(P[1],{r:h-ze,c:g-be})}break;case"F":var y=0;for(O=1;O<te.length;++O)switch(te[O].charAt(0)){case"X":g=parseInt(te[O].slice(1))-1,++y;break;case"Y":for(h=parseInt(te[O].slice(1))-1,j=F.length;j<=h;++j)F[j]=[];break;case"M":X=parseInt(te[O].slice(1))/20;break;case"F":break;case"G":break;case"P":J=U[parseInt(te[O].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(L=te[O].slice(1).split(" "),j=parseInt(L[0],10);j<=parseInt(L[1],10);++j)X=parseInt(L[2],10),H[j-1]=X===0?{hidden:!0}:{wch:X},i0(H[j-1]);break;case"C":g=parseInt(te[O].slice(1))-1,H[g]||(H[g]={});break;case"R":h=parseInt(te[O].slice(1))-1,D[h]||(D[h]={}),X>0?(D[h].hpt=X,D[h].hpx=pi(X)):X===0&&(D[h].hidden=!0);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+z)}y<1&&(J=null);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+z)}}return D.length>0&&(re["!rows"]=D),H.length>0&&(re["!cols"]=H),p&&p.sheetRows&&(F=F.slice(0,p.sheetRows)),[F,re]}function s(u,p){var _=a(u,p),h=_[0],g=_[1],C=pt(h,p);return je(g).forEach(function(O){C[O]=g[O]}),C}function f(u,p){return Zr(s(u,p),p)}function l(u,p,_,h){var g="C;Y"+(_+1)+";X"+(h+1)+";K";switch(u.t){case"n":g+=u.v||0,u.f&&!u.F&&(g+=";E"+f0(u.f,{r:_,c:h}));break;case"b":g+=u.v?"TRUE":"FALSE";break;case"e":g+=u.w||u.v;break;case"d":g+='"'+(u.w||u.v)+'"';break;case"s":g+='"'+u.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return g}function o(u,p){p.forEach(function(_,h){var g="F;W"+(h+1)+" "+(h+1)+" ";_.hidden?g+="0":(typeof _.width=="number"&&!_.wpx&&(_.wpx=dn(_.width)),typeof _.wpx=="number"&&!_.wch&&(_.wch=pn(_.wpx)),typeof _.wch=="number"&&(g+=Math.round(_.wch))),g.charAt(g.length-1)!=" "&&u.push(g)})}function c(u,p){p.forEach(function(_,h){var g="F;";_.hidden?g+="M0;":_.hpt?g+="M"+20*_.hpt+";":_.hpx&&(g+="M"+20*vn(_.hpx)+";"),g.length>2&&u.push(g+"R"+(h+1))})}function d(u,p){var _=["ID;PWXL;N;E"],h=[],g=Ae(u["!ref"]),C,O=Array.isArray(u),F=`\r
`;_.push("P;PGeneral"),_.push("F;P0;DG0G8;M255"),u["!cols"]&&o(_,u["!cols"]),u["!rows"]&&c(_,u["!rows"]),_.push("B;Y"+(g.e.r-g.s.r+1)+";X"+(g.e.c-g.s.c+1)+";D"+[g.s.c,g.s.r,g.e.c,g.e.r].join(" "));for(var U=g.s.r;U<=g.e.r;++U)for(var J=g.s.c;J<=g.e.c;++J){var re=Ee({r:U,c:J});C=O?(u[U]||[])[J]:u[re],!(!C||C.v==null&&(!C.f||C.F))&&h.push(l(C,u,U,J))}return _.join(F)+F+h.join(F)+F+"E"+F}return{to_workbook:f,to_sheet:s,from_sheet:d}}(),Ro=function(){function e(i,s){switch(s.type){case"base64":return t(Ir(i),s);case"binary":return t(i,s);case"buffer":return t(ve&&Buffer.isBuffer(i)?i.toString("binary"):Ut(i),s);case"array":return t(Sn(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),l=-1,o=-1,c=0,d=[];c!==f.length;++c){if(f[c].trim()==="BOT"){d[++l]=[],o=0;continue}if(!(l<0)){var u=f[c].trim().split(","),p=u[0],_=u[1];++c;for(var h=f[c]||"";(h.match(/["]/g)||[]).length&1&&c<f.length-1;)h+=`
`+f[++c];switch(h=h.trim(),+p){case-1:if(h==="BOT"){d[++l]=[],o=0;continue}else if(h!=="EOD")throw new Error("Unrecognized DIF special command "+h);break;case 0:h==="TRUE"?d[l][o]=!0:h==="FALSE"?d[l][o]=!1:isNaN(Dr(_))?isNaN(Nt(_).getDate())?d[l][o]=_:d[l][o]=Qe(_):d[l][o]=Dr(_),++o;break;case 1:h=h.slice(1,h.length-1),h=h.replace(/""/g,'"'),h&&h.match(/^=".*"$/)&&(h=h.slice(2,-1)),d[l][o++]=h!==""?h:null;break}if(h==="EOD")break}}return s&&s.sheetRows&&(d=d.slice(0,s.sheetRows)),d}function r(i,s){return pt(e(i,s),s)}function n(i,s){return Zr(r(i,s),s)}var a=function(){var i=function(l,o,c,d,u){l.push(o),l.push(c+","+d),l.push('"'+u.replace(/"/g,'""')+'"')},s=function(l,o,c,d){l.push(o+","+c),l.push(o==1?'"'+d.replace(/"/g,'""')+'"':d)};return function(l){var o=[],c=Ae(l["!ref"]),d,u=Array.isArray(l);i(o,"TABLE",0,1,"sheetjs"),i(o,"VECTORS",0,c.e.r-c.s.r+1,""),i(o,"TUPLES",0,c.e.c-c.s.c+1,""),i(o,"DATA",0,0,"");for(var p=c.s.r;p<=c.e.r;++p){s(o,-1,0,"BOT");for(var _=c.s.c;_<=c.e.c;++_){var h=Ee({r:p,c:_});if(d=u?(l[p]||[])[_]:l[h],!d){s(o,1,0,"");continue}switch(d.t){case"n":var g=d.w;!g&&d.v!=null&&(g=d.v),g==null?d.f&&!d.F?s(o,1,0,"="+d.f):s(o,1,0,""):s(o,0,g,"V");break;case"b":s(o,0,d.v?1:0,d.v?"TRUE":"FALSE");break;case"s":s(o,1,0,isNaN(d.v)?d.v:'="'+d.v+'"');break;case"d":d.w||(d.w=br(d.z||Ie[14],tr(Qe(d.v)))),s(o,0,d.w,"V");break;default:s(o,1,0,"")}}}s(o,-1,0,"EOD");var C=`\r
`,O=o.join(C);return O}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),hi=function(){function e(d){return d.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(d){return d.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(d,u){for(var p=d.split(`
`),_=-1,h=-1,g=0,C=[];g!==p.length;++g){var O=p[g].trim().split(":");if(O[0]==="cell"){var F=Be(O[1]);if(C.length<=F.r)for(_=C.length;_<=F.r;++_)C[_]||(C[_]=[]);switch(_=F.r,h=F.c,O[2]){case"t":C[_][h]=e(O[3]);break;case"v":C[_][h]=+O[3];break;case"vtf":var U=O[O.length-1];case"vtc":switch(O[3]){case"nl":C[_][h]=!!+O[4];break;default:C[_][h]=+O[4];break}O[2]=="vtf"&&(C[_][h]=[C[_][h],U])}}}return u&&u.sheetRows&&(C=C.slice(0,u.sheetRows)),C}function n(d,u){return pt(r(d,u),u)}function a(d,u){return Zr(n(d,u),u)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),l="--SocialCalcSpreadsheetControlSave--";function o(d){if(!d||!d["!ref"])return"";for(var u=[],p=[],_,h="",g=or(d["!ref"]),C=Array.isArray(d),O=g.s.r;O<=g.e.r;++O)for(var F=g.s.c;F<=g.e.c;++F)if(h=Ee({r:O,c:F}),_=C?(d[O]||[])[F]:d[h],!(!_||_.v==null||_.t==="z")){switch(p=["cell",h,"t"],_.t){case"s":case"str":p.push(t(_.v));break;case"n":_.f?(p[2]="vtf",p[3]="n",p[4]=_.v,p[5]=t(_.f)):(p[2]="v",p[3]=_.v);break;case"b":p[2]="vt"+(_.f?"f":"c"),p[3]="nl",p[4]=_.v?"1":"0",p[5]=t(_.f||(_.v?"TRUE":"FALSE"));break;case"d":var U=tr(Qe(_.v));p[2]="vtc",p[3]="nd",p[4]=""+U,p[5]=_.w||br(_.z||Ie[14],U);break;case"e":continue}u.push(p.join(":"))}return u.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),u.push("valueformat:1:text-wiki"),u.join(`
`)}function c(d){return[i,s,f,s,o(d),l].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),Io=function(){function e(c,d,u,p,_){_.raw?d[u][p]=c:c===""||(c==="TRUE"?d[u][p]=!0:c==="FALSE"?d[u][p]=!1:isNaN(Dr(c))?isNaN(Nt(c).getDate())?d[u][p]=c:d[u][p]=Qe(c):d[u][p]=Dr(c))}function t(c,d){var u=d||{},p=[];if(!c||c.length===0)return p;for(var _=c.split(/[\r\n]/),h=_.length-1;h>=0&&_[h].length===0;)--h;for(var g=10,C=0,O=0;O<=h;++O)C=_[O].indexOf(" "),C==-1?C=_[O].length:C++,g=Math.max(g,C);for(O=0;O<=h;++O){p[O]=[];var F=0;for(e(_[O].slice(0,g).trim(),p,O,F,u),F=1;F<=(_[O].length-g)/10+1;++F)e(_[O].slice(g+(F-1)*10,g+F*10).trim(),p,O,F,u)}return u.sheetRows&&(p=p.slice(0,u.sheetRows)),p}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(c){for(var d={},u=!1,p=0,_=0;p<c.length;++p)(_=c.charCodeAt(p))==34?u=!u:!u&&_ in r&&(d[_]=(d[_]||0)+1);_=[];for(p in d)Object.prototype.hasOwnProperty.call(d,p)&&_.push([d[p],p]);if(!_.length){d=n;for(p in d)Object.prototype.hasOwnProperty.call(d,p)&&_.push([d[p],p])}return _.sort(function(h,g){return h[0]-g[0]||n[h[1]]-n[g[1]]}),r[_.pop()[1]]||44}function i(c,d){var u=d||{},p="",_=u.dense?[]:{},h={s:{c:0,r:0},e:{c:0,r:0}};c.slice(0,4)=="sep="?c.charCodeAt(5)==13&&c.charCodeAt(6)==10?(p=c.charAt(4),c=c.slice(7)):c.charCodeAt(5)==13||c.charCodeAt(5)==10?(p=c.charAt(4),c=c.slice(6)):p=a(c.slice(0,1024)):u&&u.FS?p=u.FS:p=a(c.slice(0,1024));var g=0,C=0,O=0,F=0,U=0,J=p.charCodeAt(0),re=!1,D=0,H=c.charCodeAt(0);c=c.replace(/\r\n/mg,`
`);var L=u.dateNF!=null?js(u.dateNF):null;function X(){var j=c.slice(F,U),z={};if(j.charAt(0)=='"'&&j.charAt(j.length-1)=='"'&&(j=j.slice(1,-1).replace(/""/g,'"')),j.length===0)z.t="z";else if(u.raw)z.t="s",z.v=j;else if(j.trim().length===0)z.t="s",z.v=j;else if(j.charCodeAt(0)==61)j.charCodeAt(1)==34&&j.charCodeAt(j.length-1)==34?(z.t="s",z.v=j.slice(2,-1).replace(/""/g,'"')):Dl(j)?(z.t="n",z.f=j.slice(1)):(z.t="s",z.v=j);else if(j=="TRUE")z.t="b",z.v=!0;else if(j=="FALSE")z.t="b",z.v=!1;else if(!isNaN(O=Dr(j)))z.t="n",u.cellText!==!1&&(z.w=j),z.v=O;else if(!isNaN(Nt(j).getDate())||L&&j.match(L)){z.z=u.dateNF||Ie[14];var te=0;L&&j.match(L)&&(j=zs(j,u.dateNF,j.match(L)||[]),te=1),u.cellDates?(z.t="d",z.v=Qe(j,te)):(z.t="n",z.v=tr(Qe(j,te))),u.cellText!==!1&&(z.w=br(z.z,z.v instanceof Date?tr(z.v):z.v)),u.cellNF||delete z.z}else z.t="s",z.v=j;if(z.t=="z"||(u.dense?(_[g]||(_[g]=[]),_[g][C]=z):_[Ee({c:C,r:g})]=z),F=U+1,H=c.charCodeAt(F),h.e.c<C&&(h.e.c=C),h.e.r<g&&(h.e.r=g),D==J)++C;else if(C=0,++g,u.sheetRows&&u.sheetRows<=g)return!0}e:for(;U<c.length;++U)switch(D=c.charCodeAt(U)){case 34:H===34&&(re=!re);break;case J:case 10:case 13:if(!re&&X())break e;break}return U-F>0&&X(),_["!ref"]=Ne(h),_}function s(c,d){return!(d&&d.PRN)||d.FS||c.slice(0,4)=="sep="||c.indexOf("	")>=0||c.indexOf(",")>=0||c.indexOf(";")>=0?i(c,d):pt(t(c,d),d)}function f(c,d){var u="",p=d.type=="string"?[0,0,0,0]:Gu(c,d);switch(d.type){case"base64":u=Ir(c);break;case"binary":u=c;break;case"buffer":d.codepage==65001?u=c.toString("utf8"):(d.codepage,u=ve&&Buffer.isBuffer(c)?c.toString("binary"):Ut(c));break;case"array":u=Sn(c);break;case"string":u=c;break;default:throw new Error("Unrecognized type "+d.type)}return p[0]==239&&p[1]==187&&p[2]==191?u=yt(u.slice(3)):d.type!="string"&&d.type!="buffer"&&d.codepage==65001?u=yt(u):d.type=="binary",u.slice(0,19)=="socialcalc:version:"?hi.to_sheet(d.type=="string"?u:yt(u),d):s(u,d)}function l(c,d){return Zr(f(c,d),d)}function o(c){for(var d=[],u=Ae(c["!ref"]),p,_=Array.isArray(c),h=u.s.r;h<=u.e.r;++h){for(var g=[],C=u.s.c;C<=u.e.c;++C){var O=Ee({r:h,c:C});if(p=_?(c[h]||[])[C]:c[O],!p||p.v==null){g.push("          ");continue}for(var F=(p.w||(kr(p),p.w)||"").slice(0,10);F.length<10;)F+=" ";g.push(F+(C===0?" ":""))}d.push(g.join(""))}return d.join(`
`)}return{to_workbook:l,to_sheet:f,from_sheet:o}}(),ta=function(){function e(S,P,y){if(S){ir(S,S.l||0);for(var A=y.Enum||ze;S.l<S.length;){var G=S.read_shift(2),le=A[G]||A[65535],ce=S.read_shift(2),fe=S.l+ce,ee=le.f&&le.f(S,ce,y);if(S.l=fe,P(ee,le,G))return}}}function t(S,P){switch(P.type){case"base64":return r(pr(Ir(S)),P);case"binary":return r(pr(S),P);case"buffer":case"array":return r(S,P)}throw"Unsupported type "+P.type}function r(S,P){if(!S)return S;var y=P||{},A=y.dense?[]:{},G="Sheet1",le="",ce=0,fe={},ee=[],Se=[],de={s:{r:0,c:0},e:{r:0,c:0}},Je=y.sheetRows||0;if(S[2]==0&&(S[3]==8||S[3]==9)&&S.length>=16&&S[14]==5&&S[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(S[2]==2)y.Enum=ze,e(S,function(se,lr,Sr){switch(Sr){case 0:y.vers=se,se>=4096&&(y.qpro=!0);break;case 6:de=se;break;case 204:se&&(le=se);break;case 222:le=se;break;case 15:case 51:y.qpro||(se[1].v=se[1].v.slice(1));case 13:case 14:case 16:Sr==14&&(se[2]&112)==112&&(se[2]&15)>1&&(se[2]&15)<15&&(se[1].z=y.dateNF||Ie[14],y.cellDates&&(se[1].t="d",se[1].v=Oa(se[1].v))),y.qpro&&se[3]>ce&&(A["!ref"]=Ne(de),fe[G]=A,ee.push(G),A=y.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},ce=se[3],G=le||"Sheet"+(ce+1),le="");var Gr=y.dense?(A[se[0].r]||[])[se[0].c]:A[Ee(se[0])];if(Gr){Gr.t=se[1].t,Gr.v=se[1].v,se[1].z!=null&&(Gr.z=se[1].z),se[1].f!=null&&(Gr.f=se[1].f);break}y.dense?(A[se[0].r]||(A[se[0].r]=[]),A[se[0].r][se[0].c]=se[1]):A[Ee(se[0])]=se[1];break}},y);else if(S[2]==26||S[2]==14)y.Enum=be,S[2]==14&&(y.qpro=!0,S.l=0),e(S,function(se,lr,Sr){switch(Sr){case 204:G=se;break;case 22:se[1].v=se[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(se[3]>ce&&(A["!ref"]=Ne(de),fe[G]=A,ee.push(G),A=y.dense?[]:{},de={s:{r:0,c:0},e:{r:0,c:0}},ce=se[3],G="Sheet"+(ce+1)),Je>0&&se[0].r>=Je)break;y.dense?(A[se[0].r]||(A[se[0].r]=[]),A[se[0].r][se[0].c]=se[1]):A[Ee(se[0])]=se[1],de.e.c<se[0].c&&(de.e.c=se[0].c),de.e.r<se[0].r&&(de.e.r=se[0].r);break;case 27:se[14e3]&&(Se[se[14e3][0]]=se[14e3][1]);break;case 1537:Se[se[0]]=se[1],se[0]==ce&&(G=se[1]);break}},y);else throw new Error("Unrecognized LOTUS BOF "+S[2]);if(A["!ref"]=Ne(de),fe[le||G]=A,ee.push(le||G),!Se.length)return{SheetNames:ee,Sheets:fe};for(var me={},wr=[],De=0;De<Se.length;++De)fe[ee[De]]?(wr.push(Se[De]||ee[De]),me[Se[De]]=fe[Se[De]]||fe[ee[De]]):(wr.push(Se[De]),me[Se[De]]={"!ref":"A1"});return{SheetNames:wr,Sheets:me}}function n(S,P){var y=P||{};if(+y.codepage>=0&&It(+y.codepage),y.type=="string")throw new Error("Cannot write WK1 to JS string");var A=rr(),G=Ae(S["!ref"]),le=Array.isArray(S),ce=[];q(A,0,i(1030)),q(A,6,l(G));for(var fe=Math.min(G.e.r,8191),ee=G.s.r;ee<=fe;++ee)for(var Se=Xe(ee),de=G.s.c;de<=G.e.c;++de){ee===G.s.r&&(ce[de]=Ke(de));var Je=ce[de]+Se,me=le?(S[ee]||[])[de]:S[Je];if(!(!me||me.t=="z"))if(me.t=="n")(me.v|0)==me.v&&me.v>=-32768&&me.v<=32767?q(A,13,p(ee,de,me.v)):q(A,14,h(ee,de,me.v));else{var wr=kr(me);q(A,15,d(ee,de,wr.slice(0,239)))}}return q(A,1),A.end()}function a(S,P){var y=P||{};if(+y.codepage>=0&&It(+y.codepage),y.type=="string")throw new Error("Cannot write WK3 to JS string");var A=rr();q(A,0,s(S));for(var G=0,le=0;G<S.SheetNames.length;++G)(S.Sheets[S.SheetNames[G]]||{})["!ref"]&&q(A,27,Oe(S.SheetNames[G],le++));var ce=0;for(G=0;G<S.SheetNames.length;++G){var fe=S.Sheets[S.SheetNames[G]];if(!(!fe||!fe["!ref"])){for(var ee=Ae(fe["!ref"]),Se=Array.isArray(fe),de=[],Je=Math.min(ee.e.r,8191),me=ee.s.r;me<=Je;++me)for(var wr=Xe(me),De=ee.s.c;De<=ee.e.c;++De){me===ee.s.r&&(de[De]=Ke(De));var se=de[De]+wr,lr=Se?(fe[me]||[])[De]:fe[se];if(!(!lr||lr.t=="z"))if(lr.t=="n")q(A,23,X(me,De,ce,lr.v));else{var Sr=kr(lr);q(A,22,D(me,De,ce,Sr.slice(0,239)))}}++ce}}return q(A,1),A.end()}function i(S){var P=b(2);return P.write_shift(2,S),P}function s(S){var P=b(26);P.write_shift(2,4096),P.write_shift(2,4),P.write_shift(4,0);for(var y=0,A=0,G=0,le=0;le<S.SheetNames.length;++le){var ce=S.SheetNames[le],fe=S.Sheets[ce];if(!(!fe||!fe["!ref"])){++G;var ee=or(fe["!ref"]);y<ee.e.r&&(y=ee.e.r),A<ee.e.c&&(A=ee.e.c)}}return y>8191&&(y=8191),P.write_shift(2,y),P.write_shift(1,G),P.write_shift(1,A),P.write_shift(2,0),P.write_shift(2,0),P.write_shift(1,1),P.write_shift(1,2),P.write_shift(4,0),P.write_shift(4,0),P}function f(S,P,y){var A={s:{c:0,r:0},e:{c:0,r:0}};return P==8&&y.qpro?(A.s.c=S.read_shift(1),S.l++,A.s.r=S.read_shift(2),A.e.c=S.read_shift(1),S.l++,A.e.r=S.read_shift(2),A):(A.s.c=S.read_shift(2),A.s.r=S.read_shift(2),P==12&&y.qpro&&(S.l+=2),A.e.c=S.read_shift(2),A.e.r=S.read_shift(2),P==12&&y.qpro&&(S.l+=2),A.s.c==65535&&(A.s.c=A.e.c=A.s.r=A.e.r=0),A)}function l(S){var P=b(8);return P.write_shift(2,S.s.c),P.write_shift(2,S.s.r),P.write_shift(2,S.e.c),P.write_shift(2,S.e.r),P}function o(S,P,y){var A=[{c:0,r:0},{t:"n",v:0},0,0];return y.qpro&&y.vers!=20768?(A[0].c=S.read_shift(1),A[3]=S.read_shift(1),A[0].r=S.read_shift(2),S.l+=2):(A[2]=S.read_shift(1),A[0].c=S.read_shift(2),A[0].r=S.read_shift(2)),A}function c(S,P,y){var A=S.l+P,G=o(S,P,y);if(G[1].t="s",y.vers==20768){S.l++;var le=S.read_shift(1);return G[1].v=S.read_shift(le,"utf8"),G}return y.qpro&&S.l++,G[1].v=S.read_shift(A-S.l,"cstr"),G}function d(S,P,y){var A=b(7+y.length);A.write_shift(1,255),A.write_shift(2,P),A.write_shift(2,S),A.write_shift(1,39);for(var G=0;G<A.length;++G){var le=y.charCodeAt(G);A.write_shift(1,le>=128?95:le)}return A.write_shift(1,0),A}function u(S,P,y){var A=o(S,P,y);return A[1].v=S.read_shift(2,"i"),A}function p(S,P,y){var A=b(7);return A.write_shift(1,255),A.write_shift(2,P),A.write_shift(2,S),A.write_shift(2,y,"i"),A}function _(S,P,y){var A=o(S,P,y);return A[1].v=S.read_shift(8,"f"),A}function h(S,P,y){var A=b(13);return A.write_shift(1,255),A.write_shift(2,P),A.write_shift(2,S),A.write_shift(8,y,"f"),A}function g(S,P,y){var A=S.l+P,G=o(S,P,y);if(G[1].v=S.read_shift(8,"f"),y.qpro)S.l=A;else{var le=S.read_shift(2);U(S.slice(S.l,S.l+le),G),S.l+=le}return G}function C(S,P,y){var A=P&32768;return P&=-32769,P=(A?S:0)+(P>=8192?P-16384:P),(A?"":"$")+(y?Ke(P):Xe(P))}var O={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},F=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function U(S,P){ir(S,0);for(var y=[],A=0,G="",le="",ce="",fe="";S.l<S.length;){var ee=S[S.l++];switch(ee){case 0:y.push(S.read_shift(8,"f"));break;case 1:le=C(P[0].c,S.read_shift(2),!0),G=C(P[0].r,S.read_shift(2),!1),y.push(le+G);break;case 2:{var Se=C(P[0].c,S.read_shift(2),!0),de=C(P[0].r,S.read_shift(2),!1);le=C(P[0].c,S.read_shift(2),!0),G=C(P[0].r,S.read_shift(2),!1),y.push(Se+de+":"+le+G)}break;case 3:if(S.l<S.length){console.error("WK1 premature formula end");return}break;case 4:y.push("("+y.pop()+")");break;case 5:y.push(S.read_shift(2));break;case 6:{for(var Je="";ee=S[S.l++];)Je+=String.fromCharCode(ee);y.push('"'+Je.replace(/"/g,'""')+'"')}break;case 8:y.push("-"+y.pop());break;case 23:y.push("+"+y.pop());break;case 22:y.push("NOT("+y.pop()+")");break;case 20:case 21:fe=y.pop(),ce=y.pop(),y.push(["AND","OR"][ee-20]+"("+ce+","+fe+")");break;default:if(ee<32&&F[ee])fe=y.pop(),ce=y.pop(),y.push(ce+F[ee]+fe);else if(O[ee]){if(A=O[ee][1],A==69&&(A=S[S.l++]),A>y.length){console.error("WK1 bad formula parse 0x"+ee.toString(16)+":|"+y.join("|")+"|");return}var me=y.slice(-A);y.length-=A,y.push(O[ee][0]+"("+me.join(",")+")")}else return ee<=7?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=24?console.error("WK1 unsupported op "+ee.toString(16)):ee<=30?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=115?console.error("WK1 unsupported function opcode "+ee.toString(16)):console.error("WK1 unrecognized opcode "+ee.toString(16))}}y.length==1?P[1].f=""+y[0]:console.error("WK1 bad formula parse |"+y.join("|")+"|")}function J(S){var P=[{c:0,r:0},{t:"n",v:0},0];return P[0].r=S.read_shift(2),P[3]=S[S.l++],P[0].c=S[S.l++],P}function re(S,P){var y=J(S);return y[1].t="s",y[1].v=S.read_shift(P-4,"cstr"),y}function D(S,P,y,A){var G=b(6+A.length);G.write_shift(2,S),G.write_shift(1,y),G.write_shift(1,P),G.write_shift(1,39);for(var le=0;le<A.length;++le){var ce=A.charCodeAt(le);G.write_shift(1,ce>=128?95:ce)}return G.write_shift(1,0),G}function H(S,P){var y=J(S);y[1].v=S.read_shift(2);var A=y[1].v>>1;if(y[1].v&1)switch(A&7){case 0:A=(A>>3)*5e3;break;case 1:A=(A>>3)*500;break;case 2:A=(A>>3)/20;break;case 3:A=(A>>3)/200;break;case 4:A=(A>>3)/2e3;break;case 5:A=(A>>3)/2e4;break;case 6:A=(A>>3)/16;break;case 7:A=(A>>3)/64;break}return y[1].v=A,y}function L(S,P){var y=J(S),A=S.read_shift(4),G=S.read_shift(4),le=S.read_shift(2);if(le==65535)return A===0&&G===3221225472?(y[1].t="e",y[1].v=15):A===0&&G===3489660928?(y[1].t="e",y[1].v=42):y[1].v=0,y;var ce=le&32768;return le=(le&32767)-16446,y[1].v=(1-ce*2)*(G*Math.pow(2,le+32)+A*Math.pow(2,le)),y}function X(S,P,y,A){var G=b(14);if(G.write_shift(2,S),G.write_shift(1,y),G.write_shift(1,P),A==0)return G.write_shift(4,0),G.write_shift(4,0),G.write_shift(2,65535),G;var le=0,ce=0,fe=0,ee=0;return A<0&&(le=1,A=-A),ce=Math.log2(A)|0,A/=Math.pow(2,ce-31),ee=A>>>0,(ee&2147483648)==0&&(A/=2,++ce,ee=A>>>0),A-=ee,ee|=2147483648,ee>>>=0,A*=Math.pow(2,32),fe=A>>>0,G.write_shift(4,fe),G.write_shift(4,ee),ce+=16383+(le?32768:0),G.write_shift(2,ce),G}function j(S,P){var y=L(S);return S.l+=P-14,y}function z(S,P){var y=J(S),A=S.read_shift(4);return y[1].v=A>>6,y}function te(S,P){var y=J(S),A=S.read_shift(8,"f");return y[1].v=A,y}function ae(S,P){var y=te(S);return S.l+=P-10,y}function M(S,P){return S[S.l+P-1]==0?S.read_shift(P,"cstr"):""}function xe(S,P){var y=S[S.l++];y>P-1&&(y=P-1);for(var A="";A.length<y;)A+=String.fromCharCode(S[S.l++]);return A}function oe(S,P,y){if(!(!y.qpro||P<21)){var A=S.read_shift(1);S.l+=17,S.l+=1,S.l+=2;var G=S.read_shift(P-21,"cstr");return[A,G]}}function Fe(S,P){for(var y={},A=S.l+P;S.l<A;){var G=S.read_shift(2);if(G==14e3){for(y[G]=[0,""],y[G][0]=S.read_shift(2);S[S.l];)y[G][1]+=String.fromCharCode(S[S.l]),S.l++;S.l++}}return y}function Oe(S,P){var y=b(5+S.length);y.write_shift(2,14e3),y.write_shift(2,P);for(var A=0;A<S.length;++A){var G=S.charCodeAt(A);y[y.l++]=G>127?95:G}return y[y.l++]=0,y}var ze={0:{n:"BOF",f:si},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:u},14:{n:"NUMBER",f:_},15:{n:"LABEL",f:c},16:{n:"FORMULA",f:g},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:c},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:M},222:{n:"SHEETNAMELP",f:xe},65535:{n:""}},be={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:re},23:{n:"NUMBER17",f:L},24:{n:"NUMBER18",f:H},25:{n:"FORMULA19",f:j},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Fe},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:z},38:{n:"??"},39:{n:"NUMBER27",f:te},40:{n:"FORMULA28",f:ae},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:M},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:oe},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),ko=/^\s|\s$|[\t\n\r]/;function ui(e,t){if(!t.bookSST)return"";var r=[Pe];r[r.length]=Z("sst",null,{xmlns:dt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(ko)&&(i+=' xml:space="preserve"'),i+=">"+Te(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function No(e){return[e.read_shift(4),e.read_shift(4)]}function Po(e,t){return t||(t=b(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Lo=Af;function Mo(e){var t=rr();V(t,159,Po(e));for(var r=0;r<e.length;++r)V(t,19,Lo(e[r]));return V(t,160),t.end()}function Bo(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function xi(e){var t=0,r,n=Bo(e),a=n.length+1,i,s,f,l,o;for(r=Kr(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,l=t<<1&32767,o=f|l,t=o^s;return t^52811}var Uo=function(){function e(a,i){switch(i.type){case"base64":return t(Ir(a),i);case"binary":return t(a,i);case"buffer":return t(ve&&Buffer.isBuffer(a)?a.toString("binary"):Ut(a),i);case"array":return t(Sn(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},l=a.match(/\\trowd.*?\\row\b/g);if(!l.length)throw new Error("RTF missing table");var o={s:{c:0,r:0},e:{c:0,r:l.length-1}};return l.forEach(function(c,d){Array.isArray(f)&&(f[d]=[]);for(var u=/\\\w+\b/g,p=0,_,h=-1;_=u.exec(c);){switch(_[0]){case"\\cell":var g=c.slice(p,u.lastIndex-_[0].length);if(g[0]==" "&&(g=g.slice(1)),++h,g.length){var C={v:g,t:"s"};Array.isArray(f)?f[d][h]=C:f[Ee({r:d,c:h})]=C}break}p=u.lastIndex}h>o.e.c&&(o.e.c=h)}),f["!ref"]=Ne(o),f}function r(a,i){return Zr(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Ae(a["!ref"]),f,l=Array.isArray(a),o=s.s.r;o<=s.e.r;++o){i.push("\\trowd\\trautofit1");for(var c=s.s.c;c<=s.e.c;++c)i.push("\\cellx"+(c+1));for(i.push("\\pard\\intbl"),c=s.s.c;c<=s.e.c;++c){var d=Ee({r:o,c});f=l?(a[o]||[])[c]:a[d],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(kr(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function na(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var bo=6,Rr=bo;function dn(e){return Math.floor((e+Math.round(128/Rr)/256)*Rr)}function pn(e){return Math.floor((e-5)/Rr*100+.5)/100}function Xn(e){return Math.round((e*Rr+5)/Rr*256)/256}function i0(e){e.width?(e.wpx=dn(e.width),e.wch=pn(e.wpx),e.MDW=Rr):e.wpx?(e.wch=pn(e.wpx),e.width=Xn(e.wch),e.MDW=Rr):typeof e.wch=="number"&&(e.width=Xn(e.wch),e.wpx=dn(e.width),e.MDW=Rr),e.customWidth&&delete e.customWidth}var Wo=96,di=Wo;function vn(e){return e*96/di}function pi(e){return e*di/96}function Ho(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=Z("numFmt",null,{numFmtId:n,formatCode:Te(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=Z("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Vo(e){var t=[];return t[t.length]=Z("cellXfs",null),e.forEach(function(r){t[t.length]=Z("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=Z("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function vi(e,t){var r=[Pe,Z("styleSheet",null,{xmlns:dt[0],"xmlns:vt":Me.vt})],n;return e.SSF&&(n=Ho(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=Vo(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function Go(e,t){var r=e.read_shift(2),n=Ye(e);return[r,n]}function Xo(e,t,r){r||(r=b(6+4*t.length)),r.write_shift(2,e),Ue(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function jo(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=If(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var l=e.read_shift(1);switch(l>0&&(n.charset=l),e.l++,n.color=Rf(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=Ye(e),n}function zo(e,t){t||(t=b(25+4*32)),t.write_shift(2,e.sz*20),kf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),un(e.color,t);var n=0;return n=2,t.write_shift(1,n),Ue(e.name,t),t.length>t.l?t.slice(0,t.l):t}var $o=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Bn,Ko=Er;function aa(e,t){t||(t=b(4*3+8*7+16*1)),Bn||(Bn=$n($o));var r=Bn[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(un({auto:1},t),un({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function Yo(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function mi(e,t,r){r||(r=b(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function wt(e,t){return t||(t=b(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var Jo=Er;function Zo(e,t){return t||(t=b(51)),t.write_shift(1,0),wt(null,t),wt(null,t),wt(null,t),wt(null,t),wt(null,t),t.length>t.l?t.slice(0,t.l):t}function qo(e,t){return t||(t=b(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),hn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function Qo(e,t,r){var n=b(2052);return n.write_shift(4,e),hn(t,n),hn(r,n),n.length>n.l?n.slice(0,n.l):n}function el(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(V(e,615,mr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&V(e,44,Xo(a,t[a]))}),V(e,616))}}function rl(e){var t=1;V(e,611,mr(t)),V(e,43,zo({sz:12,color:{theme:1},name:"Calibri",family:2})),V(e,612)}function tl(e){var t=2;V(e,603,mr(t)),V(e,45,aa({patternType:"none"})),V(e,45,aa({patternType:"gray125"})),V(e,604)}function nl(e){var t=1;V(e,613,mr(t)),V(e,46,Zo()),V(e,614)}function al(e){var t=1;V(e,626,mr(t)),V(e,47,mi({numFmtId:0},65535)),V(e,627)}function il(e,t){V(e,617,mr(t.length)),t.forEach(function(r){V(e,47,mi(r,0))}),V(e,618)}function sl(e){var t=1;V(e,619,mr(t)),V(e,48,qo({xfId:0,name:"Normal"})),V(e,620)}function fl(e){var t=0;V(e,505,mr(t)),V(e,506)}function ol(e){var t=0;V(e,508,Qo(t,"TableStyleMedium9","PivotStyleMedium4")),V(e,509)}function ll(e,t){var r=rr();return V(r,278),el(r,e.SSF),rl(r),tl(r),nl(r),al(r),il(r,t.cellXfs),sl(r),fl(r),ol(r),V(r,279),r.end()}function gi(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Pe];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function cl(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ye(e)}}function hl(e){var t=b(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),Ue(e.name,t),t.slice(0,t.l)}function ul(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function xl(e){var t=b(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function dl(e,t){var r=b(8+2*t.length);return r.write_shift(4,e),Ue(t,r),r.slice(0,r.l)}function pl(e){return e.l+=4,e.read_shift(4)!=0}function vl(e,t){var r=b(8);return r.write_shift(4,e),r.write_shift(4,1),r}function ml(){var e=rr();return V(e,332),V(e,334,mr(1)),V(e,335,hl({name:"XLDAPR",version:12e4,flags:3496657072})),V(e,336),V(e,339,dl(1,"XLDAPR")),V(e,52),V(e,35,mr(514)),V(e,4096,mr(0)),V(e,4097,hr(1)),V(e,36),V(e,53),V(e,340),V(e,337,vl(1)),V(e,51,xl([[1,0]])),V(e,338),V(e,333),e.end()}function _i(){var e=[Pe];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function gl(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ee(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var ot=1024;function Ti(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Z("xml",null,{"xmlns:v":sr.v,"xmlns:o":sr.o,"xmlns:x":sr.x,"xmlns:mv":sr.mv}).replace(/\/>/,">"),Z("o:shapelayout",Z("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Z("v:shapetype",[Z("v:stroke",null,{joinstyle:"miter"}),Z("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];ot<e*1e3;)ot+=1e3;return t.forEach(function(i){var s=Be(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var l=f.type=="gradient"?Z("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,o=Z("v:fill",l,f),c={on:"t",obscured:"t"};++ot,a=a.concat(["<v:shape"+Lt({id:"_x0000_s"+ot,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",o,Z("v:shadow",null,c),Z("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Ge("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),Ge("x:AutoFill","False"),Ge("x:Row",String(s.r)),Ge("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function Ei(e){var t=[Pe,Z("comments",null,{xmlns:dt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=Te(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(l){l.a&&(a=r.indexOf(Te(l.a))),i.push(l.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(Ge("t",Te(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(Ge("t",Te(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function _l(e,t,r){var n=[Pe,Z("ThreadedComments",null,{xmlns:Me.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var l={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=l.id:l.parentId=i,s.ID=l.id,s.a&&(l.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(Z("threadedComment",Ge("text",s.t||""),l))})}),n.push("</ThreadedComments>"),n.join("")}function Tl(e){var t=[Pe,Z("personList",null,{xmlns:Me.TCMNT,"xmlns:x":dt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(Z("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function El(e){var t={};t.iauthor=e.read_shift(4);var r=rt(e);return t.rfx=r.s,t.ref=Ee(r.s),e.l+=16,t}function wl(e,t){return t==null&&(t=b(36)),t.write_shift(4,e[1].iauthor),vt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Sl=Ye;function Al(e){return Ue(e.slice(0,54))}function Fl(e){var t=rr(),r=[];return V(t,628),V(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),V(t,632,Al(a.a)))})}),V(t,631),V(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:Be(n[0]),e:Be(n[0])};V(t,635,wl([i,a])),a.t&&a.t.length>0&&V(t,637,yf(a)),V(t,636),delete a.iauthor})}),V(t,634),V(t,629),t.end()}function yl(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&we.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var wi=["xlsb","xlsm","xlam","biff8","xla"],Cl=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,l=!1;i.length==0?l=!0:i.charAt(0)=="["&&(l=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var o=i.length>0?parseInt(i,10)|0:0,c=s.length>0?parseInt(s,10)|0:0;return f?c+=t.c:--c,l?o+=t.r:--o,a+(f?"":"$")+Ke(c)+(l?"":"$")+Xe(o)}return function(a,i){return t=i,a.replace(e,r)}}(),s0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,f0=function(){return function(t,r){return t.replace(s0,function(n,a,i,s,f,l){var o=e0(s)-(i?0:r.c),c=Qn(l)-(f?0:r.r),d=c==0?"":f?c+1:"["+c+"]",u=o==0?"":i?o+1:"["+o+"]";return a+"R"+d+"C"+u})}}();function Ol(e,t){return e.replace(s0,function(r,n,a,i,s,f){return n+(a=="$"?a+i:Ke(e0(i)+t.c))+(s=="$"?s+f:Xe(Qn(f)+t.r))})}function Dl(e){return e.length!=1}function ke(e){e.l+=1}function Wr(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Si(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Ai(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=Wr(e),f=Wr(e);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Ai(e){var t=Wr(e),r=Wr(e),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Rl(e,t,r){if(r.biff<8)return Ai(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=Wr(e),s=Wr(e);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function Fi(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Il(e);var n=e.read_shift(r&&r.biff==12?4:2),a=Wr(e);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Il(e){var t=Wr(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function kl(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Nl(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Pl(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function Pl(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function Ll(e,t,r){var n=(e[e.l++]&96)>>5,a=Si(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function Ml(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Si(e,i,r);return[n,a,s]}function Bl(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Ul(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function bl(e,t,r){var n=(e[e.l++]&96)>>5,a=Rl(e,t-1,r);return[n,a]}function Wl(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function ia(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Hl(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function Vl(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Gl(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Xl(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function jl(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function yi(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function zl(e){return e.read_shift(2),yi(e)}function $l(e){return e.read_shift(2),yi(e)}function Kl(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Fi(e,0,r);return[n,a]}function Yl(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Nl(e,0,r);return[n,a]}function Jl(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=Fi(e,0,r);return[n,a,i]}function Zl(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Zc[a],Di[a],n]}function ql(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:Ql(e);return[a,(i[0]===0?Di:Jc)[i[1]]]}function Ql(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function ec(e,t,r){e.l+=r&&r.biff==2?3:4}function rc(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function tc(e){return e.l++,Ht[e.read_shift(1)]}function nc(e){return e.l++,e.read_shift(2)}function ac(e){return e.l++,e.read_shift(1)!==0}function ic(e){return e.l++,mt(e)}function sc(e,t,r){return e.l++,oi(e,t-1,r)}function fc(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Yf(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Ht[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=mt(e);break;case 2:r[1]=Qf(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function oc(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?rt:to)(e));return a}function lc(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=fc(e,r.biff);return s}function cc(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function hc(e,t,r){if(r.biff==5)return uc(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function uc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function xc(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function dc(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function pc(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function vc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var mc=Er,gc=Er,_c=Er;function Vt(e,t,r){return e.l+=2,[kl(e)]}function o0(e){return e.l+=6,[]}var Tc=Vt,Ec=o0,wc=o0,Sc=Vt;function Ci(e){return e.l+=2,[si(e),e.read_shift(2)&1]}var Ac=Vt,Fc=Ci,yc=o0,Cc=Vt,Oc=Vt,Dc=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Rc(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=Dc[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function Ic(e){return e.l+=2,[e.read_shift(4)]}function kc(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Nc(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Pc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Lc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Mc(e){return e.l+=4,[0,0]}var sa={1:{n:"PtgExp",f:rc},2:{n:"PtgTbl",f:_c},3:{n:"PtgAdd",f:ke},4:{n:"PtgSub",f:ke},5:{n:"PtgMul",f:ke},6:{n:"PtgDiv",f:ke},7:{n:"PtgPower",f:ke},8:{n:"PtgConcat",f:ke},9:{n:"PtgLt",f:ke},10:{n:"PtgLe",f:ke},11:{n:"PtgEq",f:ke},12:{n:"PtgGe",f:ke},13:{n:"PtgGt",f:ke},14:{n:"PtgNe",f:ke},15:{n:"PtgIsect",f:ke},16:{n:"PtgUnion",f:ke},17:{n:"PtgRange",f:ke},18:{n:"PtgUplus",f:ke},19:{n:"PtgUminus",f:ke},20:{n:"PtgPercent",f:ke},21:{n:"PtgParen",f:ke},22:{n:"PtgMissArg",f:ke},23:{n:"PtgStr",f:sc},26:{n:"PtgSheet",f:kc},27:{n:"PtgEndSheet",f:Nc},28:{n:"PtgErr",f:tc},29:{n:"PtgBool",f:ac},30:{n:"PtgInt",f:nc},31:{n:"PtgNum",f:ic},32:{n:"PtgArray",f:Wl},33:{n:"PtgFunc",f:Zl},34:{n:"PtgFuncVar",f:ql},35:{n:"PtgName",f:cc},36:{n:"PtgRef",f:Kl},37:{n:"PtgArea",f:Ll},38:{n:"PtgMemArea",f:xc},39:{n:"PtgMemErr",f:mc},40:{n:"PtgMemNoMem",f:gc},41:{n:"PtgMemFunc",f:dc},42:{n:"PtgRefErr",f:pc},43:{n:"PtgAreaErr",f:Bl},44:{n:"PtgRefN",f:Yl},45:{n:"PtgAreaN",f:bl},46:{n:"PtgMemAreaN",f:Pc},47:{n:"PtgMemNoMemN",f:Lc},57:{n:"PtgNameX",f:hc},58:{n:"PtgRef3d",f:Jl},59:{n:"PtgArea3d",f:Ml},60:{n:"PtgRefErr3d",f:vc},61:{n:"PtgAreaErr3d",f:Ul},255:{}},Bc={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Uc={1:{n:"PtgElfLel",f:Ci},2:{n:"PtgElfRw",f:Cc},3:{n:"PtgElfCol",f:Tc},6:{n:"PtgElfRwV",f:Oc},7:{n:"PtgElfColV",f:Sc},10:{n:"PtgElfRadical",f:Ac},11:{n:"PtgElfRadicalS",f:yc},13:{n:"PtgElfColS",f:Ec},15:{n:"PtgElfColSV",f:wc},16:{n:"PtgElfRadicalLel",f:Fc},25:{n:"PtgList",f:Rc},29:{n:"PtgSxName",f:Ic},255:{}},bc={0:{n:"PtgAttrNoop",f:Mc},1:{n:"PtgAttrSemi",f:jl},2:{n:"PtgAttrIf",f:Gl},4:{n:"PtgAttrChoose",f:Hl},8:{n:"PtgAttrGoto",f:Vl},16:{n:"PtgAttrSum",f:ec},32:{n:"PtgAttrBaxcel",f:ia},33:{n:"PtgAttrBaxcel",f:ia},64:{n:"PtgAttrSpace",f:zl},65:{n:"PtgAttrSpaceSemi",f:$l},128:{n:"PtgAttrIfError",f:Xl},255:{}};function Wc(e,t,r,n){if(n.biff<8)return Er(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=lc(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=oc(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(Er(e,t)),i}function Hc(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=sa[i]||sa[Bc[i]],(i===24||i===25)&&(a=(i===24?Uc:bc)[e[e.l+1]]),!a||!a.f?Er(e,t):s.push([a.n,a.f(e,t,r)]);return s}function Vc(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var Gc={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Xc(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Oi(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function fa(e,t,r){var n=Oi(e,t,r);return n=="#REF"?n:Xc(n,r)}function xt(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0}},f=[],l,o,c,d=0,u=0,p,_="";if(!e[0]||!e[0][0])return"";for(var h=-1,g="",C=0,O=e[0].length;C<O;++C){var F=e[0][C];switch(F[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(l=f.pop(),o=f.pop(),h>=0){switch(e[0][h][1][0]){case 0:g=Re(" ",e[0][h][1][1]);break;case 1:g=Re("\r",e[0][h][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}o=o+g,h=-1}f.push(o+Gc[F[0]]+l);break;case"PtgIsect":l=f.pop(),o=f.pop(),f.push(o+" "+l);break;case"PtgUnion":l=f.pop(),o=f.pop(),f.push(o+","+l);break;case"PtgRange":l=f.pop(),o=f.pop(),f.push(o+":"+l);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=Ot(F[1][1],s,a),f.push(Dt(c,i));break;case"PtgRefN":c=r?Ot(F[1][1],r,a):F[1][1],f.push(Dt(c,i));break;case"PtgRef3d":d=F[1][1],c=Ot(F[1][2],s,a),_=fa(n,d,a),f.push(_+"!"+Dt(c,i));break;case"PtgFunc":case"PtgFuncVar":var U=F[1][0],J=F[1][1];U||(U=0),U&=127;var re=U==0?[]:f.slice(-U);f.length-=U,J==="User"&&(J=re.shift()),f.push(J+"("+re.join(",")+")");break;case"PtgBool":f.push(F[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(F[1]);break;case"PtgNum":f.push(String(F[1]));break;case"PtgStr":f.push('"'+F[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(F[1]);break;case"PtgAreaN":p=j0(F[1][1],r?{s:r}:s,a),f.push(Ln(p,a));break;case"PtgArea":p=j0(F[1][1],s,a),f.push(Ln(p,a));break;case"PtgArea3d":d=F[1][1],p=F[1][2],_=fa(n,d,a),f.push(_+"!"+Ln(p,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":u=F[1][2];var D=(n.names||[])[u-1]||(n[0]||[])[u],H=D?D.Name:"SH33TJSNAME"+String(u);H&&H.slice(0,6)=="_xlfn."&&!a.xlfn&&(H=H.slice(6)),f.push(H);break;case"PtgNameX":var L=F[1][1];u=F[1][2];var X;if(a.biff<=5)L<0&&(L=-L),n[L]&&(X=n[L][u]);else{var j="";if(((n[L]||[])[0]||[])[0]==14849||(((n[L]||[])[0]||[])[0]==1025?n[L][u]&&n[L][u].itab>0&&(j=n.SheetNames[n[L][u].itab-1]+"!"):j=n.SheetNames[u-1]+"!"),n[L]&&n[L][u])j+=n[L][u].Name;else if(n[0]&&n[0][u])j+=n[0][u].Name;else{var z=(Oi(n,L,a)||"").split(";;");z[u-1]?j=z[u-1]:j+="SH33TJSERRX"}f.push(j);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var te="(",ae=")";if(h>=0){switch(g="",e[0][h][1][0]){case 2:te=Re(" ",e[0][h][1][1])+te;break;case 3:te=Re("\r",e[0][h][1][1])+te;break;case 4:ae=Re(" ",e[0][h][1][1])+ae;break;case 5:ae=Re("\r",e[0][h][1][1])+ae;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}h=-1}f.push(te+f.pop()+ae);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":c={c:F[1][1],r:F[1][0]};var M={c:r.c,r:r.r};if(n.sharedf[Ee(c)]){var xe=n.sharedf[Ee(c)];f.push(xt(xe,s,M,n,a))}else{var oe=!1;for(l=0;l!=n.arrayf.length;++l)if(o=n.arrayf[l],!(c.c<o[0].s.c||c.c>o[0].e.c)&&!(c.r<o[0].s.r||c.r>o[0].e.r)){f.push(xt(o[1],s,M,n,a)),oe=!0;break}oe||f.push(F[1])}break;case"PtgArray":f.push("{"+Vc(F[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":h=C;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+F[1].idx+"[#"+F[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(F));default:throw new Error("Unrecognized Formula Token: "+String(F))}var Fe=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&h>=0&&Fe.indexOf(e[0][C][0])==-1){F=e[0][h];var Oe=!0;switch(F[1][0]){case 4:Oe=!1;case 0:g=Re(" ",F[1][1]);break;case 5:Oe=!1;case 1:g=Re("\r",F[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+F[1][0])}f.push((Oe?g:"")+f.pop()+(Oe?"":g)),h=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function jc(e){if(e==null){var t=b(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return Yr(e);return Yr(0)}function zc(e,t,r,n,a){var i=Jr(t,r,a),s=jc(e.v),f=b(6),l=33;f.write_shift(2,l),f.write_shift(4,0);for(var o=b(e.bf.length),c=0;c<e.bf.length;++c)o[c]=e.bf[c];var d=Ve([i,s,f,o]);return d}function An(e,t,r){var n=e.read_shift(4),a=Hc(e,n,r),i=e.read_shift(4),s=i>0?Wc(e,i,a,r):null;return[a,s]}var $c=An,Fn=An,Kc=An,Yc=An,Jc={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Di={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Zc={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function qc(e){var t="of:="+e.replace(s0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function Qc(e){return e.replace(/\./,"!")}var Rt=typeof Map<"u";function l0(e,t,r){var n=0,a=e.length;if(r){if(Rt?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=Rt?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(Rt?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function yn(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Rr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=pn(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=Xn(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Ri(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Vr(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){Fa(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function e1(e,t,r){if(e&&e["!ref"]){var n=Ae(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function r1(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Ne(e[r])+'"/>';return t+"</mergeCells>"}function t1(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch{}i=!0,s.codeName=Pt(Te(l))}if(e&&e["!outline"]){var o={summaryBelow:1,summaryRight:1};e["!outline"].above&&(o.summaryBelow=0),e["!outline"].left&&(o.summaryRight=0),f=(f||"")+Z("outlinePr",null,o)}!i&&!f||(a[a.length]=Z("sheetPr",f,s))}var n1=["objects","scenarios","selectLockedCells","selectUnlockedCells"],a1=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function i1(e){var t={sheet:1};return n1.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),a1.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=xi(e.password).toString(16).toUpperCase()),Z("sheetProtection",null,t)}function s1(e){return Ri(e),Z("pageMargins",null,e)}function f1(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)(n=t[a])&&(r[r.length]=Z("col",null,yn(a,n)));return r[r.length]="</cols>",r.join("")}function o1(e,t,r,n){var a=typeof e.ref=="string"?e.ref:Ne(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=or(a);s.s.r==s.e.r&&(s.e.r=or(t["!ref"]).e.r,a=Ne(s));for(var f=0;f<i.length;++f){var l=i[f];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Z("autoFilter",null,{ref:a})}function l1(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Z("sheetViews",Z("sheetView",null,a),{})}function c1(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Ht[e.v];break;case"d":n&&n.cellDates?a=Qe(e.v,-1).toISOString():(e=nr(e),e.t="n",a=""+(e.v=tr(Qe(e.v)))),typeof e.z>"u"&&(e.z=Ie[14]);break;default:a=e.v;break}var f=Ge("v",Te(a)),l={r:t},o=Vr(n.cellXfs,e,n);switch(o!==0&&(l.s=o),e.t){case"n":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=Ge("v",""+l0(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=Z("f",Te(e.f),c)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),Z("c",f,l)}function h1(e,t,r,n){var a=[],i=[],s=Ae(e["!ref"]),f="",l,o="",c=[],d=0,u=0,p=e["!rows"],_=Array.isArray(e),h={r:o},g,C=-1;for(u=s.s.c;u<=s.e.c;++u)c[u]=Ke(u);for(d=s.s.r;d<=s.e.r;++d){for(i=[],o=Xe(d),u=s.s.c;u<=s.e.c;++u){l=c[u]+o;var O=_?(e[d]||[])[u]:e[l];O!==void 0&&(f=c1(O,l,e,t))!=null&&i.push(f)}(i.length>0||p&&p[d])&&(h={r:o},p&&p[d]&&(g=p[d],g.hidden&&(h.hidden=1),C=-1,g.hpx?C=vn(g.hpx):g.hpt&&(C=g.hpt),C>-1&&(h.ht=C,h.customHeight=1),g.level&&(h.outlineLevel=g.level)),a[a.length]=Z("row",i.join(""),h))}if(p)for(;d<p.length;++d)p&&p[d]&&(h={r:d+1},g=p[d],g.hidden&&(h.hidden=1),C=-1,g.hpx?C=vn(g.hpx):g.hpt&&(C=g.hpt),C>-1&&(h.ht=C,h.customHeight=1),g.level&&(h.outlineLevel=g.level),a[a.length]=Z("row","",h));return a.join("")}function Ii(e,t,r,n){var a=[Pe,Z("worksheet",null,{xmlns:dt[0],"xmlns:r":Me.r})],i=r.SheetNames[e],s=0,f="",l=r.Sheets[i];l==null&&(l={});var o=l["!ref"]||"A1",c=Ae(o);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+o+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),o=Ne(c)}n||(n={}),l["!comments"]=[];var d=[];t1(l,r,e,t,a),a[a.length]=Z("dimension",null,{ref:o}),a[a.length]=l1(l,t,e,r),t.sheetFormat&&(a[a.length]=Z("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),l["!cols"]!=null&&l["!cols"].length>0&&(a[a.length]=f1(l,l["!cols"])),a[s=a.length]="<sheetData/>",l["!links"]=[],l["!ref"]!=null&&(f=h1(l,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),l["!protect"]&&(a[a.length]=i1(l["!protect"])),l["!autofilter"]!=null&&(a[a.length]=o1(l["!autofilter"],l,r,e)),l["!merges"]!=null&&l["!merges"].length>0&&(a[a.length]=r1(l["!merges"]));var u=-1,p,_=-1;return l["!links"].length>0&&(a[a.length]="<hyperlinks>",l["!links"].forEach(function(h){h[1].Target&&(p={ref:h[0]},h[1].Target.charAt(0)!="#"&&(_=_e(n,-1,Te(h[1].Target).replace(/#.*$/,""),pe.HLINK),p["r:id"]="rId"+_),(u=h[1].Target.indexOf("#"))>-1&&(p.location=Te(h[1].Target.slice(u+1))),h[1].Tooltip&&(p.tooltip=Te(h[1].Tooltip)),a[a.length]=Z("hyperlink",null,p))}),a[a.length]="</hyperlinks>"),delete l["!links"],l["!margins"]!=null&&(a[a.length]=s1(l["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=Ge("ignoredErrors",Z("ignoredError",null,{numberStoredAsText:1,sqref:o}))),d.length>0&&(_=_e(n,-1,"../drawings/drawing"+(e+1)+".xml",pe.DRAW),a[a.length]=Z("drawing",null,{"r:id":"rId"+_}),l["!drawing"]=d),l["!comments"].length>0&&(_=_e(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",pe.VML),a[a.length]=Z("legacyDrawing",null,{"r:id":"rId"+_}),l["!legacy"]=_),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function u1(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function x1(e,t,r){var n=b(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=vn(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,l=n.l;n.l+=4;for(var o={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var d=-1,u=-1,p=c<<10;p<c+1<<10;++p){o.c=p;var _=Array.isArray(r)?(r[o.r]||[])[o.c]:r[Ee(o)];_&&(d<0&&(d=p),u=p)}d<0||(++f,n.write_shift(4,d),n.write_shift(4,u))}var h=n.l;return n.l=l,n.write_shift(4,f),n.l=h,n.length>n.l?n.slice(0,n.l):n}function d1(e,t,r,n){var a=x1(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&V(e,0,a)}var p1=rt,v1=vt;function m1(){}function g1(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Cf(e),r}function _1(e,t,r){r==null&&(r=b(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return un({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),za(e,r),r.slice(0,r.l)}function T1(e){var t=ur(e);return[t]}function E1(e,t,r){return r==null&&(r=b(8)),qr(t,r)}function w1(e){var t=Qr(e);return[t]}function S1(e,t,r){return r==null&&(r=b(4)),et(t,r)}function A1(e){var t=ur(e),r=e.read_shift(1);return[t,r,"b"]}function F1(e,t,r){return r==null&&(r=b(9)),qr(t,r),r.write_shift(1,e.v?1:0),r}function y1(e){var t=Qr(e),r=e.read_shift(1);return[t,r,"b"]}function C1(e,t,r){return r==null&&(r=b(5)),et(t,r),r.write_shift(1,e.v?1:0),r}function O1(e){var t=ur(e),r=e.read_shift(1);return[t,r,"e"]}function D1(e,t,r){return r==null&&(r=b(9)),qr(t,r),r.write_shift(1,e.v),r}function R1(e){var t=Qr(e),r=e.read_shift(1);return[t,r,"e"]}function I1(e,t,r){return r==null&&(r=b(8)),et(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function k1(e){var t=ur(e),r=e.read_shift(4);return[t,r,"s"]}function N1(e,t,r){return r==null&&(r=b(12)),qr(t,r),r.write_shift(4,t.v),r}function P1(e){var t=Qr(e),r=e.read_shift(4);return[t,r,"s"]}function L1(e,t,r){return r==null&&(r=b(8)),et(t,r),r.write_shift(4,t.v),r}function M1(e){var t=ur(e),r=mt(e);return[t,r,"n"]}function B1(e,t,r){return r==null&&(r=b(16)),qr(t,r),Yr(e.v,r),r}function U1(e){var t=Qr(e),r=mt(e);return[t,r,"n"]}function b1(e,t,r){return r==null&&(r=b(12)),et(t,r),Yr(e.v,r),r}function W1(e){var t=ur(e),r=$a(e);return[t,r,"n"]}function H1(e,t,r){return r==null&&(r=b(12)),qr(t,r),Ka(e.v,r),r}function V1(e){var t=Qr(e),r=$a(e);return[t,r,"n"]}function G1(e,t,r){return r==null&&(r=b(8)),et(t,r),Ka(e.v,r),r}function X1(e){var t=ur(e),r=r0(e);return[t,r,"is"]}function j1(e){var t=ur(e),r=Ye(e);return[t,r,"str"]}function z1(e,t,r){return r==null&&(r=b(12+4*e.v.length)),qr(t,r),Ue(e.v,r),r.length>r.l?r.slice(0,r.l):r}function $1(e){var t=Qr(e),r=Ye(e);return[t,r,"str"]}function K1(e,t,r){return r==null&&(r=b(8+4*e.v.length)),et(t,r),Ue(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Y1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=Fn(e,n-e.l,r);s[3]=xt(f,null,a,r.supbooks,r)}else e.l=n;return s}function J1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=Fn(e,n-e.l,r);s[3]=xt(f,null,a,r.supbooks,r)}else e.l=n;return s}function Z1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=mt(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=Fn(e,n-e.l,r);s[3]=xt(f,null,a,r.supbooks,r)}else e.l=n;return s}function q1(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=Ye(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=Fn(e,n-e.l,r);s[3]=xt(f,null,a,r.supbooks,r)}else e.l=n;return s}var Q1=rt,eh=vt;function rh(e,t){return t==null&&(t=b(4)),t.write_shift(4,e),t}function th(e,t){var r=e.l+t,n=rt(e),a=t0(e),i=Ye(e),s=Ye(e),f=Ye(e);e.l=r;var l={rfx:n,relId:a,loc:i,display:f};return s&&(l.Tooltip=s),l}function nh(e,t){var r=b(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));vt({s:Be(e[0]),e:Be(e[0])},r),n0("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return Ue(a||"",r),Ue(e[1].Tooltip||"",r),Ue("",r),r.slice(0,r.l)}function ah(){}function ih(e,t,r){var n=e.l+t,a=Ya(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=$c(e,n-e.l,r);s[1]=f}else e.l=n;return s}function sh(e,t,r){var n=e.l+t,a=rt(e),i=[a];if(r.cellFormula){var s=Yc(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function fh(e,t,r){r==null&&(r=b(18));var n=yn(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var ki=["left","right","top","bottom","header","footer"];function oh(e){var t={};return ki.forEach(function(r){t[r]=mt(e)}),t}function lh(e,t){return t==null&&(t=b(6*8)),Ri(e),ki.forEach(function(r){Yr(e[r],t)}),t}function ch(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function hh(e,t,r){r==null&&(r=b(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function uh(e){var t=b(24);return t.write_shift(4,4),t.write_shift(4,1),vt(e,t),t}function xh(e,t){return t==null&&(t=b(16*4+2)),t.write_shift(2,e.password?xi(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function dh(){}function ph(){}function vh(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=nr(t),t.z=t.z||Ie[14],t.v=tr(Qe(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var l={r,c:n};switch(l.s=Vr(a.cellXfs,t,a),t.l&&i["!links"].push([Ee(l),t.l]),t.c&&i["!comments"].push([Ee(l),t.c]),t.t){case"s":case"str":return a.bookSST?(f=l0(a.Strings,t.v,a.revStrings),l.t="s",l.v=f,s?V(e,18,L1(t,l)):V(e,7,N1(t,l))):(l.t="str",s?V(e,17,K1(t,l)):V(e,6,z1(t,l))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?V(e,13,G1(t,l)):V(e,2,H1(t,l)):s?V(e,16,b1(t,l)):V(e,5,B1(t,l)),!0;case"b":return l.t="b",s?V(e,15,C1(t,l)):V(e,4,F1(t,l)),!0;case"e":return l.t="e",s?V(e,14,I1(t,l)):V(e,3,D1(t,l)),!0}return s?V(e,12,S1(t,l)):V(e,1,E1(t,l)),!0}function mh(e,t,r,n){var a=Ae(t["!ref"]||"A1"),i,s="",f=[];V(e,145);var l=Array.isArray(t),o=a.e.r;t["!rows"]&&(o=Math.max(a.e.r,t["!rows"].length-1));for(var c=a.s.r;c<=o;++c){s=Xe(c),d1(e,t,a,c);var d=!1;if(c<=a.e.r)for(var u=a.s.c;u<=a.e.c;++u){c===a.s.r&&(f[u]=Ke(u)),i=f[u]+s;var p=l?(t[c]||[])[u]:t[i];if(!p){d=!1;continue}d=vh(e,p,c,u,n,t,d)}}V(e,146)}function gh(e,t){!t||!t["!merges"]||(V(e,177,rh(t["!merges"].length)),t["!merges"].forEach(function(r){V(e,176,eh(r))}),V(e,178))}function _h(e,t){!t||!t["!cols"]||(V(e,390),t["!cols"].forEach(function(r,n){r&&V(e,60,fh(n,r))}),V(e,391))}function Th(e,t){!t||!t["!ref"]||(V(e,648),V(e,649,uh(Ae(t["!ref"]))),V(e,650))}function Eh(e,t,r){t["!links"].forEach(function(n){if(n[1].Target){var a=_e(r,-1,n[1].Target.replace(/#.*$/,""),pe.HLINK);V(e,494,nh(n,a))}}),delete t["!links"]}function wh(e,t,r,n){if(t["!comments"].length>0){var a=_e(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",pe.VML);V(e,551,n0("rId"+a)),t["!legacy"]=a}}function Sh(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:Ne(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=or(i);f.s.r==f.e.r&&(f.e.r=or(t["!ref"]).e.r,i=Ne(f));for(var l=0;l<s.length;++l){var o=s[l];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+i;break}}l==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),V(e,161,vt(Ae(i))),V(e,162)}}function Ah(e,t,r){V(e,133),V(e,137,hh(t,r)),V(e,138),V(e,134)}function Fh(e,t){t["!protect"]&&V(e,535,xh(t["!protect"]))}function yh(e,t,r,n){var a=rr(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var l=Ae(s["!ref"]||"A1");if(l.e.c>16383||l.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");l.e.c=Math.min(l.e.c,16383),l.e.r=Math.min(l.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],V(a,129),(r.vbaraw||s["!outline"])&&V(a,147,_1(f,s["!outline"])),V(a,148,v1(l)),Ah(a,s,r.Workbook),_h(a,s),mh(a,s,e,t),Fh(a,s),Sh(a,s,r,e),gh(a,s),Eh(a,s,n),s["!margins"]&&V(a,476,lh(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&Th(a,s),wh(a,s,e,n),V(a,130),a.end()}function Ch(e,t){e.l+=10;var r=Ye(e);return{name:r}}var Oh=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function Dh(e){return!e.Workbook||!e.Workbook.WBProps?"false":af(e.Workbook.WBProps.date1904)?"true":"false"}var Rh="][*?/\\".split("");function Ni(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return Rh.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function Ih(e,t,r){e.forEach(function(n,a){Ni(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function kh(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];Ih(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)e1(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function Pi(e){var t=[Pe];t[t.length]=Z("workbook",null,{xmlns:dt[0],"xmlns:r":Me.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Oh.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Z("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Te(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Z("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var l={name:f.Name};f.Comment&&(l.comment=f.Comment),f.Sheet!=null&&(l.localSheetId=""+f.Sheet),f.Hidden&&(l.hidden="1"),f.Ref&&(t[t.length]=Z("definedName",Te(f.Ref),l))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Nh(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Gn(e),r.name=Ye(e),r}function Ph(e,t){return t||(t=b(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),n0(e.strRelID,t),Ue(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function Lh(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?Ye(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function Mh(e,t){t||(t=b(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),za(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function Bh(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Of(e),s=Kc(e,0,r),f=t0(e);e.l=n;var l={Name:i,Ptg:s};return a<268435455&&(l.Sheet=a),f&&(l.Comment=f),l}function Uh(e,t){V(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};V(e,156,Ph(a))}V(e,144)}function bh(e,t){t||(t=b(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return Ue("SheetJS",t),Ue(an.version,t),Ue(an.version,t),Ue("7262",t),t.length>t.l?t.slice(0,t.l):t}function Wh(e,t){t||(t=b(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Hh(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(V(e,135),V(e,158,Wh(a)),V(e,136))}}function Vh(e,t){var r=rr();return V(r,131),V(r,128,bh()),V(r,153,Mh(e.Workbook&&e.Workbook.WBProps||null)),Hh(r,e),Uh(r,e),V(r,132),r.end()}function Gh(e,t,r){return(t.slice(-4)===".bin"?Vh:Pi)(e)}function Xh(e,t,r,n,a){return(t.slice(-4)===".bin"?yh:Ii)(e,r,n,a)}function jh(e,t,r){return(t.slice(-4)===".bin"?ll:vi)(e,r)}function zh(e,t,r){return(t.slice(-4)===".bin"?Mo:ui)(e,r)}function $h(e,t,r){return(t.slice(-4)===".bin"?Fl:Ei)(e)}function Kh(e){return(e.slice(-4)===".bin"?ml:_i)()}function Yh(e,t){var r=[];return e.Props&&r.push(Xf(e.Props,t)),e.Custprops&&r.push(jf(e.Props,e.Custprops)),r.join("")}function Jh(){return""}function Zh(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(Z("NumberFormat",null,{"ss:Format":Te(Ie[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(Z("Style",i.join(""),s))}),Z("Styles",r.join(""))}function Li(e){return Z("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+f0(e.Ref,{r:0,c:0})})}function qh(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(Li(a)))}return Z("Names",r.join(""))}function Qh(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(Li(f)))}return i.join("")}function eu(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Z("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Z("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Z("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Z("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Ge("ProtectContents","True")),e["!protect"].objects&&a.push(Ge("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Ge("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(Ge("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(Ge("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":Z("WorksheetOptions",a.join(""),{xmlns:sr.x})}function ru(e){return e.map(function(t){var r=nf(t.t||""),n=Z("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return Z("Comment",n,{"ss:Author":t.a})}).join("")}function tu(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Te(f0(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var l=Be(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(l.r==s.r?"":"["+(l.r-s.r)+"]")+"C"+(l.c==s.c?"":"["+(l.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Te(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Te(e.l.Tooltip))),r["!merges"])for(var o=r["!merges"],c=0;c!=o.length;++c)o[c].s.c!=s.c||o[c].s.r!=s.r||(o[c].e.c>o[c].s.c&&(f["ss:MergeAcross"]=o[c].e.c-o[c].s.c),o[c].e.r>o[c].s.r&&(f["ss:MergeDown"]=o[c].e.r-o[c].s.r));var d="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":d="Number",u=String(e.v);break;case"b":d="Boolean",u=e.v?"1":"0";break;case"e":d="Error",u=Ht[e.v];break;case"d":d="DateTime",u=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Ie[14]);break;case"s":d="String",u=tf(e.v||"");break}var p=Vr(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+p),f["ss:Index"]=s.c+1;var _=e.v!=null?u:"",h=e.t=="z"?"":'<Data ss:Type="'+d+'">'+_+"</Data>";return(e.c||[]).length>0&&(h+=ru(e.c)),Z("Cell",h,f)}function nu(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=pi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function au(e,t,r,n){if(!e["!ref"])return"";var a=Ae(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(g,C){i0(g);var O=!!g.width,F=yn(C,g),U={"ss:Index":C+1};O&&(U["ss:Width"]=dn(F.width)),g.hidden&&(U["ss:Hidden"]="1"),f.push(Z("Column",null,U))});for(var l=Array.isArray(e),o=a.s.r;o<=a.e.r;++o){for(var c=[nu(o,(e["!rows"]||[])[o])],d=a.s.c;d<=a.e.c;++d){var u=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>d)&&!(i[s].s.r>o)&&!(i[s].e.c<d)&&!(i[s].e.r<o)){(i[s].s.c!=d||i[s].s.r!=o)&&(u=!0);break}if(!u){var p={r:o,c:d},_=Ee(p),h=l?(e[o]||[])[d]:e[_];c.push(tu(h,_,e,t,r,n,p))}}c.push("</Row>"),c.length>2&&f.push(c.join(""))}return f.join("")}function iu(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?Qh(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?au(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(eu(i,t,e,r)),n.join("")}function su(e,t){t||(t={}),e.SSF||(e.SSF=nr(Ie)),e.SSF&&(En(),Tn(e.SSF),t.revssf=wn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Vr(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Yh(e,t)),r.push(Jh()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Z("Worksheet",iu(n,t,e),{"ss:Name":Te(e.SheetNames[n])}));return r[2]=Zh(e,t),r[3]=qh(e),Pe+Z("Workbook",r.join(""),{xmlns:sr.ss,"xmlns:o":sr.o,"xmlns:x":sr.x,"xmlns:ss":sr.ss,"xmlns:dt":sr.dt,"xmlns:html":sr.html})}var Un={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function fu(e,t){var r=[],n=[],a=[],i=0,s,f=P0($0,"n"),l=P0(K0,"n");if(e.Props)for(s=je(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(l,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=je(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(l,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var o=[];for(i=0;i<a.length;++i)ii.indexOf(a[i][0])>-1||ti.indexOf(a[i][0])>-1||a[i][1]!=null&&o.push(a[i]);n.length&&we.utils.cfb_add(t,"/SummaryInformation",Q0(n,Un.SI,l,K0)),(r.length||o.length)&&we.utils.cfb_add(t,"/DocumentSummaryInformation",Q0(r,Un.DSI,f,$0,o.length?o:null,Un.UDI))}function ou(e,t){var r=t||{},n=we.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return we.utils.cfb_add(n,a,Mi(e,r)),r.biff==8&&(e.Props||e.Custprops)&&fu(e,n),r.biff==8&&e.vbaraw&&yl(n,we.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var lu={0:{f:u1},1:{f:T1},2:{f:W1},3:{f:O1},4:{f:A1},5:{f:M1},6:{f:j1},7:{f:k1},8:{f:q1},9:{f:Z1},10:{f:Y1},11:{f:J1},12:{f:w1},13:{f:V1},14:{f:R1},15:{f:y1},16:{f:U1},17:{f:$1},18:{f:P1},19:{f:r0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Bh},40:{},42:{},43:{f:jo},44:{f:Go},45:{f:Ko},46:{f:Jo},47:{f:Yo},48:{},49:{f:Ef},50:{},51:{f:ul},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:So},62:{f:X1},63:{f:gl},64:{f:dh},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Er,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:ch},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:g1},148:{f:p1,p:16},151:{f:ah},152:{},153:{f:Lh},154:{},155:{},156:{f:Nh},157:{},158:{},159:{T:1,f:No},160:{T:-1},161:{T:1,f:rt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Q1},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:cl},336:{T:-1},337:{f:pl,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Gn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:mo},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:ih},427:{f:sh},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:oh},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:m1},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:th},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Gn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Sl},633:{T:1},634:{T:-1},635:{T:1,f:El},636:{T:-1},637:{f:Ff},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Ch},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:ph},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function q(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&qn(r)&&e.push(r)}}function cu(e,t,r,n){var a=(r||[]).length||0;if(a<=8224)return q(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,l=0,o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,o),e.push(r.slice(l,l+o)),l+=o;l<a;){for(c=e.next(4),c.write_shift(2,60),o=0;o+(s[f]||8224)<=8224;)o+=s[f]||8224,f++;c.write_shift(2,o),e.push(r.slice(l,l+o)),l+=o}}}function Gt(e,t,r){return e||(e=b(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function hu(e,t,r,n){var a=b(9);return Gt(a,e,t),fi(r,n||"b",a),a}function uu(e,t,r){var n=b(8+2*r.length);return Gt(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function xu(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?tr(Qe(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?q(e,2,Co(r,n,a)):q(e,3,yo(r,n,a));return;case"b":case"e":q(e,5,hu(r,n,t.v,t.t));return;case"s":case"str":q(e,4,uu(r,n,(t.v||"").slice(0,255)));return}q(e,1,Gt(null,r,n))}function du(e,t,r,n){var a=Array.isArray(t),i=Ae(t["!ref"]||"A1"),s,f="",l=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=Ne(i)}for(var o=i.s.r;o<=i.e.r;++o){f=Xe(o);for(var c=i.s.c;c<=i.e.c;++c){o===i.s.r&&(l[c]=Ke(c)),s=l[c]+f;var d=a?(t[o]||[])[c]:t[s];d&&xu(e,d,o,c)}}}function pu(e,t){for(var r=t||{},n=rr(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return q(n,r.biff==4?1033:r.biff==3?521:9,a0(e,16,r)),du(n,e.Sheets[e.SheetNames[a]],a,r),q(n,10),n.end()}function vu(e,t,r){q(e,49,oo({sz:12,name:"Arial"},r))}function mu(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&q(e,1054,ho(a,t[a],r))})}function gu(e,t){var r=b(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),q(e,2151,r),r=b(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),ci(Ae(t["!ref"]||"A1"),r),r.write_shift(4,4),q(e,2152,r)}function _u(e,t){for(var r=0;r<16;++r)q(e,224,ra({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){q(e,224,ra(n,0,t))})}function Tu(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];q(e,440,To(n)),n[1].Tooltip&&q(e,2048,Eo(n))}delete t["!links"]}function Eu(e,t){if(t){var r=0;t.forEach(function(n,a){++r<=256&&n&&q(e,125,Ao(yn(a,n),a))})}}function wu(e,t,r,n,a){var i=16+Vr(a.cellXfs,t,a);if(t.v==null&&!t.bf){q(e,513,Jr(r,n,i));return}if(t.bf)q(e,6,zc(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?tr(Qe(t.v)):t.v;q(e,515,vo(r,n,s,i));break;case"b":case"e":q(e,517,po(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=l0(a.Strings,t.v,a.revStrings);q(e,253,lo(r,n,f,i))}else q(e,516,co(r,n,(t.v||"").slice(0,255),i,a));break;default:q(e,513,Jr(r,n,i))}}function Su(e,t,r){var n=rr(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},l=Array.isArray(i),o=t.biff==8,c,d="",u=[],p=Ae(i["!ref"]||"A1"),_=o?65536:16384;if(p.e.c>255||p.e.r>=_){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");p.e.c=Math.min(p.e.c,255),p.e.r=Math.min(p.e.c,_-1)}q(n,2057,a0(r,16,t)),q(n,13,hr(1)),q(n,12,hr(100)),q(n,15,qe(!0)),q(n,17,qe(!1)),q(n,16,Yr(.001)),q(n,95,qe(!0)),q(n,42,qe(!1)),q(n,43,qe(!1)),q(n,130,hr(1)),q(n,128,xo()),q(n,131,qe(!1)),q(n,132,qe(!1)),o&&Eu(n,i["!cols"]),q(n,512,uo(p,t)),o&&(i["!links"]=[]);for(var h=p.s.r;h<=p.e.r;++h){d=Xe(h);for(var g=p.s.c;g<=p.e.c;++g){h===p.s.r&&(u[g]=Ke(g)),c=u[g]+d;var C=l?(i[h]||[])[g]:i[c];C&&(wu(n,C,h,g,t),o&&C.l&&i["!links"].push([c,C.l]))}}var O=f.CodeName||f.name||a;return o&&q(n,574,fo((s.Views||[])[0])),o&&(i["!merges"]||[]).length&&q(n,229,_o(i["!merges"])),o&&Tu(n,i),q(n,442,li(O)),o&&gu(n,i),q(n,10),n.end()}function Au(e,t,r){var n=rr(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,l=r.biff==5;if(q(n,2057,a0(e,5,r)),r.bookType=="xla"&&q(n,135),q(n,225,f?hr(1200):null),q(n,193,Kf(2)),l&&q(n,191),l&&q(n,192),q(n,226),q(n,92,no("SheetJS",r)),q(n,66,hr(f?1200:1252)),f&&q(n,353,hr(0)),f&&q(n,448),q(n,317,Fo(e.SheetNames.length)),f&&e.vbaraw&&q(n,211),f&&e.vbaraw){var o=s.CodeName||"ThisWorkbook";q(n,442,li(o))}q(n,156,hr(17)),q(n,25,qe(!1)),q(n,18,qe(!1)),q(n,19,hr(0)),f&&q(n,431,qe(!1)),f&&q(n,444,hr(0)),q(n,61,so()),q(n,64,qe(!1)),q(n,141,hr(0)),q(n,34,qe(Dh(e)=="true")),q(n,14,qe(!0)),f&&q(n,439,qe(!1)),q(n,218,hr(0)),vu(n,e,r),mu(n,e.SSF,r),_u(n,r),f&&q(n,352,qe(!1));var c=n.end(),d=rr();f&&q(d,140,wo()),f&&r.Strings&&cu(d,252,io(r.Strings)),q(d,10);var u=d.end(),p=rr(),_=0,h=0;for(h=0;h<e.SheetNames.length;++h)_+=(f?12:11)+(f?2:1)*e.SheetNames[h].length;var g=c.length+_+u.length;for(h=0;h<e.SheetNames.length;++h){var C=i[h]||{};q(p,133,ao({pos:g,hs:C.Hidden||0,dt:0,name:e.SheetNames[h]},r)),g+=t[h].length}var O=p.end();if(_!=O.length)throw new Error("BS8 "+_+" != "+O.length);var F=[];return c.length&&F.push(c),O.length&&F.push(O),u.length&&F.push(u),Ve(F)}function Fu(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=nr(Ie)),e&&e.SSF&&(En(),Tn(e.SSF),r.revssf=wn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,c0(r),r.cellXfs=[],Vr(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Su(a,r,e);return n.unshift(Au(e,n,r)),Ve(n)}function Mi(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=or(n["!ref"]);a.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Fu(e,t);case 4:case 3:case 2:return pu(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function yu(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,l=0,o=0;o<a.length;++o)if(!(a[o].s.r>r||a[o].s.c>s)&&!(a[o].e.r<r||a[o].e.c<s)){if(a[o].s.r<r||a[o].s.c<s){f=-1;break}f=a[o].e.r-a[o].s.r+1,l=a[o].e.c-a[o].s.c+1;break}if(!(f<0)){var c=Ee({r,c:s}),d=n.dense?(e[r]||[])[s]:e[c],u=d&&d.v!=null&&(d.h||rf(d.w||(kr(d),d.w)||""))||"",p={};f>1&&(p.rowspan=f),l>1&&(p.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":d&&(p["data-t"]=d&&d.t||"z",d.v!=null&&(p["data-v"]=d.v),d.z!=null&&(p["data-z"]=d.z),d.l&&(d.l.Target||"#").charAt(0)!="#"&&(u='<a href="'+d.l.Target+'">'+u+"</a>")),p.id=(n.id||"sjs")+"-"+c,i.push(Z("td",u,p))}}var _="<tr>";return _+i.join("")+"</tr>"}var Cu='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Ou="</body></html>";function Du(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Bi(e,t){var r=t||{},n=r.header!=null?r.header:Cu,a=r.footer!=null?r.footer:Ou,i=[n],s=or(e["!ref"]);r.dense=Array.isArray(e),i.push(Du(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(yu(e,s,f,r));return i.push("</table>"+a),i.join("")}function Ui(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?Be(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),l=Math.min(n.sheetRows||1e7,f.length),o={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=or(e["!ref"]);o.s.r=Math.min(o.s.r,c.s.r),o.s.c=Math.min(o.s.c,c.s.c),o.e.r=Math.max(o.e.r,c.e.r),o.e.c=Math.max(o.e.c,c.e.c),a==-1&&(o.e.r=a=c.e.r+1)}var d=[],u=0,p=e["!rows"]||(e["!rows"]=[]),_=0,h=0,g=0,C=0,O=0,F=0;for(e["!cols"]||(e["!cols"]=[]);_<f.length&&h<l;++_){var U=f[_];if(oa(U)){if(n.display)continue;p[h]={hidden:!0}}var J=U.children;for(g=C=0;g<J.length;++g){var re=J[g];if(!(n.display&&oa(re))){var D=re.hasAttribute("data-v")?re.getAttribute("data-v"):re.hasAttribute("v")?re.getAttribute("v"):sf(re.innerHTML),H=re.getAttribute("data-z")||re.getAttribute("z");for(u=0;u<d.length;++u){var L=d[u];L.s.c==C+i&&L.s.r<h+a&&h+a<=L.e.r&&(C=L.e.c+1-i,u=-1)}F=+re.getAttribute("colspan")||1,((O=+re.getAttribute("rowspan")||1)>1||F>1)&&d.push({s:{r:h+a,c:C+i},e:{r:h+a+(O||1)-1,c:C+i+(F||1)-1}});var X={t:"s",v:D},j=re.getAttribute("data-t")||re.getAttribute("t")||"";D!=null&&(D.length==0?X.t=j||"z":n.raw||D.trim().length==0||j=="s"||(D==="TRUE"?X={t:"b",v:!0}:D==="FALSE"?X={t:"b",v:!1}:isNaN(Dr(D))?isNaN(Nt(D).getDate())||(X={t:"d",v:Qe(D)},n.cellDates||(X={t:"n",v:tr(X.v)}),X.z=n.dateNF||Ie[14]):X={t:"n",v:Dr(D)})),X.z===void 0&&H!=null&&(X.z=H);var z="",te=re.getElementsByTagName("A");if(te&&te.length)for(var ae=0;ae<te.length&&!(te[ae].hasAttribute("href")&&(z=te[ae].getAttribute("href"),z.charAt(0)!="#"));++ae);z&&z.charAt(0)!="#"&&(X.l={Target:z}),n.dense?(e[h+a]||(e[h+a]=[]),e[h+a][C+i]=X):e[Ee({c:C+i,r:h+a})]=X,o.e.c<C+i&&(o.e.c=C+i),C+=F}}++h}return d.length&&(e["!merges"]=(e["!merges"]||[]).concat(d)),o.e.r=Math.max(o.e.r,h-1+a),e["!ref"]=Ne(o),h>=l&&(e["!fullref"]=Ne((o.e.r=f.length-_+h-1+a,o))),e}function bi(e,t){var r=t||{},n=r.dense?[]:{};return Ui(n,e,t)}function Ru(e,t){return Zr(bi(e,t),t)}function oa(e){var t="",r=Iu(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Iu(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var ku=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Lt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Pe+t}}(),la=function(){var e=function(i){return Te(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var l=[];l.push('      <table:table table:name="'+Te(s.SheetNames[f])+`" table:style-name="ta1">
`);var o=0,c=0,d=or(i["!ref"]||"A1"),u=i["!merges"]||[],p=0,_=Array.isArray(i);if(i["!cols"])for(c=0;c<=d.e.c;++c)l.push("        <table:table-column"+(i["!cols"][c]?' table:style-name="co'+i["!cols"][c].ods+'"':"")+`></table:table-column>
`);var h="",g=i["!rows"]||[];for(o=0;o<d.s.r;++o)h=g[o]?' table:style-name="ro'+g[o].ods+'"':"",l.push("        <table:table-row"+h+`></table:table-row>
`);for(;o<=d.e.r;++o){for(h=g[o]?' table:style-name="ro'+g[o].ods+'"':"",l.push("        <table:table-row"+h+`>
`),c=0;c<d.s.c;++c)l.push(t);for(;c<=d.e.c;++c){var C=!1,O={},F="";for(p=0;p!=u.length;++p)if(!(u[p].s.c>c)&&!(u[p].s.r>o)&&!(u[p].e.c<c)&&!(u[p].e.r<o)){(u[p].s.c!=c||u[p].s.r!=o)&&(C=!0),O["table:number-columns-spanned"]=u[p].e.c-u[p].s.c+1,O["table:number-rows-spanned"]=u[p].e.r-u[p].s.r+1;break}if(C){l.push(r);continue}var U=Ee({r:o,c}),J=_?(i[o]||[])[c]:i[U];if(J&&J.f&&(O["table:formula"]=Te(qc(J.f)),J.F&&J.F.slice(0,U.length)==U)){var re=or(J.F);O["table:number-matrix-columns-spanned"]=re.e.c-re.s.c+1,O["table:number-matrix-rows-spanned"]=re.e.r-re.s.r+1}if(!J){l.push(t);continue}switch(J.t){case"b":F=J.v?"TRUE":"FALSE",O["office:value-type"]="boolean",O["office:boolean-value"]=J.v?"true":"false";break;case"n":F=J.w||String(J.v||0),O["office:value-type"]="float",O["office:value"]=J.v||0;break;case"s":case"str":F=J.v==null?"":J.v,O["office:value-type"]="string";break;case"d":F=J.w||Qe(J.v).toISOString(),O["office:value-type"]="date",O["office:date-value"]=Qe(J.v).toISOString(),O["table:style-name"]="ce1";break;default:l.push(t);continue}var D=e(F);if(J.l&&J.l.Target){var H=J.l.Target;H=H.charAt(0)=="#"?"#"+Qc(H.slice(1)):H,H.charAt(0)!="#"&&!H.match(/^\w+:/)&&(H="../"+H),D=Z("text:a",D,{"xlink:href":H.replace(/&/g,"&amp;")})}l.push("          "+Z("table:table-cell",Z("text:p",D,{}),O)+`
`)}l.push(`        </table:table-row>
`)}return l.push(`      </table:table>
`),l.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!cols"]){for(var c=0;c<o["!cols"].length;++c)if(o["!cols"][c]){var d=o["!cols"][c];if(d.width==null&&d.wpx==null&&d.wch==null)continue;i0(d),d.ods=f;var u=o["!cols"][c].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+u+`"/>
`),i.push(`  </style:style>
`),++f}}});var l=0;s.SheetNames.map(function(o){return s.Sheets[o]}).forEach(function(o){if(o&&o["!rows"]){for(var c=0;c<o["!rows"].length;++c)if(o["!rows"][c]){o["!rows"][c].ods=l;var d=o["!rows"][c].hpx+"px";i.push('  <style:style style:name="ro'+l+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+d+`"/>
`),i.push(`  </style:style>
`),++l}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var l=[Pe],o=Lt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),c=Lt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(l.push("<office:document"+o+c+`>
`),l.push(ei().replace(/office:document-meta/g,"office:meta"))):l.push("<office:document-content"+o+`>
`),a(l,s),l.push(`  <office:body>
`),l.push(`    <office:spreadsheet>
`);for(var d=0;d!=s.SheetNames.length;++d)l.push(n(s.Sheets[s.SheetNames[d]],s,d));return l.push(`    </office:spreadsheet>
`),l.push(`  </office:body>
`),f.bookType=="fods"?l.push("</office:document>"):l.push("</office:document-content>"),l.join("")}}();function Wi(e,t){if(t.bookType=="fods")return la(e,t);var r=Kn(),n="",a=[],i=[];return n="mimetype",ue(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",ue(r,n,la(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",ue(r,n,ku(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",ue(r,n,Pe+ei()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",ue(r,n,Gf(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",ue(r,n,Hf(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function mn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Nu(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):pr(Pt(e))}function Pu(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function Hr(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function Lu(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function Mt(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function ge(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function ut(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Le(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=Mt(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var l=r[0];e[r[0]++]>=128;);f=e.slice(l,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=Mt(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var o={data:f,type:i};t[a]==null?t[a]=[o]:t[a].push(o)}return t}function We(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){a.data&&(t.push(ge(n*8+a.type)),a.type==2&&t.push(ge(a.data.length)),t.push(a.data))})}),Hr(t)}function xr(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=Mt(e,n),i=Le(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:ut(i[1][0].data),messages:[]};i[2].forEach(function(f){var l=Le(f.data),o=ut(l[3][0].data);s.messages.push({meta:l,data:e.slice(n[0],n[0]+o)}),n[0]+=o}),(t=i[3])!=null&&t[0]&&(s.merge=ut(i[3][0].data)>>>0>0),r.push(s)}return r}function it(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:ge(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:ge(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:ge(s.data.length)}],n[2].push({data:We(s.meta),type:2})});var i=We(n);t.push(ge(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),Hr(t)}function Mu(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Mt(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var l=0,o=0;if(i==1?(o=(t[r[0]]>>2&7)+4,l=(t[r[0]++]&224)<<3,l|=t[r[0]++]):(o=(t[r[0]++]>>2)+1,i==2?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[Hr(a)],l==0)throw new Error("Invalid offset 0");if(l>a[0].length)throw new Error("Invalid offset beyond length");if(o>=l)for(a.push(a[0].slice(-l)),o-=l;o>=a[a.length-1].length;)a.push(a[a.length-1]),o-=a[a.length-1].length;a.push(a[0].slice(-l,-l+o))}}var c=Hr(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function dr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Mu(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Hr(t)}function st(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=ge(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return Hr(t)}function bn(e,t){var r=new Uint8Array(32),n=mn(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Lu(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function Wn(e,t){var r=new Uint8Array(32),n=mn(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Mr(e){var t=Le(e);return Mt(t[1][0].data)}function Bu(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&ut(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var l=0,o=mn(e[7][0].data),c=0,d=[],u=mn(e[4][0].data),p=0,_=[],h=0;h<t.length;++h){if(t[h]==null){o.setUint16(h*2,65535,!0),u.setUint16(h*2,65535);continue}o.setUint16(h*2,c,!0),u.setUint16(h*2,p,!0);var g,C;switch(typeof t[h]){case"string":g=bn({t:"s",v:t[h]},r),C=Wn({t:"s",v:t[h]},r);break;case"number":g=bn({t:"n",v:t[h]},r),C=Wn({t:"n",v:t[h]},r);break;case"boolean":g=bn({t:"b",v:t[h]},r),C=Wn({t:"b",v:t[h]},r);break;default:throw new Error("Unsupported value "+t[h])}d.push(g),c+=g.length,_.push(C),p+=C.length,++l}for(e[2][0].data=ge(l);h<e[7][0].data.length/2;++h)o.setUint16(h*2,65535,!0),u.setUint16(h*2,65535,!0);return e[6][0].data=Hr(d),e[3][0].data=Hr(_),l}function Uu(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=or(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(Ne(n)));var i=gn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(P){return P.forEach(function(y){typeof y=="string"&&s.push(y)})});var f={},l=[],o=we.read(t.numbers,{type:"base64"});o.FileIndex.map(function(P,y){return[P,o.FullPaths[y]]}).forEach(function(P){var y=P[0],A=P[1];if(y.type==2&&y.name.match(/\.iwa/)){var G=y.content,le=dr(G),ce=xr(le);ce.forEach(function(fe){l.push(fe.id),f[fe.id]={deps:[],location:A,type:ut(fe.messages[0].meta[1][0].data)}})}}),l.sort(function(P,y){return P-y});var c=l.filter(function(P){return P>1}).map(function(P){return[P,ge(P)]});o.FileIndex.map(function(P,y){return[P,o.FullPaths[y]]}).forEach(function(P){var y=P[0];if(P[1],!!y.name.match(/\.iwa/)){var A=xr(dr(y.content));A.forEach(function(G){G.messages.forEach(function(le){c.forEach(function(ce){G.messages.some(function(fe){return ut(fe.meta[1][0].data)!=11006&&Pu(fe.data,ce[1])})&&f[ce[0]].deps.push(G.id)})})})}});for(var d=we.find(o,f[1].location),u=xr(dr(d.content)),p,_=0;_<u.length;++_){var h=u[_];h.id==1&&(p=h)}var g=Mr(Le(p.messages[0].data)[1][0].data);for(d=we.find(o,f[g].location),u=xr(dr(d.content)),_=0;_<u.length;++_)h=u[_],h.id==g&&(p=h);for(g=Mr(Le(p.messages[0].data)[2][0].data),d=we.find(o,f[g].location),u=xr(dr(d.content)),_=0;_<u.length;++_)h=u[_],h.id==g&&(p=h);for(g=Mr(Le(p.messages[0].data)[2][0].data),d=we.find(o,f[g].location),u=xr(dr(d.content)),_=0;_<u.length;++_)h=u[_],h.id==g&&(p=h);var C=Le(p.messages[0].data);{C[6][0].data=ge(n.e.r+1),C[7][0].data=ge(n.e.c+1);var O=Mr(C[46][0].data),F=we.find(o,f[O].location),U=xr(dr(F.content));{for(var J=0;J<U.length&&U[J].id!=O;++J);if(U[J].id!=O)throw"Bad ColumnRowUIDMapArchive";var re=Le(U[J].messages[0].data);re[1]=[],re[2]=[],re[3]=[];for(var D=0;D<=n.e.c;++D){var H=[];H[1]=H[2]=[{type:0,data:ge(D+420690)}],re[1].push({type:2,data:We(H)}),re[2].push({type:0,data:ge(D)}),re[3].push({type:0,data:ge(D)})}re[4]=[],re[5]=[],re[6]=[];for(var L=0;L<=n.e.r;++L)H=[],H[1]=H[2]=[{type:0,data:ge(L+726270)}],re[4].push({type:2,data:We(H)}),re[5].push({type:0,data:ge(L)}),re[6].push({type:0,data:ge(L)});U[J].messages[0].data=We(re)}F.content=st(it(U)),F.size=F.content.length,delete C[46];var X=Le(C[4][0].data);{X[7][0].data=ge(n.e.r+1);var j=Le(X[1][0].data),z=Mr(j[2][0].data);F=we.find(o,f[z].location),U=xr(dr(F.content));{if(U[0].id!=z)throw"Bad HeaderStorageBucket";var te=Le(U[0].messages[0].data);for(L=0;L<i.length;++L){var ae=Le(te[2][0].data);ae[1][0].data=ge(L),ae[4][0].data=ge(i[L].length),te[2][L]={type:te[2][0].type,data:We(ae)}}U[0].messages[0].data=We(te)}F.content=st(it(U)),F.size=F.content.length;var M=Mr(X[2][0].data);F=we.find(o,f[M].location),U=xr(dr(F.content));{if(U[0].id!=M)throw"Bad HeaderStorageBucket";for(te=Le(U[0].messages[0].data),D=0;D<=n.e.c;++D)ae=Le(te[2][0].data),ae[1][0].data=ge(D),ae[4][0].data=ge(n.e.r+1),te[2][D]={type:te[2][0].type,data:We(ae)};U[0].messages[0].data=We(te)}F.content=st(it(U)),F.size=F.content.length;var xe=Mr(X[4][0].data);(function(){for(var P=we.find(o,f[xe].location),y=xr(dr(P.content)),A,G=0;G<y.length;++G){var le=y[G];le.id==xe&&(A=le)}var ce=Le(A.messages[0].data);{ce[3]=[];var fe=[];s.forEach(function(de,Je){fe[1]=[{type:0,data:ge(Je)}],fe[2]=[{type:0,data:ge(1)}],fe[3]=[{type:2,data:Nu(de)}],ce[3].push({type:2,data:We(fe)})})}A.messages[0].data=We(ce);var ee=it(y),Se=st(ee);P.content=Se,P.size=P.content.length})();var oe=Le(X[3][0].data);{var Fe=oe[1][0];delete oe[2];var Oe=Le(Fe.data);{var ze=Mr(Oe[2][0].data);(function(){for(var P=we.find(o,f[ze].location),y=xr(dr(P.content)),A,G=0;G<y.length;++G){var le=y[G];le.id==ze&&(A=le)}var ce=Le(A.messages[0].data);{delete ce[6],delete oe[7];var fe=new Uint8Array(ce[5][0].data);ce[5]=[];for(var ee=0,Se=0;Se<=n.e.r;++Se){var de=Le(fe);ee+=Bu(de,i[Se],s),de[1][0].data=ge(Se),ce[5].push({data:We(de),type:2})}ce[1]=[{type:0,data:ge(n.e.c+1)}],ce[2]=[{type:0,data:ge(n.e.r+1)}],ce[3]=[{type:0,data:ge(ee)}],ce[4]=[{type:0,data:ge(n.e.r+1)}]}A.messages[0].data=We(ce);var Je=it(y),me=st(Je);P.content=me,P.size=P.content.length})()}Fe.data=We(Oe)}X[3][0].data=We(oe)}C[4][0].data=We(X)}p.messages[0].data=We(C);var be=it(u),S=st(be);return d.content=S,d.size=d.content.length,o}function bu(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function c0(e){bu([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Wu(e,t){return t.bookType=="ods"?Wi(e,t):t.bookType=="numbers"?Uu(e,t):t.bookType=="xlsb"?Hu(e,t):Vu(e,t)}function Hu(e,t){ot=1024,e&&!e.SSF&&(e.SSF=nr(Ie)),e&&e.SSF&&(En(),Tn(e.SSF),t.revssf=wn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Rt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=wi.indexOf(t.bookType)>-1,a=Za();c0(t=t||{});var i=Kn(),s="",f=0;if(t.cellXfs=[],Vr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",ue(i,s,ri(e.Props,t)),a.coreprops.push(s),_e(t.rels,2,s,pe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var l=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&l.push(e.SheetNames[o]);e.Props.SheetNames=l}for(e.Props.Worksheets=e.Props.SheetNames.length,ue(i,s,ni(e.Props)),a.extprops.push(s),_e(t.rels,3,s,pe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",ue(i,s,ai(e.Custprops)),a.custprops.push(s),_e(t.rels,4,s,pe.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var c={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],u=(d||{})["!type"]||"sheet";switch(u){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,ue(i,s,Xh(f-1,s,t,e,c)),a.sheets.push(s),_e(t.wbrels,-1,"worksheets/sheet"+f+"."+r,pe.WS[0])}if(d){var p=d["!comments"],_=!1,h="";p&&p.length>0&&(h="xl/comments"+f+"."+r,ue(i,h,$h(p,h)),a.comments.push(h),_e(c,-1,"../comments"+f+"."+r,pe.CMNT),_=!0),d["!legacy"]&&_&&ue(i,"xl/drawings/vmlDrawing"+f+".vml",Ti(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}c["!id"].rId1&&ue(i,Qa(s),ct(c))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,ue(i,s,zh(t.Strings,s,t)),a.strs.push(s),_e(t.wbrels,-1,"sharedStrings."+r,pe.SST)),s="xl/workbook."+r,ue(i,s,Gh(e,s)),a.workbooks.push(s),_e(t.rels,1,s,pe.WB),s="xl/theme/theme1.xml",ue(i,s,gi(e.Themes,t)),a.themes.push(s),_e(t.wbrels,-1,"theme/theme1.xml",pe.THEME),s="xl/styles."+r,ue(i,s,jh(e,s,t)),a.styles.push(s),_e(t.wbrels,-1,"styles."+r,pe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",ue(i,s,e.vbaraw),a.vba.push(s),_e(t.wbrels,-1,"vbaProject.bin",pe.VBA)),s="xl/metadata."+r,ue(i,s,Kh(s)),a.metadata.push(s),_e(t.wbrels,-1,"metadata."+r,pe.XLMETA),ue(i,"[Content_Types].xml",qa(a,t)),ue(i,"_rels/.rels",ct(t.rels)),ue(i,"xl/_rels/workbook."+r+".rels",ct(t.wbrels)),delete t.revssf,delete t.ssf,i}function Vu(e,t){ot=1024,e&&!e.SSF&&(e.SSF=nr(Ie)),e&&e.SSF&&(En(),Tn(e.SSF),t.revssf=wn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Rt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=wi.indexOf(t.bookType)>-1,a=Za();c0(t=t||{});var i=Kn(),s="",f=0;if(t.cellXfs=[],Vr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",ue(i,s,ri(e.Props,t)),a.coreprops.push(s),_e(t.rels,2,s,pe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var l=[],o=0;o<e.SheetNames.length;++o)(e.Workbook.Sheets[o]||{}).Hidden!=2&&l.push(e.SheetNames[o]);e.Props.SheetNames=l}e.Props.Worksheets=e.Props.SheetNames.length,ue(i,s,ni(e.Props)),a.extprops.push(s),_e(t.rels,3,s,pe.EXT_PROPS),e.Custprops!==e.Props&&je(e.Custprops||{}).length>0&&(s="docProps/custom.xml",ue(i,s,ai(e.Custprops)),a.custprops.push(s),_e(t.rels,4,s,pe.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var d={"!id":{}},u=e.Sheets[e.SheetNames[f-1]],p=(u||{})["!type"]||"sheet";switch(p){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,ue(i,s,Ii(f-1,t,e,d)),a.sheets.push(s),_e(t.wbrels,-1,"worksheets/sheet"+f+"."+r,pe.WS[0])}if(u){var _=u["!comments"],h=!1,g="";if(_&&_.length>0){var C=!1;_.forEach(function(O){O[1].forEach(function(F){F.T==!0&&(C=!0)})}),C&&(g="xl/threadedComments/threadedComment"+f+"."+r,ue(i,g,_l(_,c,t)),a.threadedcomments.push(g),_e(d,-1,"../threadedComments/threadedComment"+f+"."+r,pe.TCMNT)),g="xl/comments"+f+"."+r,ue(i,g,Ei(_)),a.comments.push(g),_e(d,-1,"../comments"+f+"."+r,pe.CMNT),h=!0}u["!legacy"]&&h&&ue(i,"xl/drawings/vmlDrawing"+f+".vml",Ti(f,u["!comments"])),delete u["!comments"],delete u["!legacy"]}d["!id"].rId1&&ue(i,Qa(s),ct(d))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,ue(i,s,ui(t.Strings,t)),a.strs.push(s),_e(t.wbrels,-1,"sharedStrings."+r,pe.SST)),s="xl/workbook."+r,ue(i,s,Pi(e)),a.workbooks.push(s),_e(t.rels,1,s,pe.WB),s="xl/theme/theme1.xml",ue(i,s,gi(e.Themes,t)),a.themes.push(s),_e(t.wbrels,-1,"theme/theme1.xml",pe.THEME),s="xl/styles."+r,ue(i,s,vi(e,t)),a.styles.push(s),_e(t.wbrels,-1,"styles."+r,pe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",ue(i,s,e.vbaraw),a.vba.push(s),_e(t.wbrels,-1,"vbaProject.bin",pe.VBA)),s="xl/metadata."+r,ue(i,s,_i()),a.metadata.push(s),_e(t.wbrels,-1,"metadata."+r,pe.XLMETA),c.length>1&&(s="xl/persons/person.xml",ue(i,s,Tl(c)),a.people.push(s),_e(t.wbrels,-1,"persons/person.xml",pe.PEOPLE)),ue(i,"[Content_Types].xml",qa(a,t)),ue(i,"_rels/.rels",ct(t.rels)),ue(i,"xl/_rels/workbook."+r+".rels",ct(t.wbrels)),delete t.revssf,delete t.ssf,i}function Gu(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Ir(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Hi(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return bt(t.file,we.write(e,{type:ve?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return we.write(e,t)}function Xu(e,t){var r=nr(t||{}),n=Wu(e,r);return ju(n,r)}function ju(e,t){var r={},n=ve?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?we.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(_n(a))}return t.password&&typeof encrypt_agile<"u"?Hi(encrypt_agile(a,t.password),t):t.type==="file"?bt(t.file,a):t.type=="string"?yt(a):a}function zu(e,t){var r=t||{},n=ou(e,r);return Hi(n,r)}function Tr(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return kt(Pt(n));case"binary":return Pt(n);case"string":return e;case"file":return bt(t.file,n,"utf8");case"buffer":return ve?Nr(n,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(n):Tr(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function $u(e,t){switch(t.type){case"base64":return kt(e);case"binary":return e;case"string":return e;case"file":return bt(t.file,e,"binary");case"buffer":return ve?Nr(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function tn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?kt(r):t.type=="string"?yt(r):r;case"file":return bt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Vi(e,t){_s(),kh(e);var r=nr(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=Vi(e,r);return r.type="array",_n(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Tr(su(e,r),r);case"slk":case"sylk":return Tr(Do.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return Tr(Bi(e.Sheets[e.SheetNames[a]],r),r);case"txt":return $u(Gi(e.Sheets[e.SheetNames[a]],r),r);case"csv":return Tr(h0(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return Tr(Ro.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return tn(Oo.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return Tr(Io.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return Tr(Uo.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return Tr(hi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return Tr(Wi(e,r),r);case"wk1":return tn(ta.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return tn(ta.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),tn(Mi(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),zu(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return Xu(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function Ku(e,t,r,n,a,i,s,f){var l=Xe(r),o=f.defval,c=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),d=!0,u=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch{u.__rowNum__=r}else u.__rowNum__=r;if(!s||e[r])for(var p=t.s.c;p<=t.e.c;++p){var _=s?e[r][p]:e[n[p]+l];if(_===void 0||_.t===void 0){if(o===void 0)continue;i[p]!=null&&(u[i[p]]=o);continue}var h=_.v;switch(_.t){case"z":if(h==null)break;continue;case"e":h=h==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+_.t)}if(i[p]!=null){if(h==null)if(_.t=="e"&&h===null)u[i[p]]=null;else if(o!==void 0)u[i[p]]=o;else if(c&&h===null)u[i[p]]=null;else continue;else u[i[p]]=c&&(_.t!=="n"||_.t==="n"&&f.rawNumbers!==!1)?h:kr(_,h,f);h!=null&&(d=!1)}}return{row:u,isempty:d}}function gn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",l={s:{r:0,c:0},e:{r:0,c:0}},o=t||{},c=o.range!=null?o.range:e["!ref"];switch(o.header===1?n=1:o.header==="A"?n=2:Array.isArray(o.header)?n=3:o.header==null&&(n=0),typeof c){case"string":l=Ae(c);break;case"number":l=Ae(e["!ref"]),l.s.r=c;break;default:l=c}n>0&&(a=0);var d=Xe(l.s.r),u=[],p=[],_=0,h=0,g=Array.isArray(e),C=l.s.r,O=0,F={};g&&!e[C]&&(e[C]=[]);var U=o.skipHidden&&e["!cols"]||[],J=o.skipHidden&&e["!rows"]||[];for(O=l.s.c;O<=l.e.c;++O)if(!(U[O]||{}).hidden)switch(u[O]=Ke(O),r=g?e[C][O]:e[u[O]+d],n){case 1:i[O]=O-l.s.c;break;case 2:i[O]=u[O];break;case 3:i[O]=o.header[O-l.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=kr(r,null,o),h=F[s]||0,!h)F[s]=1;else{do f=s+"_"+h++;while(F[f]);F[s]=h,F[f]=1}i[O]=f}for(C=l.s.r+a;C<=l.e.r;++C)if(!(J[C]||{}).hidden){var re=Ku(e,l,C,u,n,i,g,o);(re.isempty===!1||(n===1?o.blankrows!==!1:o.blankrows))&&(p[_++]=re.row)}return p.length=_,p}var ca=/"/g;function Yu(e,t,r,n,a,i,s,f){for(var l=!0,o=[],c="",d=Xe(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var p=f.dense?(e[r]||[])[u]:e[n[u]+d];if(p==null)c="";else if(p.v!=null){l=!1,c=""+(f.rawNumbers&&p.t=="n"?p.v:kr(p,null,f));for(var _=0,h=0;_!==c.length;++_)if((h=c.charCodeAt(_))===a||h===i||h===34||f.forceQuotes){c='"'+c.replace(ca,'""')+'"';break}c=="ID"&&(c='"ID"')}else p.f!=null&&!p.F?(l=!1,c="="+p.f,c.indexOf(",")>=0&&(c='"'+c.replace(ca,'""')+'"')):c="";o.push(c)}return f.blankrows===!1&&l?null:o.join(s)}function h0(e,t){var r=[],n=t??{};if(e==null||e["!ref"]==null)return"";var a=Ae(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,l=f.charCodeAt(0),o=new RegExp((i=="|"?"\\|":i)+"+$"),c="",d=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],_=a.s.c;_<=a.e.c;++_)(u[_]||{}).hidden||(d[_]=Ke(_));for(var h=0,g=a.s.r;g<=a.e.r;++g)(p[g]||{}).hidden||(c=Yu(e,a,g,d,s,l,i,n),c!=null&&(n.strip&&(c=c.replace(o,"")),(c||n.blankrows!==!1)&&r.push((h++?f:"")+c)));return delete n.dense,r.join("")}function Gi(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=h0(e,t);return r}function Ju(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Ae(e["!ref"]),i="",s=[],f,l=[],o=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=Ke(f);for(var c=a.s.r;c<=a.e.r;++c)for(i=Xe(c),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=o?(e[c]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}l[l.length]=t+"="+n}return l}function Xi(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var l=typeof n.origin=="string"?Be(n.origin):n.origin;s=l.r,f=l.c}var o,c={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var d=Ae(i["!ref"]);c.e.c=Math.max(c.e.c,d.e.c),c.e.r=Math.max(c.e.r,d.e.r),s==-1&&(s=d.e.r+1,c.e.r=s+t.length-1+a)}else s==-1&&(s=0,c.e.r=t.length-1+a);var u=n.header||[],p=0;t.forEach(function(h,g){je(h).forEach(function(C){(p=u.indexOf(C))==-1&&(u[p=u.length]=C);var O=h[C],F="z",U="",J=Ee({c:f+p,r:s+g+a});o=Bt(i,J),O&&typeof O=="object"&&!(O instanceof Date)?i[J]=O:(typeof O=="number"?F="n":typeof O=="boolean"?F="b":typeof O=="string"?F="s":O instanceof Date?(F="d",n.cellDates||(F="n",O=tr(O)),U=n.dateNF||Ie[14]):O===null&&n.nullError&&(F="e",O=0),o?(o.t=F,o.v=O,delete o.w,delete o.R,U&&(o.z=U)):i[J]=o={t:F,v:O},U&&(o.z=U))})}),c.e.c=Math.max(c.e.c,f+u.length-1);var _=Xe(s);if(a)for(p=0;p<u.length;++p)i[Ke(p+f)+_]={t:"s",v:u[p]};return i["!ref"]=Ne(c),i}function Zu(e,t){return Xi(null,e,t)}function Bt(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Be(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Bt(e,Ee(t)):Bt(e,Ee({r:t,c:r||0}))}function qu(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function Qu(){return{SheetNames:[],Sheets:{}}}function ex(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(Ni(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function rx(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=qu(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function tx(e,t){return e.z=t,e}function ji(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function nx(e,t,r){return ji(e,"#"+t,r)}function ax(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function ix(e,t,r,n){for(var a=typeof t!="string"?t:Ae(t),i=typeof t=="string"?t:Ne(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var l=Bt(e,s,f);l.t="n",l.F=i,delete l.v,s==a.s.r&&f==a.s.c&&(l.f=r,n&&(l.D=!0))}return e}var Cr={encode_col:Ke,encode_row:Xe,encode_cell:Ee,encode_range:Ne,decode_col:e0,decode_row:Qn,split_cell:Tf,decode_cell:Be,decode_range:or,format_cell:kr,sheet_add_aoa:ja,sheet_add_json:Xi,sheet_add_dom:Ui,aoa_to_sheet:pt,json_to_sheet:Zu,table_to_sheet:bi,table_to_book:Ru,sheet_to_csv:h0,sheet_to_txt:Gi,sheet_to_json:gn,sheet_to_html:Bi,sheet_to_formulae:Ju,sheet_to_row_object_array:gn,sheet_get_cell:Bt,book_new:Qu,book_append_sheet:ex,book_set_sheet_visibility:rx,cell_set_number_format:tx,cell_set_hyperlink:ji,cell_set_internal_link:nx,cell_add_comment:ax,sheet_set_array_formula:ix,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},nn={exports:{}},sx=nn.exports,ha;function fx(){return ha||(ha=1,function(e,t){(function(r,n){n()})(sx,function(){function r(o,c){return typeof c>"u"?c={autoBom:!1}:typeof c!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),c={autoBom:!c}),c.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob(["\uFEFF",o],{type:o.type}):o}function n(o,c,d){var u=new XMLHttpRequest;u.open("GET",o),u.responseType="blob",u.onload=function(){l(u.response,c,d)},u.onerror=function(){console.error("could not download file")},u.send()}function a(o){var c=new XMLHttpRequest;c.open("HEAD",o,!1);try{c.send()}catch{}return 200<=c.status&&299>=c.status}function i(o){try{o.dispatchEvent(new MouseEvent("click"))}catch{var c=document.createEvent("MouseEvents");c.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),o.dispatchEvent(c)}}var s=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof zt=="object"&&zt.global===zt?zt:void 0,f=s.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),l=s.saveAs||(typeof window!="object"||window!==s?function(){}:"download"in HTMLAnchorElement.prototype&&!f?function(o,c,d){var u=s.URL||s.webkitURL,p=document.createElement("a");c=c||o.name||"download",p.download=c,p.rel="noopener",typeof o=="string"?(p.href=o,p.origin===location.origin?i(p):a(p.href)?n(o,c,d):i(p,p.target="_blank")):(p.href=u.createObjectURL(o),setTimeout(function(){u.revokeObjectURL(p.href)},4e4),setTimeout(function(){i(p)},0))}:"msSaveOrOpenBlob"in navigator?function(o,c,d){if(c=c||o.name||"download",typeof o!="string")navigator.msSaveOrOpenBlob(r(o,d),c);else if(a(o))n(o,c,d);else{var u=document.createElement("a");u.href=o,u.target="_blank",setTimeout(function(){i(u)})}}:function(o,c,d,u){if(u=u||open("","_blank"),u&&(u.document.title=u.document.body.innerText="downloading..."),typeof o=="string")return n(o,c,d);var p=o.type==="application/octet-stream",_=/constructor/i.test(s.HTMLElement)||s.safari,h=/CriOS\/[\d]+/.test(navigator.userAgent);if((h||p&&_||f)&&typeof FileReader<"u"){var g=new FileReader;g.onloadend=function(){var F=g.result;F=h?F:F.replace(/^data:[^;]*;/,"data:attachment/file;"),u?u.location.href=F:location=F,u=null},g.readAsDataURL(o)}else{var C=s.URL||s.webkitURL,O=C.createObjectURL(o);u?u.location=O:location.href=O,u=null,setTimeout(function(){C.revokeObjectURL(O)},4e4)}});s.saveAs=l.saveAs=l,e.exports=l})}(nn)),nn.exports}var ox=fx();class lx{async exportOrdersToExcel(t={},r="orders_export"){try{const n=await this.fetchAllOrders(t);if(!n||n.length===0)throw new Error("No orders found to export");const a=this.prepareOrdersForExcel(n),i=Cr.book_new(),s=Cr.json_to_sheet(a.orders);if(Cr.book_append_sheet(i,s,"Orders"),a.orderItems&&a.orderItems.length>0){const d=Cr.json_to_sheet(a.orderItems);Cr.book_append_sheet(i,d,"Order Items")}const f=Cr.json_to_sheet(a.summary);Cr.book_append_sheet(i,f,"Summary"),this.styleWorkbook(i);const l=Vi(i,{bookType:"xlsx",type:"array",cellStyles:!0}),o=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),c=new Date().toISOString().slice(0,19).replace(/:/g,"-");return ox.saveAs(o,`${r}_${c}.xlsx`),{success:!0,message:`Successfully exported ${n.length} orders`,filename:`${r}_${c}.xlsx`}}catch(n){throw console.error("Export error:",n),new Error(`Export failed: ${n.message}`)}}async fetchAllOrders(t){const r=[];let n=1,a=!0;const i=50;for(;a;)try{const s=await St.getAll({...t,page:n,pageSize:i});s.data&&s.data.length>0?(r.push(...s.data),a=s.data.length===i&&n<(s.totalPages||1),n++):a=!1}catch(s){console.error(`Error fetching page ${n}:`,s),a=!1}return r}prepareOrdersForExcel(t){const r=[],n=[];let a=0;const i={},s={};t.forEach(l=>{const o={"Order ID":l.id,"Customer Name":l.customerName||"N/A","Customer Email":l.customerEmail||"N/A","Order Date":this.formatDate(l.createdAt),"Total Amount":l.totalPriceAmount||0,Currency:l.totalPriceCurrency||"USD","Order Status":l.status||"Unknown","Payment Status":l.paymentStatusText||"Unknown","Payment Method":l.paymentMethodText||"Unknown","Items Count":l.itemsCount||0,"Shipping Method":l.shippingMethodName||"N/A","Shipping Address":this.formatShippingAddress(l),"Created At":this.formatDateTime(l.createdAt)};r.push(o),a+=l.totalPriceAmount||0;const c=l.status||"Unknown";i[c]=(i[c]||0)+1;const d=l.paymentStatusText||"Unknown";s[d]=(s[d]||0)+1,l.items&&l.items.length>0&&l.items.forEach(u=>{n.push({"Order ID":l.id,"Product ID":u.productId,"Product Name":u.productName||"N/A",Quantity:u.quantity,"Unit Price":u.priceAmount,Currency:u.priceCurrency,"Total Price":u.quantity*u.priceAmount||0})})});const f=[{Metric:"Total Orders",Value:t.length},{Metric:"Total Revenue",Value:a.toFixed(2)},{Metric:"Average Order Value",Value:(a/t.length).toFixed(2)},{Metric:"",Value:""},{Metric:"ORDER STATUSES",Value:""},...Object.entries(i).map(([l,o])=>({Metric:l,Value:o})),{Metric:"",Value:""},{Metric:"PAYMENT STATUSES",Value:""},...Object.entries(s).map(([l,o])=>({Metric:l,Value:o}))];return{orders:r,orderItems:n,summary:f}}formatShippingAddress(t){const r=[];return t.shippingAddressLine&&r.push(t.shippingAddressLine),t.shippingCity&&r.push(t.shippingCity),t.shippingCountry&&r.push(t.shippingCountry),r.length>0?r.join(", "):"N/A"}formatDate(t){return t?new Date(t).toLocaleDateString("en-US"):""}formatDateTime(t){return t?new Date(t).toLocaleString("en-US"):""}styleWorkbook(t){Object.keys(t.Sheets).forEach(r=>{const n=t.Sheets[r],a=Cr.decode_range(n["!ref"]),i=[];for(let s=a.s.c;s<=a.e.c;s++){let f=10;for(let l=a.s.r;l<=a.e.r;l++){const o=Cr.encode_cell({r:l,c:s}),c=n[o];if(c&&c.v){const d=c.v.toString().length;f=Math.max(f,d)}}i.push({width:Math.min(f+2,50)})}n["!cols"]=i})}async exportFilteredOrders(t,r,n="filtered_orders"){const a={...r,search:t};return await this.exportOrdersToExcel(a,n)}}const cx=new lx,hx={class:"order-list"},ux={class:"level"},xx={class:"level-right"},dx={class:"level-item"},px={class:"card mb-4"},vx={class:"card-content"},mx={class:"columns is-multiline"},gx={class:"column is-3"},_x={class:"field"},Tx={class:"control has-icons-left"},Ex={class:"column is-3"},wx={class:"field"},Sx={class:"control"},Ax={class:"select is-fullwidth"},Fx={class:"column is-3"},yx={class:"field"},Cx={class:"control"},Ox={class:"select is-fullwidth"},Dx={class:"column is-3"},Rx={class:"field"},Ix={class:"control"},kx={class:"select is-fullwidth"},Nx={key:0,class:"column is-6"},Px={class:"columns"},Lx={class:"column is-6"},Mx={class:"field"},Bx={class:"control"},Ux={class:"column is-6"},bx={class:"field"},Wx={class:"control"},Hx={class:"column is-12"},Vx={class:"field is-grouped is-grouped-right"},Gx={class:"control"},Xx={class:"card"},jx={class:"card-content"},zx={key:0,class:"has-text-centered py-6"},$x={key:1,class:"has-text-centered py-6"},Kx={key:2},Yx={class:"table-container"},Jx={class:"table is-fullwidth is-hoverable"},Zx={class:"has-text-info"},qx={class:"has-text-grey"},Qx={class:"has-text-success"},ed={class:"buttons are-small"},rd=["onClick"],td={class:"modal-card"},nd={class:"modal-card-body"},ad={key:0,class:"content"},id={class:"field"},sd={class:"control"},fd={class:"select is-fullwidth"},od={class:"field"},ld={class:"control"},cd={class:"select is-fullwidth"},hd={class:"field"},ud={class:"control"},xd={class:"modal-card-foot"},dd={__name:"OrderList",setup(e){const t=er([]),r=er(!1),n=er(!1),a=er(1),i=er(1),s=er(0),f=er(10),l=er(!1),o=er(null),c=er(""),d=er(""),u=er(""),p=er(!1),_=er(null),h=ls({search:"",status:"",paymentStatus:"",dateRange:"",dateFrom:"",dateTo:""}),g=async(ae=1)=>{r.value=!0,a.value=ae;try{const M={page:a.value,pageSize:Math.min(f.value,10),limit:Math.min(f.value,10),search:h.search,status:h.status,paymentStatus:h.paymentStatus};if(h.dateRange){const oe=new Date,Fe=new Date(oe.getFullYear(),oe.getMonth(),oe.getDate());switch(h.dateRange){case"today":dateFilters.dateFrom=Fe.toISOString().split("T")[0];break;case"yesterday":const Oe=new Date(Fe);Oe.setDate(Oe.getDate()-1),dateFilters.dateFrom=Oe.toISOString().split("T")[0],dateFilters.dateTo=Fe.toISOString().split("T")[0];break;case"last7days":const ze=new Date(Fe);ze.setDate(ze.getDate()-7),dateFilters.dateFrom=ze.toISOString().split("T")[0];break;case"last30days":const be=new Date(Fe);be.setDate(be.getDate()-30),dateFilters.dateFrom=be.toISOString().split("T")[0];break;case"thisMonth":const S=new Date(Fe.getFullYear(),Fe.getMonth(),1);dateFilters.dateFrom=S.toISOString().split("T")[0];break;case"lastMonth":const P=new Date(Fe.getFullYear(),Fe.getMonth()-1,1),y=new Date(Fe.getFullYear(),Fe.getMonth(),0);dateFilters.dateFrom=P.toISOString().split("T")[0],dateFilters.dateTo=y.toISOString().split("T")[0];break;case"custom":h.dateFrom&&(M.dateFrom=h.dateFrom),h.dateTo&&(M.dateTo=h.dateTo);break}}const xe=await St.getAll(M);console.log("Orders API response:",xe),xe.orders?(t.value=xe.orders,console.log("Orders loaded:",t.value.length)):xe.data?(t.value=xe.data,console.log("Orders loaded (data field):",t.value.length)):(t.value=[],console.log("No orders found in response")),xe.pagination&&(i.value=xe.pagination.totalPages,s.value=xe.pagination.total,a.value=xe.pagination.page,console.log("Pagination:",xe.pagination))}catch(M){console.error("Error fetching orders:",M)}finally{r.value=!1}},C=()=>{h.search="",h.status="",h.paymentStatus="",h.dateRange="",h.dateFrom="",h.dateTo="",g(1)},O=ae=>{g(ae)},F=()=>{_.value&&clearTimeout(_.value),_.value=setTimeout(()=>{g(1),_.value=null},500)},U=async()=>{try{n.value=!0;const ae={search:h.search,status:h.status,paymentStatus:h.paymentStatus,dateRange:h.dateRange,dateFrom:h.dateFrom,dateTo:h.dateTo};Object.keys(ae).forEach(xe=>{ae[xe]||delete ae[xe]});const M=await cx.exportOrdersToExcel(ae,"orders_export");console.log("Export successful:",M),alert(`Successfully exported orders to ${M.filename}`)}catch(ae){console.error("Export failed:",ae),alert(`Export failed: ${ae.message}`)}finally{n.value=!1}},J=ae=>ae?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(ae)):"",re=ae=>{if(!ae)return"₴0.00";const M=typeof ae=="string"?parseFloat(ae):ae;return new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2,maximumFractionDigits:2}).format(M)},D=ae=>({Pending:"Pending",Paid:"Paid",Shipped:"Shipped",Delivered:"Delivered",Cancelled:"Cancelled",0:"Pending",1:"Paid",2:"Shipped",3:"Delivered",4:"Cancelled"})[ae]||ae||"Unknown",H=ae=>({Pending:"is-warning",Paid:"is-info",Shipped:"is-primary",Delivered:"is-success",Cancelled:"is-danger",0:"is-warning",1:"is-info",2:"is-primary",3:"is-success",4:"is-danger"})[ae]||"is-light",L=ae=>({Pending:"Pending",Completed:"Completed",Failed:"Failed",0:"Pending",1:"Completed",2:"Failed"})[ae]||ae||"Unknown",X=ae=>({Pending:"is-warning",Completed:"is-success",Failed:"is-danger",0:"is-warning",1:"is-success",2:"is-danger"})[ae]||"is-light",j=ae=>{o.value=ae,c.value=ae.status,d.value=ae.paymentStatus,u.value="",l.value=!0},z=()=>{l.value=!1,o.value=null},te=async()=>{if(o.value){p.value=!0;try{c.value!==o.value.status&&await St.updateOrderStatus(o.value.id,c.value),d.value!==o.value.paymentStatus&&await St.updatePaymentStatus(o.value.id,d.value),u.value.trim()&&await St.addOrderNote(o.value.id,u.value);const ae=t.value.findIndex(M=>M.id===o.value.id);ae!==-1&&(t.value[ae].status=c.value,t.value[ae].paymentStatus=d.value),z()}catch(ae){console.error("Error updating order status:",ae)}finally{p.value=!1}}};return cs(()=>{g()}),(ae,M)=>{const xe=ds("router-link");return Lr(),Pr("div",hx,[B("div",ux,[M[10]||(M[10]=B("div",{class:"level-left"},[B("div",{class:"level-item"},[B("h1",{class:"title"},"Orders")])],-1)),B("div",xx,[B("div",dx,[B("button",{class:nt(["button is-primary",{"is-loading":n.value}]),onClick:U},M[9]||(M[9]=[B("span",{class:"icon"},[B("i",{class:"fas fa-download"})],-1),B("span",null,"Export Orders",-1)]),2)])])]),B("div",px,[B("div",vx,[B("div",mx,[B("div",gx,[B("div",_x,[M[12]||(M[12]=B("label",{class:"label"},"Search",-1)),B("div",Tx,[Fr(B("input",{class:"input",type:"text",placeholder:"Order ID, customer name, email...","onUpdate:modelValue":M[0]||(M[0]=oe=>h.search=oe),onInput:F},null,544),[[$t,h.search]]),M[11]||(M[11]=B("span",{class:"icon is-small is-left"},[B("i",{class:"fas fa-search"})],-1))])])]),B("div",Ex,[B("div",wx,[M[14]||(M[14]=B("label",{class:"label"},"Status",-1)),B("div",Sx,[B("div",Ax,[Fr(B("select",{"onUpdate:modelValue":M[1]||(M[1]=oe=>h.status=oe)},M[13]||(M[13]=[Kt('<option value="" data-v-4a7e8186>All Statuses</option><option value="pending" data-v-4a7e8186>Pending</option><option value="processing" data-v-4a7e8186>Processing</option><option value="shipped" data-v-4a7e8186>Shipped</option><option value="delivered" data-v-4a7e8186>Delivered</option><option value="cancelled" data-v-4a7e8186>Cancelled</option><option value="refunded" data-v-4a7e8186>Refunded</option>',7)]),512),[[Et,h.status]])])])])]),B("div",Fx,[B("div",yx,[M[16]||(M[16]=B("label",{class:"label"},"Payment Status",-1)),B("div",Cx,[B("div",Ox,[Fr(B("select",{"onUpdate:modelValue":M[2]||(M[2]=oe=>h.paymentStatus=oe)},M[15]||(M[15]=[Kt('<option value="" data-v-4a7e8186>All Payment Statuses</option><option value="paid" data-v-4a7e8186>Paid</option><option value="pending" data-v-4a7e8186>Pending</option><option value="failed" data-v-4a7e8186>Failed</option><option value="refunded" data-v-4a7e8186>Refunded</option>',5)]),512),[[Et,h.paymentStatus]])])])])]),B("div",Dx,[B("div",Rx,[M[18]||(M[18]=B("label",{class:"label"},"Date Range",-1)),B("div",Ix,[B("div",kx,[Fr(B("select",{"onUpdate:modelValue":M[3]||(M[3]=oe=>h.dateRange=oe)},M[17]||(M[17]=[Kt('<option value="" data-v-4a7e8186>All Time</option><option value="today" data-v-4a7e8186>Today</option><option value="yesterday" data-v-4a7e8186>Yesterday</option><option value="last7days" data-v-4a7e8186>Last 7 Days</option><option value="last30days" data-v-4a7e8186>Last 30 Days</option><option value="thisMonth" data-v-4a7e8186>This Month</option><option value="lastMonth" data-v-4a7e8186>Last Month</option><option value="custom" data-v-4a7e8186>Custom Range</option>',8)]),512),[[Et,h.dateRange]])])])])]),h.dateRange==="custom"?(Lr(),Pr("div",Nx,[B("div",Px,[B("div",Lx,[B("div",Mx,[M[19]||(M[19]=B("label",{class:"label"},"From",-1)),B("div",Bx,[Fr(B("input",{class:"input",type:"date","onUpdate:modelValue":M[4]||(M[4]=oe=>h.dateFrom=oe)},null,512),[[$t,h.dateFrom]])])])]),B("div",Ux,[B("div",bx,[M[20]||(M[20]=B("label",{class:"label"},"To",-1)),B("div",Wx,[Fr(B("input",{class:"input",type:"date","onUpdate:modelValue":M[5]||(M[5]=oe=>h.dateTo=oe)},null,512),[[$t,h.dateTo]])])])])])])):w0("",!0),B("div",Hx,[B("div",Vx,[B("div",{class:"control"},[B("button",{class:"button is-light",onClick:C}," Reset ")]),B("div",Gx,[B("button",{class:nt(["button is-primary",{"is-loading":r.value}]),onClick:g}," Apply Filters ",2)])])])])])]),B("div",Xx,[B("div",jx,[r.value&&!t.value.length?(Lr(),Pr("div",zx,M[21]||(M[21]=[B("span",{class:"icon is-large"},[B("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),B("p",{class:"mt-2"},"Loading orders...",-1)]))):t.value.length?(Lr(),Pr("div",Kx,[B("div",Yx,[B("table",Jx,[M[26]||(M[26]=B("thead",null,[B("tr",null,[B("th",null,"Order ID"),B("th",null,"Customer"),B("th",null,"Date"),B("th",null,"Total"),B("th",null,"Status"),B("th",null,"Payment"),B("th",null,"Actions")])],-1)),B("tbody",null,[(Lr(!0),Pr(hs,null,us(t.value,oe=>(Lr(),Pr("tr",{key:oe.id},[B("td",null,[B("code",Zx,yr(oe.id),1)]),B("td",null,[B("div",null,[B("strong",null,yr(oe.customerName||oe.userName||"N/A"),1),M[23]||(M[23]=B("br",null,null,-1)),B("small",qx,yr(oe.customerEmail||"No email"),1)])]),B("td",null,yr(J(oe.createdAt)),1),B("td",null,[B("strong",Qx,yr(re(oe.total||oe.totalPriceAmount)),1)]),B("td",null,[B("span",{class:nt(["tag",H(oe.status)])},yr(D(oe.status)),3)]),B("td",null,[B("span",{class:nt(["tag",X(oe.paymentStatusText||oe.paymentStatus)])},yr(L(oe.paymentStatusText||oe.paymentStatus)),3)]),B("td",null,[B("div",ed,[kn(xe,{to:`/admin/orders/${oe.id}`,class:"button is-info",title:"View"},{default:xs(()=>M[24]||(M[24]=[B("span",{class:"icon is-small"},[B("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),B("button",{class:"button is-primary",onClick:Fe=>j(oe),title:"Update Status"},M[25]||(M[25]=[B("span",{class:"icon is-small"},[B("i",{class:"fas fa-edit"})],-1)]),8,rd)])])]))),128))])])]),kn(vs,{"current-page":a.value,"total-pages":i.value,onPageChanged:O},null,8,["current-page","total-pages"])])):(Lr(),Pr("div",$x,M[22]||(M[22]=[B("span",{class:"icon is-large"},[B("i",{class:"fas fa-shopping-cart fa-2x"})],-1),B("p",{class:"mt-2"},"No orders found",-1),B("p",{class:"mt-2"},"Try adjusting your filters",-1)])))])]),B("div",{class:nt(["modal",{"is-active":l.value}])},[B("div",{class:"modal-background",onClick:z}),B("div",td,[B("header",{class:"modal-card-head"},[M[27]||(M[27]=B("p",{class:"modal-card-title"},"Update Order Status",-1)),B("button",{class:"delete","aria-label":"close",onClick:z})]),B("section",nd,[o.value?(Lr(),Pr("div",ad,[B("p",null,[M[28]||(M[28]=B("strong",null,"Order ID:",-1)),S0(" "+yr(o.value.id),1)]),B("p",null,[M[29]||(M[29]=B("strong",null,"Customer:",-1)),S0(" "+yr(o.value.userName),1)]),B("p",null,[M[30]||(M[30]=B("strong",null,"Current Status:",-1)),kn(ps,{status:o.value.status,type:"order"},null,8,["status"])]),B("div",id,[M[32]||(M[32]=B("label",{class:"label"},"New Status",-1)),B("div",sd,[B("div",fd,[Fr(B("select",{"onUpdate:modelValue":M[6]||(M[6]=oe=>c.value=oe)},M[31]||(M[31]=[Kt('<option value="pending" data-v-4a7e8186>Pending</option><option value="processing" data-v-4a7e8186>Processing</option><option value="shipped" data-v-4a7e8186>Shipped</option><option value="delivered" data-v-4a7e8186>Delivered</option><option value="cancelled" data-v-4a7e8186>Cancelled</option><option value="refunded" data-v-4a7e8186>Refunded</option>',6)]),512),[[Et,c.value]])])])]),B("div",od,[M[34]||(M[34]=B("label",{class:"label"},"Payment Status",-1)),B("div",ld,[B("div",cd,[Fr(B("select",{"onUpdate:modelValue":M[7]||(M[7]=oe=>d.value=oe)},M[33]||(M[33]=[B("option",{value:"pending"},"Pending",-1),B("option",{value:"paid"},"Paid",-1),B("option",{value:"failed"},"Failed",-1),B("option",{value:"refunded"},"Refunded",-1)]),512),[[Et,d.value]])])])]),B("div",hd,[M[35]||(M[35]=B("label",{class:"label"},"Note (Optional)",-1)),B("div",ud,[Fr(B("textarea",{class:"textarea","onUpdate:modelValue":M[8]||(M[8]=oe=>u.value=oe),placeholder:"Add a note about this status change"},"                ",512),[[$t,u.value]])])])])):w0("",!0)]),B("footer",xd,[B("button",{class:nt(["button is-primary",{"is-loading":p.value}]),onClick:te}," Update Status ",2),B("button",{class:"button",onClick:z},"Cancel")])])],2)])}}},Td=os(dd,[["__scopeId","data-v-4a7e8186"]]);export{Td as default};
