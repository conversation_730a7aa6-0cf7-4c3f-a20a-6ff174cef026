import{_ as _s,g as u,z as hs,h as X,f as ys,i as gs,c as n,a as s,b as f,w as _,r as bs,d as D,t as a,k as d,n as c,F as g,p as b,A as m,B as h,C as ws,H as ks,e as Cs,o as i}from"./index-DMg5qKr1.js";import{u as I}from"./users-BrKe_L0p.js";/* empty css                                                                    */import{C as Ns}from"./ConfirmDialog-D-ERnN1w.js";const Us={class:"user-detail"},As={class:"level"},Ss={class:"level-right"},Rs={class:"level-item"},xs={key:0,class:"has-text-centered py-6"},Ds={key:1,class:"notification is-danger"},Is={key:2,class:"notification is-warning"},Ms={key:3},Es={class:"card mb-4"},qs={class:"card-content"},Os={class:"columns"},Vs={class:"column is-8"},Ls={class:"user-title"},Bs={class:"user-subtitle"},Ps={class:"columns"},Fs={class:"column is-4"},$s={class:"card"},Ts={class:"card-content"},js={class:"user-avatar"},zs=["src","alt"],Gs={class:"info-group"},Hs={class:"info-value"},Js={class:"info-group"},Ks={class:"info-value"},Qs={class:"info-group"},Ws={class:"info-value"},Xs={class:"info-group"},Ys={class:"info-value"},Zs={key:0,class:"info-group"},se={class:"info-value"},ee={class:"tag is-success"},te={key:1,class:"info-group"},le={class:"info-value"},ae={class:"tag is-success"},oe={key:0},ne={key:0,class:"card mt-4"},ie={class:"card-content"},re={class:"columns is-multiline"},de={class:"column is-6"},ue={class:"info-group"},ce={class:"info-value"},ve={class:"column is-6"},pe={class:"info-group"},fe={class:"info-value"},me={class:"column is-6"},_e={class:"info-group"},he={class:"info-value"},ye={class:"column is-6"},ge={class:"info-group"},be={class:"info-value"},we={key:0,class:"column is-6"},ke={class:"info-group"},Ce={class:"info-value"},Ne={class:"column is-6"},Ue={class:"info-group"},Ae={class:"info-value"},Se={class:"card mt-4"},Re={class:"card-content"},xe={class:"info-group"},De={class:"info-value"},Ie=["href"],Me={class:"info-group"},Ee={class:"info-value"},qe={class:"info-group"},Oe={class:"address"},Ve={key:0},Le={key:1},Be={key:2},Pe={key:3},Fe={key:4},$e={class:"column is-8"},Te={class:"card"},je={class:"card-header"},ze={class:"card-header-icon"},Ge={class:"card-content"},He={key:0,class:"has-text-centered py-4"},Je={key:1,class:"has-text-centered py-4"},Ke={key:2},Qe={class:"table-container"},We={class:"table is-fullwidth is-hoverable"},Xe={class:"tag is-info"},Ye={class:"buttons are-small"},Ze={key:0,class:"card mt-4"},st={class:"card-content"},et={class:"table-container"},tt={class:"table is-fullwidth is-hoverable"},lt={key:0},at={class:"buttons are-small"},ot={key:1,class:"card mt-4"},nt={class:"card-content"},it={class:"table-container"},rt={class:"table is-fullwidth is-hoverable"},dt={class:"stars"},ut={key:2,class:"card mt-4"},ct={class:"card-content"},vt={class:"columns"},pt={class:"column is-6"},ft={class:"info-group"},mt={class:"info-value"},_t={class:"column is-6"},ht={class:"info-group"},yt={class:"info-value"},gt={key:0,class:"column is-6"},bt={class:"info-group"},wt={class:"info-value"},kt={key:1,class:"column is-6"},Ct={class:"info-group"},Nt={class:"info-value"},Ut={key:2,class:"column is-12"},At={class:"info-group"},St={class:"info-value"},Rt={key:3,class:"card mt-4"},xt={class:"card-content"},Dt={class:"info-group"},It={class:"info-value"},Mt={class:"info-group"},Et={class:"info-value"},qt={class:"info-group"},Ot={class:"info-value"},Vt={class:"info-group"},Lt={class:"info-value"},Bt={class:"info-group"},Pt={class:"info-value"},Ft={class:"card mt-4"},$t={class:"card-content"},Tt={key:0,class:"has-text-centered py-4"},jt={key:1,class:"has-text-centered py-4"},zt={key:2,class:"activity-list"},Gt={class:"activity-icon"},Ht={class:"activity-content"},Jt={class:"activity-text"},Kt={class:"activity-date"},Qt={class:"modal-card"},Wt={class:"modal-card-body"},Xt={class:"field"},Yt={class:"control"},Zt={class:"field"},sl={class:"control"},el={class:"field"},tl={class:"control"},ll={class:"field"},al={class:"control"},ol={class:"field"},nl={class:"control"},il={class:"field"},rl={class:"control"},dl={class:"select is-fullwidth"},ul={class:"field"},cl={class:"control"},vl={class:"modal-card-foot"},pl=["disabled"],fl={__name:"UserDetail",setup(ml){const Y=ys(),Z=Cs(),C=u(!0),ss=u(!1),es=u(!1),y=u(null),t=u({}),N=u([]),U=u([]),A=u(!1),S=u(!1),r=hs({firstName:"",lastName:"",username:"",email:"",phone:"",role:"",password:""}),R=u(!1),M=X(()=>Y.params.id),E=X(()=>r.firstName&&r.lastName&&r.username&&r.email&&r.role),ts=async()=>{C.value=!0,y.value=null;try{const l=await I.getDetailedUserById(M.value);t.value=l,l.orders&&(N.value=l.orders),l.recentNotifications&&(U.value=l.recentNotifications.map(e=>({id:e.id,type:"notification",description:e.title,createdAt:e.createdAt})))}catch(l){console.error("Error fetching user:",l),y.value="Failed to load user data. Please try again."}finally{C.value=!1}},v=l=>l?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(l)):"",ls=l=>l?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(l)):"",x=l=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(l),as=l=>{if(!l)return"Unknown";let e;return typeof l=="string"?e=l:typeof l=="number"?e={0:"Buyer",1:"Seller",2:"Seller Owner",3:"Moderator",4:"Admin"}[l]||"Unknown":l&&typeof l=="object"?l.hasOwnProperty("name")?e=l.name:l.hasOwnProperty("value")?typeof l.value=="string"?e=l.value:typeof l.value=="number"?e={0:"Buyer",1:"Seller",2:"Seller Owner",3:"Moderator",4:"Admin"}[l.value]||"Unknown":e="Unknown":e="Unknown":e="Unknown",e.charAt(0).toUpperCase()+e.slice(1)},os=l=>{if(!l)return"is-light";let e;switch(typeof l=="string"?e=l:typeof l=="number"?e={0:"buyer",1:"seller",2:"sellerowner",3:"moderator",4:"admin"}[l]||"unknown":l&&typeof l=="object"?l.hasOwnProperty("name")?e=l.name:l.hasOwnProperty("value")?typeof l.value=="string"?e=l.value:typeof l.value=="number"?e={0:"buyer",1:"seller",2:"sellerowner",3:"moderator",4:"admin"}[l.value]||"unknown":e="unknown":e="unknown":e="unknown",typeof e=="string"?e.toLowerCase():"unknown"){case"admin":return"is-danger";case"seller":case"sellerowner":return"is-info";case"buyer":return"is-success";case"moderator":return"is-warning";default:return"is-light"}},ns=l=>{switch(l){case"login":return"fas fa-sign-in-alt";case"logout":return"fas fa-sign-out-alt";case"order":return"fas fa-shopping-cart";case"payment":return"fas fa-credit-card";case"profile":return"fas fa-user-edit";case"product":return"fas fa-box";default:return"fas fa-history"}},is=l=>{switch(l){case"login":return"has-text-success";case"logout":return"has-text-info";case"order":return"has-text-primary";case"payment":return"has-text-success";case"profile":return"has-text-warning";case"product":return"has-text-info";default:return"has-text-grey"}},rs=l=>{switch(l==null?void 0:l.toLowerCase()){case"pending":return"is-warning";case"processing":return"is-info";case"shipped":return"is-primary";case"delivered":return"is-success";case"cancelled":return"is-danger";default:return"is-light"}},ds=l=>{switch(l==null?void 0:l.toLowerCase()){case"pending":return"is-warning";case"approved":return"is-success";case"rejected":return"is-danger";default:return"is-light"}},us=l=>{l.target.src="https://via.placeholder.com/150?text=No+Image"},cs=()=>{r.id=t.value.id,r.firstName=t.value.firstName,r.lastName=t.value.lastName,r.username=t.value.username,r.email=t.value.email,r.phone=t.value.phone||"";let l="";t.value.roleString?l=t.value.roleString.toLowerCase():typeof t.value.role=="string"?l=t.value.role.toLowerCase():typeof t.value.role=="number"&&(l={0:"buyer",1:"seller",2:"sellerowner",3:"moderator",4:"admin"}[t.value.role]||"buyer"),r.role=l,r.password="",A.value=!0},w=()=>{A.value=!1},vs=async()=>{if(E.value){S.value=!0;try{const l={firstName:r.firstName,lastName:r.lastName,username:r.username,email:r.email,phone:r.phone,role:r.role};r.password&&(l.password=r.password);const e=await I.updateUser(r.id,l);if(e&&e.success)t.value={...t.value,...l},alert("User updated successfully"),w();else throw new Error("Failed to update user")}catch(l){console.error("Error updating user:",l),alert("Failed to update user. Please try again.")}finally{S.value=!1}}},ps=()=>{R.value=!0},fs=()=>{R.value=!1},ms=async()=>{try{await I.deleteUser(M.value),Z.push("/admin/users")}catch(l){console.error("Error deleting user:",l)}};return gs(()=>{ts()}),(l,e)=>{var k,q,O,V,L,B,P,F,$,T,j,z,G,H,J,K,Q;const p=bs("router-link");return i(),n("div",Us,[s("div",As,[e[9]||(e[9]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"User Details")])],-1)),s("div",Ss,[s("div",Rs,[f(p,{to:"/admin/users",class:"button is-light"},{default:_(()=>e[8]||(e[8]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Users",-1)])),_:1})])])]),C.value?(i(),n("div",xs,e[10]||(e[10]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading user details...",-1)]))):y.value?(i(),n("div",Ds,[s("button",{class:"delete",onClick:e[0]||(e[0]=o=>y.value=null)}),D(" "+a(y.value),1)])):t.value.id?(i(),n("div",Ms,[s("div",Es,[s("div",qs,[s("div",Os,[s("div",Vs,[s("h2",Ls,a(t.value.firstName)+" "+a(t.value.lastName),1),s("p",Bs,a(t.value.email),1)]),s("div",{class:"column is-4 has-text-right"},[s("div",{class:"buttons is-right"},[s("button",{class:"button is-primary",onClick:cs},e[13]||(e[13]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit",-1)])),s("button",{class:"button is-danger",onClick:ps},e[14]||(e[14]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete",-1)]))])])])])]),s("div",Ps,[s("div",Fs,[s("div",$s,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Profile")],-1)),s("div",Ts,[s("div",js,[s("img",{src:t.value.avatar||"https://via.placeholder.com/150",alt:`${t.value.firstName} ${t.value.lastName}`,onError:us},null,40,zs)]),s("div",Gs,[e[15]||(e[15]=s("h3",{class:"info-label"},"Username",-1)),s("p",Hs,a(t.value.username),1)]),s("div",Js,[e[16]||(e[16]=s("h3",{class:"info-label"},"Role",-1)),s("p",Ks,[s("span",{class:c(["tag",os(t.value.roleString||t.value.role)])},a(as(t.value.roleString||t.value.role)),3)])]),s("div",Qs,[e[17]||(e[17]=s("h3",{class:"info-label"},"Registered",-1)),s("p",Ws,a(v(t.value.createdAt)),1)]),s("div",Xs,[e[18]||(e[18]=s("h3",{class:"info-label"},"Last Login",-1)),s("p",Ys,a(t.value.lastSeenAt?v(t.value.lastSeenAt):"Never"),1)]),t.value.emailConfirmed?(i(),n("div",Zs,[e[20]||(e[20]=s("h3",{class:"info-label"},"Email Confirmed",-1)),s("p",se,[s("span",ee,[e[19]||(e[19]=s("span",{class:"icon"},[s("i",{class:"fas fa-check"})],-1)),s("span",null,a(v(t.value.emailConfirmedAt)),1)])])])):d("",!0),t.value.isApproved?(i(),n("div",te,[e[23]||(e[23]=s("h3",{class:"info-label"},"Approved",-1)),s("p",le,[s("span",ae,[e[21]||(e[21]=s("span",{class:"icon"},[s("i",{class:"fas fa-check"})],-1)),s("span",null,a(v(t.value.approvedAt)),1)]),e[22]||(e[22]=s("br",null,null,-1)),t.value.approvedByUsername?(i(),n("small",oe,"by "+a(t.value.approvedByUsername),1)):d("",!0)])])):d("",!0)])]),t.value.statistics?(i(),n("div",ne,[e[31]||(e[31]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Statistics")],-1)),s("div",ie,[s("div",re,[s("div",de,[s("div",ue,[e[25]||(e[25]=s("h3",{class:"info-label"},"Total Orders",-1)),s("p",ce,a(t.value.statistics.totalOrders),1)])]),s("div",ve,[s("div",pe,[e[26]||(e[26]=s("h3",{class:"info-label"},"Total Spent",-1)),s("p",fe,a(x(t.value.statistics.totalSpent)),1)])]),s("div",me,[s("div",_e,[e[27]||(e[27]=s("h3",{class:"info-label"},"Reviews Given",-1)),s("p",he,a(t.value.statistics.totalReviews),1)])]),s("div",ye,[s("div",ge,[e[28]||(e[28]=s("h3",{class:"info-label"},"Average Rating",-1)),s("p",be,a(t.value.statistics.averageRatingGiven.toFixed(1))+"/5",1)])]),t.value.statistics.totalCompanies>0?(i(),n("div",we,[s("div",ke,[e[29]||(e[29]=s("h3",{class:"info-label"},"Companies",-1)),s("p",Ce,a(t.value.statistics.totalCompanies),1)])])):d("",!0),s("div",Ne,[s("div",Ue,[e[30]||(e[30]=s("h3",{class:"info-label"},"Active Chats",-1)),s("p",Ae,a(t.value.statistics.activeChats),1)])])])])])):d("",!0),s("div",Se,[e[35]||(e[35]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Contact Information")],-1)),s("div",Re,[s("div",xe,[e[32]||(e[32]=s("h3",{class:"info-label"},"Email",-1)),s("p",De,[s("a",{href:`mailto:${t.value.email}`},a(t.value.email),9,Ie)])]),s("div",Me,[e[33]||(e[33]=s("h3",{class:"info-label"},"Phone",-1)),s("p",Ee,a(t.value.phone||"Not provided"),1)]),s("div",qe,[e[34]||(e[34]=s("h3",{class:"info-label"},"Address",-1)),s("address",Oe,[(k=t.value.address)!=null&&k.address1?(i(),n("p",Ve,a(t.value.address.address1),1)):d("",!0),(q=t.value.address)!=null&&q.address2?(i(),n("p",Le,a(t.value.address.address2),1)):d("",!0),(O=t.value.address)!=null&&O.city||(V=t.value.address)!=null&&V.state||(L=t.value.address)!=null&&L.postalCode?(i(),n("p",Be,a(((B=t.value.address)==null?void 0:B.city)||"")+" "+a((P=t.value.address)!=null&&P.city&&((F=t.value.address)!=null&&F.state)?",":"")+" "+a((($=t.value.address)==null?void 0:$.state)||"")+" "+a((T=t.value.address)!=null&&T.postalCode?t.value.address.postalCode:""),1)):d("",!0),(j=t.value.address)!=null&&j.country?(i(),n("p",Pe,a(t.value.address.country),1)):d("",!0),(z=t.value.address)!=null&&z.address1?d("",!0):(i(),n("p",Fe,"Not provided"))])])])])]),s("div",$e,[s("div",Te,[s("div",je,[e[37]||(e[37]=s("p",{class:"card-header-title"},"Orders",-1)),s("div",ze,[f(p,{to:`/admin/orders?userId=${t.value.id}`,class:"button is-small is-primary is-outlined"},{default:_(()=>e[36]||(e[36]=[s("span",null,"View All",-1),s("span",{class:"icon is-small"},[s("i",{class:"fas fa-arrow-right"})],-1)])),_:1},8,["to"])])]),s("div",Ge,[ss.value?(i(),n("div",He,e[38]||(e[38]=[s("span",{class:"icon"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"mt-2"},"Loading orders...",-1)]))):N.value.length?(i(),n("div",Ke,[s("div",Qe,[s("table",We,[e[41]||(e[41]=s("thead",null,[s("tr",null,[s("th",null,"Order ID"),s("th",null,"Date"),s("th",null,"Total"),s("th",null,"Status"),s("th",null,"Items"),s("th",null,"Actions")])],-1)),s("tbody",null,[(i(!0),n(g,null,b(N.value,o=>(i(),n("tr",{key:o.id},[s("td",null,a(o.id),1),s("td",null,a(v(o.createdAt)),1),s("td",null,a(x(o.totalAmount))+" "+a(o.currency),1),s("td",null,[s("span",{class:c(["tag",rs(o.status)])},a(o.status),3)]),s("td",null,[s("span",Xe,a(o.itemsCount)+" items ",1)]),s("td",null,[s("div",Ye,[f(p,{to:`/admin/orders/${o.id}`,class:"button is-info",title:"View"},{default:_(()=>e[40]||(e[40]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"])])])]))),128))])])])])):(i(),n("div",Je,e[39]||(e[39]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-shopping-cart fa-2x"})],-1),s("p",{class:"mt-2"},"No orders found for this user",-1)])))])]),t.value.companies&&t.value.companies.length>0?(i(),n("div",Ze,[e[45]||(e[45]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Companies")],-1)),s("div",st,[s("div",et,[s("table",tt,[e[44]||(e[44]=s("thead",null,[s("tr",null,[s("th",null,"Name"),s("th",null,"Role"),s("th",null,"Status"),s("th",null,"Created"),s("th",null,"Actions")])],-1)),s("tbody",null,[(i(!0),n(g,null,b(t.value.companies,o=>(i(),n("tr",{key:o.id},[s("td",null,[s("strong",null,a(o.name),1),e[42]||(e[42]=s("br",null,null,-1)),o.description?(i(),n("small",lt,a(o.description),1)):d("",!0)]),s("td",null,[s("span",{class:c(["tag",o.isOwner?"is-warning":"is-info"])},a(o.isOwner?"Owner":"Member"),3)]),s("td",null,[s("span",{class:c(["tag",o.isApproved?"is-success":"is-warning"])},a(o.isApproved?"Approved":"Pending"),3)]),s("td",null,a(v(o.approvedAt)),1),s("td",null,[s("div",at,[f(p,{to:`/admin/companies/${o.id}`,class:"button is-info",title:"View Company"},{default:_(()=>e[43]||(e[43]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"])])])]))),128))])])])])])):d("",!0),t.value.reviews&&t.value.reviews.length>0?(i(),n("div",ot,[e[48]||(e[48]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Recent Reviews")],-1)),s("div",nt,[s("div",it,[s("table",rt,[e[47]||(e[47]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Rating"),s("th",null,"Comment"),s("th",null,"Date")])],-1)),s("tbody",null,[(i(!0),n(g,null,b(t.value.reviews,o=>(i(),n("tr",{key:o.id},[s("td",null,[f(p,{to:`/admin/products/${o.productId}`},{default:_(()=>[D(a(o.productName),1)]),_:2},1032,["to"])]),s("td",null,[s("div",dt,[(i(),n(g,null,b(5,W=>s("span",{key:W,class:c(["star",{"is-active":W<=o.rating}])},e[46]||(e[46]=[s("i",{class:"fas fa-star"},null,-1)]),2)),64))])]),s("td",null,a(o.comment||"No comment"),1),s("td",null,a(v(o.createdAt)),1)]))),128))])])])])])):d("",!0),t.value.sellerRequest?(i(),n("div",ut,[e[54]||(e[54]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Seller Request")],-1)),s("div",ct,[s("div",vt,[s("div",pt,[s("div",ft,[e[49]||(e[49]=s("h3",{class:"info-label"},"Status",-1)),s("p",mt,[s("span",{class:c(["tag",ds(t.value.sellerRequest.status)])},a(t.value.sellerRequest.status),3)])])]),s("div",_t,[s("div",ht,[e[50]||(e[50]=s("h3",{class:"info-label"},"Requested",-1)),s("p",yt,a(v(t.value.sellerRequest.createdAt)),1)])]),t.value.sellerRequest.processedAt?(i(),n("div",gt,[s("div",bt,[e[51]||(e[51]=s("h3",{class:"info-label"},"Processed",-1)),s("p",wt,a(v(t.value.sellerRequest.processedAt)),1)])])):d("",!0),t.value.sellerRequest.processedByUsername?(i(),n("div",kt,[s("div",Ct,[e[52]||(e[52]=s("h3",{class:"info-label"},"Processed By",-1)),s("p",Nt,a(t.value.sellerRequest.processedByUsername),1)])])):d("",!0),t.value.sellerRequest.reason?(i(),n("div",Ut,[s("div",At,[e[53]||(e[53]=s("h3",{class:"info-label"},"Reason",-1)),s("p",St,a(t.value.sellerRequest.reason),1)])])):d("",!0)])])])):d("",!0),t.value.role==="seller"?(i(),n("div",Rt,[e[60]||(e[60]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Seller Information")],-1)),s("div",xt,[s("div",Dt,[e[55]||(e[55]=s("h3",{class:"info-label"},"Store Name",-1)),s("p",It,a(((G=t.value.sellerInfo)==null?void 0:G.storeName)||"Not set"),1)]),s("div",Mt,[e[56]||(e[56]=s("h3",{class:"info-label"},"Store Description",-1)),s("p",Et,a(((H=t.value.sellerInfo)==null?void 0:H.storeDescription)||"Not provided"),1)]),s("div",qt,[e[57]||(e[57]=s("h3",{class:"info-label"},"Products",-1)),s("p",Ot,a(((J=t.value.sellerInfo)==null?void 0:J.productCount)||0),1)]),s("div",Vt,[e[58]||(e[58]=s("h3",{class:"info-label"},"Sales",-1)),s("p",Lt,a(((K=t.value.sellerInfo)==null?void 0:K.salesCount)||0)+" orders",1)]),s("div",Bt,[e[59]||(e[59]=s("h3",{class:"info-label"},"Revenue",-1)),s("p",Pt,a(x(((Q=t.value.sellerInfo)==null?void 0:Q.totalRevenue)||0)),1)])])])):d("",!0),s("div",Ft,[e[63]||(e[63]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Activity Log")],-1)),s("div",$t,[es.value?(i(),n("div",Tt,e[61]||(e[61]=[s("span",{class:"icon"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"mt-2"},"Loading activity...",-1)]))):U.value.length?(i(),n("div",zt,[(i(!0),n(g,null,b(U.value,o=>(i(),n("div",{key:o.id,class:"activity-item"},[s("div",Gt,[s("span",{class:c(["icon",is(o.type)])},[s("i",{class:c(ns(o.type))},null,2)],2)]),s("div",Ht,[s("p",Jt,a(o.description),1),s("p",Kt,a(ls(o.createdAt)),1)])]))),128))])):(i(),n("div",jt,e[62]||(e[62]=[s("p",null,"No activity recorded for this user",-1)])))])])])])])):(i(),n("div",Is,[e[12]||(e[12]=s("p",null,"User not found.",-1)),f(p,{to:"/admin/users",class:"button is-primary mt-4"},{default:_(()=>e[11]||(e[11]=[D(" Back to Users ")])),_:1})])),s("div",{class:c(["modal",{"is-active":A.value}])},[s("div",{class:"modal-background",onClick:w}),s("div",Qt,[s("header",{class:"modal-card-head"},[e[64]||(e[64]=s("p",{class:"modal-card-title"},"Edit User",-1)),s("button",{class:"delete","aria-label":"close",onClick:w})]),s("section",Wt,[s("div",Xt,[e[65]||(e[65]=s("label",{class:"label"},"First Name*",-1)),s("div",Yt,[m(s("input",{class:"input",type:"text","onUpdate:modelValue":e[1]||(e[1]=o=>r.firstName=o),required:"",placeholder:"Enter first name"},null,512),[[h,r.firstName]])])]),s("div",Zt,[e[66]||(e[66]=s("label",{class:"label"},"Last Name*",-1)),s("div",sl,[m(s("input",{class:"input",type:"text","onUpdate:modelValue":e[2]||(e[2]=o=>r.lastName=o),required:"",placeholder:"Enter last name"},null,512),[[h,r.lastName]])])]),s("div",el,[e[67]||(e[67]=s("label",{class:"label"},"Username*",-1)),s("div",tl,[m(s("input",{class:"input",type:"text","onUpdate:modelValue":e[3]||(e[3]=o=>r.username=o),required:"",placeholder:"Enter username"},null,512),[[h,r.username]])])]),s("div",ll,[e[68]||(e[68]=s("label",{class:"label"},"Email*",-1)),s("div",al,[m(s("input",{class:"input",type:"email","onUpdate:modelValue":e[4]||(e[4]=o=>r.email=o),required:"",placeholder:"Enter email"},null,512),[[h,r.email]])])]),s("div",ol,[e[69]||(e[69]=s("label",{class:"label"},"Phone",-1)),s("div",nl,[m(s("input",{class:"input",type:"tel","onUpdate:modelValue":e[5]||(e[5]=o=>r.phone=o),placeholder:"Enter phone number"},null,512),[[h,r.phone]])])]),s("div",il,[e[71]||(e[71]=s("label",{class:"label"},"Role*",-1)),s("div",rl,[s("div",dl,[m(s("select",{"onUpdate:modelValue":e[6]||(e[6]=o=>r.role=o),required:""},e[70]||(e[70]=[ks('<option value="buyer" data-v-4b38c374>Buyer</option><option value="seller" data-v-4b38c374>Seller</option><option value="sellerowner" data-v-4b38c374>Seller Owner</option><option value="moderator" data-v-4b38c374>Moderator</option><option value="admin" data-v-4b38c374>Admin</option>',5)]),512),[[ws,r.role]])])])]),s("div",ul,[e[72]||(e[72]=s("label",{class:"label"},"New Password",-1)),s("div",cl,[m(s("input",{class:"input",type:"password","onUpdate:modelValue":e[7]||(e[7]=o=>r.password=o),placeholder:"Leave blank to keep current password"},null,512),[[h,r.password]])])])]),s("footer",vl,[s("button",{class:c(["button is-primary",{"is-loading":S.value}]),onClick:vs,disabled:!E.value}," Update User ",10,pl),s("button",{class:"button",onClick:w},"Cancel")])])],2),f(Ns,{"is-open":R.value,title:"Delete User",message:`Are you sure you want to delete ${t.value.firstName} ${t.value.lastName}? This action cannot be undone.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:ms,onCancel:fs},null,8,["is-open","message"])])}}},bl=_s(fl,[["__scopeId","data-v-4b38c374"]]);export{bl as default};
