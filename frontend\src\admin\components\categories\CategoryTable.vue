<template>
  <div class="category-table">
    <div class="table-container">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Slug</th>
            <th>Description</th>
            <th>Products</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && categories.length > 0">
          <tr v-for="category in categories" :key="category.id">
            <td>{{ category.id }}</td>
            <td>{{ category.name }}</td>
            <td>{{ category.slug }}</td>
            <td>{{ truncateText(category.description, 50) }}</td>
            <td>{{ category.productCount }}</td>
            <td>
              <div class="buttons are-small">
                <button 
                  class="button is-primary" 
                  @click="$emit('edit', category)">
                  <span class="icon"><i class="fas fa-edit"></i></span>
                </button>
                <button 
                  class="button is-danger" 
                  @click="$emit('delete', category)"
                  :disabled="category.productCount > 0">
                  <span class="icon"><i class="fas fa-trash"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="has-text-centered">
              No categories found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
defineProps({
  categories: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['edit', 'delete']);

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};
</script>

<style scoped>
.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}
</style>
