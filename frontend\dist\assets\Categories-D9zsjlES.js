import{_ as T,c as n,o as a,a as s,F as U,p as V,k,t as f,n as S,g as b,h as M,r as ns,y as W,s as ds,b as E,z as rs,x as H,m as cs,A as P,B as I,C as F,i as us,H as K,d as vs,K as gs,I as A}from"./index-DMg5qKr1.js";import{C as ps}from"./ConfirmDialog-D-ERnN1w.js";const ms={class:"category-table"},hs={class:"table-container"},fs={class:"table is-fullwidth is-striped"},ys={key:0},bs={key:0,class:"image is-48x48"},$s=["src","alt"],_s={key:1,class:"icon is-large has-text-grey-light"},Cs={class:"content"},ks={class:"title is-6"},ws={class:"has-text-grey"},Ps={key:0,class:"is-size-7 has-text-grey mt-1"},xs={class:"tags"},Is={key:0,class:"tag is-primary is-small"},Ss={key:1,class:"tag is-info is-small"},Ds={class:"level is-mobile"},Es={class:"level-left"},Ms={class:"level-item"},Ts={key:0,class:"level-right"},Us={class:"level-item"},Vs=["onClick"],As={class:"tags"},Os={key:0,class:"tag is-success is-small",title:"Has Meta Title"},Hs={key:1,class:"tag is-info is-small",title:"Has Meta Description"},Fs={key:2,class:"tag is-warning is-small",title:"Has Meta Image"},Ns={key:3,class:"tag is-light is-small"},Bs={class:"buttons are-small"},Rs=["onClick"],zs=["onClick","disabled"],Ls={key:1},qs={key:2},js={__name:"CategoryTable",props:{categories:{type:Array,required:!0},loading:{type:Boolean,default:!1}},emits:["edit","delete","view-products"],setup(v){const $=(u,t)=>u?u.length>t?u.substring(0,t)+"...":u:"",g=u=>!u||u===0?"is-light":u<5?"is-warning":u<20?"is-info":"is-success";return(u,t)=>(a(),n("div",ms,[s("div",hs,[s("table",fs,[t[14]||(t[14]=s("thead",null,[s("tr",null,[s("th",{width:"60"},"Image"),s("th",null,"Category"),s("th",null,"Hierarchy"),s("th",null,"Products"),s("th",null,"SEO"),s("th",{width:"120"},"Actions")])],-1)),!v.loading&&v.categories.length>0?(a(),n("tbody",ys,[(a(!0),n(U,null,V(v.categories,r=>(a(),n("tr",{key:r.id,class:"category-row"},[s("td",null,[r.image?(a(),n("figure",bs,[s("img",{src:r.image,alt:r.name,class:"is-rounded"},null,8,$s)])):(a(),n("span",_s,t[0]||(t[0]=[s("i",{class:"fas fa-folder fa-2x"},null,-1)])))]),s("td",null,[s("div",Cs,[s("div",null,[s("strong",ks,f(r.name),1),t[1]||(t[1]=s("br",null,null,-1)),s("small",ws,f(r.slug),1),t[2]||(t[2]=s("br",null,null,-1)),r.description?(a(),n("p",Ps,f($(r.description,80)),1)):k("",!0)])])]),s("td",null,[s("div",xs,[r.parentId?(a(),n("span",Ss,t[4]||(t[4]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-sitemap"})],-1),s("span",null,"Child",-1)]))):(a(),n("span",Is,t[3]||(t[3]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-layer-group"})],-1),s("span",null,"Root",-1)])))])]),s("td",null,[s("div",Ds,[s("div",Es,[s("div",Ms,[s("span",{class:S(["tag is-medium",g(r.productCount)])},[t[5]||(t[5]=s("span",{class:"icon is-small"},[s("i",{class:"fas fa-cube"})],-1)),s("span",null,f(r.productCount||0),1)],2)])]),r.productCount>0?(a(),n("div",Ts,[s("div",Us,[s("button",{class:"button is-small is-text",onClick:_=>u.$emit("view-products",r),title:"View products"},t[6]||(t[6]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-external-link-alt"})],-1)]),8,Vs)])])):k("",!0)])]),s("td",null,[s("div",As,[r.metaTitle?(a(),n("span",Os,t[7]||(t[7]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-heading"})],-1)]))):k("",!0),r.metaDescription?(a(),n("span",Hs,t[8]||(t[8]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-align-left"})],-1)]))):k("",!0),r.metaImage?(a(),n("span",Fs,t[9]||(t[9]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-image"})],-1)]))):k("",!0),!r.metaTitle&&!r.metaDescription&&!r.metaImage?(a(),n("span",Ns," No SEO ")):k("",!0)])]),s("td",null,[s("div",Bs,[s("button",{class:"button is-primary is-small",onClick:_=>u.$emit("edit",r),title:"Edit category"},t[10]||(t[10]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})],-1)]),8,Rs),s("button",{class:"button is-danger is-small",onClick:_=>u.$emit("delete",r),disabled:r.productCount>0,title:"Delete category"},t[11]||(t[11]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-trash"})],-1)]),8,zs)])])]))),128))])):v.loading?(a(),n("tbody",Ls,t[12]||(t[12]=[s("tr",null,[s("td",{colspan:"6",class:"has-text-centered"},[s("div",{class:"loader-wrapper"},[s("div",{class:"loader is-loading"})])])],-1)]))):(a(),n("tbody",qs,t[13]||(t[13]=[s("tr",null,[s("td",{colspan:"6",class:"has-text-centered"}," No categories found. ")],-1)])))])])]))}},Ws=T(js,[["__scopeId","data-v-c5170ae1"]]),Ks={class:"card-content py-3"},Qs={class:"level is-mobile"},Gs={class:"level-left"},Js={class:"level-item"},Xs={class:"media"},Ys={key:0,class:"media-left"},Zs={class:"image is-48x48"},se=["src","alt"],ee={key:1,class:"media-left"},te={class:"media-content"},le={class:"content"},ae={class:"level is-mobile"},ie={class:"level-left"},oe={class:"level-item"},ne={class:"title is-6"},de={class:"has-text-grey"},re={class:"level-right"},ce={class:"level-item"},ue={class:"tags"},ve={key:0,class:"tag is-primary is-small"},ge={key:1,class:"tag is-info is-small"},pe={key:2,class:"tag is-success is-small"},me={key:0,class:"is-size-7 has-text-grey mt-2"},he={class:"level-right"},fe={class:"level-item"},ye={class:"buttons are-small"},be={key:0,class:"children"},$e={key:1,class:"has-text-centered mb-3"},_e={class:"icon is-small"},Ce={__name:"CategoryHierarchyItem",props:{category:{type:Object,required:!0},level:{type:Number,default:0}},emits:["edit","delete","add-child"],setup(v){const $=v,g=b($.level<2),u=M(()=>$.category.children&&$.category.children.length>0),t=()=>{g.value=!g.value},r=p=>!p||p===0?"is-light":p<5?"is-warning":p<20?"is-info":"is-success",_=(p,d)=>p?p.length>d?p.substring(0,d)+"...":p:"";return(p,d)=>{const w=ns("CategoryHierarchyItem",!0);return a(),n("div",{class:S(["hierarchy-item",{"has-children":u.value}])},[s("div",{class:"card mb-3",style:W({marginLeft:`${v.level*2}rem`})},[s("div",Ks,[s("div",Qs,[s("div",Gs,[s("div",Js,[s("div",Xs,[v.category.image?(a(),n("div",Ys,[s("figure",Zs,[s("img",{src:v.category.image,alt:v.category.name,class:"is-rounded"},null,8,se)])])):(a(),n("div",ee,d[6]||(d[6]=[s("span",{class:"icon is-large has-text-grey-light"},[s("i",{class:"fas fa-folder fa-2x"})],-1)]))),s("div",te,[s("div",le,[s("div",ae,[s("div",ie,[s("div",oe,[s("div",null,[s("strong",ne,f(v.category.name),1),d[8]||(d[8]=s("br",null,null,-1)),s("small",de,f(v.category.slug),1),d[9]||(d[9]=s("br",null,null,-1)),s("span",{class:S(["tag is-small",r(v.category.productCount)])},[d[7]||(d[7]=s("span",{class:"icon is-small"},[s("i",{class:"fas fa-cube"})],-1)),s("span",null,f(v.category.productCount||0)+" products",1)],2)])])]),s("div",re,[s("div",ce,[s("div",ue,[v.level===0?(a(),n("span",ve,"Root")):(a(),n("span",ge,"Level "+f(v.level+1),1)),u.value?(a(),n("span",pe,f(v.category.children.length)+" children ",1)):k("",!0)])])])]),v.category.description?(a(),n("p",me,f(_(v.category.description,100)),1)):k("",!0)])])])])]),s("div",he,[s("div",fe,[s("div",ye,[s("button",{class:"button is-primary is-small",onClick:d[0]||(d[0]=C=>p.$emit("edit",v.category)),title:"Edit category"},d[10]||(d[10]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})],-1)])),s("button",{class:"button is-success is-small",onClick:d[1]||(d[1]=C=>p.$emit("add-child",v.category)),title:"Add subcategory"},d[11]||(d[11]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-plus"})],-1)])),s("button",{class:"button is-danger is-small",onClick:d[2]||(d[2]=C=>p.$emit("delete",v.category)),title:"Delete category"},d[12]||(d[12]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-trash"})],-1)]))])])])])])],4),u.value&&g.value?(a(),n("div",be,[(a(!0),n(U,null,V(v.category.children,C=>(a(),ds(w,{key:C.id,category:C,level:v.level+1,onEdit:d[3]||(d[3]=c=>p.$emit("edit",c)),onDelete:d[4]||(d[4]=c=>p.$emit("delete",c)),onAddChild:d[5]||(d[5]=c=>p.$emit("add-child",c))},null,8,["category","level"]))),128))])):k("",!0),u.value?(a(),n("div",$e,[s("button",{class:"button is-small is-light",onClick:t,style:W({marginLeft:`${v.level*2}rem`})},[s("span",_e,[s("i",{class:S(["fas",g.value?"fa-chevron-up":"fa-chevron-down"])},null,2)]),s("span",null,f(g.value?"Collapse":"Expand")+" ("+f(v.category.children.length)+")",1)],4)])):k("",!0)],2)}}},ke=T(Ce,[["__scopeId","data-v-d83b7fd2"]]),we={class:"category-hierarchy"},Pe={__name:"CategoryHierarchy",props:{categories:{type:Array,default:()=>[]}},emits:["edit","delete","add-child"],setup(v){return($,g)=>(a(),n("div",we,[(a(!0),n(U,null,V(v.categories,u=>(a(),n("div",{key:u.id,class:"hierarchy-item root"},[E(ke,{category:u,level:0,onEdit:g[0]||(g[0]=t=>$.$emit("edit",t)),onDelete:g[1]||(g[1]=t=>$.$emit("delete",t)),onAddChild:g[2]||(g[2]=t=>$.$emit("add-child",t))},null,8,["category"])]))),128))]))}},xe=T(Pe,[["__scopeId","data-v-6e2f33fd"]]),Ie={class:"modal-card"},Se={class:"modal-card-head"},De={class:"modal-card-title"},Ee={class:"modal-card-body"},Me={class:"field"},Te={class:"control"},Ue={class:"field"},Ve={class:"control"},Ae={class:"field"},Oe={class:"control"},He={class:"field"},Fe={class:"control"},Ne={class:"select is-fullwidth"},Be=["value","disabled"],Re={class:"field"},ze={class:"control"},Le={class:"field"},qe={class:"control"},je={class:"modal-card-foot"},We=["disabled"],Ke={key:0},Qe={key:1},Ge={__name:"CategoryFormModal",props:{isOpen:{type:Boolean,required:!0},category:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","save"],setup(v,{emit:$}){const g=v,u=$,t=rs({id:null,name:"",slug:"",description:"",parentId:"",imageUrl:"",displayOrder:0}),r=b(!1),_=M(()=>g.categories.filter(c=>c.id!==t.id)),p=()=>{var c;t.name&&(!t.slug||t.slug===d(((c=g.category)==null?void 0:c.name)||""))&&(t.slug=d(t.name))},d=c=>c.toString().toLowerCase().trim().replace(/\s+/g,"-").replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),w=async()=>{r.value=!0;try{const c={...t};c.displayOrder=parseInt(c.displayOrder),c.parentId===""&&(c.parentId=null),u("save",c)}catch(c){console.error("Error submitting form:",c)}finally{r.value=!1}},C=()=>{t.id=null,t.name="",t.slug="",t.description="",t.parentId="",t.imageUrl="",t.displayOrder=0};return H(()=>g.category,c=>{c?Object.keys(t).forEach(l=>{l in c&&(t[l]=c[l])}):C()},{immediate:!0}),H(()=>g.isOpen,c=>{c||C()}),(c,l)=>(a(),n("div",{class:S(["modal",{"is-active":v.isOpen}])},[s("div",{class:"modal-background",onClick:l[0]||(l[0]=m=>c.$emit("close"))}),s("div",Ie,[s("header",Se,[s("p",De,f(v.category?"Edit Category":"Add Category"),1),s("button",{class:"delete","aria-label":"close",onClick:l[1]||(l[1]=m=>c.$emit("close"))})]),s("section",Ee,[s("form",{onSubmit:cs(w,["prevent"])},[s("div",Me,[l[9]||(l[9]=s("label",{class:"label"},"Name",-1)),s("div",Te,[P(s("input",{class:"input",type:"text",placeholder:"Category name","onUpdate:modelValue":l[2]||(l[2]=m=>t.name=m),onInput:p,required:""},null,544),[[I,t.name]])])]),s("div",Ue,[l[10]||(l[10]=s("label",{class:"label"},"Slug",-1)),s("div",Ve,[P(s("input",{class:"input",type:"text",placeholder:"category-slug","onUpdate:modelValue":l[3]||(l[3]=m=>t.slug=m),required:""},null,512),[[I,t.slug]])]),l[11]||(l[11]=s("p",{class:"help"},"URL-friendly version of the name. Auto-generated but can be edited.",-1))]),s("div",Ae,[l[12]||(l[12]=s("label",{class:"label"},"Description",-1)),s("div",Oe,[P(s("textarea",{class:"textarea",placeholder:"Category description","onUpdate:modelValue":l[4]||(l[4]=m=>t.description=m),rows:"3"},null,512),[[I,t.description]])])]),s("div",He,[l[14]||(l[14]=s("label",{class:"label"},"Parent Category",-1)),s("div",Fe,[s("div",Ne,[P(s("select",{"onUpdate:modelValue":l[5]||(l[5]=m=>t.parentId=m)},[l[13]||(l[13]=s("option",{value:""},"None (Top Level)",-1)),(a(!0),n(U,null,V(_.value,m=>(a(),n("option",{key:m.id,value:m.id,disabled:m.id===t.id},f(m.name),9,Be))),128))],512),[[F,t.parentId]])])])]),s("div",Re,[l[15]||(l[15]=s("label",{class:"label"},"Image URL",-1)),s("div",ze,[P(s("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":l[6]||(l[6]=m=>t.imageUrl=m)},null,512),[[I,t.imageUrl]])])]),s("div",Le,[l[16]||(l[16]=s("label",{class:"label"},"Display Order",-1)),s("div",qe,[P(s("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":l[7]||(l[7]=m=>t.displayOrder=m)},null,512),[[I,t.displayOrder,void 0,{number:!0}]])]),l[17]||(l[17]=s("p",{class:"help"},"Categories with lower numbers will be displayed first.",-1))])],32)]),s("footer",je,[s("button",{class:"button is-primary",onClick:w,disabled:r.value},[r.value?(a(),n("span",Ke,l[18]||(l[18]=[s("span",{class:"icon"},[s("i",{class:"fas fa-spinner fa-spin"})],-1),s("span",null,"Saving...",-1)]))):(a(),n("span",Qe,"Save"))],8,We),s("button",{class:"button",onClick:l[8]||(l[8]=m=>c.$emit("close"))},"Cancel")])])],2))}},Je=T(Ge,[["__scopeId","data-v-1407cc1b"]]),Xe={class:"categories-page"},Ye={class:"columns mb-5"},Ze={class:"column is-3"},st={class:"card has-background-primary-light"},et={class:"card-content"},tt={class:"level"},lt={class:"level-left"},at={class:"level-item"},it={class:"title is-4 has-text-primary"},ot={class:"column is-3"},nt={class:"card has-background-success-light"},dt={class:"card-content"},rt={class:"level"},ct={class:"level-left"},ut={class:"level-item"},vt={class:"title is-4 has-text-success"},gt={class:"column is-3"},pt={class:"card has-background-info-light"},mt={class:"card-content"},ht={class:"level"},ft={class:"level-left"},yt={class:"level-item"},bt={class:"title is-4 has-text-info"},$t={class:"column is-3"},_t={class:"card has-background-warning-light"},Ct={class:"card-content"},kt={class:"level"},wt={class:"level-left"},Pt={class:"level-item"},xt={class:"title is-4 has-text-warning"},It={class:"card mb-5"},St={class:"card-content"},Dt={class:"columns"},Et={class:"column is-6"},Mt={class:"field"},Tt={class:"control has-icons-left"},Ut={class:"column is-3"},Vt={class:"field"},At={class:"control"},Ot={class:"select is-fullwidth"},Ht={class:"column is-3"},Ft={class:"field"},Nt={class:"control"},Bt={class:"select is-fullwidth"},Rt={class:"level"},zt={class:"level-left"},Lt={class:"level-item"},qt={class:"field is-grouped"},jt={class:"control"},Wt={class:"checkbox"},Kt={class:"card"},Qt={class:"card-content"},Gt={key:0,class:"has-text-centered py-6"},Jt={key:1,class:"notification is-danger"},Xt={key:2},Yt={key:0,class:"hierarchy-view"},Zt={key:1,class:"table-view"},sl={key:0,class:"pagination is-centered mt-5",role:"navigation"},el=["disabled"],tl=["disabled"],ll={class:"pagination-list"},al=["onClick"],il={key:1,class:"pagination-ellipsis"},ol={__name:"Categories",setup(v){const $=b(!1),g=b(""),u=b([]),t=b([]),r=b(null),_=b(null),p=b(!1),d=b(!1),w=b(!1),C=b(!1),c=b(""),l=b({type:"",parentId:null}),m=b("name"),N=b("asc"),y=b({currentPage:1,totalPages:1,totalItems:0,perPage:20}),D=b({total:0,rootCategories:0,withProducts:0,totalProducts:0}),Q=M(()=>u.value.filter(i=>!i.parentId)),G=M(()=>{const i=y.value.currentPage,e=y.value.totalPages,o=[];if(e<=7)for(let h=1;h<=e;h++)o.push(h);else if(i<=4){for(let h=1;h<=5;h++)o.push(h);o.push("..."),o.push(e)}else if(i>=e-3){o.push(1),o.push("...");for(let h=e-4;h<=e;h++)o.push(h)}else{o.push(1),o.push("...");for(let h=i-1;h<=i+1;h++)o.push(h);o.push("..."),o.push(e)}return o}),J=M(()=>{if(!_.value)return"";const i=u.value.some(h=>h.parentId===_.value.id),e=_.value.productCount>0;let o=`Are you sure you want to delete '${_.value.name}'?`;return i&&(o+=`

This category has subcategories that will also be affected.`),e&&(o+=`

This category contains ${_.value.productCount} products.`),o}),x=async()=>{try{$.value=!0,g.value="";const i={filter:c.value||void 0,orderBy:m.value,descending:N.value==="desc",page:y.value.currentPage,pageSize:y.value.perPage};if(l.value.type)switch(l.value.type){case"root":i.parentId=null;break;case"child":i.hasParent=!0;break;case"with-products":i.hasProducts=!0;break;case"empty":i.hasProducts=!1;break}const e=await A.getAll(i);u.value=e.data||[],y.value={currentPage:e.currentPage||1,totalPages:e.totalPages||1,totalItems:e.totalItems||0,perPage:e.pageSize||20},C.value&&B(),X()}catch(i){console.error("Error fetching categories:",i),g.value="Failed to load categories. Please try again."}finally{$.value=!1}},B=()=>{const i=new Map,e=[];u.value.forEach(o=>{i.set(o.id,{...o,children:[]})}),u.value.forEach(o=>{if(o.parentId){const h=i.get(o.parentId);h&&h.children.push(i.get(o.id))}else e.push(i.get(o.id))}),t.value=e},X=()=>{D.value={total:u.value.length,rootCategories:u.value.filter(i=>!i.parentId).length,withProducts:u.value.filter(i=>i.productCount>0).length,totalProducts:u.value.reduce((i,e)=>i+(e.productCount||0),0)}},R=()=>{y.value.currentPage=1,x()},Y=()=>{y.value.currentPage=1,x()},Z=()=>{y.value.currentPage=1,x()},ss=()=>{c.value="",l.value={type:"",parentId:null},m.value="name",N.value="asc",y.value.currentPage=1,x()},es=()=>{C.value&&B()},O=i=>{i>=1&&i<=y.value.totalPages&&(y.value.currentPage=i,x())},ts=()=>{r.value=null,w.value=!1,p.value=!0},ls=i=>{r.value={parentId:i.id},w.value=!1,p.value=!0},z=i=>{r.value={...i},w.value=!0,p.value=!0},L=()=>{p.value=!1,r.value=null},q=i=>{_.value=i,d.value=!0},j=()=>{d.value=!1,_.value=null},as=async i=>{try{w.value?await A.update(r.value.id,i):await A.create(i),L(),x()}catch(e){console.error("Error saving category:",e),g.value="Failed to save category. Please try again."}},is=async()=>{try{await A.delete(_.value.id),j(),x()}catch(i){console.error("Error deleting category:",i),g.value="Failed to delete category. Please try again."}},os=i=>{window.location.href=`/admin/products?categoryId=${i.id}`};return H([c,l,m],()=>{clearTimeout(window.searchTimeout),window.searchTimeout=setTimeout(()=>{R()},300)},{deep:!0}),us(()=>{x()}),(i,e)=>(a(),n("div",Xe,[s("div",{class:"hero is-light is-bold mb-6"},[s("div",{class:"hero-body py-4"},[s("div",{class:"level"},[e[9]||(e[9]=K('<div class="level-left" data-v-d1296298><div class="level-item" data-v-d1296298><div data-v-d1296298><h1 class="title is-2 has-text-dark" data-v-d1296298><span class="icon-text" data-v-d1296298><span class="icon has-text-primary" data-v-d1296298><i class="fas fa-sitemap" data-v-d1296298></i></span><span data-v-d1296298>Categories Management</span></span></h1><p class="subtitle is-5 has-text-grey" data-v-d1296298> Manage product categories and their hierarchy </p></div></div></div>',1)),s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("button",{class:"button is-primary is-medium",onClick:ts},e[8]||(e[8]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Category",-1)]))])])])])]),s("div",Ye,[s("div",Ze,[s("div",st,[s("div",et,[s("div",tt,[s("div",lt,[s("div",at,[s("div",null,[s("p",it,f(D.value.total),1),e[10]||(e[10]=s("p",{class:"subtitle is-6 has-text-grey"},"Total Categories",-1))])])]),e[11]||(e[11]=s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("span",{class:"icon is-large has-text-primary"},[s("i",{class:"fas fa-sitemap fa-2x"})])])],-1))])])])]),s("div",ot,[s("div",nt,[s("div",dt,[s("div",rt,[s("div",ct,[s("div",ut,[s("div",null,[s("p",vt,f(D.value.rootCategories),1),e[12]||(e[12]=s("p",{class:"subtitle is-6 has-text-grey"},"Root Categories",-1))])])]),e[13]||(e[13]=s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("span",{class:"icon is-large has-text-success"},[s("i",{class:"fas fa-layer-group fa-2x"})])])],-1))])])])]),s("div",gt,[s("div",pt,[s("div",mt,[s("div",ht,[s("div",ft,[s("div",yt,[s("div",null,[s("p",bt,f(D.value.withProducts),1),e[14]||(e[14]=s("p",{class:"subtitle is-6 has-text-grey"},"With Products",-1))])])]),e[15]||(e[15]=s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("span",{class:"icon is-large has-text-info"},[s("i",{class:"fas fa-boxes fa-2x"})])])],-1))])])])]),s("div",$t,[s("div",_t,[s("div",Ct,[s("div",kt,[s("div",wt,[s("div",Pt,[s("div",null,[s("p",xt,f(D.value.totalProducts),1),e[16]||(e[16]=s("p",{class:"subtitle is-6 has-text-grey"},"Total Products",-1))])])]),e[17]||(e[17]=s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("span",{class:"icon is-large has-text-warning"},[s("i",{class:"fas fa-cube fa-2x"})])])],-1))])])])])]),s("div",It,[s("div",St,[s("div",Dt,[s("div",Et,[s("div",Mt,[e[19]||(e[19]=s("label",{class:"label"},"Search Categories",-1)),s("div",Tt,[P(s("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>c.value=o),class:"input",type:"text",placeholder:"Search by name, slug, or description...",onInput:R},null,544),[[I,c.value]]),e[18]||(e[18]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-search"})],-1))])])]),s("div",Ut,[s("div",Vt,[e[21]||(e[21]=s("label",{class:"label"},"Filter by Type",-1)),s("div",At,[s("div",Ot,[P(s("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>l.value.type=o),onChange:Y},e[20]||(e[20]=[K('<option value="" data-v-d1296298>All Categories</option><option value="root" data-v-d1296298>Root Categories</option><option value="child" data-v-d1296298>Child Categories</option><option value="with-products" data-v-d1296298>With Products</option><option value="empty" data-v-d1296298>Empty Categories</option>',5)]),544),[[F,l.value.type]])])])])]),s("div",Ht,[s("div",Ft,[e[23]||(e[23]=s("label",{class:"label"},"Sort by",-1)),s("div",Nt,[s("div",Bt,[P(s("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>m.value=o),onChange:Z},e[22]||(e[22]=[s("option",{value:"name"},"Name",-1),s("option",{value:"productCount"},"Product Count",-1),s("option",{value:"createdAt"},"Created Date",-1)]),544),[[F,m.value]])])])])])]),s("div",Rt,[s("div",zt,[s("div",Lt,[s("div",qt,[s("div",jt,[s("label",Wt,[P(s("input",{type:"checkbox","onUpdate:modelValue":e[3]||(e[3]=o=>C.value=o),onChange:es},null,544),[[gs,C.value]]),e[24]||(e[24]=vs(" Show Hierarchy View "))])])])])]),s("div",{class:"level-right"},[s("div",{class:"level-item"},[s("button",{class:"button is-light",onClick:ss},e[25]||(e[25]=[s("span",{class:"icon"},[s("i",{class:"fas fa-undo"})],-1),s("span",null,"Reset Filters",-1)]))])])])])]),s("div",Kt,[s("div",Qt,[$.value?(a(),n("div",Gt,e[26]||(e[26]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading categories...",-1)]))):g.value?(a(),n("div",Jt,[s("p",null,f(g.value),1),s("button",{class:"button is-light mt-2",onClick:x},e[27]||(e[27]=[s("span",{class:"icon"},[s("i",{class:"fas fa-redo"})],-1),s("span",null,"Retry",-1)]))])):(a(),n("div",Xt,[C.value?(a(),n("div",Yt,[E(xe,{categories:t.value,onEdit:z,onDelete:q,onAddChild:ls},null,8,["categories"])])):(a(),n("div",Zt,[E(Ws,{categories:u.value,loading:$.value,onEdit:z,onDelete:q,onViewProducts:os},null,8,["categories","loading"])]))]))])]),y.value.totalPages>1?(a(),n("nav",sl,[s("button",{class:"pagination-previous",disabled:y.value.currentPage===1,onClick:e[4]||(e[4]=o=>O(y.value.currentPage-1))}," Previous ",8,el),s("button",{class:"pagination-next",disabled:y.value.currentPage===y.value.totalPages,onClick:e[5]||(e[5]=o=>O(y.value.currentPage+1))}," Next page ",8,tl),s("ul",ll,[(a(!0),n(U,null,V(G.value,o=>(a(),n("li",{key:o},[o!=="..."?(a(),n("button",{key:0,class:S(["pagination-link",{"is-current":o===y.value.currentPage}]),onClick:h=>O(o)},f(o),11,al)):(a(),n("span",il,"…"))]))),128))])])):k("",!0),E(Je,{show:p.value,"onUpdate:show":e[6]||(e[6]=o=>p.value=o),category:r.value,"is-edit":w.value,"parent-categories":Q.value,onSave:as,onCancel:L},null,8,["show","category","is-edit","parent-categories"]),E(ps,{show:d.value,"onUpdate:show":e[7]||(e[7]=o=>d.value=o),title:"Delete Category",message:J.value,"confirm-text":"Delete","confirm-class":"is-danger",onConfirm:is,onCancel:j},null,8,["show","message"])]))}},rl=T(ol,[["__scopeId","data-v-d1296298"]]);export{rl as default};
