<template>
  <div class="admin-seller-requests">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Seller Applications</h1>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <seller-request-filters @filter-changed="applyFilters" />

    <!-- Seller Requests Table -->
    <seller-request-table
      :requests="sellerRequests"
      :loading="loading"
      @view="viewRequestDetails"
      @approve="approveRequest"
      @reject="rejectRequest" />

    <!-- Pagination -->
    <div class="pagination-wrapper">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="changePage" />
    </div>

    <!-- Request Details Modal -->
    <seller-request-details-modal
      :is-open="isRequestDetailsModalOpen"
      :request="selectedRequest"
      @close="closeRequestDetailsModal"
      @approve="approveRequest"
      @reject="rejectRequest" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SellerRequestFilters from '@/admin/components/seller-requests/SellerRequestFilters.vue';
import SellerRequestTable from '@/admin/components/seller-requests/SellerRequestTable.vue';
import SellerRequestDetailsModal from '@/admin/components/seller-requests/SellerRequestDetailsModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { sellerRequestsService } from '@/admin/services/sellerRequests';

// State
const sellerRequests = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const filters = ref({});
const isRequestDetailsModalOpen = ref(false);
const selectedRequest = ref(null);

// Fetch seller requests with pagination and filters
const fetchSellerRequests = async () => {
  loading.value = true;
  try {
    const response = await sellerRequestsService.getSellerRequests({
      page: currentPage.value,
      ...filters.value
    });
    sellerRequests.value = response.items;
    totalPages.value = response.totalPages;
  } catch (error) {
    console.error('Error fetching seller requests:', error);
  } finally {
    loading.value = false;
  }
};

// Change page
const changePage = (page) => {
  currentPage.value = page;
  fetchSellerRequests();
};

// Apply filters
const applyFilters = (newFilters) => {
  filters.value = newFilters;
  currentPage.value = 1; // Reset to first page when filters change
  fetchSellerRequests();
};

// View request details
const viewRequestDetails = async (requestId) => {
  loading.value = true;
  try {
    const requestDetails = await sellerRequestsService.getSellerRequestById(requestId);
    selectedRequest.value = requestDetails;
    isRequestDetailsModalOpen.value = true;
  } catch (error) {
    console.error('Error fetching seller request details:', error);
  } finally {
    loading.value = false;
  }
};

// Close request details modal
const closeRequestDetailsModal = () => {
  isRequestDetailsModalOpen.value = false;
  selectedRequest.value = null;
};

// Approve seller request
const approveRequest = async (requestId) => {
  loading.value = true;
  try {
    await sellerRequestsService.approveSellerRequest(requestId);
    fetchSellerRequests();

    // Close the modal if it's open
    if (isRequestDetailsModalOpen.value) {
      closeRequestDetailsModal();
    }
  } catch (error) {
    console.error('Error approving seller request:', error);
  } finally {
    loading.value = false;
  }
};

// Reject seller request
const rejectRequest = async (requestId, reason) => {
  loading.value = true;
  try {
    await sellerRequestsService.rejectSellerRequest(requestId, reason);
    fetchSellerRequests();

    // Close the modal if it's open
    if (isRequestDetailsModalOpen.value) {
      closeRequestDetailsModal();
    }
  } catch (error) {
    console.error('Error rejecting seller request:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchSellerRequests();
});
</script>

<style scoped>
.admin-seller-requests {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
