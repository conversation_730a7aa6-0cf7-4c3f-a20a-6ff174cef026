<template>
  <div class="modal" :class="{ 'is-active': isOpen }">
    <div class="modal-background" @click="$emit('close')"></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">{{ category ? 'Edit Category' : 'Add Category' }}</p>
        <button class="delete" aria-label="close" @click="$emit('close')"></button>
      </header>
      <section class="modal-card-body">
        <form @submit.prevent="submitForm">
          <!-- Basic Info -->
          <div class="field">
            <label class="label">Name</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="Category name" 
                v-model="form.name"
                @input="generateSlug"
                required>
            </div>
          </div>
          
          <div class="field">
            <label class="label">Slug</label>
            <div class="control">
              <input 
                class="input" 
                type="text" 
                placeholder="category-slug" 
                v-model="form.slug"
                required>
            </div>
            <p class="help">URL-friendly version of the name. Auto-generated but can be edited.</p>
          </div>
          
          <div class="field">
            <label class="label">Description</label>
            <div class="control">
              <textarea 
                class="textarea" 
                placeholder="Category description" 
                v-model="form.description"
                rows="3"></textarea>
            </div>
          </div>
          
          <!-- Parent Category -->
          <div class="field">
            <label class="label">Parent Category</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="form.parentId">
                  <option value="">None (Top Level)</option>
                  <option 
                    v-for="cat in availableParentCategories" 
                    :key="cat.id" 
                    :value="cat.id"
                    :disabled="cat.id === form.id">
                    {{ cat.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- Image URL -->
          <div class="field">
            <label class="label">Image URL</label>
            <div class="control">
              <input 
                class="input" 
                type="url" 
                placeholder="https://example.com/image.jpg" 
                v-model="form.imageUrl">
            </div>
          </div>
          
          <!-- Display Order -->
          <div class="field">
            <label class="label">Display Order</label>
            <div class="control">
              <input 
                class="input" 
                type="number" 
                min="0" 
                placeholder="0" 
                v-model.number="form.displayOrder">
            </div>
            <p class="help">Categories with lower numbers will be displayed first.</p>
          </div>
        </form>
      </section>
      <footer class="modal-card-foot">
        <button class="button is-primary" @click="submitForm" :disabled="isSubmitting">
          <span v-if="isSubmitting">
            <span class="icon">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Saving...</span>
          </span>
          <span v-else>Save</span>
        </button>
        <button class="button" @click="$emit('close')">Cancel</button>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  category: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const form = reactive({
  id: null,
  name: '',
  slug: '',
  description: '',
  parentId: '',
  imageUrl: '',
  displayOrder: 0
});

// Submission state
const isSubmitting = ref(false);

// Available parent categories (exclude self to prevent circular references)
const availableParentCategories = computed(() => {
  return props.categories.filter(cat => cat.id !== form.id);
});

// Generate slug from name
const generateSlug = () => {
  if (!form.name) return;
  
  // Only auto-generate if user hasn't manually edited the slug
  if (!form.slug || form.slug === slugify(props.category?.name || '')) {
    form.slug = slugify(form.name);
  }
};

// Slugify text
const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')        // Replace spaces with -
    .replace(/&/g, '-and-')      // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')    // Remove all non-word characters
    .replace(/\-\-+/g, '-')      // Replace multiple - with single -
    .replace(/^-+/, '')          // Trim - from start of text
    .replace(/-+$/, '');         // Trim - from end of text
};

// Submit form
const submitForm = async () => {
  isSubmitting.value = true;
  
  try {
    // Create a clean form object
    const categoryData = { ...form };
    
    // Convert string numbers to actual numbers
    categoryData.displayOrder = parseInt(categoryData.displayOrder);
    
    // If parentId is empty string, set to null
    if (categoryData.parentId === '') {
      categoryData.parentId = null;
    }
    
    emit('save', categoryData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form
const resetForm = () => {
  form.id = null;
  form.name = '';
  form.slug = '';
  form.description = '';
  form.parentId = '';
  form.imageUrl = '';
  form.displayOrder = 0;
};

// Watch for category changes to update form
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    // Populate form with category data
    Object.keys(form).forEach(key => {
      if (key in newCategory) {
        form[key] = newCategory[key];
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>

<style scoped>
.modal-card {
  width: 80%;
  max-width: 600px;
}
</style>
