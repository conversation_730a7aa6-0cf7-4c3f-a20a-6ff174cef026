import{_ as p,h as g,c as o,o as r,a as t,k as P,n as d,F as y,p as V,t as k}from"./index-DMg5qKr1.js";const C={class:"pagination is-centered",role:"navigation","aria-label":"pagination"},N=["disabled"],$=["disabled"],w={class:"pagination-list"},B={key:0},E={key:1},F=["onClick"],L={key:2},M={key:3},q={__name:"Pagination",props:{currentPage:{type:Number,required:!0},totalPages:{type:Number,required:!0},maxVisiblePages:{type:Number,default:5}},emits:["page-changed"],setup(s,{emit:b}){const a=s,f=b,l=g(()=>{if(a.totalPages<=a.maxVisiblePages)return Array.from({length:a.totalPages},(i,c)=>c+1);let n=Math.max(a.currentPage-Math.floor(a.maxVisiblePages/2),1),e=n+a.maxVisiblePages-1;return e>a.totalPages&&(e=a.totalPages,n=Math.max(e-a.maxVisiblePages+1,1)),Array.from({length:e-n+1},(i,c)=>n+c)}),m=g(()=>a.totalPages>a.maxVisiblePages&&l.value[0]>1),x=g(()=>m.value&&l.value[0]>2),v=g(()=>a.totalPages>a.maxVisiblePages&&l.value[l.value.length-1]<a.totalPages),h=g(()=>v.value&&l.value[l.value.length-1]<a.totalPages-1),u=n=>{n>=1&&n<=a.totalPages&&n!==a.currentPage&&f("page-changed",n)};return(n,e)=>(r(),o("nav",C,[t("a",{class:"pagination-previous",disabled:s.currentPage<=1,onClick:e[0]||(e[0]=i=>u(s.currentPage-1))},e[4]||(e[4]=[t("span",{class:"icon"},[t("i",{class:"fas fa-chevron-left"})],-1),t("span",null,"Previous",-1)]),8,N),t("a",{class:"pagination-next",disabled:s.currentPage>=s.totalPages,onClick:e[1]||(e[1]=i=>u(s.currentPage+1))},e[5]||(e[5]=[t("span",null,"Next",-1),t("span",{class:"icon"},[t("i",{class:"fas fa-chevron-right"})],-1)]),8,$),t("ul",w,[m.value?(r(),o("li",B,[t("a",{class:d(["pagination-link",{"is-current":s.currentPage===1}]),onClick:e[2]||(e[2]=i=>u(1))}," 1 ",2)])):P("",!0),x.value?(r(),o("li",E,e[6]||(e[6]=[t("span",{class:"pagination-ellipsis"},"…",-1)]))):P("",!0),(r(!0),o(y,null,V(l.value,i=>(r(),o("li",{key:i},[t("a",{class:d(["pagination-link",{"is-current":s.currentPage===i}]),onClick:c=>u(i)},k(i),11,F)]))),128)),h.value?(r(),o("li",L,e[7]||(e[7]=[t("span",{class:"pagination-ellipsis"},"…",-1)]))):P("",!0),v.value?(r(),o("li",M,[t("a",{class:d(["pagination-link",{"is-current":s.currentPage===s.totalPages}]),onClick:e[3]||(e[3]=i=>u(s.totalPages))},k(s.totalPages),3)])):P("",!0)])]))}},z=p(q,[["__scopeId","data-v-c65d6ac7"]]);export{z as P};
