﻿using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Queries.Order;

public class GetOrderQueryHandler : IRequestHandler<GetOrderQuery, OrderResponse>
{
    private readonly IOrderRepository _repository;
    private readonly IMapper _mapper;

    public GetOrderQueryHandler(IOrderRepository repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<OrderResponse> Handle(GetOrderQuery request, CancellationToken cancellationToken)
    {
        var item = await _repository.GetByIdAsync(request.Id, cancellationToken,
            o => o.Customer,
            o => o.ShippingMethod,
            o => o.ShippingAddress);
        if (item == null)
            return null;

        return _mapper.Map<OrderResponse>(item);
    }
}
