﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.Enums;
using MediatR;

namespace Marketplace.Application.Commands.Order;

public class UpdateOrderCommand : IRequest<UpdateOrderResponse>
{
    public Guid Id { get; set; }
    public OrderStatus? Status { get; set; }
    public PaymentStatus? PaymentStatus { get; set; }
    public string? ShippingAddress { get; set; }
    public string? TrackingNumber { get; set; }
    public string? ShippingMethod { get; set; }
    public string? Notes { get; set; }
}

public class UpdateOrderResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Guid OrderId { get; set; }
}