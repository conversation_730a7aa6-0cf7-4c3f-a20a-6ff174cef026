import{_ as N,g as v,i as V,c as i,a as s,k as _,b as w,w as g,d as m,r as x,n as d,t as a,F as f,p,f as T,e as I,o}from"./index-C1YYMYJd.js";import{r as k}from"./reviews-I3pIRo8A.js";const S={class:"review-detail"},$={class:"level"},B={class:"level-left"},F={class:"level-item"},L={class:"breadcrumb"},U={class:"level-right"},M={class:"level-item"},P={key:0,class:"has-text-centered"},z={key:1,class:"notification is-danger"},E={key:2},H={class:"columns"},j={class:"column is-8"},q={class:"card"},G={class:"card-content"},J={class:"columns"},K={class:"column is-6"},O={class:"field"},Q={class:"info-value"},W={class:"field"},X={class:"info-value"},Y={class:"field"},Z={class:"info-value"},ss={class:"stars"},es={class:"ml-2"},ls={class:"column is-6"},ts={class:"field"},as={class:"info-value"},is={key:0,class:"field"},os={class:"info-value"},ns={key:0,class:"field"},ds={class:"content"},cs={key:0,class:"card mt-4"},rs={class:"card-content"},vs={class:"columns"},us={class:"column is-4"},_s={class:"field"},ms={class:"stars"},fs={class:"ml-2"},ps={class:"column is-4"},bs={class:"field"},hs={class:"stars"},ws={class:"ml-2"},gs={class:"column is-4"},ys={class:"field"},Rs={class:"stars"},ks={class:"ml-2"},As={class:"column is-4"},Cs={class:"card"},Ds={class:"card-content"},Ns={class:"buttons is-fullwidth"},Vs={class:"card mt-4"},xs={class:"card-content"},Ts={class:"field"},Is={class:"info-value"},Ss={class:"field"},$s={class:"info-value"},Bs={class:"modal-card"},Fs={class:"modal-card-head"},Ls={class:"modal-card-foot"},Us={__name:"ReviewDetail",setup(Ms){const A=T(),C=I(),l=v({}),u=v(!1),c=v(null),b=v(!1),r=v(!1),y=async()=>{u.value=!0,c.value=null;try{const n=await k.getReviewById(A.params.id);l.value=n}catch(n){c.value=n.message||"Failed to load review details"}finally{u.value=!1}},D=async()=>{b.value=!0;try{await k.deleteReview(l.value.id),C.push({name:"AdminReviews"})}catch(n){c.value=n.message||"Failed to delete review"}finally{b.value=!1,r.value=!1}},R=n=>new Date(n).toLocaleString();return V(()=>{y()}),(n,e)=>{const h=x("router-link");return o(),i("div",S,[s("div",$,[s("div",B,[s("div",F,[s("nav",L,[s("ul",null,[s("li",null,[w(h,{to:"/admin/reviews"},{default:g(()=>e[5]||(e[5]=[m("Reviews")])),_:1})]),e[6]||(e[6]=s("li",{class:"is-active"},[s("a",null,"Review Details")],-1))])])])]),s("div",U,[s("div",M,[s("button",{class:d(["button is-primary",{"is-loading":u.value}]),onClick:y},e[7]||(e[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),u.value&&!l.value.id?(o(),i("div",P,e[8]||(e[8]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading review details...",-1)]))):c.value?(o(),i("div",z,[s("button",{class:"delete",onClick:e[0]||(e[0]=t=>c.value=null)}),m(" "+a(c.value),1)])):l.value.id?(o(),i("div",E,[s("div",H,[s("div",j,[s("div",q,[e[15]||(e[15]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Review Information")],-1)),s("div",G,[s("div",J,[s("div",K,[s("div",O,[e[9]||(e[9]=s("label",{class:"label"},"Product",-1)),s("p",Q,[w(h,{to:{name:"AdminProductDetail",params:{id:l.value.productId}}},{default:g(()=>[m(a(l.value.productName),1)]),_:1},8,["to"])])]),s("div",W,[e[10]||(e[10]=s("label",{class:"label"},"User",-1)),s("p",X,[w(h,{to:{name:"AdminUserDetail",params:{id:l.value.userId}}},{default:g(()=>[m(a(l.value.userName),1)]),_:1},8,["to"])])]),s("div",Y,[e[11]||(e[11]=s("label",{class:"label"},"Rating",-1)),s("p",Z,[s("div",ss,[(o(),i(f,null,p(5,t=>s("span",{key:t,class:d(["star",{"is-filled":t<=l.value.rating}])}," ★ ",2)),64)),s("span",es,"("+a(l.value.rating)+"/5)",1)])])])]),s("div",ls,[s("div",ts,[e[12]||(e[12]=s("label",{class:"label"},"Created At",-1)),s("p",as,a(R(l.value.createdAt)),1)]),l.value.updatedAt?(o(),i("div",is,[e[13]||(e[13]=s("label",{class:"label"},"Updated At",-1)),s("p",os,a(R(l.value.updatedAt)),1)])):_("",!0)])]),l.value.comment?(o(),i("div",ns,[e[14]||(e[14]=s("label",{class:"label"},"Comment",-1)),s("div",ds,[s("p",null,a(l.value.comment),1)])])):_("",!0)])]),l.value.rating?(o(),i("div",cs,[e[19]||(e[19]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Detailed Rating")],-1)),s("div",rs,[s("div",vs,[s("div",us,[s("div",_s,[e[16]||(e[16]=s("label",{class:"label"},"Service",-1)),s("div",ms,[(o(),i(f,null,p(5,t=>s("span",{key:t,class:d(["star",{"is-filled":t<=l.value.serviceRating}])}," ★ ",2)),64)),s("span",fs,"("+a(l.value.serviceRating||"N/A")+")",1)])])]),s("div",ps,[s("div",bs,[e[17]||(e[17]=s("label",{class:"label"},"Delivery Time",-1)),s("div",hs,[(o(),i(f,null,p(5,t=>s("span",{key:t,class:d(["star",{"is-filled":t<=l.value.deliveryTimeRating}])}," ★ ",2)),64)),s("span",ws,"("+a(l.value.deliveryTimeRating||"N/A")+")",1)])])]),s("div",gs,[s("div",ys,[e[18]||(e[18]=s("label",{class:"label"},"Accuracy",-1)),s("div",Rs,[(o(),i(f,null,p(5,t=>s("span",{key:t,class:d(["star",{"is-filled":t<=l.value.accuracyRating}])}," ★ ",2)),64)),s("span",ks,"("+a(l.value.accuracyRating||"N/A")+")",1)])])])])])])):_("",!0)]),s("div",As,[s("div",Cs,[e[21]||(e[21]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Actions")],-1)),s("div",Ds,[s("div",Ns,[s("button",{class:"button is-danger is-fullwidth",onClick:e[1]||(e[1]=t=>r.value=!0)},e[20]||(e[20]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete Review",-1)]))])])]),s("div",Vs,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Statistics")],-1)),s("div",xs,[s("div",Ts,[e[22]||(e[22]=s("label",{class:"label"},"Helpful Votes",-1)),s("p",Is,a(l.value.helpfulVotes||0),1)]),s("div",Ss,[e[23]||(e[23]=s("label",{class:"label"},"Total Votes",-1)),s("p",$s,a(l.value.totalVotes||0),1)])])])])])])):_("",!0),s("div",{class:d(["modal",{"is-active":r.value}])},[s("div",{class:"modal-background",onClick:e[2]||(e[2]=t=>r.value=!1)}),s("div",Bs,[s("header",Fs,[e[25]||(e[25]=s("p",{class:"modal-card-title"},"Delete Review",-1)),s("button",{class:"delete",onClick:e[3]||(e[3]=t=>r.value=!1)})]),e[26]||(e[26]=s("section",{class:"modal-card-body"},[s("p",null,"Are you sure you want to delete this review? This action cannot be undone.")],-1)),s("footer",Ls,[s("button",{class:d(["button is-danger",{"is-loading":b.value}]),onClick:D}," Delete Review ",2),s("button",{class:"button",onClick:e[4]||(e[4]=t=>r.value=!1)},"Cancel")])])],2)])}}},Es=N(Us,[["__scopeId","data-v-db74c035"]]);export{Es as default};
