<template>
  <div class="product-table">
    <div class="table-container">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th style="width: 80px;">Image</th>
            <th>Name</th>
            <th>Category</th>
            <th>Price</th>
            <th>Stock</th>
            <th>Status</th>
            <th style="width: 140px;">Actions</th>
          </tr>
        </thead>
        <tbody v-if="!loading && products.length > 0">
          <tr v-for="product in products" :key="product.id">
            <td>
              <figure class="image is-64x64">
                <img
                  :src="getProductImageUrl(product)"
                  :alt="product.name"
                  class="product-thumbnail"
                  @error="handleImageError" />
              </figure>
            </td>
            <td>
              <div>
                <strong>{{ product.name }}</strong>
                <br>
                <small class="has-text-grey">{{ product.slug }}</small>
              </div>
            </td>
            <td>{{ product.categoryName || 'Unknown' }}</td>
            <td>{{ formatCurrency(product.priceAmount || product.price || product.priceValue) }}</td>
            <td>
              <span class="tag" :class="getStockClass(product.stock || product.stockQuantity || 0)">
                {{ product.stock || product.stockQuantity || 0 }}
              </span>
            </td>
            <td>
              <status-badge :status="product.status" type="product" />
            </td>
            <td style="width: 140px;">
              <div class="buttons are-small has-addons">
                <button
                  class="button is-info is-small"
                  @click="$emit('view', product)"
                  title="View Product">
                  <span class="icon is-small"><i class="fas fa-eye"></i></span>
                </button>
                <button
                  class="button is-primary is-small"
                  @click="$emit('edit', product)"
                  title="Edit Product">
                  <span class="icon is-small"><i class="fas fa-edit"></i></span>
                </button>
                <button
                  class="button is-danger is-small"
                  @click="$emit('delete', product)"
                  title="Delete Product">
                  <span class="icon is-small"><i class="fas fa-trash"></i></span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else-if="loading">
          <tr>
            <td colspan="7" class="has-text-centered">
              <div class="loader-wrapper">
                <div class="loader is-loading"></div>
              </div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="7" class="has-text-centered">
              No products found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const props = defineProps({
  products: {
    type: Array,
    required: true
  },
  categories: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view', 'edit', 'delete']);

const formatCurrency = (value) => {
  if (!value || isNaN(value)) return '₴0.00';

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2
  }).format(value);
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    // Handle backend date format: "2025-06-02 20:35:40.231835+03"
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return dateString;
  }
};

// Function to get category name by ID
const getCategoryName = (categoryId) => {
  if (!categoryId || !props.categories || props.categories.length === 0) {
    return 'Unknown';
  }

  // Recursive function to search in nested categories
  const findCategory = (categories, id) => {
    for (const category of categories) {
      if (category.id === id) {
        return category.name;
      }
      if (category.children && category.children.length > 0) {
        const found = findCategory(category.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  return findCategory(props.categories, categoryId) || 'Unknown';
};

// Function to get display category name with logging
// Removed - using direct categoryName in template

// Function to get stock level class
const getStockClass = (stock) => {
  const stockValue = parseInt(stock) || 0;

  if (stockValue <= 0) {
    return 'is-danger'; // Out of stock
  } else if (stockValue <= 10) {
    return 'is-warning'; // Low stock
  } else {
    return 'is-success'; // In stock
  }
};

// Function to get product image URL with fallback
const getProductImageUrl = (product) => {
  // Try different possible image fields
  const imageUrl = product.imageUrl || product.image || product.mainImageUrl;

  if (imageUrl && imageUrl.trim() !== '') {
    return imageUrl;
  }

  // Return placeholder image
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMiA0OEM0MC4yODQzIDQ4IDQ4IDQwLjI4NDMgNDggMzJDNDggMjMuNzE1NyA0MC4yODQzIDE2IDMyIDE2QzIzLjcxNTcgMTYgMTYgMjMuNzE1NyAxNiAzMkMxNiA0MC4yODQzIDIzLjcxNTcgNDggMzIgNDhaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhIMjZWMzBIMjRWMjhaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zOCAyOEg0MFYzMEgzOFYyOFoiIGZpbGw9IiNEMUQxRDEiLz4KPHBhdGggZD0iTTI2IDM2QzI2IDM4LjIwOTEgMjcuNzkwOSA0MCAzMCA0MEMzMi4yMDkxIDQwIDM0IDM4LjIwOTEgMzQgMzZIMjZaIiBmaWxsPSIjRDFEMUQxIi8+Cjwvc3ZnPgo=';
};

// Function to handle image loading errors
const handleImageError = (event) => {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMiA0OEM0MC4yODQzIDQ4IDQ4IDQwLjI4NDMgNDggMzJDNDggMjMuNzE1NyA0MC4yODQzIDE2IDMyIDE2QzIzLjcxNTcgMTYgMTYgMjMuNzE1NyAxNiAzMkMxNiA0MC4yODQzIDIzLjcxNTcgNDggMzIgNDhaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhIMjZWMzBIMjRWMjhaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zOCAyOEg0MFYzMEgzOFYyOFoiIGZpbGw9IiNEMUQxRDEiLz4KPHBhdGggZD0iTTI2IDM2QzI2IDM4LjIwOTEgMjcuNzkwOSA0MCAzMCA0MEMzMi4yMDkxIDQwIDM0IDM4LjIwOTEgMzQgMzZIMjZaIiBmaWxsPSIjRDFEMUQxIi8+Cjwvc3ZnPgo=';
};

// Functions available in template automatically
</script>

<style scoped>
.product-thumbnail {
  object-fit: cover;
  width: 64px;
  height: 64px;
  border-radius: 8px;
  border: 2px solid #e5e5e5;
  transition: all 0.2s ease;
}

.product-thumbnail:hover {
  border-color: #3273dc;
  transform: scale(1.05);
}

.image.is-64x64 {
  width: 64px;
  height: 64px;
}

.loader-wrapper {
  padding: 2rem;
  display: flex;
  justify-content: center;
}

.loader {
  height: 80px;
  width: 80px;
}

/* Improved table styling */
.table td {
  vertical-align: middle;
}

.table td:first-child {
  text-align: center;
}

/* Better button spacing */
.buttons.are-small.has-addons .button {
  margin-right: 0;
}

/* Product name styling */
.table td strong {
  font-weight: 600;
}

.table td small {
  font-size: 0.75rem;
  opacity: 0.8;
}
</style>
