﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplace.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOrderStatusEnum : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update old "Paid" status to "Delivered" since paid orders are typically delivered
            migrationBuilder.Sql("UPDATE \"Orders\" SET \"Status\" = 'Delivered' WHERE \"Status\" = 'Paid'");

            // Also remove any payments with invalid status
            migrationBuilder.Sql("DELETE FROM \"Payments\" WHERE \"OrderId\" IN (SELECT \"Id\" FROM \"Orders\" WHERE \"Status\" NOT IN ('Processing', 'Pending', 'Shipped', 'Delivered', 'Cancelled'))");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Revert "Delivered" status back to "Paid" (this is not perfect but needed for rollback)
            migrationBuilder.Sql("UPDATE \"Orders\" SET \"Status\" = 'Paid' WHERE \"Status\" = 'Delivered'");
        }
    }
}
