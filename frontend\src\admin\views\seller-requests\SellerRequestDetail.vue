<template>
  <div class="seller-request-detail">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Seller Request Details</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/seller-requests" class="button is-light">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Seller Requests</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading seller request details...</p>
    </div>
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>
    <div v-else-if="!request.id" class="notification is-warning">
      <p>Seller request not found.</p>
      <router-link to="/admin/seller-requests" class="button is-primary mt-4">
        Back to Seller Requests
      </router-link>
    </div>
    <div v-else>
      <!-- Request Header -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-8">
              <h2 class="request-title">{{ request.companyRequestData?.name || 'Seller Request' }}</h2>
              <p class="request-subtitle">
                <status-badge
                  :status="getStatusString(request.status)"
                  type="default" />
                <span class="ml-2">Submitted on {{ formatDate(request.createdAt) }}</span>
              </p>
            </div>
            <div class="column is-4 has-text-right">
              <div v-if="getStatusString(request.status) === 'pending'" class="buttons is-right">
                <button
                  class="button is-success"
                  @click="confirmApprove"
                  :class="{ 'is-loading': processing }">
                  <span class="icon">
                    <i class="fas fa-check"></i>
                  </span>
                  <span>Approve</span>
                </button>
                <button
                  class="button is-danger"
                  @click="confirmReject"
                  :class="{ 'is-loading': processing }">
                  <span class="icon">
                    <i class="fas fa-times"></i>
                  </span>
                  <span>Reject</span>
                </button>
              </div>
              <div v-else-if="getStatusString(request.status) === 'approved'" class="notification is-success is-light">
                <p>
                  <span class="icon">
                    <i class="fas fa-check-circle"></i>
                  </span>
                  <span>Approved on {{ formatDate(request.updatedAt) }}</span>
                </p>
              </div>
              <div v-else-if="getStatusString(request.status) === 'rejected'" class="notification is-danger is-light">
                <p>
                  <span class="icon">
                    <i class="fas fa-times-circle"></i>
                  </span>
                  <span>Rejected on {{ formatDate(request.updatedAt) }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <!-- User Information -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Applicant Information</p>
            </div>
            <div class="card-content">
              <div class="user-avatar">
                <img
                  :src="request.user?.avatar || 'https://via.placeholder.com/150'"
                  :alt="`${request.user?.firstName || ''} ${request.user?.lastName || ''}`"
                  @error="handleImageError">
              </div>

              <div class="info-group">
                <h3 class="info-label">Username</h3>
                <p class="info-value">{{ request.user?.username || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Email</h3>
                <p class="info-value">
                  <a v-if="request.user?.email" :href="`mailto:${request.user.email}`">{{ request.user.email }}</a>
                  <span v-else>N/A</span>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Role</h3>
                <p class="info-value">{{ request.user?.role || 'Not provided' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Registered Since</h3>
                <p class="info-value">{{ formatDate(request.user?.createdAt) }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">User Profile</h3>
                <p class="info-value">
                  <router-link v-if="request.userId" :to="`/admin/users/${request.userId}`" class="button is-info is-small">
                    <span class="icon is-small">
                      <i class="fas fa-user"></i>
                    </span>
                    <span>View Profile</span>
                  </router-link>
                  <span v-else>N/A</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Request Details -->
        <div class="column is-8">
          <!-- Company Information -->
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Company Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column">
                  <div class="info-group">
                    <h3 class="info-label">Company Name</h3>
                    <p class="info-value">{{ request.companyRequestData?.name || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Description</h3>
                    <p class="info-value">{{ request.companyRequestData?.description || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Contact Email</h3>
                    <p class="info-value">{{ request.companyRequestData?.contactEmail || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Contact Phone</h3>
                    <p class="info-value">{{ request.companyRequestData?.contactPhone || 'N/A' }}</p>
                  </div>
                </div>
                <div class="column">
                  <div class="info-group">
                    <h3 class="info-label">Address</h3>
                    <p class="info-value">{{ getFullAddress() }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Meta Title</h3>
                    <p class="info-value">{{ request.companyRequestData?.metaTitle || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Meta Description</h3>
                    <p class="info-value">{{ request.companyRequestData?.metaDescription || 'N/A' }}</p>
                  </div>
                </div>
              </div>
              <div v-if="request.companyRequestData?.imageUrl" class="info-group">
                <h3 class="info-label">Company Image</h3>
                <figure class="image is-128x128">
                  <img :src="request.companyRequestData.imageUrl" alt="Company image" class="is-rounded">
                </figure>
              </div>
            </div>
          </div>

          <!-- Finance Information -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Finance Information</p>
            </div>
            <div class="card-content">
              <div class="columns">
                <div class="column">
                  <div class="info-group">
                    <h3 class="info-label">Bank Name</h3>
                    <p class="info-value">{{ request.financeRequestData?.bankName || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Bank Account</h3>
                    <p class="info-value">{{ request.financeRequestData?.bankAccount || 'N/A' }}</p>
                  </div>
                </div>
                <div class="column">
                  <div class="info-group">
                    <h3 class="info-label">Bank Code</h3>
                    <p class="info-value">{{ request.financeRequestData?.bankCode || 'N/A' }}</p>
                  </div>

                  <div class="info-group">
                    <h3 class="info-label">Tax ID</h3>
                    <p class="info-value">{{ request.financeRequestData?.taxId || 'N/A' }}</p>
                  </div>
                </div>
              </div>
              <div class="info-group">
                <h3 class="info-label">Payment Details</h3>
                <p class="info-value">{{ request.financeRequestData?.paymentDetails || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Schedule Information -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Schedule Information</p>
            </div>
            <div class="card-content">
              <div v-if="request.scheduleRequestData?.daySchedules" class="table-container">
                <table class="table is-fullwidth">
                  <thead>
                    <tr>
                      <th>Day</th>
                      <th>Open Time</th>
                      <th>Close Time</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="schedule in request.scheduleRequestData.daySchedules" :key="schedule.day">
                      <td>{{ getDayName(schedule.day) }}</td>
                      <td>{{ schedule.isClosed ? '-' : schedule.openTime }}</td>
                      <td>{{ schedule.isClosed ? '-' : schedule.closeTime }}</td>
                      <td>
                        <span class="tag" :class="schedule.isClosed ? 'is-danger' : 'is-success'">
                          {{ schedule.isClosed ? 'Closed' : 'Open' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="has-text-centered py-4">
                <p class="has-text-grey">No schedule information provided</p>
              </div>
            </div>
          </div>

          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Additional Information</p>
            </div>
            <div class="card-content">
              <div v-if="request.additionalInformation" class="info-group">
                <p class="info-value">{{ request.additionalInformation }}</p>
              </div>
              <div v-else class="has-text-centered py-4">
                <p class="has-text-grey">No additional information provided</p>
              </div>
            </div>
          </div>

          <div v-if="request.documents && request.documents.length > 0" class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Documents</p>
            </div>
            <div class="card-content">
              <div class="documents-list">
                <div
                  v-for="(document, index) in request.documents"
                  :key="index"
                  class="document-item">
                  <div class="document-icon">
                    <span class="icon">
                      <i :class="getDocumentIcon(document.type)"></i>
                    </span>
                  </div>
                  <div class="document-info">
                    <p class="document-name">{{ document.name }}</p>
                    <p class="document-type">{{ document.type }}</p>
                  </div>
                  <div class="document-actions">
                    <a
                      :href="document.url"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="button is-small is-info">
                      <span class="icon is-small">
                        <i class="fas fa-download"></i>
                      </span>
                      <span>Download</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="request.status === 'rejected' && request.rejectionReason" class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Rejection Reason</p>
            </div>
            <div class="card-content">
              <p>{{ request.rejectionReason }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Approve Confirmation Modal -->
    <confirm-dialog
      :is-open="showApproveModal"
      title="Approve Seller Request"
      :message="`Are you sure you want to approve ${request.user?.firstName || 'this user'} ${request.user?.lastName || ''}'s seller request for '${request.storeName || 'this store'}'?`"
      confirm-text="Approve"
      cancel-text="Cancel"
      confirm-button-class="is-success"
      @confirm="approveRequest"
      @cancel="cancelProcess" />

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="cancelProcess"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Seller Request</p>
          <button class="delete" aria-label="close" @click="cancelProcess"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to reject {{ request.user?.firstName || 'this user' }}'s seller request for '{{ request.storeName || 'this store' }}'?</p>

          <div class="field mt-4">
            <label class="label">Reason for Rejection (Optional)</label>
            <div class="control">
              <textarea
                class="textarea"
                v-model="rejectionReason"
                placeholder="Provide a reason for rejection">
              </textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-danger"
            @click="rejectRequest"
            :class="{ 'is-loading': processing }">
            Reject
          </button>
          <button class="button is-light" @click="cancelProcess">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { sellerRequestsService } from '@/admin/services/seller-requests';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const error = ref(null);
const request = ref({});
const processing = ref(false);

// Modal state
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Computed properties
const requestId = computed(() => route.params.id);

// Fetch request data
const fetchRequest = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await sellerRequestsService.getSellerRequest(requestId.value);
    request.value = response.data;
  } catch (err) {
    console.error('Error fetching seller request:', err);
    error.value = 'Failed to load seller request data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Get full address
const getFullAddress = () => {
  const data = request.value?.companyRequestData;
  if (!data) return 'N/A';

  const parts = [
    data.addressStreet,
    data.addressCity,
    data.addressRegion,
    data.addressPostalCode
  ].filter(Boolean);

  return parts.length > 0 ? parts.join(', ') : 'N/A';
};

// Get day name
const getDayName = (dayNumber) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayNumber] || 'Unknown';
};

// Convert status enum to string
const getStatusString = (status) => {
  if (typeof status === 'string') {
    return status.toLowerCase();
  }

  switch (status) {
    case 0: return 'pending';
    case 1: return 'approved';
    case 2: return 'rejected';
    default: return 'pending';
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150?text=No+Image';
};

// Get social class
const getSocialClass = (platform) => {
  switch (platform.toLowerCase()) {
    case 'facebook':
      return 'link';
    case 'twitter':
      return 'info';
    case 'instagram':
      return 'danger';
    case 'linkedin':
      return 'link';
    case 'youtube':
      return 'danger';
    default:
      return 'light';
  }
};

// Get document icon
const getDocumentIcon = (type) => {
  if (!type) return 'fas fa-file';

  type = type.toLowerCase();

  if (type.includes('pdf')) {
    return 'fas fa-file-pdf';
  } else if (type.includes('word') || type.includes('doc')) {
    return 'fas fa-file-word';
  } else if (type.includes('excel') || type.includes('xls')) {
    return 'fas fa-file-excel';
  } else if (type.includes('image') || type.includes('jpg') || type.includes('png')) {
    return 'fas fa-file-image';
  } else {
    return 'fas fa-file';
  }
};

// Confirm approve
const confirmApprove = () => {
  showApproveModal.value = true;
};

// Confirm reject
const confirmReject = () => {
  rejectionReason.value = '';
  showRejectModal.value = true;
};

// Cancel process
const cancelProcess = () => {
  showApproveModal.value = false;
  showRejectModal.value = false;
  rejectionReason.value = '';
};

// Approve request
const approveRequest = async () => {
  processing.value = true;

  try {
    await sellerRequestsService.approveSellerRequest(requestId.value);

    // Update request data
    request.value.status = 1; // Approved
    request.value.approvedAt = new Date();

    // Close modal
    showApproveModal.value = false;
  } catch (err) {
    console.error('Error approving seller request:', err);
    error.value = 'Failed to approve seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Reject request
const rejectRequest = async () => {
  processing.value = true;

  try {
    await sellerRequestsService.rejectSellerRequest(
      requestId.value,
      rejectionReason.value
    );

    // Update request data
    request.value.status = 2; // Rejected
    request.value.rejectedAt = new Date();
    request.value.rejectionReason = rejectionReason.value;

    // Close modal
    showRejectModal.value = false;
    rejectionReason.value = '';
  } catch (err) {
    console.error('Error rejecting seller request:', err);
    error.value = 'Failed to reject seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchRequest();
});
</script>

<style scoped>
.seller-request-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.request-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.request-subtitle {
  font-size: 1rem;
  color: #7a7a7a;
}

.user-avatar {
  width: 150px;
  height: 150px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.social-links {
  display: flex;
  flex-wrap: wrap;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.document-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: #7a7a7a;
}

.document-info {
  flex: 1;
}

.document-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.document-type {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.document-actions {
  margin-left: 1rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.tag.is-primary {
  background-color: #ff7700;
}

.tag.is-primary.is-light {
  background-color: #fff2e5;
  color: #ff7700;
}
</style>
