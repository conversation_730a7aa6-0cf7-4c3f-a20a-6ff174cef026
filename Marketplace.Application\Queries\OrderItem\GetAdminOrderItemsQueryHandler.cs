using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.OrderItem;

public class GetAdminOrderItemsQueryHandler :
    PaginatedQueryHandler<GetAdminOrderItemsQuery, Domain.Entities.OrderItem, OrderItemResponse>,
    IRequestHandler<GetAdminOrderItemsQuery, PaginatedResponse<OrderItemResponse>>
{
    private readonly IOrderItemRepository _orderItemRepository;
    private readonly IOrderRepository _orderRepository;

    public GetAdminOrderItemsQueryHandler(
        IOrderItemRepository orderItemRepository,
        IOrderRepository orderRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _orderItemRepository = orderItemRepository;
        _orderRepository = orderRepository;
    }

    public async Task<PaginatedResponse<OrderItemResponse>> Handle(GetAdminOrderItemsQuery request, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи замовлення існує (для адміна не потрібно перевіряти власника)
        var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
        if (order == null)
            return null;

        // Створюємо фільтр для елементів замовлення
        Expression<Func<Domain.Entities.OrderItem, bool>>? filter = null;

        // Базовий фільтр - тільки елементи цього замовлення
        filter = oi => oi.OrderId == request.OrderId;

        // Додаємо пошук по назві продукту, якщо вказано
        if (!string.IsNullOrEmpty(request.Filter))
        {
            Expression<Func<Domain.Entities.OrderItem, bool>> searchFilter =
                oi => oi.Product.Name.Contains(request.Filter) ||
                      oi.Product.Id.ToString().Contains(request.Filter);
            filter = CombineFilters(filter, searchFilter);
        }

        // Отримуємо пагіновані дані з Product для назви та зображення
        var pagedResult = await _orderItemRepository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "CreatedAt",
            descending: request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 10,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.OrderItem, object>>[] {
                oi => oi.Product // Включаємо Product для отримання назви та інших деталей
            }
        );

        // Мапимо результати
        var orderItemResponses = Mapper.Map<List<OrderItemResponse>>(pagedResult.Items);

        // Створюємо пагіновану відповідь
        var response = CreatePaginatedResponse(request, pagedResult, "order-items");
        response.Data = orderItemResponses;
        return response;
    }

    private static Expression<Func<Domain.Entities.OrderItem, bool>> CombineFilters(
        Expression<Func<Domain.Entities.OrderItem, bool>> filter1,
        Expression<Func<Domain.Entities.OrderItem, bool>> filter2)
    {
        var parameter = Expression.Parameter(typeof(Domain.Entities.OrderItem), "oi");
        var body1 = Expression.Invoke(filter1, parameter);
        var body2 = Expression.Invoke(filter2, parameter);
        var combined = Expression.AndAlso(body1, body2);
        return Expression.Lambda<Func<Domain.Entities.OrderItem, bool>>(combined, parameter);
    }
}
