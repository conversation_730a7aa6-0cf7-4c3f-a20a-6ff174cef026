import{_ as T,g as c,z as K,h as I,f as z,i as j,c as n,a as e,t as h,b as E,w as U,r as q,d as f,A as o,B as u,C as W,F as $,p as G,k as F,L as k,n as H,m as J,I as v,e as Q,o as r}from"./index-BtyG65bR.js";const X={class:"category-form"},Y={class:"level"},Z={class:"level-left"},ee={class:"level-item"},se={class:"title"},te={class:"level-right"},ae={class:"level-item"},le={key:0,class:"has-text-centered py-6"},oe={key:1,class:"notification is-danger"},ie={key:2},ne={class:"columns"},re={class:"column is-8"},de={class:"card"},ce={class:"card-content"},ue={class:"field"},pe={class:"control"},ve={class:"field"},me={class:"control"},ge={class:"field"},fe={class:"control"},ye={class:"field"},_e={class:"control"},be={class:"select is-fullwidth"},he=["value"],Ce={class:"card mt-4"},xe={class:"card-content"},we={class:"columns"},Ie={class:"column is-4"},ke={class:"image-preview"},Ve=["src"],Ee={class:"column is-8"},Ue={class:"file is-boxed"},Fe={class:"file-label"},De={key:0,class:"mt-4"},Me={key:1,class:"mt-4"},Le={class:"card mt-4"},Ne={class:"card-content"},Ae={class:"field"},Oe={class:"control"},Pe={class:"field"},Re={class:"control"},Se={class:"field"},Be={class:"control"},Te={class:"column is-4"},Ke={class:"card"},ze={class:"card-content"},je={class:"field"},qe={class:"checkbox"},We={class:"field"},$e={class:"checkbox"},Ge={class:"field"},He={class:"checkbox"},Je={class:"field"},Qe={class:"control"},Xe={class:"card mt-4"},Ye={class:"card-content"},Ze={class:"field is-grouped"},es={class:"control is-expanded"},ss={class:"control"},ts={__name:"CategoryForm",setup(as){const V=z(),D=Q(),C=c(!1),x=c(!1),i=c(null),y=c([]),w=c(!1),_=c(null),p=c(null),t=K({name:"",description:"",slug:"",parentId:null,image:"",isActive:!0,isVisible:!0,isFeatured:!1,displayOrder:0,metaTitle:"",metaDescription:"",metaKeywords:""}),d=I(()=>!!V.params.id),m=I(()=>V.params.id),M=I(()=>{if(!d.value)return y.value;const a=L(m.value);return y.value.filter(s=>s.id!==m.value&&!a.includes(s.id))}),L=a=>{const s=[],g=l=>{y.value.filter(b=>b.parentId===l).forEach(b=>{s.push(b.id),g(b.id)})};return g(a),s},N=async()=>{if(d.value){C.value=!0;try{const a=await v.getCategoryById(m.value);Object.keys(t).forEach(s=>{a[s]!==void 0&&(t[s]=a[s])}),t.image&&(_.value=t.image)}catch(a){console.error("Error fetching category:",a),i.value="Failed to load category data. Please try again."}finally{C.value=!1}}},A=async()=>{try{const a=await v.getCategories();a.categories&&(y.value=a.categories)}catch(a){console.error("Error fetching categories:",a)}},O=a=>{const s=a.target.files[0];if(s){if(!s.type.startsWith("image/")){i.value="Please select an image file.";return}p.value=s,_.value=URL.createObjectURL(s),d.value&&P()}},P=async()=>{if(!(!p.value||!d.value)){w.value=!0;try{const a=await v.uploadCategoryImage(m.value,p.value);a.imageUrl&&(t.image=a.imageUrl)}catch(a){console.error("Error uploading image:",a),i.value="Failed to upload image. Please try again."}finally{w.value=!1}}},R=()=>{t.image="",_.value=null,p.value=null},S=a=>{a.target.src="https://via.placeholder.com/200?text=No+Image"},B=async()=>{x.value=!0,i.value=null;try{let a;d.value?a=await v.updateCategory(m.value,t):(a=await v.createCategory(t),p.value&&a.category&&a.category.id&&await v.uploadCategoryImage(a.category.id,p.value)),D.push("/admin/categories")}catch(a){console.error("Error saving category:",a),i.value="Failed to save category. Please check your input and try again."}finally{x.value=!1}};return j(()=>{A(),N()}),(a,s)=>{const g=q("router-link");return r(),n("div",X,[e("div",Y,[e("div",Z,[e("div",ee,[e("h1",se,h(d.value?"Edit Category":"Add Category"),1)])]),e("div",te,[e("div",ae,[E(g,{to:"/admin/categories",class:"button is-light"},{default:U(()=>s[12]||(s[12]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Categories",-1)])),_:1})])])]),C.value?(r(),n("div",le,s[13]||(s[13]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading category data...",-1)]))):i.value?(r(),n("div",oe,[e("button",{class:"delete",onClick:s[0]||(s[0]=l=>i.value=null)}),f(" "+h(i.value),1)])):(r(),n("div",ie,[e("form",{onSubmit:J(B,["prevent"])},[e("div",ne,[e("div",re,[e("div",de,[s[20]||(s[20]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"Category Information")],-1)),e("div",ce,[e("div",ue,[s[14]||(s[14]=e("label",{class:"label"},"Category Name*",-1)),e("div",pe,[o(e("input",{class:"input",type:"text","onUpdate:modelValue":s[1]||(s[1]=l=>t.name=l),required:"",placeholder:"Enter category name"},null,512),[[u,t.name]])])]),e("div",ve,[s[15]||(s[15]=e("label",{class:"label"},"Description",-1)),e("div",me,[o(e("textarea",{class:"textarea","onUpdate:modelValue":s[2]||(s[2]=l=>t.description=l),placeholder:"Enter category description",rows:"4"},null,512),[[u,t.description]])])]),e("div",ge,[s[16]||(s[16]=e("label",{class:"label"},"Slug",-1)),e("div",fe,[o(e("input",{class:"input",type:"text","onUpdate:modelValue":s[3]||(s[3]=l=>t.slug=l),placeholder:"Enter category slug"},null,512),[[u,t.slug]])]),s[17]||(s[17]=e("p",{class:"help"},"Leave empty to auto-generate from name",-1))]),e("div",ye,[s[19]||(s[19]=e("label",{class:"label"},"Parent Category",-1)),e("div",_e,[e("div",be,[o(e("select",{"onUpdate:modelValue":s[4]||(s[4]=l=>t.parentId=l)},[s[18]||(s[18]=e("option",{value:null},"None (Root Category)",-1)),(r(!0),n($,null,G(M.value,l=>(r(),n("option",{key:l.id,value:l.id},h(l.name),9,he))),128))],512),[[W,t.parentId]])])])])])]),e("div",Ce,[s[25]||(s[25]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"Category Image")],-1)),e("div",xe,[e("div",we,[e("div",Ie,[e("div",ke,[e("img",{src:_.value||"https://via.placeholder.com/200?text=No+Image",alt:"Category image preview",onError:S},null,40,Ve)])]),e("div",Ee,[e("div",Ue,[e("label",Fe,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:O},null,32),s[21]||(s[21]=e("span",{class:"file-cta"},[e("span",{class:"file-icon"},[e("i",{class:"fas fa-upload"})]),e("span",{class:"file-label"}," Choose an image... ")],-1))])]),s[24]||(s[24]=e("p",{class:"help mt-2"},"Recommended size: 800x600 pixels",-1)),w.value?(r(),n("div",De,s[22]||(s[22]=[e("progress",{class:"progress is-primary",max:"100"},null,-1),e("p",{class:"help"},"Uploading image...",-1)]))):F("",!0),t.image?(r(),n("div",Me,[e("button",{type:"button",class:"button is-danger is-small",onClick:R},s[23]||(s[23]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Remove Image",-1)]))])):F("",!0)])])])]),e("div",Le,[s[30]||(s[30]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"SEO Information")],-1)),e("div",Ne,[e("div",Ae,[s[26]||(s[26]=e("label",{class:"label"},"Meta Title",-1)),e("div",Oe,[o(e("input",{class:"input",type:"text","onUpdate:modelValue":s[5]||(s[5]=l=>t.metaTitle=l),placeholder:"Enter meta title"},null,512),[[u,t.metaTitle]])]),s[27]||(s[27]=e("p",{class:"help"},"Leave empty to use category name",-1))]),e("div",Pe,[s[28]||(s[28]=e("label",{class:"label"},"Meta Description",-1)),e("div",Re,[o(e("textarea",{class:"textarea","onUpdate:modelValue":s[6]||(s[6]=l=>t.metaDescription=l),placeholder:"Enter meta description",rows:"3"},null,512),[[u,t.metaDescription]])])]),e("div",Se,[s[29]||(s[29]=e("label",{class:"label"},"Meta Keywords",-1)),e("div",Be,[o(e("input",{class:"input",type:"text","onUpdate:modelValue":s[7]||(s[7]=l=>t.metaKeywords=l),placeholder:"Enter meta keywords (comma separated)"},null,512),[[u,t.metaKeywords]])])])])])]),e("div",Te,[e("div",Ke,[s[36]||(s[36]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"Status & Visibility")],-1)),e("div",ze,[e("div",je,[e("label",qe,[o(e("input",{type:"checkbox","onUpdate:modelValue":s[8]||(s[8]=l=>t.isActive=l)},null,512),[[k,t.isActive]]),s[31]||(s[31]=f(" Active "))])]),e("div",We,[e("label",$e,[o(e("input",{type:"checkbox","onUpdate:modelValue":s[9]||(s[9]=l=>t.isVisible=l)},null,512),[[k,t.isVisible]]),s[32]||(s[32]=f(" Visible in navigation "))])]),e("div",Ge,[e("label",He,[o(e("input",{type:"checkbox","onUpdate:modelValue":s[10]||(s[10]=l=>t.isFeatured=l)},null,512),[[k,t.isFeatured]]),s[33]||(s[33]=f(" Featured category "))])]),e("div",Je,[s[34]||(s[34]=e("label",{class:"label"},"Display Order",-1)),e("div",Qe,[o(e("input",{class:"input",type:"number","onUpdate:modelValue":s[11]||(s[11]=l=>t.displayOrder=l),min:"0",step:"1"},null,512),[[u,t.displayOrder]])]),s[35]||(s[35]=e("p",{class:"help"},"Lower numbers appear first",-1))])])]),e("div",Xe,[e("div",Ye,[e("div",Ze,[e("div",es,[e("button",{type:"submit",class:H(["button is-primary is-fullwidth",{"is-loading":x.value}])},h(d.value?"Update Category":"Create Category"),3)]),e("div",ss,[E(g,{to:"/admin/categories",class:"button is-light"},{default:U(()=>s[37]||(s[37]=[f(" Cancel ")])),_:1})])])])])])])],32)]))])}}},is=T(ts,[["__scopeId","data-v-1cf0d470"]]);export{is as default};
