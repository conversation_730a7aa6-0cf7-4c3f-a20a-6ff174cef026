﻿using Marketplace.Application.Commands.Meta;
using Marketplace.Application.Commands.Product;
using Marketplace.Application.Commands.ProductImage;
using Marketplace.Application.Queries.Product;
using Marketplace.Application.Queries.ProductImage;
using Marketplace.Application.Requests.Common;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplace.Presentation.Controllers.Admin;

[ApiController]
[Route("api/admin/products")]
[Authorize(Roles = "Admin,Moderator")]
public class AdminProductController : BasicApiController
{
    private readonly IMediator _mediator;

    public AdminProductController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] PaginationRequest request,
        [FromQuery] string? status = null,
        [FromQuery] Guid? categoryId = null,
        [FromQuery] string? stock = null,
        CancellationToken cancellationToken = default)
    {
        // Parse status if provided
        Domain.Entities.ProductStatus? productStatus = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<Domain.Entities.ProductStatus>(status, true, out var parsedStatus))
        {
            productStatus = parsedStatus;
        }

        var query = new GetAllProductQuery(
            request.Filter,
            request.OrderBy,
            request.Descending,
            request.Page,
            request.PageSize,
            productStatus,
            categoryId,
            stock);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetProductQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null ? Ok(response) : NotFound(ApiResponse.Failure("Продукт не знайдено."));
    }

    [HttpGet("{id}/with-images")]
    public async Task<IActionResult> GetByIdWithImages([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetProductWithImagesQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null
            ? Ok(ApiResponse<ProductWithImagesResponse>.SuccessWithData(response))
            : NotFound(ApiResponse.Failure("Продукт не знайдено."));
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] StoreProductCommand command, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(command, cancellationToken);

        return response != null
            ? CreatedAtAction(nameof(GetById), new { id = response }, ApiResponse<Guid>.SuccessWithData(response, "Продукт успішно створено."))
            : BadRequest(ApiResponse.Failure("Не вдалося створити продукт."));
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update([FromRoute] Guid id, [FromBody] UpdateProductCommand command, CancellationToken cancellationToken)
    {
        var commandWithId = command with { Id = id };
        var result = await _mediator.Send(commandWithId, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Продукт успішно оновлено."))
            : NotFound(ApiResponse.Failure("Продукт не знайдено."));
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var command = new DeleteProductCommand(id);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Продукт не знайдено."));
    }

    [HttpGet("{id}/images")]
    public async Task<IActionResult> GetImages([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetProductImagesQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<List<ProductImageResponse>>.SuccessWithData(response));
    }

    [HttpPost("{id}/images")]
    public async Task<IActionResult> UploadImages([FromRoute] Guid id, IFormFileCollection images, CancellationToken cancellationToken)
    {
        try
        {
            // Debug logging
            Console.WriteLine($"Received request to upload images for product {id}");
            Console.WriteLine($"Number of files received: {images?.Count ?? 0}");

            if (images == null || images.Count == 0)
            {
                return BadRequest(ApiResponse.Failure("Не вибрано жодного файлу для завантаження."));
            }

            // Validate file types and sizes
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/jfif" };
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".jfif" };
            var maxFileSize = 5 * 1024 * 1024; // 5MB

            foreach (var file in images)
            {
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var isValidType = allowedTypes.Contains(file.ContentType.ToLower()) || allowedExtensions.Contains(fileExtension);

                if (!isValidType)
                {
                    return BadRequest(ApiResponse.Failure($"Файл {file.FileName} має недопустимий тип. Дозволені типи: JPEG, JPG, PNG, GIF, WebP, JFIF."));
                }

                if (file.Length > maxFileSize)
                {
                    return BadRequest(ApiResponse.Failure($"Файл {file.FileName} занадто великий. Максимальний розмір: 5MB."));
                }
            }

            // Перевіряємо, чи продукт існує
            var productQuery = new GetProductQuery(id);
            var product = await _mediator.Send(productQuery, cancellationToken);

            if (product == null)
            {
                return NotFound(ApiResponse.Failure("Продукт не знайдено."));
            }

            // Створюємо команду для завантаження кількох зображень
            var command = new UploadMultipleProductImagesCommand(id, images);
            var results = await _mediator.Send(command, cancellationToken);

            return Ok(ApiResponse<List<ProductImageResponse>>.SuccessWithData(results, "Зображення успішно завантажено."));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error uploading images: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return BadRequest(ApiResponse.Failure($"Помилка завантаження зображень: {ex.Message}"));
        }
    }

    [HttpPost("{id}/images/single")]
    public async Task<IActionResult> UploadSingleImage([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        try
        {
            Console.WriteLine($"Received request to upload single image for product {id}");
            Console.WriteLine($"Content-Type: {Request.ContentType}");
            Console.WriteLine($"Content-Length: {Request.ContentLength}");

            // Check if request has form content
            if (!Request.HasFormContentType)
            {
                return BadRequest(ApiResponse.Failure("Request must be multipart/form-data"));
            }

            // Read form data
            var form = await Request.ReadFormAsync(cancellationToken);
            var image = form.Files.FirstOrDefault();

            if (image == null)
            {
                return BadRequest(ApiResponse.Failure("Не вибрано файл для завантаження."));
            }

            Console.WriteLine($"File: {image.FileName}, Size: {image.Length}, ContentType: {image.ContentType}");

            // Validate file type and size
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/jfif" };
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".jfif" };
            var maxFileSize = 5 * 1024 * 1024; // 5MB

            var fileExtension = Path.GetExtension(image.FileName).ToLower();
            var isValidType = allowedTypes.Contains(image.ContentType.ToLower()) || allowedExtensions.Contains(fileExtension);

            if (!isValidType)
            {
                return BadRequest(ApiResponse.Failure($"Файл {image.FileName} має недопустимий тип. Дозволені типи: JPEG, JPG, PNG, GIF, WebP, JFIF."));
            }

            if (image.Length > maxFileSize)
            {
                return BadRequest(ApiResponse.Failure($"Файл {image.FileName} занадто великий. Максимальний розмір: 5MB."));
            }

            // Перевіряємо, чи продукт існує
            var productQuery = new GetProductQuery(id);
            var product = await _mediator.Send(productQuery, cancellationToken);

            if (product == null)
            {
                return NotFound(ApiResponse.Failure("Продукт не знайдено."));
            }

            // Створюємо команду для завантаження одного зображення
            var command = new UploadProductImageCommand(id, image);
            var result = await _mediator.Send(command, cancellationToken);

            Console.WriteLine($"Upload result: Id={result.Id}, FileUrl={result.FileUrl}");

            return result != null
                ? Ok(ApiResponse<Guid>.SuccessWithData(result.Id, "Зображення успішно завантажено."))
                : BadRequest(ApiResponse.Failure("Не вдалося завантажити зображення."));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error uploading single image: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return BadRequest(ApiResponse.Failure($"Помилка завантаження зображення: {ex.Message}"));
        }
    }

    [HttpDelete("{productId}/images/{imageId}")]
    public async Task<IActionResult> DeleteImage([FromRoute] Guid productId, [FromRoute] Guid imageId, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var productQuery = new GetProductQuery(productId);
        var product = await _mediator.Send(productQuery, cancellationToken);

        if (product == null)
        {
            return NotFound(ApiResponse.Failure("Продукт не знайдено."));
        }

        // Видаляємо зображення
        var command = new DeleteProductImageCommand(productId, imageId);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound(ApiResponse.Failure("Зображення не знайдено."));
    }

    [HttpPatch("{productId}/images/{imageId}/main")]
    public async Task<IActionResult> SetMainImage([FromRoute] Guid productId, [FromRoute] Guid imageId, CancellationToken cancellationToken)
    {
        var command = new SetMainProductImageCommand(productId, imageId);
        var result = await _mediator.Send(command, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Головне зображення встановлено."))
            : BadRequest(ApiResponse.Failure("Не вдалося встановити головне зображення."));
    }
    [HttpPost("{id}/meta-image")]
    [AllowAnonymous]
    public async Task<IActionResult> UploadMetaImage([FromRoute] Guid id, IFormFile image, CancellationToken cancellationToken)
    {
        try
        {
            Console.WriteLine($"=== UPLOAD META IMAGE START ===");
            Console.WriteLine($"Product ID: {id}");
            Console.WriteLine($"Image: {image?.FileName}, {image?.ContentType}, {image?.Length} bytes");

            if (image == null)
            {
                Console.WriteLine("ERROR: No image provided");
                return BadRequest(new { error = "No image provided" });
            }

            // Перевіряємо, чи продукт існує
            var productQuery = new GetProductQuery(id);
            var product = await _mediator.Send(productQuery, cancellationToken);

            if (product == null)
            {
                Console.WriteLine("ERROR: Product not found");
                return NotFound();
            }

            Console.WriteLine($"Product found: {product.Name}");

            // Зчитуємо файл у масив байтів
            using var memoryStream = new MemoryStream();
            await image.CopyToAsync(memoryStream, cancellationToken);
            var imageBytes = memoryStream.ToArray();

            Console.WriteLine($"Image bytes read: {imageBytes.Length}");

            // Створюємо команду для завантаження зображення
            var command = new UploadMetaImageCommand("product", id, image);
            Console.WriteLine($"Sending command: {command}");

            var result = await _mediator.Send(command, cancellationToken);

            Console.WriteLine($"Command result: {result?.FileUrl}");
            Console.WriteLine($"=== UPLOAD META IMAGE END ===");

            return result != null ? Ok(new { success = true, fileUrl = result.FileUrl }) : BadRequest();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR in UploadMetaImage: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpDelete("{id}/meta-image")]
    public async Task<IActionResult> DeleteMetaImage([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        // Перевіряємо, чи продукт існує
        var productQuery = new GetProductQuery(id);
        var product = await _mediator.Send(productQuery, cancellationToken);

        if (product == null)
        {
            return NotFound();
        }

        // Видаляємо мета-зображення
        var command = new DeleteMetaImageCommand("product", id);
        var result = await _mediator.Send(command, cancellationToken);

        return result ? NoContent() : NotFound();
    }

    // Moderator functionality for product approval/rejection
    [HttpGet("pending")]
    public async Task<IActionResult> GetPendingProducts(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetPendingProductsQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<PaginatedResponse<ProductResponse>>.SuccessWithData(response));
    }

    [HttpPost("{id}/approve")]
    public async Task<IActionResult> ApproveProduct([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId()!.Value;

        var command = new ModeratorApproveProductCommand(id, userId);
        var result = await _mediator.Send(command, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Продукт успішно схвалено."))
            : NotFound(ApiResponse.Failure("Продукт не знайдено або не може бути схвалений."));
    }

    [HttpPost("{id}/reject")]
    public async Task<IActionResult> RejectProduct(
        [FromRoute] Guid id,
        [FromBody] ModeratorRejectProductCommand command,
        CancellationToken cancellationToken)
    {
        var commandWithId = command with { ProductId = id };
        var result = await _mediator.Send(commandWithId, cancellationToken);

        return result
            ? Ok(ApiResponse.SuccessResponse("Продукт успішно відхилено."))
            : NotFound(ApiResponse.Failure("Продукт не знайдено або не може бути відхилений."));
    }

    [HttpPost("bulk-approve")]
    public async Task<IActionResult> BulkApproveProducts(
        [FromBody] ModeratorBulkApproveProductsCommand command,
        CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId()!.Value;

        var commandWithModeratorId = command with { ModeratorId = userId };
        var result = await _mediator.Send(commandWithModeratorId, cancellationToken);

        return Ok(ApiResponse<object>.SuccessWithData(
            new { approvedCount = result },
            $"Успішно схвалено {result} продуктів."));
    }

    [HttpPost("bulk-reject")]
    public async Task<IActionResult> BulkRejectProducts(
        [FromBody] ModeratorBulkRejectProductsCommand command,
        CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(command, cancellationToken);

        return Ok(ApiResponse<object>.SuccessWithData(
            new { rejectedCount = result },
            $"Успішно відхилено {result} продуктів."));
    }

    // Simple test endpoint for file upload
    [HttpPost("test-upload")]
    [AllowAnonymous]
    public async Task<IActionResult> TestUpload()
    {
        try
        {
            Console.WriteLine("=== TEST UPLOAD START ===");
            Console.WriteLine($"Content-Type: {Request.ContentType}");
            Console.WriteLine($"Content-Length: {Request.ContentLength}");
            Console.WriteLine($"Has Form Content: {Request.HasFormContentType}");

            if (!Request.HasFormContentType)
            {
                return BadRequest(new { error = "Request must be multipart/form-data" });
            }

            var form = await Request.ReadFormAsync();
            var file = form.Files.FirstOrDefault();

            if (file == null)
            {
                return BadRequest(new { error = "No file provided" });
            }

            Console.WriteLine($"File received: {file.FileName}, {file.ContentType}, {file.Length} bytes");

            return Ok(new
            {
                success = true,
                fileName = file.FileName,
                contentType = file.ContentType,
                size = file.Length,
                message = "File received successfully"
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test upload error: {ex.Message}");
            return BadRequest(new { error = ex.Message });
        }
    }
}
