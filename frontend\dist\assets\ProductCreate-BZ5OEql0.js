import{s as a,e as n,o as u}from"./index-BtyG65bR.js";import m from"./ProductEdit-DMGe_nnb.js";import"./products-d_ocQBUS.js";import"./companies-Cx9IcxUK.js";const f={__name:"ProductCreate",emits:["save","cancel"],setup(i,{emit:c}){const t=n(),o=c,r=e=>{console.log("Product created:",e),o("save",e),t.push("/admin/products")},s=()=>{o("cancel"),t.push("/admin/products")};return(e,p)=>(u(),a(m,{"is-create":!0,onSave:r,onCancel:s}))}};export{f as default};
