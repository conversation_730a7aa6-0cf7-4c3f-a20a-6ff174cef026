{"name": "frontend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "gapi-script": "^1.2.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuex": "^4.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.3"}}