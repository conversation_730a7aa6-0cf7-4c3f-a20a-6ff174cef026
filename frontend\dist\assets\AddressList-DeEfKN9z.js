import{q as b,_ as z,g as v,x as re,c as f,o as p,a as e,A as k,B as S,n as O,t as g,m as ne,h as M,i as de,b as R,s as q,k as T,F as ie,p as ue,J as ce,w as ve,d as pe,r as ge}from"./index-C1YYMYJd.js";import{S as me}from"./SearchAndFilters-CqvX4-wB.js";import{<PERSON> as he}from"./Pagination-BHQDGWaJ.js";const x={async getAddresses(i={}){var d,a;try{return(await b.get("/api/admin/addresses",{params:i})).data}catch(o){throw console.error("Error fetching addresses:",o),new Error(((a=(d=o.response)==null?void 0:d.data)==null?void 0:a.message)||"Failed to load addresses")}},async getAddressById(i){var d,a;try{return(await b.get(`/api/admin/addresses/${i}`)).data}catch(o){throw console.error("Error fetching address:",o),new Error(((a=(d=o.response)==null?void 0:d.data)==null?void 0:a.message)||"Failed to load address details")}},async getAddressesByUserId(i,d={}){var a,o;try{return(await b.get(`/api/admin/addresses/user/${i}`,{params:d})).data}catch(t){throw console.error("Error fetching user addresses:",t),new Error(((o=(a=t.response)==null?void 0:a.data)==null?void 0:o.message)||"Failed to load user addresses")}},async createAddress(i){var d,a;try{return(await b.post("/api/admin/addresses",i)).data}catch(o){throw console.error("Error creating address:",o),new Error(((a=(d=o.response)==null?void 0:d.data)==null?void 0:a.message)||"Failed to create address")}},async updateAddress(i,d){var a,o;try{return(await b.put(`/api/admin/addresses/${i}`,d)).data}catch(t){throw console.error("Error updating address:",t),new Error(((o=(a=t.response)==null?void 0:a.data)==null?void 0:o.message)||"Failed to update address")}},async deleteAddress(i){var d,a;try{return(await b.delete(`/api/admin/addresses/${i}`)).data}catch(o){throw console.error("Error deleting address:",o),new Error(((a=(d=o.response)==null?void 0:d.data)==null?void 0:a.message)||"Failed to delete address")}},async getAddressStats(){var i,d;try{return(await b.get("/api/admin/addresses/stats")).data.data}catch(a){throw console.error("Error fetching address stats:",a),new Error(((d=(i=a.response)==null?void 0:i.data)==null?void 0:d.message)||"Failed to load address statistics")}}},fe={class:"field"},ye={class:"control"},be={class:"field"},Ce={class:"control"},_e={class:"field"},ke={class:"control"},we={class:"field"},Ae={class:"control"},$e={class:"field"},Fe={class:"control"},Se={class:"field is-grouped"},xe={class:"control"},Ee=["disabled"],Ue={class:"control"},Ve=["disabled"],Ie={__name:"AddressForm",props:{address:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["submit","cancel"],setup(i,{emit:d}){const a=i,o=d,t=v({userId:"",region:"",city:"",street:"",postalCode:""});re(()=>a.address,u=>{var l,r,C,_;u?t.value={userId:u.userId||"",region:((l=u.addressVO)==null?void 0:l.region)||u.region||"",city:((r=u.addressVO)==null?void 0:r.city)||u.city||"",street:((C=u.addressVO)==null?void 0:C.street)||u.street||"",postalCode:((_=u.addressVO)==null?void 0:_.postalCode)||u.postalCode||""}:t.value={userId:"",region:"",city:"",street:"",postalCode:""}},{immediate:!0});const m=()=>{const u={...t.value,addressVO:{region:t.value.region,city:t.value.city,street:t.value.street,postalCode:t.value.postalCode}};u.userId||delete u.userId,o("submit",u)};return(u,l)=>(p(),f("form",{onSubmit:ne(m,["prevent"])},[e("div",fe,[l[6]||(l[6]=e("label",{class:"label"},"User ID (Optional)",-1)),e("div",ye,[k(e("input",{type:"text",class:"input","onUpdate:modelValue":l[0]||(l[0]=r=>t.value.userId=r),placeholder:"Enter user ID or leave empty"},null,512),[[S,t.value.userId]])])]),e("div",be,[l[7]||(l[7]=e("label",{class:"label"},"Region *",-1)),e("div",Ce,[k(e("input",{type:"text",class:"input","onUpdate:modelValue":l[1]||(l[1]=r=>t.value.region=r),placeholder:"Enter region",required:""},null,512),[[S,t.value.region]])])]),e("div",_e,[l[8]||(l[8]=e("label",{class:"label"},"City *",-1)),e("div",ke,[k(e("input",{type:"text",class:"input","onUpdate:modelValue":l[2]||(l[2]=r=>t.value.city=r),placeholder:"Enter city",required:""},null,512),[[S,t.value.city]])])]),e("div",we,[l[9]||(l[9]=e("label",{class:"label"},"Street *",-1)),e("div",Ae,[k(e("input",{type:"text",class:"input","onUpdate:modelValue":l[3]||(l[3]=r=>t.value.street=r),placeholder:"Enter street address",required:""},null,512),[[S,t.value.street]])])]),e("div",$e,[l[10]||(l[10]=e("label",{class:"label"},"Postal Code *",-1)),e("div",Fe,[k(e("input",{type:"text",class:"input","onUpdate:modelValue":l[4]||(l[4]=r=>t.value.postalCode=r),placeholder:"Enter postal code",required:""},null,512),[[S,t.value.postalCode]])])]),e("div",Se,[e("div",xe,[e("button",{type:"submit",class:O(["button is-primary",{"is-loading":i.loading}]),disabled:i.loading},g(i.address?"Update":"Create")+" Address ",11,Ee)]),e("div",Ue,[e("button",{type:"button",class:"button is-light",onClick:l[5]||(l[5]=r=>u.$emit("cancel")),disabled:i.loading}," Cancel ",8,Ve)])])],32))}},Ne=z(Ie,[["__scopeId","data-v-38007dc2"]]),Oe={class:"address-list"},De={class:"level"},Be={class:"level-right"},Pe={class:"level-item"},Le={class:"buttons"},Me=["disabled"],Re={key:0,class:"has-text-centered py-6"},qe={key:1,class:"notification is-danger"},Te={key:2,class:"card"},ze={class:"card-content"},je={class:"table is-fullwidth is-hoverable"},He={class:"checkbox"},Je=["checked","indeterminate"],Qe={class:"checkbox"},Ge=["value"],Ke={key:1,class:"has-text-grey"},We={class:"buttons"},Xe=["onClick","disabled"],Ye=["onClick","disabled"],Ze={class:"modal-card"},es={class:"modal-card-head"},ss={class:"modal-card-title"},ts={class:"modal-card-body"},as={__name:"AddressList",setup(i){const d=(n,s)=>{let F;return function(...U){const V=()=>{clearTimeout(F),n(...U)};clearTimeout(F),F=setTimeout(V,s)}},a=v([]),o=v(!1),t=v(null),m=v(!1),u=v(!1),l=v(""),r=v([]),C=v(!0),_=v(!1),w=v(!1),A=v(null),$=v({region:"",hasUser:""}),j=v({region:{type:"text",label:"Region",placeholder:"Filter by region..."},hasUser:{type:"select",label:"User Status",options:[{value:"true",label:"Has User"},{value:"false",label:"No User"}]}}),H=v("createdAt"),J=v("desc"),y=v(1),I=v(1),D=v(0),Q=v(15),B=M(()=>a.value.length>0&&r.value.length===a.value.length),G=M(()=>r.value.length>0&&r.value.length<a.value.length),h=async()=>{o.value=!0,t.value=null;try{const n={filter:l.value,region:$.value.region,hasUser:$.value.hasUser,orderBy:H.value,descending:J.value==="desc",page:y.value,pageSize:Q.value},s=await x.getAddresses(n);a.value=s.data||[],y.value=s.currentPage||1,I.value=s.totalPages||1,D.value=s.totalItems||0,r.value=[]}catch(n){t.value=n.message||"Failed to load addresses",a.value=[]}finally{o.value=!1,C.value=!1}},K=n=>{l.value=n,y.value=1,Z()},W=()=>{y.value=1,h()},X=()=>{l.value="",$.value.region="",$.value.hasUser="",y.value=1,h()},Y=n=>{y.value=n,h()},Z=d(h,300),ee=()=>{B.value?r.value=[]:r.value=a.value.map(n=>n.id)},se=n=>{A.value=n,w.value=!0},E=()=>{_.value=!1,w.value=!1,A.value=null},te=async n=>{u.value=!0;try{w.value&&A.value?await x.updateAddress(A.value.id,n):await x.createAddress(n),E(),await h()}catch(s){t.value=s.message||"Failed to save address"}finally{u.value=!1}},ae=async n=>{if(confirm("Are you sure you want to delete this address?")){m.value=!0;try{await x.deleteAddress(n),await h()}catch(s){t.value=s.message||"Failed to delete address"}finally{m.value=!1}}},le=async()=>{if(confirm(`Are you sure you want to delete ${r.value.length} selected addresses?`)){m.value=!0;try{for(const n of r.value)await x.deleteAddress(n);await h()}catch(n){t.value=n.message||"Failed to delete addresses"}finally{m.value=!1}}},oe=n=>n?new Date(n).toLocaleDateString():"N/A";return de(()=>{h()}),(n,s)=>{const F=ge("router-link");return p(),f("div",Oe,[e("div",De,[s[5]||(s[5]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Address Management")])],-1)),e("div",Be,[e("div",Pe,[e("div",Le,[e("button",{class:"button is-primary",onClick:s[0]||(s[0]=c=>_.value=!0)},s[3]||(s[3]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add Address",-1)])),r.value.length>0?(p(),f("button",{key:0,class:"button is-danger",onClick:le,disabled:r.value.length===0||m.value},[s[4]||(s[4]=e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)),e("span",null,"Delete Selected ("+g(r.value.length)+")",1)],8,Me)):T("",!0)])])])]),R(me,{search:l.value,"onUpdate:search":s[1]||(s[1]=c=>l.value=c),filters:$.value,"filter-fields":j.value,"search-label":"Search Addresses","search-placeholder":"Search by region, city, street, or user name...","search-column-class":"is-4","total-items":D.value,"item-name":"addresses",loading:o.value,onSearchChanged:K,onFilterChanged:W,onResetFilters:X},null,8,["search","filters","filter-fields","total-items","loading"]),o.value&&C.value?(p(),f("div",Re,s[6]||(s[6]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading addresses...",-1)]))):t.value?(p(),f("div",qe,[e("p",null,g(t.value),1),e("button",{class:"button is-light mt-2",onClick:h},s[7]||(s[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(p(),f("div",Te,[e("div",ze,[e("div",{class:O(["table-container",{"is-loading":o.value&&!C.value}])},[e("table",je,[e("thead",null,[e("tr",null,[e("th",null,[e("label",He,[e("input",{type:"checkbox",onChange:ee,checked:B.value,indeterminate:G.value},null,40,Je)])]),s[8]||(s[8]=e("th",null,"User",-1)),s[9]||(s[9]=e("th",null,"Region",-1)),s[10]||(s[10]=e("th",null,"City",-1)),s[11]||(s[11]=e("th",null,"Street",-1)),s[12]||(s[12]=e("th",null,"Postal Code",-1)),s[13]||(s[13]=e("th",null,"Created",-1)),s[14]||(s[14]=e("th",null,"Actions",-1))])]),e("tbody",null,[(p(!0),f(ie,null,ue(a.value,c=>{var U,V,P,L;return p(),f("tr",{key:c.id},[e("td",null,[e("label",Qe,[k(e("input",{type:"checkbox",value:c.id,"onUpdate:modelValue":s[2]||(s[2]=N=>r.value=N)},null,8,Ge),[[ce,r.value]])])]),e("td",null,[c.userId?(p(),q(F,{key:0,to:{name:"AdminUserDetail",params:{id:c.userId}},class:"has-text-link"},{default:ve(()=>[pe(g(c.userName||"Unknown User"),1)]),_:2},1032,["to"])):(p(),f("span",Ke,"No User"))]),e("td",null,g(((U=c.addressVO)==null?void 0:U.region)||c.region||"N/A"),1),e("td",null,g(((V=c.addressVO)==null?void 0:V.city)||c.city||"N/A"),1),e("td",null,g(((P=c.addressVO)==null?void 0:P.street)||c.street||"N/A"),1),e("td",null,g(((L=c.addressVO)==null?void 0:L.postalCode)||c.postalCode||"N/A"),1),e("td",null,g(oe(c.createdAt)),1),e("td",null,[e("div",We,[e("button",{class:"button is-small is-info",onClick:N=>se(c),disabled:m.value},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})],-1),e("span",null,"Edit",-1)]),8,Xe),e("button",{class:"button is-small is-danger",onClick:N=>ae(c.id),disabled:m.value},s[16]||(s[16]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,Ye)])])])}),128))])])],2)])])),I.value>1?(p(),q(he,{key:3,"current-page":y.value,"total-pages":I.value,onPageChanged:Y},null,8,["current-page","total-pages"])):T("",!0),e("div",{class:O(["modal",{"is-active":_.value||w.value}])},[e("div",{class:"modal-background",onClick:E}),e("div",Ze,[e("header",es,[e("p",ss,g(w.value?"Edit Address":"Create New Address"),1),e("button",{class:"delete",onClick:E})]),e("section",ts,[R(Ne,{address:A.value,loading:u.value,onSubmit:te,onCancel:E},null,8,["address","loading"])])])],2)])}}},ns=z(as,[["__scopeId","data-v-143baf73"]]);export{ns as default};
