import{s as a,e as n,o as u}from"./index-DMg5qKr1.js";import m from"./ProductEdit-DAzH4Um_.js";import"./products-BHT6Tynz.js";import"./companies-br2VMpfN.js";const f={__name:"ProductCreate",emits:["save","cancel"],setup(i,{emit:c}){const t=n(),o=c,r=e=>{console.log("Product created:",e),o("save",e),t.push("/admin/products")},s=()=>{o("cancel"),t.push("/admin/products")};return(e,p)=>(u(),a(m,{"is-create":!0,onSave:r,onCancel:s}))}};export{f as default};
