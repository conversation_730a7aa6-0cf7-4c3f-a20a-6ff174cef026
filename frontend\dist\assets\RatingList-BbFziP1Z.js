import{q as y,_ as z,h as w,g as D,c as g,a as e,b as j,s as q,k as E,t as v,D as d,n as f,F as k,p as S,A as G,L as H,o as u}from"./index-BtyG65bR.js";import{u as J,S as K}from"./useAdminSearch-DD4t1sR1.js";import{P as Q}from"./Pagination-Cgfsjo8C.js";const R={async getRatings(i={}){var n,l;try{return(await y.get("/api/admin/ratings",{params:i})).data.data}catch(s){throw console.error("Error fetching ratings:",s),new Error(((l=(n=s.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to load ratings")}},async getRatingById(i){var n,l;try{return(await y.get(`/api/admin/ratings/${i}`)).data.data}catch(s){throw console.error("Error fetching rating:",s),new Error(((l=(n=s.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to load rating details")}},async updateRating(i,n){var l,s;try{return(await y.put(`/api/admin/ratings/${i}`,n)).data}catch(p){throw console.error("Error updating rating:",p),new Error(((s=(l=p.response)==null?void 0:l.data)==null?void 0:s.message)||"Failed to update rating")}},async deleteRating(i){var n,l;try{return(await y.delete(`/api/admin/ratings/${i}`)).data}catch(s){throw console.error("Error deleting rating:",s),new Error(((l=(n=s.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to delete rating")}},async bulkDeleteRatings(i){var n,l;try{return(await y.post("/api/admin/ratings/bulk-delete",{ids:i})).data}catch(s){throw console.error("Error bulk deleting ratings:",s),new Error(((l=(n=s.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to delete ratings")}},async getRatingStats(){var i,n;try{return(await y.get("/api/admin/ratings/stats")).data.data}catch(l){throw console.error("Error fetching rating stats:",l),new Error(((n=(i=l.response)==null?void 0:i.data)==null?void 0:n.message)||"Failed to load rating statistics")}}},W={class:"rating-list"},X={class:"level"},Y={class:"level-right"},Z={class:"level-item"},ee={class:"buttons"},te=["disabled"],ae={key:0,class:"has-text-centered py-6"},se={key:1,class:"notification is-danger"},le={key:2,class:"card"},ne={class:"card-content"},re={class:"table is-fullwidth is-hoverable"},oe={class:"checkbox"},ie=["checked","indeterminate"],de={class:"checkbox"},ce=["value"],ue={class:"stars"},ge={class:"ml-1"},ve={class:"stars"},pe={class:"ml-1"},he={class:"stars"},me={class:"ml-1"},ye={class:"stars"},be={class:"ml-1"},fe={class:"buttons"},ke=["onClick","disabled"],Se={__name:"RatingList",setup(i){const n=[{key:"minRating",label:"Min Rating",type:"select",columnClass:"is-3",options:[{value:"",label:"Any"},{value:"1",label:"1 Star"},{value:"2",label:"2 Stars"},{value:"3",label:"3 Stars"},{value:"4",label:"4 Stars"},{value:"5",label:"5 Stars"}]},{key:"maxRating",label:"Max Rating",type:"select",columnClass:"is-3",options:[{value:"",label:"Any"},{value:"1",label:"1 Star"},{value:"2",label:"2 Stars"},{value:"3",label:"3 Stars"},{value:"4",label:"4 Stars"},{value:"5",label:"5 Stars"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",options:[{value:"CreatedAt",label:"Date Created"},{value:"UpdatedAt",label:"Date Updated"},{value:"Service",label:"Service Rating"},{value:"DeliveryTime",label:"Delivery Rating"},{value:"Accuracy",label:"Accuracy Rating"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}],{items:l,loading:s,error:p,isFirstLoad:C,currentPage:x,totalPages:F,totalItems:P,filters:h,fetchData:_,handlePageChange:B}=J({fetchFunction:R.getRatings,defaultFilters:{minRating:"",maxRating:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:15,clientSideSearch:!1}),b=w(()=>l.value.map(a=>({...a,average:(a.service+a.deliveryTime+a.accuracy)/3}))),m=D(!1),c=D([]),A=w(()=>b.value.length>0&&c.value.length===b.value.length),L=w(()=>c.value.length>0&&c.value.length<b.value.length),U=a=>{h.search=a},N=(a,t)=>{h[a]=t},T=()=>{Object.keys(h).forEach(a=>{var t,r,o;a==="search"?h[a]="":h[a]=((o=(r=(t=n.find(I=>I.key===a))==null?void 0:t.options)==null?void 0:r[0])==null?void 0:o.value)||""}),_(1)},$=()=>{A.value?c.value=[]:c.value=b.value.map(a=>a.id)},M=async a=>{if(confirm("Are you sure you want to delete this rating?")){m.value=!0;try{await R.deleteRating(a),await _()}catch(t){p.value=t.message||"Failed to delete rating"}finally{m.value=!1}}},O=async()=>{if(confirm(`Are you sure you want to delete ${c.value.length} selected ratings?`)){m.value=!0;try{await R.bulkDeleteRatings(c.value),await _()}catch(a){p.value=a.message||"Failed to delete ratings"}finally{m.value=!1}}},V=a=>a?new Date(a).toLocaleDateString():"N/A";return(a,t)=>(u(),g("div",W,[e("div",X,[t[3]||(t[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Ratings Management")])],-1)),e("div",Y,[e("div",Z,[e("div",ee,[c.value.length>0?(u(),g("button",{key:0,class:"button is-danger",onClick:O,disabled:c.value.length===0||m.value},[t[2]||(t[2]=e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)),e("span",null,"Delete Selected ("+v(c.value.length)+")",1)],8,te)):E("",!0)])])])]),j(K,{filters:d(h),"filter-fields":n,"search-label":"Search Ratings","search-placeholder":"Search by product name, user name, or comment...","search-column-class":"is-4","total-items":d(P),"item-name":"ratings",loading:d(s),onSearchChanged:U,onFilterChanged:N,onResetFilters:T},null,8,["filters","total-items","loading"]),d(s)&&d(C)?(u(),g("div",ae,t[4]||(t[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading ratings...",-1)]))):d(p)?(u(),g("div",se,[e("p",null,v(d(p)),1),e("button",{class:"button is-light mt-2",onClick:t[0]||(t[0]=(...r)=>a.fetchRatings&&a.fetchRatings(...r))},t[5]||(t[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(u(),g("div",le,[e("div",ne,[e("div",{class:f(["table-container",{"is-loading":d(s)&&!d(C)}])},[e("table",re,[e("thead",null,[e("tr",null,[e("th",null,[e("label",oe,[e("input",{type:"checkbox",onChange:$,checked:A.value,indeterminate:L.value},null,40,ie)])]),t[6]||(t[6]=e("th",null,"Product",-1)),t[7]||(t[7]=e("th",null,"User",-1)),t[8]||(t[8]=e("th",null,"Service",-1)),t[9]||(t[9]=e("th",null,"Delivery",-1)),t[10]||(t[10]=e("th",null,"Accuracy",-1)),t[11]||(t[11]=e("th",null,"Average",-1)),t[12]||(t[12]=e("th",null,"Created",-1)),t[13]||(t[13]=e("th",null,"Actions",-1))])]),e("tbody",null,[(u(!0),g(k,null,S(b.value,r=>(u(),g("tr",{key:r.id},[e("td",null,[e("label",de,[G(e("input",{type:"checkbox",value:r.id,"onUpdate:modelValue":t[1]||(t[1]=o=>c.value=o)},null,8,ce),[[H,c.value]])])]),e("td",null,[e("strong",null,v(r.productName||"Unknown Product"),1)]),e("td",null,v(r.userName||"Unknown User"),1),e("td",null,[e("div",ue,[(u(),g(k,null,S(5,o=>e("span",{key:o,class:f(["star",{"is-filled":o<=r.service}])}," ★ ",2)),64)),e("span",ge,v(r.service),1)])]),e("td",null,[e("div",ve,[(u(),g(k,null,S(5,o=>e("span",{key:o,class:f(["star",{"is-filled":o<=r.deliveryTime}])}," ★ ",2)),64)),e("span",pe,v(r.deliveryTime),1)])]),e("td",null,[e("div",he,[(u(),g(k,null,S(5,o=>e("span",{key:o,class:f(["star",{"is-filled":o<=r.accuracy}])}," ★ ",2)),64)),e("span",me,v(r.accuracy),1)])]),e("td",null,[e("div",ye,[(u(),g(k,null,S(5,o=>e("span",{key:o,class:f(["star",{"is-filled":o<=r.average}])}," ★ ",2)),64)),e("span",be,v(r.average.toFixed(1)),1)])]),e("td",null,v(V(r.createdAt)),1),e("td",null,[e("div",fe,[e("button",{class:"button is-small is-danger",onClick:o=>M(r.id),disabled:m.value},t[14]||(t[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,ke)])])]))),128))])])],2)])])),d(F)>1?(u(),q(Q,{key:3,"current-page":d(x),"total-pages":d(F),onPageChanged:d(B)},null,8,["current-page","total-pages","onPageChanged"])):E("",!0)]))}},Ce=z(Se,[["__scopeId","data-v-9a8e6b40"]]);export{Ce as default};
