import{_ as O,c as u,o as g,a as e,F as R,p as L,t as M,n as z,b as P,z as te,g as k,x as E,i as T,m as le,A as p,B as h,C as Z,k as oe,D as c,I as ae,e as ie}from"./index-DMg5qKr1.js";import{u as ne,S as re}from"./useAdminSearch-BQKMobin.js";import{S as de}from"./StatusBadge-nLr7h2vF.js";import{p as x}from"./products-BHT6Tynz.js";import{P as ce}from"./Pagination-edGYNZJ6.js";import{C as ue}from"./ConfirmDialog-D-ERnN1w.js";/* empty css                                                                    */const ge={class:"product-table"},me={class:"table-container"},pe={class:"table is-fullwidth is-striped"},ve={key:0},Ie={class:"image is-64x64"},fe=["src","alt"],be={class:"has-text-grey"},ye={style:{width:"140px"}},De={class:"buttons are-small has-addons"},he=["onClick"],Me=["onClick"],Ce=["onClick"],ke={key:1},Se={key:2},Pe={__name:"ProductTable",props:{products:{type:Array,required:!0},categories:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["view","edit","delete"],setup(v){const C=a=>!a||isNaN(a)?"₴0.00":new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",minimumFractionDigits:2}).format(a),I=a=>{const i=parseInt(a)||0;return i<=0?"is-danger":i<=10?"is-warning":"is-success"},A=a=>{let i=null;return a.imageUrl&&a.imageUrl.trim()!==""?i=a.imageUrl:a.mainImageUrl&&a.mainImageUrl.trim()!==""?i=a.mainImageUrl:a.image&&a.image.trim()!==""&&(i=a.image),i||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K"},t=a=>{a.target.dataset.errorHandled||(a.target.dataset.errorHandled="true",a.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNNDAgNTZDNDkuOTQxMSA1NiA1OCA0Ny45NDExIDU4IDM4QzU4IDI4LjA1ODkgNDkuOTQxMSAyMCA0MCAyMEMzMC4wNTg5IDIwIDIyIDI4LjA1ODkgMjIgMzhDMjIgNDcuOTQxMSAzMC4wNTg5IDU2IDQwIDU2WiIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTMwIDMySDMyVjM0SDMwVjMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8cGF0aCBkPSJNNDggMzJINTBWMzRINDhWMzJaIiBmaWxsPSIjRDFEMUQxIi8+CjxwYXRoIGQ9Ik0zMiA0NEMzMiA0Ni4yMDkxIDMzLjc5MDkgNDggMzYgNDhDMzguMjA5MSA0OCA0MCA0Ni4yMDkxIDQwIDQ0SDMyWiIgZmlsbD0iI0QxRDFEMSIvPgo8L3N2Zz4K",a.target.classList.add("image-error"))};return(a,i)=>(g(),u("div",ge,[e("div",me,[e("table",pe,[i[6]||(i[6]=e("thead",null,[e("tr",null,[e("th",{style:{width:"100px"}},"Image"),e("th",null,"Name"),e("th",null,"Category"),e("th",null,"Price"),e("th",null,"Stock"),e("th",null,"Status"),e("th",{style:{width:"140px"}},"Actions")])],-1)),!v.loading&&v.products.length>0?(g(),u("tbody",ve,[(g(!0),u(R,null,L(v.products,d=>(g(),u("tr",{key:d.id},[e("td",null,[e("figure",Ie,[e("img",{src:A(d),alt:d.name,class:"product-thumbnail",onError:t},null,40,fe)])]),e("td",null,[e("div",null,[e("strong",null,M(d.name),1),i[0]||(i[0]=e("br",null,null,-1)),e("small",be,M(d.slug),1)])]),e("td",null,M(d.categoryName||"Unknown"),1),e("td",null,M(C(d.priceAmount||d.price||d.priceValue)),1),e("td",null,[e("span",{class:z(["tag",I(d.stock||d.stockQuantity||0)])},M(d.stock||d.stockQuantity||0),3)]),e("td",null,[P(de,{status:d.status,type:"product"},null,8,["status"])]),e("td",ye,[e("div",De,[e("button",{class:"button is-info is-small",onClick:y=>a.$emit("view",d),title:"View Product"},i[1]||(i[1]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)]),8,he),e("button",{class:"button is-primary is-small",onClick:y=>a.$emit("edit",d),title:"Edit Product"},i[2]||(i[2]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-edit"})],-1)]),8,Me),e("button",{class:"button is-danger is-small",onClick:y=>a.$emit("delete",d),title:"Delete Product"},i[3]||(i[3]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Ce)])])]))),128))])):v.loading?(g(),u("tbody",ke,i[4]||(i[4]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"},[e("div",{class:"loader-wrapper"},[e("div",{class:"loader is-loading"})])])],-1)]))):(g(),u("tbody",Se,i[5]||(i[5]=[e("tr",null,[e("td",{colspan:"7",class:"has-text-centered"}," No products found. ")],-1)])))])])]))}},Ae=O(Pe,[["__scopeId","data-v-8b7b180f"]]),_e={class:"modal-card"},Ne={class:"modal-card-head"},we={class:"modal-card-title"},Ue={class:"modal-card-body"},xe={class:"field"},$e={class:"control"},je={class:"field"},Oe={class:"control"},ze={class:"columns"},Fe={class:"column is-6"},Be={class:"field"},Qe={class:"control"},Ee={class:"select is-fullwidth"},Ze=["value"],Re={class:"column is-6"},Le={class:"field"},Te={class:"control"},Ge={class:"select is-fullwidth"},We={class:"columns"},He={class:"column is-4"},Ve={class:"field"},Je={class:"control"},qe={class:"column is-4"},Ye={class:"field"},Xe={class:"control"},Ke={class:"column is-4"},es={class:"field"},ss={class:"control"},ts={class:"field"},ls={class:"control"},os={class:"columns"},as={class:"column is-6"},is={class:"field"},ns={class:"control"},rs={class:"column is-6"},ds={class:"field"},cs={class:"control"},us={class:"modal-card-foot"},gs=["disabled"],ms={key:0},ps={key:1},vs={__name:"ProductFormModal",props:{isOpen:{type:Boolean,required:!0},product:{type:Object,default:null}},emits:["close","save"],setup(v,{emit:C}){const I=v,A=C,t=te({id:null,name:"",description:"",categoryId:"",price:0,salePrice:null,stock:0,status:"active",imageUrl:"",sku:"",slug:""}),a=k(!1),i=k([]),d=async()=>{try{const l=await x.getCategories();i.value=l}catch(l){console.error("Error fetching categories:",l)}},y=async()=>{a.value=!0;try{const l={...t};l.price=parseFloat(l.price),l.stock=parseInt(l.stock),l.salePrice&&(l.salePrice=parseFloat(l.salePrice)),A("save",l)}catch(l){console.error("Error submitting form:",l)}finally{a.value=!1}},_=()=>{t.id=null,t.name="",t.description="",t.categoryId="",t.price=0,t.salePrice=null,t.stock=0,t.status="active",t.imageUrl="",t.sku="",t.slug=""};return E(()=>I.product,l=>{l?Object.keys(t).forEach(s=>{s in l&&(t[s]=l[s])}):_()},{immediate:!0}),E(()=>I.isOpen,l=>{l||_()}),T(()=>{d()}),(l,s)=>(g(),u("div",{class:z(["modal",{"is-active":v.isOpen}])},[e("div",{class:"modal-background",onClick:s[0]||(s[0]=r=>l.$emit("close"))}),e("div",_e,[e("header",Ne,[e("p",we,M(v.product?"Edit Product":"Add Product"),1),e("button",{class:"delete","aria-label":"close",onClick:s[1]||(s[1]=r=>l.$emit("close"))})]),e("section",Ue,[e("form",{onSubmit:le(y,["prevent"])},[e("div",xe,[s[13]||(s[13]=e("label",{class:"label"},"Name",-1)),e("div",$e,[p(e("input",{class:"input",type:"text",placeholder:"Product name","onUpdate:modelValue":s[2]||(s[2]=r=>t.name=r),required:""},null,512),[[h,t.name]])])]),e("div",je,[s[14]||(s[14]=e("label",{class:"label"},"Description",-1)),e("div",Oe,[p(e("textarea",{class:"textarea",placeholder:"Product description","onUpdate:modelValue":s[3]||(s[3]=r=>t.description=r),rows:"3"},null,512),[[h,t.description]])])]),e("div",ze,[e("div",Fe,[e("div",Be,[s[16]||(s[16]=e("label",{class:"label"},"Category",-1)),e("div",Qe,[e("div",Ee,[p(e("select",{"onUpdate:modelValue":s[4]||(s[4]=r=>t.categoryId=r),required:""},[s[15]||(s[15]=e("option",{value:"",disabled:""},"Select category",-1)),(g(!0),u(R,null,L(i.value,r=>(g(),u("option",{key:r.id,value:r.id},M(r.name),9,Ze))),128))],512),[[Z,t.categoryId]])])])])]),e("div",Re,[e("div",Le,[s[18]||(s[18]=e("label",{class:"label"},"Status",-1)),e("div",Te,[e("div",Ge,[p(e("select",{"onUpdate:modelValue":s[5]||(s[5]=r=>t.status=r),required:""},s[17]||(s[17]=[e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"out_of_stock"},"Out of Stock",-1)]),512),[[Z,t.status]])])])])])]),e("div",We,[e("div",He,[e("div",Ve,[s[19]||(s[19]=e("label",{class:"label"},"Price ($)",-1)),e("div",Je,[p(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[6]||(s[6]=r=>t.price=r),required:""},null,512),[[h,t.price]])])])]),e("div",qe,[e("div",Ye,[s[20]||(s[20]=e("label",{class:"label"},"Sale Price ($)",-1)),e("div",Xe,[p(e("input",{class:"input",type:"number",step:"0.01",min:"0",placeholder:"0.00","onUpdate:modelValue":s[7]||(s[7]=r=>t.salePrice=r)},null,512),[[h,t.salePrice]])])])]),e("div",Ke,[e("div",es,[s[21]||(s[21]=e("label",{class:"label"},"Stock",-1)),e("div",ss,[p(e("input",{class:"input",type:"number",min:"0",placeholder:"0","onUpdate:modelValue":s[8]||(s[8]=r=>t.stock=r),required:""},null,512),[[h,t.stock]])])])])]),e("div",ts,[s[22]||(s[22]=e("label",{class:"label"},"Image URL",-1)),e("div",ls,[p(e("input",{class:"input",type:"url",placeholder:"https://example.com/image.jpg","onUpdate:modelValue":s[9]||(s[9]=r=>t.imageUrl=r)},null,512),[[h,t.imageUrl]])])]),e("div",os,[e("div",as,[e("div",is,[s[23]||(s[23]=e("label",{class:"label"},"SKU",-1)),e("div",ns,[p(e("input",{class:"input",type:"text",placeholder:"SKU-123","onUpdate:modelValue":s[10]||(s[10]=r=>t.sku=r)},null,512),[[h,t.sku]])])])]),e("div",rs,[e("div",ds,[s[24]||(s[24]=e("label",{class:"label"},"Slug",-1)),e("div",cs,[p(e("input",{class:"input",type:"text",placeholder:"product-slug","onUpdate:modelValue":s[11]||(s[11]=r=>t.slug=r)},null,512),[[h,t.slug]])])])])])],32)]),e("footer",us,[e("button",{class:"button is-primary",onClick:y,disabled:a.value},[a.value?(g(),u("span",ms,s[25]||(s[25]=[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1),e("span",null,"Saving...",-1)]))):(g(),u("span",ps,"Save"))],8,gs),e("button",{class:"button",onClick:s[12]||(s[12]=r=>l.$emit("close"))},"Cancel")])])],2))}},Is=O(vs,[["__scopeId","data-v-fd05cff4"]]),fs={class:"admin-products"},bs={key:0,class:"has-text-centered py-6"},ys={key:1,class:"notification is-danger"},Ds={key:2,class:"card"},hs={class:"card-content"},Ms={key:3,class:"pagination-wrapper"},Cs={__name:"Products",setup(v){const C=ie(),I=k([{key:"categoryId",label:"Category",type:"select",columnClass:"is-3",allOption:"All Categories",options:[]},{key:"status",label:"Status",type:"select",columnClass:"is-3",allOption:"All Statuses",options:[{value:"0",label:"Pending"},{value:"1",label:"Approved"},{value:"2",label:"Rejected"}]},{key:"stock",label:"Stock Level",type:"select",columnClass:"is-3",allOption:"All Stock Levels",options:[{value:"in-stock",label:"In Stock (>10)"},{value:"low-stock",label:"Low Stock (1-10)"},{value:"out-of-stock",label:"Out of Stock (0)"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",allOption:!1,options:[{value:"Name",label:"Name"},{value:"CreatedAt",label:"Created Date"},{value:"Stock",label:"Stock"},{value:"Status",label:"Status"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}]),{items:A,loading:t,error:a,isFirstLoad:i,currentPage:d,totalPages:y,totalItems:_,filters:l,fetchData:s,handlePageChange:r}=ne({fetchFunction:x.getProducts,defaultFilters:{search:"",categoryId:"",status:"",stock:"",sortBy:"CreatedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:20,clientSideSearch:!1}),F=k(!1),$=k(!1),N=k(null),j=k([]),G=async()=>{try{console.log("🗂️ Loading categories...");const o=await ae.getCategories();console.log("📋 Categories service response:",o);const n=o.data||o.categories||[];console.log("📊 Categories data:",n),j.value=n,console.log("✅ Categories stored for table:",j.value.length,"categories");const m=(f,U="")=>{let D=[];return f.forEach(b=>{const Q=U?`${U} > ${b.name}`:b.name;D.push({value:b.id,label:Q}),b.children&&b.children.length>0&&(D=D.concat(m(b.children,Q)))}),D},S=m(n);console.log("🔄 Flattened categories for filter:",S);const w=I.value.find(f=>f.key==="categoryId");w?(w.options=S,console.log("✅ Category filter updated with",S.length,"options"),console.log("📋 Sample categories:",S.slice(0,3)),I.value=[...I.value]):console.error("❌ Category filter field not found")}catch(o){console.error("❌ Error loading categories:",o)}},W=o=>{l.search=o},H=(o,n)=>{if(console.log(`🔄 Filter changed: ${o} = "${n}"`),o==="categoryId"&&n){const m=V(n);console.log("📂 Selected category includes subcategories:",m),l.categoryIds=m,l[o]=n}else o==="categoryId"&&!n&&delete l.categoryIds,l[o]=n;console.log("📊 Current filters state:",{...l})},V=o=>{const n=[o],m=(S,w)=>{for(const f of S){if(f.id===w){const U=D=>{D.children&&D.children.length>0&&D.children.forEach(b=>{n.push(b.id),U(b)})};return U(f),!0}if(f.children&&f.children.length>0&&m(f.children,w))return!0}return!1};return m(j.value,o),n},J=()=>{Object.keys(l).forEach(o=>{o==="search"?l[o]="":o==="sortBy"?l[o]="CreatedAt":o==="sortOrder"?l[o]="desc":l[o]=""}),delete l.categoryIds,s(1)},q=()=>{C.push("/admin/products/create")},Y=o=>{C.push(`/admin/products/${o.id}/view`)},X=o=>{C.push(`/admin/products/${o.id}/edit`)},B=()=>{F.value=!1,N.value=null},K=async o=>{try{o.id?await x.updateProduct(o.id,o):await x.createProduct(o),B(),s(d.value)}catch(n){console.error("Error saving product:",n)}},ee=o=>{N.value=o,$.value=!0},se=async()=>{if(N.value)try{await x.deleteProduct(N.value.id),$.value=!1,s(d.value)}catch(o){console.error("Error deleting product:",o)}};return T(async()=>{await G(),s(1)}),(o,n)=>(g(),u("div",fs,[e("div",{class:"level"},[n[3]||(n[3]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Products")])],-1)),e("div",{class:"level-right"},[e("div",{class:"level-item"},[e("button",{class:"button is-primary",onClick:q},n[2]||(n[2]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Create Product",-1)]))])])]),P(re,{filters:c(l),"filter-fields":I.value,"search-label":"Search Products","search-placeholder":"Search by name, description, or category...","search-column-class":"is-4","total-items":c(_),"item-name":"products",loading:c(t),onSearchChanged:W,onFilterChanged:H,onResetFilters:J},null,8,["filters","filter-fields","total-items","loading"]),c(t)&&c(i)?(g(),u("div",bs,n[4]||(n[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading products...",-1)]))):c(a)?(g(),u("div",ys,[e("p",null,M(c(a)),1),e("button",{class:"button is-light mt-2",onClick:n[0]||(n[0]=(...m)=>c(s)&&c(s)(...m))},n[5]||(n[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-redo"})],-1),e("span",null,"Retry",-1)]))])):(g(),u("div",Ds,[e("div",hs,[e("div",{class:z(["table-container",{"is-loading":c(t)&&!c(i)}])},[P(Ae,{products:c(A),categories:j.value,loading:c(t),onView:Y,onEdit:X,onDelete:ee},null,8,["products","categories","loading"])],2)])])),c(y)>1||c(_)>0?(g(),u("div",Ms,[P(ce,{"current-page":c(d),"total-pages":c(y),onPageChanged:c(r)},null,8,["current-page","total-pages","onPageChanged"])])):oe("",!0),P(Is,{"is-open":F.value,product:N.value,onClose:B,onSave:K},null,8,["is-open","product"]),P(ue,{"is-open":$.value,title:"Delete Product",message:"Are you sure you want to delete this product? This action cannot be undone.",onConfirm:se,onCancel:n[1]||(n[1]=m=>$.value=!1)},null,8,["is-open"])]))}},Us=O(Cs,[["__scopeId","data-v-37ef1c8d"]]);export{Us as default};
