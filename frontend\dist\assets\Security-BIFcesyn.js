import{_ as R,g as d,i as E,c as n,a as s,k as p,n as m,A as u,C as L,F as h,p as S,t as i,d as g,J as b,B as x,q as c,o as a}from"./index-C1YYMYJd.js";const V={class:"admin-security"},I={class:"tabs"},M={key:0,class:"security-content"},D={class:"box"},$={class:"field is-grouped"},F={class:"control"},z={class:"select"},B={class:"control"},O={class:"select"},W={class:"table-container"},G={class:"table is-fullwidth is-striped"},J={key:1,class:"security-content"},j={class:"box"},H={class:"table-container"},K={class:"table is-fullwidth is-striped"},Q={class:"tag is-info"},X={key:2,class:"security-content"},Y={class:"box"},Z={class:"table-container"},ss={class:"table is-fullwidth is-striped"},ts={class:"is-size-7"},es=["onClick"],ls={key:1,class:"tag is-success"},is={key:3,class:"security-content"},os={class:"box"},ns={class:"field"},as={class:"field"},rs={class:"checkbox"},us={class:"field"},ds={class:"checkbox"},cs={class:"field"},vs={class:"checkbox"},ps={class:"field"},ms={class:"control"},ys={class:"field"},gs={class:"field"},bs={class:"checkbox"},hs={key:0,class:"field"},Ss={class:"control"},_s={__name:"Security",setup(ks){const r=d("logs"),_=d([]),k=d([]),f=d([]),o=d({passwordPolicy:{requireUppercase:!0,requireNumbers:!0,requireSpecialChars:!0},sessionSettings:{sessionTimeout:30},rateLimiting:{enabled:!0,requestsPerMinute:60}}),v=d({level:"",source:""}),A=async()=>{try{const l=await c.get("/api/admin/security/logs",{params:v.value});_.value=l.data.data.logs}catch(l){console.error("Error loading logs:",l)}},C=async()=>{try{const l=await c.get("/api/admin/security/audit-trail");k.value=l.data.data.auditEntries}catch(l){console.error("Error loading audit trail:",l)}},w=async()=>{try{const l=await c.get("/api/admin/security/active-sessions");f.value=l.data.data}catch(l){console.error("Error loading active sessions:",l)}},T=async()=>{try{const l=await c.get("/api/admin/security/security-settings");o.value=l.data.data}catch(l){console.error("Error loading security settings:",l)}},q=async()=>{try{await c.put("/api/admin/security/security-settings",o.value),alert("Security settings saved successfully!")}catch(l){console.error("Error saving security settings:",l),alert("Error saving security settings")}},P=async l=>{if(confirm("Are you sure you want to terminate this session?"))try{await c.delete(`/api/admin/security/active-sessions/${l}`),await w()}catch(t){console.error("Error terminating session:",t)}},y=l=>new Date(l).toLocaleString(),U=l=>{switch(l){case"ERROR":return"is-danger";case"WARNING":return"is-warning";case"INFO":return"is-info";default:return""}},N=l=>l.length>50?l.substring(0,50)+"...":l;return E(()=>{A(),C(),w(),T()}),(l,t)=>(a(),n("div",V,[t[29]||(t[29]=s("h1",{class:"title"},"Security & Logs",-1)),s("div",I,[s("ul",null,[s("li",{class:m({"is-active":r.value==="logs"})},[s("a",{onClick:t[0]||(t[0]=e=>r.value="logs")},"System Logs")],2),s("li",{class:m({"is-active":r.value==="audit"})},[s("a",{onClick:t[1]||(t[1]=e=>r.value="audit")},"Audit Trail")],2),s("li",{class:m({"is-active":r.value==="sessions"})},[s("a",{onClick:t[2]||(t[2]=e=>r.value="sessions")},"Active Sessions")],2),s("li",{class:m({"is-active":r.value==="settings"})},[s("a",{onClick:t[3]||(t[3]=e=>r.value="settings")},"Security Settings")],2)])]),r.value==="logs"?(a(),n("div",M,[s("div",D,[t[15]||(t[15]=s("h2",{class:"subtitle"},"System Logs",-1)),s("div",$,[s("div",F,[s("div",z,[u(s("select",{"onUpdate:modelValue":t[4]||(t[4]=e=>v.value.level=e)},t[12]||(t[12]=[s("option",{value:""},"All Levels",-1),s("option",{value:"INFO"},"Info",-1),s("option",{value:"WARNING"},"Warning",-1),s("option",{value:"ERROR"},"Error",-1)]),512),[[L,v.value.level]])])]),s("div",B,[s("div",O,[u(s("select",{"onUpdate:modelValue":t[5]||(t[5]=e=>v.value.source=e)},t[13]||(t[13]=[s("option",{value:""},"All Sources",-1),s("option",{value:"Authentication"},"Authentication",-1),s("option",{value:"Authorization"},"Authorization",-1),s("option",{value:"Database"},"Database",-1)]),512),[[L,v.value.source]])])]),s("div",{class:"control"},[s("button",{class:"button is-primary",onClick:A},"Refresh")])]),s("div",W,[s("table",G,[t[14]||(t[14]=s("thead",null,[s("tr",null,[s("th",null,"Timestamp"),s("th",null,"Level"),s("th",null,"Source"),s("th",null,"Message"),s("th",null,"IP Address")])],-1)),s("tbody",null,[(a(!0),n(h,null,S(_.value,e=>(a(),n("tr",{key:e.id},[s("td",null,i(y(e.timestamp)),1),s("td",null,[s("span",{class:m(["tag",U(e.level)])},i(e.level),3)]),s("td",null,i(e.source),1),s("td",null,i(e.message),1),s("td",null,i(e.ipAddress),1)]))),128))])])])])])):p("",!0),r.value==="audit"?(a(),n("div",J,[s("div",j,[t[17]||(t[17]=s("h2",{class:"subtitle"},"Audit Trail",-1)),s("div",H,[s("table",K,[t[16]||(t[16]=s("thead",null,[s("tr",null,[s("th",null,"Timestamp"),s("th",null,"Action"),s("th",null,"User"),s("th",null,"Target"),s("th",null,"Details"),s("th",null,"IP Address")])],-1)),s("tbody",null,[(a(!0),n(h,null,S(k.value,e=>(a(),n("tr",{key:e.id},[s("td",null,i(y(e.timestamp)),1),s("td",null,[s("span",Q,i(e.action),1)]),s("td",null,i(e.userName),1),s("td",null,i(e.targetType),1),s("td",null,i(e.details),1),s("td",null,i(e.ipAddress),1)]))),128))])])])])])):p("",!0),r.value==="sessions"?(a(),n("div",X,[s("div",Y,[t[19]||(t[19]=s("h2",{class:"subtitle"},"Active Sessions",-1)),s("div",Z,[s("table",ss,[t[18]||(t[18]=s("thead",null,[s("tr",null,[s("th",null,"User"),s("th",null,"IP Address"),s("th",null,"User Agent"),s("th",null,"Login Time"),s("th",null,"Last Activity"),s("th",null,"Actions")])],-1)),s("tbody",null,[(a(!0),n(h,null,S(f.value,e=>(a(),n("tr",{key:e.id},[s("td",null,i(e.userName),1),s("td",null,i(e.ipAddress),1),s("td",ts,i(N(e.userAgent)),1),s("td",null,i(y(e.loginTime)),1),s("td",null,i(y(e.lastActivity)),1),s("td",null,[e.isCurrentSession?(a(),n("span",ls,"Current")):(a(),n("button",{key:0,class:"button is-small is-danger",onClick:fs=>P(e.id)}," Terminate ",8,es))])]))),128))])])])])])):p("",!0),r.value==="settings"?(a(),n("div",is,[s("div",os,[t[28]||(t[28]=s("h2",{class:"subtitle"},"Security Settings",-1)),s("div",ns,[t[23]||(t[23]=s("label",{class:"label"},"Password Policy",-1)),s("div",as,[s("label",rs,[u(s("input",{type:"checkbox","onUpdate:modelValue":t[6]||(t[6]=e=>o.value.passwordPolicy.requireUppercase=e)},null,512),[[b,o.value.passwordPolicy.requireUppercase]]),t[20]||(t[20]=g(" Require uppercase letters "))])]),s("div",us,[s("label",ds,[u(s("input",{type:"checkbox","onUpdate:modelValue":t[7]||(t[7]=e=>o.value.passwordPolicy.requireNumbers=e)},null,512),[[b,o.value.passwordPolicy.requireNumbers]]),t[21]||(t[21]=g(" Require numbers "))])]),s("div",cs,[s("label",vs,[u(s("input",{type:"checkbox","onUpdate:modelValue":t[8]||(t[8]=e=>o.value.passwordPolicy.requireSpecialChars=e)},null,512),[[b,o.value.passwordPolicy.requireSpecialChars]]),t[22]||(t[22]=g(" Require special characters "))])])]),s("div",ps,[t[24]||(t[24]=s("label",{class:"label"},"Session Timeout (minutes)",-1)),s("div",ms,[u(s("input",{class:"input",type:"number","onUpdate:modelValue":t[9]||(t[9]=e=>o.value.sessionSettings.sessionTimeout=e)},null,512),[[x,o.value.sessionSettings.sessionTimeout]])])]),s("div",ys,[t[27]||(t[27]=s("label",{class:"label"},"Rate Limiting",-1)),s("div",gs,[s("label",bs,[u(s("input",{type:"checkbox","onUpdate:modelValue":t[10]||(t[10]=e=>o.value.rateLimiting.enabled=e)},null,512),[[b,o.value.rateLimiting.enabled]]),t[25]||(t[25]=g(" Enable rate limiting "))])]),o.value.rateLimiting.enabled?(a(),n("div",hs,[t[26]||(t[26]=s("label",{class:"label"},"Requests per minute",-1)),s("div",Ss,[u(s("input",{class:"input",type:"number","onUpdate:modelValue":t[11]||(t[11]=e=>o.value.rateLimiting.requestsPerMinute=e)},null,512),[[x,o.value.rateLimiting.requestsPerMinute]])])])):p("",!0)]),s("div",{class:"field is-grouped"},[s("div",{class:"control"},[s("button",{class:"button is-primary",onClick:q}," Save Settings ")])])])])):p("",!0)]))}},ws=R(_s,[["__scopeId","data-v-4e8c44a4"]]);export{ws as default};
