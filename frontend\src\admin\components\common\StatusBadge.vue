<template>
  <span class="tag" :class="statusClass">
    {{ formattedStatus }}
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: [String, Number],
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'order', 'payment', 'user', 'product'].includes(value)
  }
});

// Format status text
const formattedStatus = computed(() => {
  if (props.status === null || props.status === undefined) return 'Unknown';

  if (props.label) return props.label;

  // Handle numeric status values
  if (typeof props.status === 'number') {
    switch (props.status) {
      case 0: return props.type === 'product' ? 'Pending' : 'Pending';
      case 1: return props.type === 'product' ? 'Approved' : 'Approved';
      case 2: return props.type === 'product' ? 'Rejected' : 'Rejected';
      default: return props.status.toString();
    }
  }

  // Handle string status values for products
  if (props.type === 'product') {
    const status = props.status.toString().toLowerCase();
    switch (status) {
      case 'pending': return 'Pending';
      case 'approved': return 'Approved';
      case 'rejected': return 'Rejected';
      default: return props.status.toString();
    }
  }

  // Handle string status values for orders
  if (props.type === 'order') {
    const status = props.status.toString().toLowerCase();
    switch (status) {
      case 'pending': return 'Pending';
      case 'paid': return 'Paid';
      case 'shipped': return 'Shipped';
      case 'delivered': return 'Delivered';
      case 'cancelled': return 'Cancelled';
      default: return props.status.toString();
    }
  }

  // Handle string status values for payments
  if (props.type === 'payment') {
    const status = props.status.toString().toLowerCase();
    switch (status) {
      case 'pending': return 'Pending';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      case 'unknown': return 'Unknown';
      default: return props.status.toString();
    }
  }

  // Handle string status values for other types
  return props.status.toString();
});

// Determine status class based on status and type
const statusClass = computed(() => {
  if (props.status === null || props.status === undefined) return 'is-light';

  // Convert to string and then to lowercase for consistent comparison
  const status = props.status.toString().toLowerCase();

  // Default status classes
  if (props.type === 'default') {
    switch (status) {
      case 'active':
      case 'approved':
      case 'completed':
      case '1':
        return 'is-success';
      case 'inactive':
      case 'pending':
      case '0':
        return 'is-warning';
      case 'rejected':
      case 'cancelled':
      case '2':
        return 'is-danger';
      default:
        return 'is-light';
    }
  }

  // Order status classes
  if (props.type === 'order') {
    switch (status) {
      case 'pending':
        return 'is-warning';
      case 'processing':
        return 'is-info';
      case 'shipped':
        return 'is-primary';
      case 'delivered':
        return 'is-success';
      case 'cancelled':
        return 'is-danger';
      case 'refunded':
        return 'is-danger is-light';
      case 'on hold':
        return 'is-warning is-light';
      default:
        return 'is-light';
    }
  }

  // Payment status classes
  if (props.type === 'payment') {
    switch (status) {
      case 'paid':
        return 'is-success';
      case 'pending':
        return 'is-warning';
      case 'failed':
        return 'is-danger';
      case 'refunded':
        return 'is-danger is-light';
      case 'partially refunded':
        return 'is-warning is-light';
      default:
        return 'is-light';
    }
  }



  // Product status classes
  if (props.type === 'product') {
    switch (status) {
      case 'approved':
      case '1':
        return 'is-success';
      case 'pending':
      case '0':
        return 'is-warning';
      case 'rejected':
      case '2':
        return 'is-danger';
      case 'out of stock':
        return 'is-danger';
      case 'low stock':
        return 'is-warning is-light';
      default:
        return 'is-light';
    }
  }

  // Order status classes
  if (props.type === 'order') {
    switch (status) {
      case 'pending':
      case '0':
        return 'is-warning';
      case 'processing':
      case 'paid':
      case '1':
        return 'is-info';
      case 'shipped':
      case '2':
        return 'is-primary';
      case 'delivered':
      case 'completed':
      case '3':
        return 'is-success';
      case 'cancelled':
      case 'refunded':
      case '4':
        return 'is-danger';
      default:
        return 'is-light';
    }
  }

  // Payment status classes
  if (props.type === 'payment') {
    switch (status) {
      case 'pending':
      case '0':
        return 'is-warning';
      case 'completed':
      case 'paid':
      case '1':
        return 'is-success';
      case 'failed':
      case 'declined':
      case '2':
        return 'is-danger';
      case 'unknown':
        return 'is-light';
      default:
        return 'is-light';
    }
  }

  return 'is-light';
});
</script>

<style scoped>
.tag {
  text-transform: capitalize;
  font-weight: 500;
}

.tag.is-success {
  background-color: #48c774;
}

.tag.is-warning {
  background-color: #ffdd57;
  color: rgba(0, 0, 0, 0.7);
}

.tag.is-danger {
  background-color: #ff3860;
}

.tag.is-info {
  background-color: #3298dc;
}

.tag.is-primary {
  background-color: #ff7700;
}

.tag.is-light {
  background-color: #f5f5f5;
  color: #363636;
}

.tag.is-success.is-light {
  background-color: #effaf3;
  color: #257942;
}

.tag.is-warning.is-light {
  background-color: #fffbeb;
  color: #947600;
}

.tag.is-danger.is-light {
  background-color: #feecf0;
  color: #cc0f35;
}

.tag.is-info.is-light {
  background-color: #eef6fc;
  color: #1d72aa;
}

.tag.is-primary.is-light {
  background-color: #fff0e6;
  color: #cc5f00;
}
</style>
