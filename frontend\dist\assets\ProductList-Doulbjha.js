import{_ as K,g as n,z as j,i as H,c,a as s,b as v,w as y,r as O,A as b,B as R,C as k,F as w,p as A,t as g,n as I,I as q,o as d}from"./index-C1YYMYJd.js";import{p as x}from"./products-DEnweLaU.js";import{S as G}from"./StatusBadge-7HpbPXqn.js";import{P as J}from"./Pagination-uai-tBBw.js";import{C as Q}from"./ConfirmDialog-hpHY5nH2.js";/* empty css                                                                    */const W={class:"product-list"},X={class:"level"},Y={class:"level-right"},Z={class:"level-item"},ss={class:"card mb-4"},ts={class:"card-content"},es={class:"columns is-multiline"},ls={class:"column is-3"},os={class:"field"},as={class:"control has-icons-left"},ns={class:"column is-3"},is={class:"field"},rs={class:"control"},cs={class:"select is-fullwidth"},ds=["value"],us={class:"column is-3"},vs={class:"field"},ps={class:"control"},gs={class:"select is-fullwidth"},ms={class:"column is-3"},fs={class:"field"},_s={class:"control"},hs={class:"select is-fullwidth"},ys={class:"column is-12"},bs={class:"field is-grouped is-grouped-right"},ks={class:"control"},Cs={class:"card"},Ps={class:"card-content"},Ss={key:0,class:"has-text-centered py-6"},ws={key:1,class:"has-text-centered py-6"},As={class:"mt-4"},Is={key:2},xs={class:"table-container"},Ds={class:"table is-fullwidth is-hoverable"},Ns={class:"image-cell"},Us={class:"image is-48x48"},Es=["src","alt"],Ts={class:"product-name"},Vs={class:"product-sku"},$s={class:"buttons are-small"},Bs=["onClick"],Fs={__name:"ProductList",setup(Ls){const a=n([]),i=n([]),m=n(!1),f=n(1),C=n(1),D=n(0),P=n(10),_=n(!1),u=n(null),h=n(null),o=j({search:"",categoryId:"",status:"",stock:""}),r=async(e=1)=>{m.value=!0,f.value=e;try{const t=await x.getProducts({page:f.value,pageSize:P.value,limit:P.value,...o});console.log("Products API response:",t),t.data?(a.value=t.data,console.log("Products loaded:",a.value.length)):t.products?(a.value=t.products,console.log("Products loaded (products field):",a.value.length)):t.items?(a.value=t.items,console.log("Products loaded (items field):",a.value.length)):(a.value=[],console.log("No products found in response")),t.pagination&&(C.value=t.pagination.totalPages,D.value=t.pagination.total,f.value=t.pagination.page,console.log("Pagination:",t.pagination))}catch(t){console.error("Error fetching products:",t)}finally{m.value=!1}},N=async()=>{try{const e=await q.getCategories();console.log("Categories API response:",e),e.categories?i.value=e.categories:e.data?i.value=e.data:Array.isArray(e)?i.value=e:i.value=[],console.log("Categories loaded:",i.value.length)}catch(e){console.error("Error fetching categories:",e),i.value=[]}},U=()=>{o.search="",o.categoryId="",o.status="",o.stock="",r(1)},E=e=>{r(e)},T=()=>{h.value&&clearTimeout(h.value),h.value=setTimeout(()=>{r(1),h.value=null},500)},V=e=>{const t=i.value.find(p=>p.id===e);return t?t.name:"Unknown"},$=e=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(e),B=e=>e<=0?"is-danger":e<=5?"is-warning":"is-success",F=e=>{e.target.src="https://via.placeholder.com/48?text=No+Image"},L=e=>{u.value=e,_.value=!0},M=async()=>{if(u.value)try{await x.deleteProduct(u.value.id),a.value=a.value.filter(e=>e.id!==u.value.id),_.value=!1,u.value=null}catch(e){console.error("Error deleting product:",e)}},z=()=>{_.value=!1,u.value=null};return H(()=>{r(),N()}),(e,t)=>{var S;const p=O("router-link");return d(),c("div",W,[s("div",X,[t[8]||(t[8]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Products")])],-1)),s("div",Y,[s("div",Z,[v(p,{to:"/admin/products/create",class:"button is-primary"},{default:y(()=>t[7]||(t[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Product",-1)])),_:1})])])]),s("div",ss,[s("div",ts,[s("div",es,[s("div",ls,[s("div",os,[t[10]||(t[10]=s("label",{class:"label"},"Search",-1)),s("div",as,[b(s("input",{class:"input",type:"text",placeholder:"Search by name, SKU, description...","onUpdate:modelValue":t[0]||(t[0]=l=>o.search=l),onInput:T},null,544),[[R,o.search]]),t[9]||(t[9]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-search"})],-1))])])]),s("div",ns,[s("div",is,[t[12]||(t[12]=s("label",{class:"label"},"Category",-1)),s("div",rs,[s("div",cs,[b(s("select",{"onUpdate:modelValue":t[1]||(t[1]=l=>o.categoryId=l),onChange:t[2]||(t[2]=l=>r(1))},[t[11]||(t[11]=s("option",{value:""},"All Categories",-1)),(d(!0),c(w,null,A(i.value,l=>(d(),c("option",{key:l.id,value:l.id},g(l.name),9,ds))),128))],544),[[k,o.categoryId]])])])])]),s("div",us,[s("div",vs,[t[14]||(t[14]=s("label",{class:"label"},"Status",-1)),s("div",ps,[s("div",gs,[b(s("select",{"onUpdate:modelValue":t[3]||(t[3]=l=>o.status=l),onChange:t[4]||(t[4]=l=>r(1))},t[13]||(t[13]=[s("option",{value:""},"All Statuses",-1),s("option",{value:"active"},"Active",-1),s("option",{value:"inactive"},"Inactive",-1),s("option",{value:"draft"},"Draft",-1)]),544),[[k,o.status]])])])])]),s("div",ms,[s("div",fs,[t[16]||(t[16]=s("label",{class:"label"},"Stock",-1)),s("div",_s,[s("div",hs,[b(s("select",{"onUpdate:modelValue":t[5]||(t[5]=l=>o.stock=l),onChange:t[6]||(t[6]=l=>r(1))},t[15]||(t[15]=[s("option",{value:""},"All",-1),s("option",{value:"in_stock"},"In Stock",-1),s("option",{value:"low_stock"},"Low Stock",-1),s("option",{value:"out_of_stock"},"Out of Stock",-1)]),544),[[k,o.stock]])])])])]),s("div",ys,[s("div",bs,[s("div",{class:"control"},[s("button",{class:"button is-light",onClick:U}," Reset ")]),s("div",ks,[s("button",{class:I(["button is-primary",{"is-loading":m.value}]),onClick:r}," Apply Filters ",2)])])])])])]),s("div",Cs,[s("div",Ps,[m.value&&!a.value.length?(d(),c("div",Ss,t[17]||(t[17]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading products...",-1)]))):a.value.length?(d(),c("div",Is,[s("div",xs,[s("table",Ds,[t[25]||(t[25]=s("thead",null,[s("tr",null,[s("th",null,"Image"),s("th",null,"Name"),s("th",null,"Category"),s("th",null,"Price"),s("th",null,"Stock"),s("th",null,"Status"),s("th",null,"Actions")])],-1)),s("tbody",null,[(d(!0),c(w,null,A(a.value,l=>(d(),c("tr",{key:l.id},[s("td",Ns,[s("figure",Us,[s("img",{src:l.image||"https://via.placeholder.com/48",alt:l.name,onError:F},null,40,Es)])]),s("td",null,[s("div",Ts,g(l.name),1),s("div",Vs,"SKU: "+g(l.sku||"N/A"),1)]),s("td",null,g(V(l.categoryId)),1),s("td",null,g($(l.priceAmount||l.price)),1),s("td",null,[s("span",{class:I(["tag",B(l.stock)])},g(l.stock),3)]),s("td",null,[v(G,{status:l.status,type:"product"},null,8,["status"])]),s("td",null,[s("div",$s,[v(p,{to:`/admin/products/${l.id}`,class:"button is-info",title:"View"},{default:y(()=>t[22]||(t[22]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),v(p,{to:`/admin/products/${l.id}/edit`,class:"button is-primary",title:"Edit"},{default:y(()=>t[23]||(t[23]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})],-1)])),_:2},1032,["to"]),s("button",{class:"button is-danger",onClick:Ms=>L(l),title:"Delete"},t[24]||(t[24]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-trash"})],-1)]),8,Bs)])])]))),128))])])]),v(J,{"current-page":f.value,"total-pages":C.value,onPageChanged:E},null,8,["current-page","total-pages"])])):(d(),c("div",ws,[t[19]||(t[19]=s("span",{class:"icon is-large"},[s("i",{class:"fas fa-box fa-2x"})],-1)),t[20]||(t[20]=s("p",{class:"mt-2"},"No products found",-1)),t[21]||(t[21]=s("p",{class:"mt-2"},"Try adjusting your filters or add a new product",-1)),s("div",As,[v(p,{to:"/admin/products/create",class:"button is-primary"},{default:y(()=>t[18]||(t[18]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Product",-1)])),_:1})])]))])]),v(Q,{"is-open":_.value,title:"Delete Product",message:`Are you sure you want to delete '${(S=u.value)==null?void 0:S.name}'? This action cannot be undone.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:M,onCancel:z},null,8,["is-open","message"])])}}},qs=K(Fs,[["__scopeId","data-v-0f50a373"]]);export{qs as default};
