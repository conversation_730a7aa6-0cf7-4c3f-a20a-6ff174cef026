import apiService from '@/services/api';

// Cache for API responses
const cache = {
  categories: null,
  categoryTree: null,
  categoryDetails: {},
  lastFetched: {
    categories: null,
    categoryTree: null
  },
  cacheTimeout: 5 * 60 * 1000 // 5 minutes in milliseconds
};

// Check if cache is valid
const isCacheValid = (key) => {
  if (!cache.lastFetched[key]) return false;
  const now = new Date().getTime();
  return (now - cache.lastFetched[key]) < cache.cacheTimeout;
};

export const categoriesService = {
  async getAll(params = {}) {
    return categoriesService.getCategories(params);
  },

  async getCategories(params = {}) {
    // If no specific filters and cache is valid, use cached data
    const hasFilters = Object.keys(params).length > 0;
    if (!hasFilters && isCacheValid('categories') && cache.categories) {
      return cache.categories;
    }

    try {
      // First try the admin endpoint (which doesn't exist, so skip to public)
      try {
        console.log('Fetching categories with params:', params);
        // Skip admin endpoint for now and use public endpoint directly
        const response = await apiService.get('/api/categories/all', { params });
        console.log('Categories API response:', response.data);

        // Handle different response structures from backend
        let categoriesData = [];
        let totalCount = 0;

        if (response.data) {
          // Check if it's a paginated response
          if (response.data.data && Array.isArray(response.data.data)) {
            categoriesData = response.data.data;
            totalCount = response.data.total || response.data.totalItems || response.data.data.length;
          }
          // Check if it's a direct array
          else if (Array.isArray(response.data)) {
            categoriesData = response.data;
            totalCount = response.data.length;
          }
          // Check if it's wrapped in another structure
          else if (response.data.items && Array.isArray(response.data.items)) {
            categoriesData = response.data.items;
            totalCount = response.data.total || response.data.totalItems || response.data.items.length;
          }
        }

        // Transform to expected format
        const transformedData = {
          data: categoriesData,
          total: totalCount,
          categories: categoriesData, // For backward compatibility
          totalCount: totalCount // For backward compatibility
        };

        // Cache the response if no filters were applied
        if (!hasFilters) {
          cache.categories = transformedData;
          cache.lastFetched.categories = new Date().getTime();
        }

        return transformedData;
      } catch (adminError) {
        // If admin endpoint fails, try the public endpoint
        console.warn('Admin categories endpoint failed, falling back to public endpoint:', adminError.message);

        try {
          const fallbackResponse = await apiService.get('/api/categories/all', { params });
          console.log('Fallback categories API response:', fallbackResponse.data);

          let categoriesData = [];
          let totalCount = 0;

          if (fallbackResponse.data) {
            if (fallbackResponse.data.data && Array.isArray(fallbackResponse.data.data)) {
              categoriesData = fallbackResponse.data.data;
              totalCount = fallbackResponse.data.total || fallbackResponse.data.totalItems || fallbackResponse.data.data.length;
            } else if (Array.isArray(fallbackResponse.data)) {
              categoriesData = fallbackResponse.data;
              totalCount = fallbackResponse.data.length;
            }
          }

          const transformedData = {
            data: categoriesData,
            total: totalCount,
            categories: categoriesData,
            totalCount: totalCount
          };

          // Cache the transformed response
          if (!hasFilters) {
            cache.categories = transformedData;
            cache.lastFetched.categories = new Date().getTime();
          }

          return transformedData;
        } catch (publicError) {
          console.error('Both admin and public categories endpoints failed:', publicError.message);

          // Return empty data structure instead of throwing error
          const emptyData = {
            data: [],
            total: 0,
            categories: [],
            totalCount: 0
          };

          return emptyData;
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  async getCategoryById(id) {
    // Check if we have this category in cache
    if (cache.categoryDetails[id]) {
      return cache.categoryDetails[id];
    }

    try {
      const response = await apiService.get(`/api/admin/categories/${id}`);

      // Cache the response
      cache.categoryDetails[id] = response.data;

      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  },

  async createCategory(categoryData) {
    try {
      const response = await apiService.post('/api/admin/categories', categoryData);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  async updateCategory(id, categoryData) {
    try {
      const response = await apiService.put(`/api/admin/categories/${id}`, categoryData);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Update category details cache if it exists
      if (cache.categoryDetails[id]) {
        cache.categoryDetails[id] = response.data.category || response.data;
      }

      return response.data;
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);
      throw error;
    }
  },

  async deleteCategory(id) {
    try {
      const response = await apiService.delete(`/api/admin/categories/${id}`);

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Remove from category details cache
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);
      throw error;
    }
  },

  // Alias for deleteCategory
  async delete(id) {
    return this.deleteCategory(id);
  },

  async uploadCategoryImage(id, imageFile) {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await apiService.post(`/api/admin/categories/${id}/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      // Invalidate specific category in cache
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error uploading image for category ${id}:`, error);
      throw error;
    }
  },

  async getCategoryProducts(id, params = {}) {
    try {
      const response = await apiService.get(`/api/admin/categories/${id}/products`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching products for category ${id}:`, error);
      throw error;
    }
  },

  async getCategoryTree() {
    // If cache is valid, use cached data
    if (isCacheValid('categoryTree') && cache.categoryTree) {
      return cache.categoryTree;
    }

    try {
      // First try the admin endpoint
      try {
        const response = await apiService.get('/api/admin/categories/tree');

        // Cache the response
        cache.categoryTree = response.data;
        cache.lastFetched.categoryTree = new Date().getTime();

        return response.data;
      } catch (adminError) {
        // If admin endpoint fails, build tree from regular categories endpoint
        console.warn('Admin categories tree endpoint failed, building tree from regular categories:', adminError.message);

        // Get all categories and build tree manually
        const categoriesResponse = await this.getCategories();
        const allCategories = categoriesResponse.categories || [];

        // Build tree structure
        const tree = this.buildCategoryTree(allCategories);

        // Cache the tree
        cache.categoryTree = tree;
        cache.lastFetched.categoryTree = new Date().getTime();

        return tree;
      }
    } catch (error) {
      console.error('Error fetching category tree:', error);

      // Provide a more user-friendly error message
      const errorMessage = error.response?.status === 404
        ? 'Category tree endpoint not found. Please check API configuration.'
        : error.response?.status === 403
          ? 'You do not have permission to access the category tree.'
          : error.response?.status === 401
            ? 'Authentication required. Please log in again.'
            : error.message || 'Failed to fetch category tree';

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      throw enhancedError;
    }
  },

  // Helper method to build a category tree from a flat list
  buildCategoryTree(categories) {
    // First, create a map of all categories by ID for quick lookup
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.id] = {
        ...category,
        children: []
      };
    });

    // Build the tree structure
    const rootCategories = [];
    categories.forEach(category => {
      const categoryWithChildren = categoryMap[category.id];

      if (!category.parentId) {
        // This is a root category
        rootCategories.push(categoryWithChildren);
      } else if (categoryMap[category.parentId]) {
        // This is a child category, add it to its parent
        categoryMap[category.parentId].children.push(categoryWithChildren);
      } else {
        // Parent doesn't exist, treat as root
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  },

  async moveCategory(id, newParentId) {
    try {
      const response = await apiService.patch(`/api/admin/categories/${id}/move`, { parentId: newParentId });

      // Invalidate cache
      cache.categories = null;
      cache.categoryTree = null;
      cache.lastFetched.categories = null;
      cache.lastFetched.categoryTree = null;

      // Update category details cache if it exists
      if (cache.categoryDetails[id]) {
        delete cache.categoryDetails[id];
      }

      return response.data;
    } catch (error) {
      console.error(`Error moving category ${id}:`, error);
      throw error;
    }
  },

  // Helper method to clear cache
  clearCache() {
    cache.categories = null;
    cache.categoryTree = null;
    cache.categoryDetails = {};
    cache.lastFetched.categories = null;
    cache.lastFetched.categoryTree = null;
  }
};
