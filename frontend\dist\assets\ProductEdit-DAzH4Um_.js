import{_ as X,g as h,h as ne,x as ie,i as K,c as r,o as d,a as e,k as M,d as z,t as _,n as E,A as T,B as V,F as R,p as q,m as ee,J as re,q as B,f as ue,b as oe,C as ve,e as me}from"./index-DMg5qKr1.js";import{p as Y}from"./products-BHT6Tynz.js";import{c as pe}from"./companies-br2VMpfN.js";const ge={class:"category-select"},fe={class:"label"},he={key:0,class:"has-text-danger"},ye={class:"control"},_e={class:"dropdown-trigger"},be={class:"field has-addons"},Ie={class:"control is-expanded"},Ce=["placeholder","readonly"],$e={class:"control"},we=["disabled"],ke={class:"icon"},xe={class:"dropdown-menu",role:"menu"},Ae={class:"dropdown-content"},De={key:0,class:"dropdown-item"},Me={key:1,class:"dropdown-item"},Pe=["onMousedown"],Se={class:"category-item"},Ee={class:"category-name"},Fe={class:"category-slug has-text-grey is-size-7"},Ue={key:0,class:"help"},Te={__name:"CategorySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Category"},placeholder:{type:String,default:"Search and select category..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(I,{emit:O}){const k=I,F=O,g=h([]),S=h(!1),m=h(""),f=h(!1),v=h(null),$=ne(()=>{var C;const t=Array.isArray(g.value)?g.value:[];if(console.log("🏷️ Filtering categories:",{totalCategories:t.length,searchQuery:m.value,isDropdownOpen:f.value}),!m.value.trim()||m.value===(((C=v.value)==null?void 0:C.name)||""))return console.log("📋 Showing all categories:",t.length),t;const u=m.value.toLowerCase().trim(),c=t.filter(w=>!w||typeof w!="object"?!1:w.name&&w.name.toLowerCase().includes(u)||w.slug&&w.slug.toLowerCase().includes(u)).slice(0,50);return console.log("🎯 Filtered categories:",c.length),c}),A=async()=>{try{S.value=!0,console.log("🏷️ Fetching categories...");const t=await Y.getCategories({pageSize:1e3});if(console.log("📦 Categories response:",t),g.value=Array.isArray(t)?t:[],console.log("✅ Categories loaded:",g.value.length),k.modelValue&&g.value.length>0){const u=g.value.find(c=>c&&c.id===k.modelValue);u?(v.value=u,m.value=u.name||"",console.log("🎯 Selected category found:",u.name)):console.log("❌ Category not found for ID:",k.modelValue)}}catch(t){console.error("❌ Error fetching categories:",t),g.value=[]}finally{S.value=!1}},L=t=>{if(!t||typeof t!="object"||!t.id){console.error("❌ Invalid category object:",t);return}v.value=t,m.value=t.name||"",f.value=!1,console.log("✅ Category selected:",t.name),F("update:modelValue",t.id),F("change",t)},y=()=>{k.readonly||(f.value=!0,console.log("📂 Category dropdown opened"))},U=()=>{f.value=!1,console.log("📁 Category dropdown closed")},a=()=>{k.readonly||(f.value?U():(v.value&&m.value===v.value.name&&(m.value=""),y()))},D=()=>{f.value||(f.value=!0,console.log("📂 Category dropdown opened via search input"))},P=()=>{setTimeout(()=>{U(),v.value?m.value=v.value.name:m.value=""},200)};return ie(()=>k.modelValue,t=>{const u=Array.isArray(g.value)?g.value:[];if(t&&u.length>0){const c=u.find(C=>C&&C.id===t);c?(v.value=c,m.value=c.name||"",console.log("🔄 Category updated via watch:",c.name)):console.log("🔍 Category not found in watch for ID:",t)}else t||(v.value=null,m.value="",console.log("🧹 Cleared category selection"))}),K(()=>{A()}),(t,u)=>(d(),r("div",ge,[e("label",fe,[z(_(I.label)+" ",1),I.required?(d(),r("span",he,"*")):M("",!0)]),e("div",ye,[e("div",{class:E(["dropdown",{"is-active":f.value}])},[e("div",_e,[e("div",be,[e("div",Ie,[T(e("input",{class:"input",type:"text",placeholder:I.placeholder,"onUpdate:modelValue":u[0]||(u[0]=c=>m.value=c),onInput:D,onFocus:y,onBlur:P,readonly:I.readonly},null,40,Ce),[[V,m.value]])]),e("div",$e,[e("button",{class:"button",type:"button",onClick:a,disabled:I.readonly},[e("span",ke,[e("i",{class:E(["fas fa-chevron-down",{"fa-rotate-180":f.value}])},null,2)])],8,we)])])]),e("div",xe,[e("div",Ae,[S.value?(d(),r("div",De,u[1]||(u[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),z(" Loading categories... ")],-1)]))):$.value.length===0?(d(),r("div",Me,u[2]||(u[2]=[e("div",{class:"has-text-grey has-text-centered"}," No categories found ",-1)]))):(d(!0),r(R,{key:2},q($.value,c=>{var C;return d(),r("a",{key:c.id,class:E(["dropdown-item",{"is-active":((C=v.value)==null?void 0:C.id)===c.id}]),onMousedown:ee(w=>L(c),["prevent"])},[e("div",Se,[e("div",Ee,_(c.name),1),e("div",Fe,_(c.slug),1)])],42,Pe)}),128))])])],2)]),v.value?(d(),r("p",Ue," Selected: "+_(v.value.name),1)):M("",!0)]))}},Le=X(Te,[["__scopeId","data-v-80c00f51"]]),Ve={class:"company-select"},je={class:"label"},Oe={key:0,class:"has-text-danger"},Be={class:"control"},Ne={class:"dropdown-trigger"},ze={class:"field has-addons"},Re={class:"control is-expanded"},qe=["placeholder","readonly"],Ge={class:"control"},Qe=["disabled"],We={class:"icon"},Ze={class:"dropdown-menu",role:"menu"},He={class:"dropdown-content"},Je={key:0,class:"dropdown-item"},Ye={key:1,class:"dropdown-item"},Xe=["onMousedown"],Ke={class:"company-item"},es={class:"company-name"},ss={class:"company-details has-text-grey is-size-7"},as={key:0,class:"help"},ts={__name:"CompanySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Company"},placeholder:{type:String,default:"Search and select company..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(I,{emit:O}){const k=I,F=O,g=h([]),S=h(!1),m=h(""),f=h(!1),v=h(null),$=ne(()=>{var C;const t=Array.isArray(g.value)?g.value:[];if(console.log("🔍 Filtering companies:",{totalCompanies:t.length,searchQuery:m.value,isDropdownOpen:f.value}),!m.value.trim()||m.value===(((C=v.value)==null?void 0:C.name)||""))return console.log("📋 Showing all companies:",t.length),t.slice(0,50);const u=m.value.toLowerCase().trim(),c=t.filter(w=>!w||typeof w!="object"?!1:w.name&&w.name.toLowerCase().includes(u)||w.contactEmail&&w.contactEmail.toLowerCase().includes(u)||w.addressCity&&w.addressCity.toLowerCase().includes(u)||w.slug&&w.slug.toLowerCase().includes(u)).slice(0,50);return console.log("🎯 Filtered companies:",c.length),c}),A=async()=>{try{S.value=!0,console.log("🏢 Fetching companies...");const t=await pe.getCompanies({pageSize:1e3});console.log("📦 Companies API response:",t);let u=[];if(t&&t.data&&Array.isArray(t.data)?u=t.data:t&&t.companies&&Array.isArray(t.companies)?u=t.companies:Array.isArray(t)?u=t:(console.warn("⚠️ Unexpected API response structure:",t),u=[]),g.value=u,console.log("✅ Companies loaded:",g.value.length),k.modelValue&&Array.isArray(g.value)){const c=g.value.find(C=>C&&C.id===k.modelValue);c?(v.value=c,m.value=c.name||"",console.log("🎯 Selected company found:",c.name)):console.log("❌ Company not found for ID:",k.modelValue)}}catch(t){console.error("❌ Error fetching companies:",t),g.value=[]}finally{S.value=!1}},L=t=>{if(!t||typeof t!="object"||!t.id){console.error("❌ Invalid company object:",t);return}v.value=t,m.value=t.name||"",f.value=!1,console.log("✅ Company selected:",t.name),F("update:modelValue",t.id),F("change",t)},y=()=>{k.readonly||(f.value=!0,console.log("📂 Dropdown opened"))},U=()=>{f.value=!1,console.log("📁 Dropdown closed")},a=()=>{k.readonly||(f.value?U():(v.value&&m.value===v.value.name&&(m.value=""),y()))},D=()=>{f.value||(f.value=!0,console.log("📂 Dropdown opened via search input"))},P=()=>{setTimeout(()=>{U(),v.value?m.value=v.value.name||"":m.value=""},200)};return ie(()=>k.modelValue,t=>{const u=Array.isArray(g.value)?g.value:[];if(t&&u.length>0){const c=u.find(C=>C&&C.id===t);c?(v.value=c,m.value=c.name||"",console.log("🔄 Company updated via watch:",c.name)):console.log("🔍 Company not found in watch for ID:",t)}else t||(v.value=null,m.value="",console.log("🧹 Cleared company selection"))}),K(()=>{A()}),(t,u)=>(d(),r("div",Ve,[e("label",je,[z(_(I.label)+" ",1),I.required?(d(),r("span",Oe,"*")):M("",!0)]),e("div",Be,[e("div",{class:E(["dropdown",{"is-active":f.value}])},[e("div",Ne,[e("div",ze,[e("div",Re,[T(e("input",{class:"input",type:"text",placeholder:I.placeholder,"onUpdate:modelValue":u[0]||(u[0]=c=>m.value=c),onInput:D,onFocus:y,onBlur:P,readonly:I.readonly},null,40,qe),[[V,m.value]])]),e("div",Ge,[e("button",{class:"button",type:"button",onClick:a,disabled:I.readonly},[e("span",We,[e("i",{class:E(["fas fa-chevron-down",{"fa-rotate-180":f.value}])},null,2)])],8,Qe)])])]),e("div",Ze,[e("div",He,[S.value?(d(),r("div",Je,u[1]||(u[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),z(" Loading companies... ")],-1)]))):$.value.length===0?(d(),r("div",Ye,u[2]||(u[2]=[e("div",{class:"has-text-grey has-text-centered"}," No companies found ",-1)]))):(d(!0),r(R,{key:2},q($.value,c=>{var C;return d(),r("a",{key:c.id,class:E(["dropdown-item",{"is-active":((C=v.value)==null?void 0:C.id)===c.id}]),onMousedown:ee(w=>L(c),["prevent"])},[e("div",Ke,[e("div",es,_(c.name),1),e("div",ss,_(c.contactEmail)+" • "+_(c.addressCity),1)])],42,Xe)}),128))])])],2)]),v.value?(d(),r("p",as," Selected: "+_(v.value.name),1)):M("",!0)]))}},ls=X(ts,[["__scopeId","data-v-becaac52"]]),os={key:0,class:"product-image-manager-loading"},ns={key:1,class:"product-image-manager"},is={class:"upload-section mb-5"},rs={class:"field"},ds={class:"control"},cs=["disabled"],us={class:"drop-zone-content"},vs={class:"icon is-large has-text-grey-light"},ms={key:0,class:"fas fa-cloud-upload-alt fa-3x"},ps={key:1,class:"fas fa-spinner fa-spin fa-3x"},gs={class:"has-text-grey mt-3"},fs={key:0},hs={key:1},ys={key:0,class:"help"},_s={key:0,class:"notification is-info"},bs={class:"level"},Is={class:"level-left"},Cs={class:"level-item"},$s={class:"icon-text"},ws={class:"level-right"},ks={class:"level-item"},xs=["value","max"],As={key:0,class:"current-images mb-5"},Ds={class:"label"},Ms={class:"columns is-multiline"},Ps={class:"card image-card"},Ss={class:"card-image"},Es={class:"image is-4by3"},Fs=["src","alt","onClick"],Us={class:"image-overlay"},Ts={class:"image-actions"},Ls=["onClick"],Vs=["onClick"],js=["onClick"],Os={class:"card-content p-3"},Bs={class:"level is-mobile"},Ns={class:"level-left"},zs={class:"level-item"},Rs={class:"level-right"},qs={class:"level-item"},Gs={key:0,class:"tag is-small is-info"},Qs={key:1,class:"pending-images mb-5"},Ws={class:"label"},Zs={class:"columns is-multiline"},Hs={class:"card image-card"},Js={class:"card-image"},Ys={class:"image is-4by3"},Xs=["src","alt"],Ks={class:"card-content p-3"},ea={class:"level is-mobile"},sa={class:"level-left"},aa={class:"level-item"},ta={class:"level-right"},la={class:"level-item"},oa={class:"buttons are-small"},na=["onClick"],ia=["onClick"],ra={class:"content is-small"},da={key:2,class:"no-images has-background-light has-text-centered p-6 is-rounded"},ca={class:"modal-content"},ua={class:"image"},va=["src"],ma=5*1024*1024,pa={__name:"ProductImageManager",props:{productId:{type:String,default:null},images:{type:Array,default:()=>[]},isCreate:{type:Boolean,default:!1}},emits:["images-updated","main-image-changed","image-uploaded","image-deleted","pending-images-changed"],setup(I,{expose:O,emit:k}){const F=I,g=k,S=h(!1),m=h(!1),f=h(!1),v=h(null),$=h(!1),A=h(!1),L=h([]),y=h([]),U=h(!1),a=h(!1),D=h(null),P=h(null),t=h({current:0,total:0}),u=["image/jpeg","image/jpg","image/png","image/gif","image/webp","image/jfif"],c=()=>{if(!A.value&&v.value&&m.value&&f.value)try{v.value&&typeof v.value.click=="function"?v.value.click():console.warn("File input element not properly initialized")}catch(n){console.warn("File input not ready:",n)}},C=n=>{if(!n.target||!n.target.files)return;const l=Array.from(n.target.files);Q(l),n.target.value=""},w=n=>{if(n.preventDefault(),$.value=!1,A.value)return;const l=Array.from(n.dataTransfer.files);Q(l)},se=n=>{n.preventDefault(),A.value||($.value=!0)},ae=()=>{$.value=!1},Q=n=>{if(n.length===0)return;const l=[],x=[];n.forEach(b=>{if(!u.includes(b.type.toLowerCase())){x.push(`${b.name}: Unsupported file type`);return}if(b.size>ma){x.push(`${b.name}: File too large (max 5MB)`);return}l.push(b)}),x.length>0&&alert(`Some files were rejected:
`+x.join(`
`)),l.length!==0&&(L.value=l,l.forEach((b,p)=>{const j=new FileReader;j.onload=async de=>{const ce={id:`temp-${Date.now()}-${Math.random()}-${p}`,file:b,name:b.name,preview:de.target.result,size:b.size,isTemp:!0,isMain:y.value.length===0&&F.images.length===0};y.value.push(ce),await re(),g("pending-images-changed",y.value)},j.readAsDataURL(b)}))},W=async n=>{if(F.productId)try{const l=await B.patch(`/api/admin/products/${F.productId}/images/${n}/main`);l.data&&l.data.success&&(g("main-image-changed",n),g("images-updated"))}catch(l){console.error("Error setting main image:",l),alert("Failed to set main image. Please try again.")}},G=n=>{y.value.forEach(l=>{l.isMain=l.id===n}),g("pending-images-changed",y.value)},Z=n=>{const l=y.value.findIndex(x=>x.id===n);l!==-1&&(y.value.splice(l,1),g("pending-images-changed",y.value))},H=n=>{D.value=n,U.value=!0},J=()=>{U.value=!1,D.value=null},te=n=>{P.value=n,a.value=!0},N=()=>{a.value=!1,P.value=null},le=async()=>{if(P.value)try{await B.delete(`/api/admin/products/${F.productId}/images/${P.value.id}`),g("image-deleted",P.value.id),g("images-updated"),N()}catch(n){console.error("Error deleting image:",n),alert("Failed to delete image. Please try again.")}},o=n=>{n.target.dataset.errorHandled||(n.target.dataset.errorHandled="true",n.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNMTAwIDExMEMxMTQuMTQyIDExMCAxMjYgOTguMTQyMSAxMjYgODRDMTI2IDY5Ljg1NzkgMTE0LjE0MiA1OCAxMDAgNThDODUuODU3OSA1OCA3NCA2OS44NTc5IDc0IDg0Qzc0IDk4LjE0MjEgODUuODU3OSAxMTAgMTAwIDExMFoiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik04NiA3NEg5MFY3OEg4NlY3NFoiIGZpbGw9IiNEMUQxRDEiLz4KPHA+CjwvcGF0aD4KPC9zdmc+Cg==",n.target.classList.add("image-error"))},s=n=>{if(!n)return"";try{const l=new Date(n);return new Intl.DateTimeFormat("uk-UA",{month:"short",day:"numeric"}).format(l)}catch{return""}};return O({uploadPendingImages:async n=>{if(y.value.length!==0)try{A.value=!0,t.value={current:0,total:y.value.length};for(const l of y.value){const x=new FormData;x.append("image",l.file);try{const b=await B.post(`/api/admin/products/${n}/images/single`,x);if(l.isMain&&b.data&&b.data.success&&b.data.data){const p=b.data.data;await W(p)}t.value.current++,g("image-uploaded",b.data)}catch(b){throw console.error(`Failed to upload image: ${l.name}`,b),new Error(`Failed to upload ${l.name}: ${b.message}`)}}y.value=[],g("pending-images-changed",[]),g("images-updated")}catch(l){throw console.error("Error uploading pending images:",l),l}finally{A.value=!1,t.value={current:0,total:0}}},getPendingImages:()=>y.value,clearPendingImages:()=>{y.value=[],g("pending-images-changed",[])}}),K(async()=>{try{S.value=!0,await re(),await new Promise(n=>{const l=()=>{document.readyState==="complete"||document.readyState==="interactive"?(m.value=!0,n()):setTimeout(l,10)};l()}),setTimeout(()=>{f.value=!0},50)}catch(n){console.warn("ProductImageManager mount error:",n),setTimeout(()=>{S.value=!0,m.value=!0,f.value=!0},200)}}),(n,l)=>{var x,b;return!S.value||!m.value||!f.value?(d(),r("div",os,l[0]||(l[0]=[e("div",{class:"has-text-centered py-4"},[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-spin fa-2x"})]),e("p",{class:"mt-2"},"Loading image manager...")],-1)]))):(d(),r("div",ns,[l[13]||(l[13]=e("h2",{class:"title is-5 mb-4"},"🖼️ Product Images",-1)),e("div",is,[e("div",rs,[l[2]||(l[2]=e("label",{class:"label"},"Upload Images",-1)),e("div",ds,[e("div",{class:E(["drop-zone",{"is-dragover":$.value,"is-disabled":A.value}]),onDrop:w,onDragover:ee(se,["prevent"]),onDragleave:ae,onClick:c},[e("input",{ref_key:"fileInput",ref:v,type:"file",accept:"image/*,.jfif",multiple:"",onChange:C,disabled:A.value,style:{display:"none"}},null,40,cs),e("div",us,[e("span",vs,[A.value?(d(),r("i",ps)):(d(),r("i",ms))]),e("p",gs,[A.value?(d(),r("strong",hs,"Uploading images...")):(d(),r("strong",fs,"Drop images here or click to browse"))]),l[1]||(l[1]=e("p",{class:"has-text-grey-light is-size-7"}," Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each) ",-1))])],34)]),L.value.length>0?(d(),r("p",ys,_(L.value.length)+" file(s) selected ",1)):M("",!0)]),A.value?(d(),r("div",_s,[e("div",bs,[e("div",Is,[e("div",Cs,[e("span",$s,[l[3]||(l[3]=e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1)),e("span",null,"Uploading "+_(t.value.current)+" of "+_(t.value.total)+" images...",1)])])]),e("div",ws,[e("div",ks,[e("progress",{class:"progress is-info",value:t.value.current,max:t.value.total},_(Math.round(t.value.current/t.value.total*100))+"% ",9,xs)])])])])):M("",!0)]),I.images.length>0?(d(),r("div",As,[e("label",Ds,"Current Images ("+_(I.images.length)+")",1),e("div",Ms,[(d(!0),r(R,null,q(I.images,p=>(d(),r("div",{key:p.id,class:"column is-6-tablet is-4-desktop"},[e("div",Ps,[e("div",Ss,[e("figure",Es,[e("img",{src:p.imageUrl||p.url,alt:p.altText||"Product image",onError:o,onClick:j=>H(p)},null,40,Fs)]),e("div",Us,[e("div",Ts,[p.isMain?M("",!0):(d(),r("button",{key:0,class:"button is-small is-info",onClick:j=>W(p.id),title:"Set as main image"},l[4]||(l[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-star"})],-1)]),8,Ls)),e("button",{class:"button is-small is-primary",onClick:j=>H(p),title:"View full size"},l[5]||(l[5]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)]),8,Vs),e("button",{class:"button is-small is-danger",onClick:j=>te(p),title:"Delete image"},l[6]||(l[6]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,js)])])]),e("div",Os,[e("div",Bs,[e("div",Ns,[e("div",zs,[e("span",{class:E(["tag is-small",p.isMain?"is-primary":"is-light"])},_(p.isMain?"Main":`#${p.order||0}`),3)])]),e("div",Rs,[e("div",qs,[p.createdAt?(d(),r("span",Gs,_(s(p.createdAt)),1)):M("",!0)])])])])])]))),128))])])):M("",!0),y.value.length>0?(d(),r("div",Qs,[e("label",Ws,"Images to Upload ("+_(y.value.length)+")",1),l[9]||(l[9]=e("div",{class:"notification is-warning is-light"},[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"These images will be uploaded when you save the product.")])],-1)),e("div",Zs,[(d(!0),r(R,null,q(y.value,p=>(d(),r("div",{key:p.id,class:"column is-6-tablet is-4-desktop"},[e("div",Hs,[e("div",Js,[e("figure",Ys,[e("img",{src:p.preview,alt:p.name},null,8,Xs)])]),e("div",Ks,[e("div",ea,[e("div",sa,[e("div",aa,[e("span",{class:E(["tag is-small",p.isMain?"is-warning":"is-light"])},_(p.isMain?"Will be Main":"Pending"),3)])]),e("div",ta,[e("div",la,[e("div",oa,[p.isMain?M("",!0):(d(),r("button",{key:0,class:"button is-warning is-small",onClick:j=>G(p.id),title:"Set as main"},l[7]||(l[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-star"})],-1)]),8,na)),e("button",{class:"button is-danger is-small",onClick:j=>Z(p.id),title:"Remove"},l[8]||(l[8]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1)]),8,ia)])])])]),e("div",ra,[e("p",null,[e("strong",null,_(p.name),1)]),e("p",null,_((p.size/1024/1024).toFixed(2))+" MB",1)])])])]))),128))])])):M("",!0),I.images.length===0&&y.value.length===0?(d(),r("div",da,l[10]||(l[10]=[e("span",{class:"icon is-large has-text-grey"},[e("i",{class:"fas fa-images fa-3x"})],-1),e("p",{class:"has-text-grey mt-2"},"No images uploaded yet",-1),e("p",{class:"has-text-grey-light is-size-7"},"Use the upload area above to add product images",-1)]))):M("",!0),e("div",{class:E(["modal",{"is-active":U.value}])},[e("div",{class:"modal-background",onClick:J}),e("div",ca,[e("p",ua,[D.value?(d(),r("img",{key:0,src:((x=D.value)==null?void 0:x.imageUrl)||((b=D.value)==null?void 0:b.url)},null,8,va)):M("",!0)])]),e("button",{class:"modal-close is-large",onClick:J})],2),e("div",{class:E(["modal",{"is-active":a.value}])},[e("div",{class:"modal-background",onClick:N}),e("div",{class:"modal-card"},[e("header",{class:"modal-card-head"},[l[11]||(l[11]=e("p",{class:"modal-card-title"},"Confirm Delete",-1)),e("button",{class:"delete",onClick:N})]),l[12]||(l[12]=e("section",{class:"modal-card-body"},[e("p",null,"Are you sure you want to delete this image?"),e("p",{class:"has-text-danger is-size-7 mt-2"},"This action cannot be undone.")],-1)),e("footer",{class:"modal-card-foot"},[e("button",{class:"button is-danger",onClick:le},"Delete"),e("button",{class:"button",onClick:N},"Cancel")])])],2)]))}}},ga=X(pa,[["__scopeId","data-v-c11dd395"]]),fa={class:"product-edit"},ha={key:0,class:"has-text-centered py-6"},ya={key:1,class:"notification is-danger"},_a={class:"level mb-5"},ba={class:"level-left"},Ia={class:"level-item"},Ca={class:"title is-3"},$a={key:0,class:"subtitle is-6 has-text-grey"},wa={class:"level-right"},ka={class:"level-item"},xa={class:"buttons"},Aa={class:"form-sections"},Da={class:"box mb-5"},Ma={class:"columns is-multiline"},Pa={class:"column is-6"},Sa={class:"column is-6"},Ea={class:"column is-12"},Fa={class:"field"},Ua={class:"control"},Ta={class:"column is-12"},La={class:"field"},Va={class:"control"},ja={class:"column is-12"},Oa={class:"field"},Ba={class:"control"},Na={class:"box mb-5"},za={class:"box mb-5"},Ra={class:"columns"},qa={class:"column is-4"},Ga={class:"field"},Qa={class:"control has-icons-left"},Wa={class:"column is-4"},Za={class:"field"},Ha={class:"control has-icons-left"},Ja={class:"column is-4"},Ya={class:"field"},Xa={class:"control"},Ka={class:"select is-fullwidth"},et={class:"box mb-5"},st={class:"field is-grouped mb-4"},at={class:"control is-expanded"},tt={class:"control is-expanded"},lt={key:0,class:"table-container"},ot={class:"table is-fullwidth is-striped is-hoverable"},nt={class:"tags"},it=["onClick"],rt=["onClick"],dt={key:1,class:"notification is-light has-text-centered"},ct={class:"box mb-5"},ut={class:"columns is-multiline"},vt={class:"column is-12"},mt={class:"field"},pt={class:"control"},gt={class:"column is-12"},ft={class:"field"},ht={class:"control"},yt={class:"column is-12"},_t={class:"field"},bt={class:"field has-addons"},It={class:"control is-expanded"},Ct={class:"file has-name is-fullwidth"},$t={class:"file-label"},wt=["disabled"],kt={key:0,class:"file-icon"},xt={class:"file-label"},At={class:"file-name"},Dt={key:0,class:"control"},Mt=["disabled"],Pt={key:0,class:"has-text-centered mt-4"},St={class:"box has-background-light p-4"},Et={class:"image is-128x128 is-inline-block"},Ft=["src","alt"],Ut={key:0,class:"box mb-5"},Tt={class:"columns"},Lt={class:"column is-6"},Vt={class:"field"},jt={class:"control"},Ot=["value"],Bt={key:0,class:"column is-6"},Nt={class:"field"},zt={class:"control"},Rt=["value"],qt={__name:"ProductEdit",props:{productId:{type:String,required:!1},isCreate:{type:Boolean,default:!1}},emits:["save","cancel"],setup(I,{emit:O}){const k=I,F=O,g=ue(),S=me(),m=h(!1),f=h(!1),v=h(!1),$=h(null),A=h(""),L=h([]),y=h(null),U=h(""),a=h({id:"",companyId:"",name:"",slug:"",description:"",priceAmount:0,priceCurrency:0,stock:0,status:"0",categoryId:"",attributes:{},metaTitle:"",metaDescription:"",metaImage:"",createdAt:null,updatedAt:null}),D=h({key:"",value:""}),P=ne(()=>k.productId||g.params.id),t=()=>{if(!D.value.key.trim()||!D.value.value.trim())return;const o=D.value.key.trim(),s=D.value.value.trim();a.value.attributes[o]?Array.isArray(a.value.attributes[o])?a.value.attributes[o].push(s):a.value.attributes[o]=[a.value.attributes[o],s]:a.value.attributes[o]=s,D.value.key="",D.value.value=""},u=o=>{delete a.value.attributes[o]},c=(o,s)=>{const i=a.value.attributes[o];Array.isArray(i)?i.length===1?delete a.value.attributes[o]:i.splice(s,1):delete a.value.attributes[o]},C=async o=>{const s=o.target.files[0];if(s){A.value=s.name,v.value=!0;try{const i=new FileReader;i.onload=n=>{a.value.metaImage=n.target.result},i.readAsDataURL(s)}catch(i){console.error("Error processing meta image:",i),$.value="Failed to process meta image. Please try again."}finally{v.value=!1}}},w=()=>{a.value.metaImage="",A.value=""},se=o=>{console.log("Main image changed:",o)},ae=o=>{console.log("Image uploaded:",o),G()},Q=o=>{console.log("Image deleted:",o)},W=o=>{console.log("Pending images changed:",o.length)},G=async()=>{if(!(k.isCreate||!P.value))try{const o=await B.get(`/api/admin/products/${P.value}/with-images`);o.data&&o.data.success&&o.data.data&&(L.value=o.data.data.images||[])}catch(o){console.error("Error loading product images:",o),L.value=[]}},Z=o=>{if(!o)return"N/A";try{const s=new Date(o);return new Intl.DateTimeFormat("uk-UA",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(s)}catch(s){return console.error("Error formatting date:",s,o),o}},H=o=>{console.log("Category changed:",o)},J=o=>{console.log("Company changed:",o)},te=()=>{S.push("/admin/products")},N=async()=>{try{if(f.value=!0,$.value=null,!a.value.companyId){$.value="Company is required",f.value=!1;return}if(!a.value.name||!a.value.name.trim()){$.value="Product name is required",f.value=!1;return}if(!a.value.description||!a.value.description.trim()){$.value="Product description is required",f.value=!1;return}if(!a.value.categoryId){$.value="Category is required",f.value=!1;return}if(!a.value.priceAmount||a.value.priceAmount<=0){$.value="Price must be greater than 0",f.value=!1;return}const o=a.value.slug||a.value.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"),s={companyId:a.value.companyId,name:a.value.name.trim(),slug:o,description:a.value.description.trim(),priceCurrency:0,priceAmount:parseFloat(a.value.priceAmount)||0,stock:parseInt(a.value.stock)||0,categoryId:a.value.categoryId,status:parseInt(a.value.status)||0,attributes:a.value.attributes||null,metaTitle:a.value.metaTitle||a.value.name||"",metaDescription:a.value.metaDescription||a.value.description||"",metaImage:a.value.metaImage&&!a.value.metaImage.startsWith("data:")?a.value.metaImage.trim():"https://via.placeholder.com/300x200.png?text=Product+Image"};console.log("📤 Sending product data:",s);let i;if(k.isCreate){const n=await Y.createProduct(s);if(console.log("✅ Product created:",n),i=n.data||n,a.value.metaImage&&a.value.metaImage.startsWith("data:")&&i)try{const x=await(await fetch(a.value.metaImage)).blob(),b=new File([x],A.value||"meta-image.jpg",{type:x.type}),p=new FormData;p.append("image",b),await B.post(`/api/admin/products/${i}/meta-image`,p,{headers:{"Content-Type":"multipart/form-data"}}),console.log("✅ Meta image uploaded for new product")}catch(l){console.error("❌ Error uploading meta image:",l)}}else{const n=await Y.updateProduct(P.value,s);if(console.log("✅ Product updated:",n),i=P.value,a.value.metaImage&&a.value.metaImage.startsWith("data:"))try{const x=await(await fetch(a.value.metaImage)).blob(),b=new File([x],A.value||"meta-image.jpg",{type:x.type}),p=new FormData;p.append("image",b),await B.post(`/api/admin/products/${i}/meta-image`,p,{headers:{"Content-Type":"multipart/form-data"}}),console.log("✅ Meta image updated")}catch(l){console.error("❌ Error updating meta image:",l)}else if(!a.value.metaImage&&U.value)try{await B.delete(`/api/admin/products/${i}/meta-image`),console.log("✅ Meta image deleted")}catch(l){console.error("❌ Error deleting meta image:",l)}}if(y.value&&i){const n=y.value.getPendingImages();n.length>0&&(console.log(`🔄 Uploading ${n.length} pending images...`),await y.value.uploadPendingImages(i))}F("save",s)}catch(o){console.error("❌ Error saving product:",o);let s="Failed to save product";o.response&&o.response.data?o.response.data.message?s=o.response.data.message:o.response.data.errors?s=Object.values(o.response.data.errors).flat().join(", "):typeof o.response.data=="string"&&(s=o.response.data):o.message&&(s=o.message),$.value=s}finally{f.value=!1}};ie(()=>a.value.name,o=>{o&&!a.value.slug&&(a.value.slug=o.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"))});const le=async()=>{if(!k.isCreate)try{m.value=!0,$.value=null;const o=await Y.getProductById(P.value),s=o.data||o;if(Object.keys(a.value).forEach(i=>{s[i]!==void 0&&(a.value[i]=s[i])}),U.value=s.metaImage||"",s.attributes)try{a.value.attributes=typeof s.attributes=="string"?JSON.parse(s.attributes):s.attributes}catch(i){console.error("Error parsing attributes:",i),a.value.attributes={}}console.log("Product loaded for editing:",a.value),await G()}catch(o){console.error("Error loading product:",o),$.value=o.message||"Failed to load product"}finally{m.value=!1}};return K(async()=>{await le()}),(o,s)=>(d(),r("div",fa,[m.value?(d(),r("div",ha,s[13]||(s[13]=[e("div",{class:"loader is-loading"},null,-1),e("p",{class:"mt-4"},"Loading product data...",-1)]))):$.value?(d(),r("div",ya,[s[14]||(s[14]=e("strong",null,"Error:",-1)),z(" "+_($.value),1)])):(d(),r("form",{key:2,onSubmit:ee(N,["prevent"]),class:"product-form"},[e("div",_a,[e("div",ba,[e("div",{class:"level-item"},[e("button",{class:"button",onClick:te},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Products",-1)]))]),e("div",Ia,[e("div",null,[e("h1",Ca,_(I.isCreate?"Create Product":"Edit Product"),1),I.isCreate?M("",!0):(d(),r("p",$a,_(a.value.id),1))])])]),e("div",wa,[e("div",ka,[e("div",xa,[e("button",{type:"button",class:"button",onClick:s[0]||(s[0]=i=>o.$emit("cancel"))}," Cancel "),e("button",{type:"submit",class:E(["button is-primary",{"is-loading":f.value}])},_(I.isCreate?"Create Product":"Save Changes"),3)])])])]),e("div",Aa,[e("div",Da,[s[20]||(s[20]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-primary"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"Basic Information")])],-1)),e("div",Ma,[e("div",Pa,[oe(ls,{modelValue:a.value.companyId,"onUpdate:modelValue":s[1]||(s[1]=i=>a.value.companyId=i),label:"Company",placeholder:"Search and select company...",required:!0,onChange:J},null,8,["modelValue"])]),e("div",Sa,[oe(Le,{modelValue:a.value.categoryId,"onUpdate:modelValue":s[2]||(s[2]=i=>a.value.categoryId=i),label:"Category",placeholder:"Search and select category...",required:!0,onChange:H},null,8,["modelValue"])]),e("div",Ea,[e("div",Fa,[s[16]||(s[16]=e("label",{class:"label"},"Product Name *",-1)),e("div",Ua,[T(e("input",{"onUpdate:modelValue":s[3]||(s[3]=i=>a.value.name=i),class:"input",type:"text",placeholder:"Enter product name",required:""},null,512),[[V,a.value.name]])])])]),e("div",Ta,[e("div",La,[s[17]||(s[17]=e("label",{class:"label"},"Slug",-1)),e("div",Va,[T(e("input",{"onUpdate:modelValue":s[4]||(s[4]=i=>a.value.slug=i),class:"input",type:"text",placeholder:"Auto-generated from name"},null,512),[[V,a.value.slug]])]),s[18]||(s[18]=e("p",{class:"help"},"Leave empty to auto-generate from product name",-1))])]),e("div",ja,[e("div",Oa,[s[19]||(s[19]=e("label",{class:"label"},"Description",-1)),e("div",Ba,[T(e("textarea",{"onUpdate:modelValue":s[5]||(s[5]=i=>a.value.description=i),class:"textarea",placeholder:"Enter product description",rows:"4"},null,512),[[V,a.value.description]])])])])])]),e("div",Na,[s[21]||(s[21]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-link"},[e("i",{class:"fas fa-images"})]),e("span",null,"Product Images")])],-1)),oe(ga,{ref_key:"imageManager",ref:y,"product-id":P.value,images:L.value,"is-create":k.isCreate,onImagesUpdated:G,onMainImageChanged:se,onImageUploaded:ae,onImageDeleted:Q,onPendingImagesChanged:W},null,8,["product-id","images","is-create"])]),e("div",za,[s[28]||(s[28]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-success"},[e("i",{class:"fas fa-dollar-sign"})]),e("span",null,"Pricing & Inventory")])],-1)),e("div",Ra,[e("div",qa,[e("div",Ga,[s[23]||(s[23]=e("label",{class:"label"},"Price *",-1)),e("div",Qa,[T(e("input",{"onUpdate:modelValue":s[6]||(s[6]=i=>a.value.priceAmount=i),class:"input",type:"number",step:"0.01",placeholder:"0.00",required:""},null,512),[[V,a.value.priceAmount,void 0,{number:!0}]]),s[22]||(s[22]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-hryvnia-sign"})],-1))])])]),e("div",Wa,[e("div",Za,[s[25]||(s[25]=e("label",{class:"label"},"Stock *",-1)),e("div",Ha,[T(e("input",{"onUpdate:modelValue":s[7]||(s[7]=i=>a.value.stock=i),class:"input",type:"number",min:"0",placeholder:"0",required:""},null,512),[[V,a.value.stock,void 0,{number:!0}]]),s[24]||(s[24]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-boxes"})],-1))])])]),e("div",Ja,[e("div",Ya,[s[27]||(s[27]=e("label",{class:"label"},"Status",-1)),e("div",Xa,[e("div",Ka,[T(e("select",{"onUpdate:modelValue":s[8]||(s[8]=i=>a.value.status=i)},s[26]||(s[26]=[e("option",{value:"0"},"Pending",-1),e("option",{value:"1"},"Approved",-1),e("option",{value:"2"},"Rejected",-1)]),512),[[ve,a.value.status]])])])])])])]),e("div",et,[s[33]||(s[33]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-info"},[e("i",{class:"fas fa-tags"})]),e("span",null,"Product Attributes")])],-1)),e("div",st,[e("div",at,[T(e("input",{"onUpdate:modelValue":s[9]||(s[9]=i=>D.value.key=i),class:"input",type:"text",placeholder:"Attribute name (e.g., Color, Size)"},null,512),[[V,D.value.key]])]),e("div",tt,[T(e("input",{"onUpdate:modelValue":s[10]||(s[10]=i=>D.value.value=i),class:"input",type:"text",placeholder:"Attribute value (e.g., Red, Large)"},null,512),[[V,D.value.value]])]),e("div",{class:"control"},[e("button",{type:"button",class:"button is-primary",onClick:t},s[29]||(s[29]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add",-1)]))])]),Object.keys(a.value.attributes).length>0?(d(),r("div",lt,[e("table",ot,[s[31]||(s[31]=e("thead",null,[e("tr",null,[e("th",null,"Attribute"),e("th",null,"Values"),e("th",{width:"100"},"Actions")])],-1)),e("tbody",null,[(d(!0),r(R,null,q(a.value.attributes,(i,n)=>(d(),r("tr",{key:n},[e("td",null,[e("strong",null,_(n),1)]),e("td",null,[e("span",nt,[(d(!0),r(R,null,q(Array.isArray(i)?i:[i],(l,x)=>(d(),r("span",{key:x,class:"tag is-light"},[z(_(l)+" ",1),e("button",{type:"button",class:"delete is-small",onClick:b=>c(n,x)},null,8,it)]))),128))])]),e("td",null,[e("button",{type:"button",class:"button is-small is-danger",onClick:l=>u(n)},s[30]||(s[30]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)]),8,rt)])]))),128))])])])):(d(),r("div",dt,s[32]||(s[32]=[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"No attributes added yet. Use the form above to add product attributes.")],-1)])))]),e("div",ct,[s[40]||(s[40]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-warning"},[e("i",{class:"fas fa-search"})]),e("span",null,"SEO Meta Information")])],-1)),e("div",ut,[e("div",vt,[e("div",mt,[s[34]||(s[34]=e("label",{class:"label"},"Meta Title",-1)),e("div",pt,[T(e("input",{"onUpdate:modelValue":s[11]||(s[11]=i=>a.value.metaTitle=i),class:"input",type:"text",placeholder:"SEO title for search engines"},null,512),[[V,a.value.metaTitle]])])])]),e("div",gt,[e("div",ft,[s[35]||(s[35]=e("label",{class:"label"},"Meta Description",-1)),e("div",ht,[T(e("textarea",{"onUpdate:modelValue":s[12]||(s[12]=i=>a.value.metaDescription=i),class:"textarea",placeholder:"SEO description for search engines",rows:"3"},null,512),[[V,a.value.metaDescription]])])])]),e("div",yt,[e("div",_t,[s[38]||(s[38]=e("label",{class:"label"},"Meta Image",-1)),e("div",bt,[e("div",It,[e("div",Ct,[e("label",$t,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:C,disabled:v.value},null,40,wt),e("span",{class:E(["file-cta",{"is-loading":v.value}])},[v.value?M("",!0):(d(),r("span",kt,s[36]||(s[36]=[e("i",{class:"fas fa-upload"},null,-1)]))),e("span",xt,_(v.value?"Uploading...":"Choose image..."),1)],2),e("span",At,_(A.value||"No file selected"),1)])])]),a.value.metaImage?(d(),r("div",Dt,[e("button",{type:"button",class:"button is-danger",onClick:w,disabled:v.value},s[37]||(s[37]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Clear",-1)]),8,Mt)])):M("",!0)])])])]),a.value.metaImage?(d(),r("div",Pt,[e("div",St,[e("figure",Et,[e("img",{src:a.value.metaImage,alt:a.value.name,class:"is-rounded"},null,8,Ft)]),s[39]||(s[39]=e("p",{class:"has-text-grey mt-2"},[e("small",null,"Meta Image Preview")],-1))])])):M("",!0)]),I.isCreate?M("",!0):(d(),r("div",Ut,[s[43]||(s[43]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-grey"},[e("i",{class:"fas fa-clock"})]),e("span",null,"Timestamps")])],-1)),e("div",Tt,[e("div",Lt,[e("div",Vt,[s[41]||(s[41]=e("label",{class:"label"},"Created At",-1)),e("div",jt,[e("input",{class:"input",type:"text",value:Z(a.value.createdAt),readonly:""},null,8,Ot)])])]),a.value.updatedAt?(d(),r("div",Bt,[e("div",Nt,[s[42]||(s[42]=e("label",{class:"label"},"Updated At",-1)),e("div",zt,[e("input",{class:"input",type:"text",value:Z(a.value.updatedAt),readonly:""},null,8,Rt)])])])):M("",!0)])]))])],32))]))}},Zt=X(qt,[["__scopeId","data-v-ae5b1a1c"]]);export{Zt as default};
