﻿using FluentValidation;
using Marketplace.Application.Commands.Order;

namespace Marketplace.Application.Validators;

public class StoreOrderRequestValidator : AbstractValidator<StoreOrderCommand>
{
    public StoreOrderRequestValidator()
    {
        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("Ідентифікатор клієнта не може бути пустим.");

        RuleFor(x => x.TotalPriceCurrency)
            .NotEmpty().WithMessage("Валюта загальної суми не може бути пустою.");

        RuleFor(x => x.TotalPriceAmount)
            .NotEmpty().WithMessage("Сума замовлення не може бути пустою.");

        RuleFor(x => x.ShippingAddressId)
            .NotEmpty().WithMessage("Ідентифікатор адреси доставки не може бути пустим.");

        RuleFor(x => x.ShippingMethodId)
            .NotEmpty().WithMessage("Ідентифікатор методу доставки не може бути пустим.");
    }
}

public class UpdateOrderRequestValidator : AbstractValidator<UpdateOrderCommand>
{
    public UpdateOrderRequestValidator()
    {
        RuleFor(x => x.Status)
            .IsInEnum().WithMessage("Невірний статус замовлення.")
            .When(x => x.Status.HasValue);

        RuleFor(x => x.PaymentStatus)
            .IsInEnum().WithMessage("Невірний статус платежу.")
            .When(x => x.PaymentStatus.HasValue);

        RuleFor(x => x.ShippingAddress)
            .MaximumLength(500).WithMessage("Адреса доставки не може перевищувати 500 символів.")
            .When(x => !string.IsNullOrEmpty(x.ShippingAddress));

        RuleFor(x => x.TrackingNumber)
            .MaximumLength(100).WithMessage("Номер відстеження не може перевищувати 100 символів.")
            .When(x => !string.IsNullOrEmpty(x.TrackingNumber));

        RuleFor(x => x.ShippingMethod)
            .MaximumLength(100).WithMessage("Метод доставки не може перевищувати 100 символів.")
            .When(x => !string.IsNullOrEmpty(x.ShippingMethod));

        RuleFor(x => x.Notes)
            .MaximumLength(1000).WithMessage("Нотатки не можуть перевищувати 1000 символів.")
            .When(x => !string.IsNullOrEmpty(x.Notes));
    }
}

