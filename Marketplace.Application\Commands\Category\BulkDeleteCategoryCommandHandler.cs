﻿﻿using Marketplace.Domain.Repositories;
using MediatR;

namespace Marketplace.Application.Commands.Category;

public class BulkDeleteCategoryCommandHandler : IRequestHandler<BulkDeleteCategoryCommand, int>
{
    private readonly ICategoryRepository _repository;

    public BulkDeleteCategoryCommandHandler(ICategoryRepository repository)
    {
        _repository = repository;
    }

    public async Task<int> Handle(BulkDeleteCategoryCommand request, CancellationToken cancellationToken)
    {
        int deletedCount = 0;

        foreach (var id in request.Ids)
        {
            var item = await _repository.GetByIdAsync(id, cancellationToken);
            if (item != null)
            {
                await _repository.DeleteAsync(item.Id, cancellationToken);
                deletedCount++;
            }
        }

        return deletedCount;
    }
}
