const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/NotFoundPage-BsHt-cUw.js","assets/NotFoundPage-CYYAFLUo.css","assets/AdminLayout-Cv8tSEUJ.js","assets/AdminLayout-dOIlMIHf.css","assets/Dashboard-CbicfS87.js","assets/auto-Cz6uSJnr.js","assets/StatusBadge-Tzu5Tszv.js","assets/StatusBadge-DOAc9VY6.css","assets/Dashboard-Bl7jn66j.css","assets/UserList-DESvC1RO.js","assets/users-CHeUShab.js","assets/Pagination-BX7rerWh.js","assets/Pagination-DRPk5ywh.css","assets/ConfirmDialog-Cm3a8HQn.js","assets/ConfirmDialog-Ciq4Lct0.css","assets/UserList-KfO-dYkO.css","assets/UserDetail-CC-AwMLs.js","assets/UserDetail-qS4_ki7-.css","assets/Products-CxqKb4pE.js","assets/useAdminSearch-DD4t1sR1.js","assets/useAdminSearch-CT-OHu28.css","assets/products-d_ocQBUS.js","assets/Products-DOEb2zmr.css","assets/ProductView-B32do9Ws.js","assets/companies-Cx9IcxUK.js","assets/ProductView-DQSO-bh3.css","assets/ProductEdit-DMGe_nnb.js","assets/ProductEdit-gKOFBOr7.css","assets/ProductCreate-BZ5OEql0.js","assets/CategoryList-C9tCzFrW.js","assets/CategoryList-BydSEEQi.css","assets/CategoryDetail-GiN5JLA4.js","assets/CategoryDetail-B4IKCyPy.css","assets/CategoryForm-BFA-MzER.js","assets/CategoryForm-B3RdiLfv.css","assets/OrderList-D_PaRh9V.js","assets/orders-D4s5ZKaF.js","assets/OrderList-Bn_03X-k.css","assets/OrderDetail-MMuuH13h.js","assets/OrderDetail-CVFXdiNN.css","assets/SellerRequestList-zSoqrLS1.js","assets/seller-requests-BdlPleid.js","assets/SellerRequestList-VDtJTVtz.css","assets/SellerRequestDetail-BTolo_nV.js","assets/SellerRequestDetail-CglLDR5r.css","assets/CompanyList-K-yE_voF.js","assets/CompanyList-CGKCizUa.css","assets/CompanyDetail-DAB2A9Cu.js","assets/CompanyDetail-B67gj86p.css","assets/CompanyEdit-PevIfUrT.js","assets/CompanyEdit-8cgn5VqO.css","assets/ReviewList-W5LUiEy0.js","assets/reviews-DkYxr4GQ.js","assets/Pagination-Cgfsjo8C.js","assets/Pagination-D8AmIHT6.css","assets/ReviewList-DLdVcqqv.css","assets/ReviewDetail-sshVlUxY.js","assets/ReviewDetail-B6JCSIst.css","assets/RatingList-BbFziP1Z.js","assets/RatingList-1IgW6TpA.css","assets/ChatList-BgqtKI14.js","assets/chats-BRUJzhVm.js","assets/SearchAndFilters-BGvJDale.js","assets/SearchAndFilters-Cu63lczy.css","assets/ChatList-DtcMA9p3.css","assets/ChatDetail-DSqpAgF1.js","assets/ChatDetail-BKvrnliU.css","assets/AddressList-V2kbPOK0.js","assets/AddressList-DUoQNMeQ.css","assets/ApiTest-CDWWdvvg.js","assets/ApiTest-C0k86QHm.css","assets/Settings-D8Q_UYEa.js","assets/Settings-BaQGZkOH.css","assets/Security-WXIWPOVF.js","assets/Security-DnZKgZUg.css","assets/Reports-Drunj1GZ.js","assets/Reports-Bxawa4RO.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function vi(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Ne={},os=[],Zt=()=>{},Id=()=>!1,Ur=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),yi=e=>e.startsWith("onUpdate:"),tt=Object.assign,_i=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},$d=Object.prototype.hasOwnProperty,Pe=(e,t)=>$d.call(e,t),le=Array.isArray,is=e=>Ws(e)==="[object Map]",gs=e=>Ws(e)==="[object Set]",na=e=>Ws(e)==="[object Date]",he=e=>typeof e=="function",ze=e=>typeof e=="string",jt=e=>typeof e=="symbol",De=e=>e!==null&&typeof e=="object",Hl=e=>(De(e)||he(e))&&he(e.then)&&he(e.catch),zl=Object.prototype.toString,Ws=e=>zl.call(e),Fd=e=>Ws(e).slice(8,-1),Kl=e=>Ws(e)==="[object Object]",bi=e=>ze(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ts=vi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),qr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Dd=/-(\w)/g,Mt=qr(e=>e.replace(Dd,(t,n)=>n?n.toUpperCase():"")),Md=/\B([A-Z])/g,Wn=qr(e=>e.replace(Md,"-$1").toLowerCase()),Br=qr(e=>e.charAt(0).toUpperCase()+e.slice(1)),yo=qr(e=>e?`on${Br(e)}`:""),Cn=(e,t)=>!Object.is(e,t),hr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Wl=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Er=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Nd=e=>{const t=ze(e)?Number(e):NaN;return isNaN(t)?e:t};let sa;const Gr=()=>sa||(sa=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function wi(e){if(le(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ze(s)?Ud(s):wi(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ze(e)||De(e))return e}const Vd=/;(?![^(]*\))/g,Ld=/:([^]+)/,jd=/\/\*[^]*?\*\//g;function Ud(e){const t={};return e.replace(jd,"").split(Vd).forEach(n=>{if(n){const s=n.split(Ld);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Te(e){let t="";if(ze(e))t=e;else if(le(e))for(let n=0;n<e.length;n++){const s=Te(e[n]);s&&(t+=s+" ")}else if(De(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const qd="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Bd=vi(qd);function Jl(e){return!!e||e===""}function Gd(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Js(e[s],t[s]);return n}function Js(e,t){if(e===t)return!0;let n=na(e),s=na(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=jt(e),s=jt(t),n||s)return e===t;if(n=le(e),s=le(t),n||s)return n&&s?Gd(e,t):!1;if(n=De(e),s=De(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!Js(e[i],t[i]))return!1}}return String(e)===String(t)}function Ei(e,t){return e.findIndex(n=>Js(n,t))}const Yl=e=>!!(e&&e.__v_isRef===!0),K=e=>ze(e)?e:e==null?"":le(e)||De(e)&&(e.toString===zl||!he(e.toString))?Yl(e)?K(e.value):JSON.stringify(e,Zl,2):String(e),Zl=(e,t)=>Yl(t)?Zl(e,t.value):is(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[_o(s,o)+" =>"]=r,n),{})}:gs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>_o(n))}:jt(t)?_o(t):De(t)&&!le(t)&&!Kl(t)?String(t):t,_o=(e,t="")=>{var n;return jt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Rt;class Ql{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Rt,!t&&Rt&&(this.index=(Rt.scopes||(Rt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Rt;try{return Rt=this,t()}finally{Rt=n}}}on(){Rt=this}off(){Rt=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Hd(e){return new Ql(e)}function zd(){return Rt}let Le;const bo=new WeakSet;class Xl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Rt&&Rt.active&&Rt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,bo.has(this)&&(bo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||tc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ra(this),nc(this);const t=Le,n=Lt;Le=this,Lt=!0;try{return this.fn()}finally{sc(this),Le=t,Lt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ai(t);this.deps=this.depsTail=void 0,ra(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?bo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){qo(this)&&this.run()}get dirty(){return qo(this)}}let ec=0,Rs,xs;function tc(e,t=!1){if(e.flags|=8,t){e.next=xs,xs=e;return}e.next=Rs,Rs=e}function Si(){ec++}function Ci(){if(--ec>0)return;if(xs){let t=xs;for(xs=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Rs;){let t=Rs;for(Rs=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function nc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function sc(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Ai(s),Kd(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function qo(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(rc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function rc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Fs))return;e.globalVersion=Fs;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!qo(e)){e.flags&=-3;return}const n=Le,s=Lt;Le=e,Lt=!0;try{nc(e);const r=e.fn(e._value);(t.version===0||Cn(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{Le=n,Lt=s,sc(e),e.flags&=-3}}function Ai(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ai(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Kd(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Lt=!0;const oc=[];function xn(){oc.push(Lt),Lt=!1}function Pn(){const e=oc.pop();Lt=e===void 0?!0:e}function ra(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Le;Le=void 0;try{t()}finally{Le=n}}}let Fs=0;class Wd{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Oi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Le||!Lt||Le===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Le)n=this.activeLink=new Wd(Le,this),Le.deps?(n.prevDep=Le.depsTail,Le.depsTail.nextDep=n,Le.depsTail=n):Le.deps=Le.depsTail=n,ic(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=Le.depsTail,n.nextDep=void 0,Le.depsTail.nextDep=n,Le.depsTail=n,Le.deps===n&&(Le.deps=s)}return n}trigger(t){this.version++,Fs++,this.notify(t)}notify(t){Si();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ci()}}}function ic(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)ic(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Sr=new WeakMap,Un=Symbol(""),Bo=Symbol(""),Ds=Symbol("");function ut(e,t,n){if(Lt&&Le){let s=Sr.get(e);s||Sr.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Oi),r.map=s,r.key=n),r.track()}}function cn(e,t,n,s,r,o){const i=Sr.get(e);if(!i){Fs++;return}const a=l=>{l&&l.trigger()};if(Si(),t==="clear")i.forEach(a);else{const l=le(e),d=l&&bi(n);if(l&&n==="length"){const c=Number(s);i.forEach((f,p)=>{(p==="length"||p===Ds||!jt(p)&&p>=c)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),d&&a(i.get(Ds)),t){case"add":l?d&&a(i.get("length")):(a(i.get(Un)),is(e)&&a(i.get(Bo)));break;case"delete":l||(a(i.get(Un)),is(e)&&a(i.get(Bo)));break;case"set":is(e)&&a(i.get(Un));break}}Ci()}function Jd(e,t){const n=Sr.get(e);return n&&n.get(t)}function Zn(e){const t=Ae(e);return t===e?t:(ut(t,"iterate",Ds),Dt(e)?t:t.map(dt))}function Hr(e){return ut(e=Ae(e),"iterate",Ds),e}const Yd={__proto__:null,[Symbol.iterator](){return wo(this,Symbol.iterator,dt)},concat(...e){return Zn(this).concat(...e.map(t=>le(t)?Zn(t):t))},entries(){return wo(this,"entries",e=>(e[1]=dt(e[1]),e))},every(e,t){return en(this,"every",e,t,void 0,arguments)},filter(e,t){return en(this,"filter",e,t,n=>n.map(dt),arguments)},find(e,t){return en(this,"find",e,t,dt,arguments)},findIndex(e,t){return en(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return en(this,"findLast",e,t,dt,arguments)},findLastIndex(e,t){return en(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return en(this,"forEach",e,t,void 0,arguments)},includes(...e){return Eo(this,"includes",e)},indexOf(...e){return Eo(this,"indexOf",e)},join(e){return Zn(this).join(e)},lastIndexOf(...e){return Eo(this,"lastIndexOf",e)},map(e,t){return en(this,"map",e,t,void 0,arguments)},pop(){return bs(this,"pop")},push(...e){return bs(this,"push",e)},reduce(e,...t){return oa(this,"reduce",e,t)},reduceRight(e,...t){return oa(this,"reduceRight",e,t)},shift(){return bs(this,"shift")},some(e,t){return en(this,"some",e,t,void 0,arguments)},splice(...e){return bs(this,"splice",e)},toReversed(){return Zn(this).toReversed()},toSorted(e){return Zn(this).toSorted(e)},toSpliced(...e){return Zn(this).toSpliced(...e)},unshift(...e){return bs(this,"unshift",e)},values(){return wo(this,"values",dt)}};function wo(e,t,n){const s=Hr(e),r=s[t]();return s!==e&&!Dt(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Zd=Array.prototype;function en(e,t,n,s,r,o){const i=Hr(e),a=i!==e&&!Dt(e),l=i[t];if(l!==Zd[t]){const f=l.apply(e,o);return a?dt(f):f}let d=n;i!==e&&(a?d=function(f,p){return n.call(this,dt(f),p,e)}:n.length>2&&(d=function(f,p){return n.call(this,f,p,e)}));const c=l.call(i,d,s);return a&&r?r(c):c}function oa(e,t,n,s){const r=Hr(e);let o=n;return r!==e&&(Dt(e)?n.length>3&&(o=function(i,a,l){return n.call(this,i,a,l,e)}):o=function(i,a,l){return n.call(this,i,dt(a),l,e)}),r[t](o,...s)}function Eo(e,t,n){const s=Ae(e);ut(s,"iterate",Ds);const r=s[t](...n);return(r===-1||r===!1)&&Pi(n[0])?(n[0]=Ae(n[0]),s[t](...n)):r}function bs(e,t,n=[]){xn(),Si();const s=Ae(e)[t].apply(e,n);return Ci(),Pn(),s}const Qd=vi("__proto__,__v_isRef,__isVue"),ac=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(jt));function Xd(e){jt(e)||(e=String(e));const t=Ae(this);return ut(t,"has",e),t.hasOwnProperty(e)}class lc{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?uf:fc:o?dc:uc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=le(t);if(!r){let l;if(i&&(l=Yd[n]))return l;if(n==="hasOwnProperty")return Xd}const a=Reflect.get(t,n,He(t)?t:s);return(jt(n)?ac.has(n):Qd(n))||(r||ut(t,"get",n),o)?a:He(a)?i&&bi(n)?a:a.value:De(a)?r?Ri(a):Qt(a):a}}class cc extends lc{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const l=Gn(o);if(!Dt(s)&&!Gn(s)&&(o=Ae(o),s=Ae(s)),!le(t)&&He(o)&&!He(s))return l?!1:(o.value=s,!0)}const i=le(t)&&bi(n)?Number(n)<t.length:Pe(t,n),a=Reflect.set(t,n,s,He(t)?t:r);return t===Ae(r)&&(i?Cn(s,o)&&cn(t,"set",n,s):cn(t,"add",n,s)),a}deleteProperty(t,n){const s=Pe(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&cn(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!jt(n)||!ac.has(n))&&ut(t,"has",n),s}ownKeys(t){return ut(t,"iterate",le(t)?"length":Un),Reflect.ownKeys(t)}}class ef extends lc{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const tf=new cc,nf=new ef,sf=new cc(!0);const Go=e=>e,rr=e=>Reflect.getPrototypeOf(e);function rf(e,t,n){return function(...s){const r=this.__v_raw,o=Ae(r),i=is(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,d=r[e](...s),c=n?Go:t?Ho:dt;return!t&&ut(o,"iterate",l?Bo:Un),{next(){const{value:f,done:p}=d.next();return p?{value:f,done:p}:{value:a?[c(f[0]),c(f[1])]:c(f),done:p}},[Symbol.iterator](){return this}}}}function or(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function of(e,t){const n={get(r){const o=this.__v_raw,i=Ae(o),a=Ae(r);e||(Cn(r,a)&&ut(i,"get",r),ut(i,"get",a));const{has:l}=rr(i),d=t?Go:e?Ho:dt;if(l.call(i,r))return d(o.get(r));if(l.call(i,a))return d(o.get(a));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ut(Ae(r),"iterate",Un),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Ae(o),a=Ae(r);return e||(Cn(r,a)&&ut(i,"has",r),ut(i,"has",a)),r===a?o.has(r):o.has(r)||o.has(a)},forEach(r,o){const i=this,a=i.__v_raw,l=Ae(a),d=t?Go:e?Ho:dt;return!e&&ut(l,"iterate",Un),a.forEach((c,f)=>r.call(o,d(c),d(f),i))}};return tt(n,e?{add:or("add"),set:or("set"),delete:or("delete"),clear:or("clear")}:{add(r){!t&&!Dt(r)&&!Gn(r)&&(r=Ae(r));const o=Ae(this);return rr(o).has.call(o,r)||(o.add(r),cn(o,"add",r,r)),this},set(r,o){!t&&!Dt(o)&&!Gn(o)&&(o=Ae(o));const i=Ae(this),{has:a,get:l}=rr(i);let d=a.call(i,r);d||(r=Ae(r),d=a.call(i,r));const c=l.call(i,r);return i.set(r,o),d?Cn(o,c)&&cn(i,"set",r,o):cn(i,"add",r,o),this},delete(r){const o=Ae(this),{has:i,get:a}=rr(o);let l=i.call(o,r);l||(r=Ae(r),l=i.call(o,r)),a&&a.call(o,r);const d=o.delete(r);return l&&cn(o,"delete",r,void 0),d},clear(){const r=Ae(this),o=r.size!==0,i=r.clear();return o&&cn(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=rf(r,e,t)}),n}function Ti(e,t){const n=of(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Pe(n,r)&&r in s?n:s,r,o)}const af={get:Ti(!1,!1)},lf={get:Ti(!1,!0)},cf={get:Ti(!0,!1)};const uc=new WeakMap,dc=new WeakMap,fc=new WeakMap,uf=new WeakMap;function df(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ff(e){return e.__v_skip||!Object.isExtensible(e)?0:df(Fd(e))}function Qt(e){return Gn(e)?e:xi(e,!1,tf,af,uc)}function hc(e){return xi(e,!1,sf,lf,dc)}function Ri(e){return xi(e,!0,nf,cf,fc)}function xi(e,t,n,s,r){if(!De(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=r.get(e);if(o)return o;const i=ff(e);if(i===0)return e;const a=new Proxy(e,i===2?s:n);return r.set(e,a),a}function as(e){return Gn(e)?as(e.__v_raw):!!(e&&e.__v_isReactive)}function Gn(e){return!!(e&&e.__v_isReadonly)}function Dt(e){return!!(e&&e.__v_isShallow)}function Pi(e){return e?!!e.__v_raw:!1}function Ae(e){const t=e&&e.__v_raw;return t?Ae(t):e}function hf(e){return!Pe(e,"__v_skip")&&Object.isExtensible(e)&&Wl(e,"__v_skip",!0),e}const dt=e=>De(e)?Qt(e):e,Ho=e=>De(e)?Ri(e):e;function He(e){return e?e.__v_isRef===!0:!1}function ve(e){return gc(e,!1)}function pc(e){return gc(e,!0)}function gc(e,t){return He(e)?e:new pf(e,t)}class pf{constructor(t,n){this.dep=new Oi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Ae(t),this._value=n?t:dt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Dt(t)||Gn(t);t=s?t:Ae(t),Cn(t,n)&&(this._rawValue=t,this._value=s?t:dt(t),this.dep.trigger())}}function Ce(e){return He(e)?e.value:e}function ie(e){return he(e)?e():Ce(e)}const gf={get:(e,t,n)=>t==="__v_raw"?e:Ce(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return He(r)&&!He(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function mc(e){return as(e)?e:new Proxy(e,gf)}class mf{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Jd(Ae(this._object),this._key)}}class vf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Nn(e,t,n){return He(e)?e:he(e)?new vf(e):De(e)&&arguments.length>1?yf(e,t,n):ve(e)}function yf(e,t,n){const s=e[t];return He(s)?s:new mf(e,t,n)}class _f{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Oi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Fs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Le!==this)return tc(this,!0),!0}get value(){const t=this.dep.track();return rc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function bf(e,t,n=!1){let s,r;return he(e)?s=e:(s=e.get,r=e.set),new _f(s,r,n)}const ir={},Cr=new WeakMap;let Mn;function wf(e,t=!1,n=Mn){if(n){let s=Cr.get(n);s||Cr.set(n,s=[]),s.push(e)}}function Ef(e,t,n=Ne){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:a,call:l}=n,d=A=>r?A:Dt(A)||r===!1||r===0?un(A,1):un(A);let c,f,p,g,y=!1,S=!1;if(He(e)?(f=()=>e.value,y=Dt(e)):as(e)?(f=()=>d(e),y=!0):le(e)?(S=!0,y=e.some(A=>as(A)||Dt(A)),f=()=>e.map(A=>{if(He(A))return A.value;if(as(A))return d(A);if(he(A))return l?l(A,2):A()})):he(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){xn();try{p()}finally{Pn()}}const A=Mn;Mn=c;try{return l?l(e,3,[g]):e(g)}finally{Mn=A}}:f=Zt,t&&r){const A=f,h=r===!0?1/0:r;f=()=>un(A(),h)}const E=zd(),C=()=>{c.stop(),E&&E.active&&_i(E.effects,c)};if(o&&t){const A=t;t=(...h)=>{A(...h),C()}}let P=S?new Array(e.length).fill(ir):ir;const D=A=>{if(!(!(c.flags&1)||!c.dirty&&!A))if(t){const h=c.run();if(r||y||(S?h.some((G,O)=>Cn(G,P[O])):Cn(h,P))){p&&p();const G=Mn;Mn=c;try{const O=[h,P===ir?void 0:S&&P[0]===ir?[]:P,g];l?l(t,3,O):t(...O),P=h}finally{Mn=G}}}else c.run()};return a&&a(D),c=new Xl(f),c.scheduler=i?()=>i(D,!1):D,g=A=>wf(A,!1,c),p=c.onStop=()=>{const A=Cr.get(c);if(A){if(l)l(A,4);else for(const h of A)h();Cr.delete(c)}},t?s?D(!0):P=c.run():i?i(D.bind(null,!0),!0):c.run(),C.pause=c.pause.bind(c),C.resume=c.resume.bind(c),C.stop=C,C}function un(e,t=1/0,n){if(t<=0||!De(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,He(e))un(e.value,t,n);else if(le(e))for(let s=0;s<e.length;s++)un(e[s],t,n);else if(gs(e)||is(e))e.forEach(s=>{un(s,t,n)});else if(Kl(e)){for(const s in e)un(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&un(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ys(e,t,n,s){try{return s?e(...s):e()}catch(r){zr(r,t,n)}}function Ut(e,t,n,s){if(he(e)){const r=Ys(e,t,n,s);return r&&Hl(r)&&r.catch(o=>{zr(o,t,n)}),r}if(le(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Ut(e[o],t,n,s));return r}}function zr(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Ne;if(t){let a=t.parent;const l=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,d)===!1)return}a=a.parent}if(o){xn(),Ys(o,null,10,[e,l,d]),Pn();return}}Sf(e,n,r,s,i)}function Sf(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const mt=[];let Wt=-1;const ls=[];let yn=null,ns=0;const vc=Promise.resolve();let Ar=null;function xt(e){const t=Ar||vc;return e?t.then(this?e.bind(this):e):t}function Cf(e){let t=Wt+1,n=mt.length;for(;t<n;){const s=t+n>>>1,r=mt[s],o=Ms(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function ki(e){if(!(e.flags&1)){const t=Ms(e),n=mt[mt.length-1];!n||!(e.flags&2)&&t>=Ms(n)?mt.push(e):mt.splice(Cf(t),0,e),e.flags|=1,yc()}}function yc(){Ar||(Ar=vc.then(bc))}function Af(e){le(e)?ls.push(...e):yn&&e.id===-1?yn.splice(ns+1,0,e):e.flags&1||(ls.push(e),e.flags|=1),yc()}function ia(e,t,n=Wt+1){for(;n<mt.length;n++){const s=mt[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;mt.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function _c(e){if(ls.length){const t=[...new Set(ls)].sort((n,s)=>Ms(n)-Ms(s));if(ls.length=0,yn){yn.push(...t);return}for(yn=t,ns=0;ns<yn.length;ns++){const n=yn[ns];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}yn=null,ns=0}}const Ms=e=>e.id==null?e.flags&2?-1:1/0:e.id;function bc(e){try{for(Wt=0;Wt<mt.length;Wt++){const t=mt[Wt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ys(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Wt<mt.length;Wt++){const t=mt[Wt];t&&(t.flags&=-2)}Wt=-1,mt.length=0,_c(),Ar=null,(mt.length||ls.length)&&bc()}}let et=null,wc=null;function Or(e){const t=et;return et=e,wc=e&&e.type.__scopeId||null,t}function Ge(e,t=et,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&va(-1);const o=Or(t);let i;try{i=e(...r)}finally{Or(o),s._d&&va(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function qe(e,t){if(et===null)return e;const n=Qr(et),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,a,l=Ne]=t[r];o&&(he(o)&&(o={mounted:o,updated:o}),o.deep&&un(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function In(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const a=r[i];o&&(a.oldValue=o[i].value);let l=a.dir[s];l&&(xn(),Ut(l,n,8,[e.el,a,e,t]),Pn())}}const Of=Symbol("_vte"),Ec=e=>e.__isTeleport,_n=Symbol("_leaveCb"),ar=Symbol("_enterCb");function Tf(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return kn(()=>{e.isMounted=!0}),Ii(()=>{e.isUnmounting=!0}),e}const Ft=[Function,Array],Sc={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ft,onEnter:Ft,onAfterEnter:Ft,onEnterCancelled:Ft,onBeforeLeave:Ft,onLeave:Ft,onAfterLeave:Ft,onLeaveCancelled:Ft,onBeforeAppear:Ft,onAppear:Ft,onAfterAppear:Ft,onAppearCancelled:Ft},Cc=e=>{const t=e.subTree;return t.component?Cc(t.component):t},Rf={name:"BaseTransition",props:Sc,setup(e,{slots:t}){const n=Qs(),s=Tf();return()=>{const r=t.default&&Tc(t.default(),!0);if(!r||!r.length)return;const o=Ac(r),i=Ae(e),{mode:a}=i;if(s.isLeaving)return So(o);const l=aa(o);if(!l)return So(o);let d=zo(l,i,s,n,f=>d=f);l.type!==vt&&Ns(l,d);let c=n.subTree&&aa(n.subTree);if(c&&c.type!==vt&&!Vn(l,c)&&Cc(n).type!==vt){let f=zo(c,i,s,n);if(Ns(c,f),a==="out-in"&&l.type!==vt)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},So(o);a==="in-out"&&l.type!==vt?f.delayLeave=(p,g,y)=>{const S=Oc(s,c);S[String(c.key)]=c,p[_n]=()=>{g(),p[_n]=void 0,delete d.delayedLeave,c=void 0},d.delayedLeave=()=>{y(),delete d.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function Ac(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==vt){t=n;break}}return t}const xf=Rf;function Oc(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function zo(e,t,n,s,r){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:d,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:p,onLeave:g,onAfterLeave:y,onLeaveCancelled:S,onBeforeAppear:E,onAppear:C,onAfterAppear:P,onAppearCancelled:D}=t,A=String(e.key),h=Oc(n,e),G=(I,ne)=>{I&&Ut(I,s,9,ne)},O=(I,ne)=>{const q=ne[1];G(I,ne),le(I)?I.every(B=>B.length<=1)&&q():I.length<=1&&q()},W={mode:i,persisted:a,beforeEnter(I){let ne=l;if(!n.isMounted)if(o)ne=E||l;else return;I[_n]&&I[_n](!0);const q=h[A];q&&Vn(e,q)&&q.el[_n]&&q.el[_n](),G(ne,[I])},enter(I){let ne=d,q=c,B=f;if(!n.isMounted)if(o)ne=C||d,q=P||c,B=D||f;else return;let pe=!1;const Se=I[ar]=Me=>{pe||(pe=!0,Me?G(B,[I]):G(q,[I]),W.delayedLeave&&W.delayedLeave(),I[ar]=void 0)};ne?O(ne,[I,Se]):Se()},leave(I,ne){const q=String(e.key);if(I[ar]&&I[ar](!0),n.isUnmounting)return ne();G(p,[I]);let B=!1;const pe=I[_n]=Se=>{B||(B=!0,ne(),Se?G(S,[I]):G(y,[I]),I[_n]=void 0,h[q]===e&&delete h[q])};h[q]=e,g?O(g,[I,pe]):pe()},clone(I){const ne=zo(I,t,n,s,r);return r&&r(ne),ne}};return W}function So(e){if(Kr(e))return e=Rn(e),e.children=null,e}function aa(e){if(!Kr(e))return Ec(e.type)&&e.children?Ac(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&he(n.default))return n.default()}}function Ns(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ns(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Tc(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===$e?(i.patchFlag&128&&r++,s=s.concat(Tc(i.children,t,a))):(t||i.type!==vt)&&s.push(a!=null?Rn(i,{key:a}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Zs(e,t){return he(e)?tt({name:e.name},t,{setup:e}):e}function Rc(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Tr(e,t,n,s,r=!1){if(le(e)){e.forEach((y,S)=>Tr(y,t&&(le(t)?t[S]:t),n,s,r));return}if(cs(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Tr(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Qr(s.component):s.el,i=r?null:o,{i:a,r:l}=e,d=t&&t.r,c=a.refs===Ne?a.refs={}:a.refs,f=a.setupState,p=Ae(f),g=f===Ne?()=>!1:y=>Pe(p,y);if(d!=null&&d!==l&&(ze(d)?(c[d]=null,g(d)&&(f[d]=null)):He(d)&&(d.value=null)),he(l))Ys(l,a,12,[i,c]);else{const y=ze(l),S=He(l);if(y||S){const E=()=>{if(e.f){const C=y?g(l)?f[l]:c[l]:l.value;r?le(C)&&_i(C,o):le(C)?C.includes(o)||C.push(o):y?(c[l]=[o],g(l)&&(f[l]=c[l])):(l.value=[o],e.k&&(c[e.k]=l.value))}else y?(c[l]=i,g(l)&&(f[l]=i)):S&&(l.value=i,e.k&&(c[e.k]=i))};i?(E.id=-1,Tt(E,n)):E()}}}Gr().requestIdleCallback;Gr().cancelIdleCallback;const cs=e=>!!e.type.__asyncLoader,Kr=e=>e.type.__isKeepAlive;function Pf(e,t){xc(e,"a",t)}function kf(e,t){xc(e,"da",t)}function xc(e,t,n=rt){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Wr(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Kr(r.parent.vnode)&&If(s,t,n,r),r=r.parent}}function If(e,t,n,s){const r=Wr(t,e,s,!0);Pc(()=>{_i(s[t],r)},n)}function Wr(e,t,n=rt,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{xn();const a=Xs(n),l=Ut(t,n,e,i);return a(),Pn(),l});return s?r.unshift(o):r.push(o),o}}const pn=e=>(t,n=rt)=>{(!js||e==="sp")&&Wr(e,(...s)=>t(...s),n)},$f=pn("bm"),kn=pn("m"),Ff=pn("bu"),Df=pn("u"),Ii=pn("bum"),Pc=pn("um"),Mf=pn("sp"),Nf=pn("rtg"),Vf=pn("rtc");function kc(e,t=rt){Wr("ec",e,t)}const Ic="components";function st(e,t){return Fc(Ic,e,!0,t)||e}const $c=Symbol.for("v-ndc");function Jr(e){return ze(e)?Fc(Ic,e,!1)||e:e||$c}function Fc(e,t,n=!0,s=!1){const r=et||rt;if(r){const o=r.type;{const a=Oh(o,!1);if(a&&(a===t||a===Mt(t)||a===Br(Mt(t))))return o}const i=la(r[e]||o[e],t)||la(r.appContext[e],t);return!i&&s?o:i}}function la(e,t){return e&&(e[t]||e[Mt(t)]||e[Br(Mt(t))])}function ht(e,t,n,s){let r;const o=n,i=le(e);if(i||ze(e)){const a=i&&as(e);let l=!1;a&&(l=!Dt(e),e=Hr(e)),r=new Array(e.length);for(let d=0,c=e.length;d<c;d++)r[d]=t(l?dt(e[d]):e[d],d,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let a=0;a<e;a++)r[a]=t(a+1,a,void 0,o)}else if(De(e))if(e[Symbol.iterator])r=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);r=new Array(a.length);for(let l=0,d=a.length;l<d;l++){const c=a[l];r[l]=t(e[c],c,l,o)}}else r=[];return r}function Lf(e,t,n={},s,r){if(et.ce||et.parent&&cs(et.parent)&&et.parent.ce)return t!=="default"&&(n.name=t),R(),On($e,null,[ae("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),R();const i=o&&Dc(o(n)),a=n.key||i&&i.key,l=On($e,{key:(a&&!jt(a)?a:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),l}function Dc(e){return e.some(t=>Ls(t)?!(t.type===vt||t.type===$e&&!Dc(t.children)):!0)?e:null}const Ko=e=>e?tu(e)?Qr(e):Ko(e.parent):null,Ps=tt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ko(e.parent),$root:e=>Ko(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Nc(e),$forceUpdate:e=>e.f||(e.f=()=>{ki(e.update)}),$nextTick:e=>e.n||(e.n=xt.bind(e.proxy)),$watch:e=>ah.bind(e)}),Co=(e,t)=>e!==Ne&&!e.__isScriptSetup&&Pe(e,t),jf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:a,appContext:l}=e;let d;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Co(s,t))return i[t]=1,s[t];if(r!==Ne&&Pe(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&Pe(d,t))return i[t]=3,o[t];if(n!==Ne&&Pe(n,t))return i[t]=4,n[t];Wo&&(i[t]=0)}}const c=Ps[t];let f,p;if(c)return t==="$attrs"&&ut(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Ne&&Pe(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,Pe(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Co(r,t)?(r[t]=n,!0):s!==Ne&&Pe(s,t)?(s[t]=n,!0):Pe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let a;return!!n[i]||e!==Ne&&Pe(e,i)||Co(t,i)||(a=o[0])&&Pe(a,i)||Pe(s,i)||Pe(Ps,i)||Pe(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Pe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ca(e){return le(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Wo=!0;function Uf(e){const t=Nc(e),n=e.proxy,s=e.ctx;Wo=!1,t.beforeCreate&&ua(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:a,provide:l,inject:d,created:c,beforeMount:f,mounted:p,beforeUpdate:g,updated:y,activated:S,deactivated:E,beforeDestroy:C,beforeUnmount:P,destroyed:D,unmounted:A,render:h,renderTracked:G,renderTriggered:O,errorCaptured:W,serverPrefetch:I,expose:ne,inheritAttrs:q,components:B,directives:pe,filters:Se}=t;if(d&&qf(d,s,null),i)for(const X in i){const be=i[X];he(be)&&(s[X]=be.bind(n))}if(r){const X=r.call(n,n);De(X)&&(e.data=Qt(X))}if(Wo=!0,o)for(const X in o){const be=o[X],Ye=he(be)?be.bind(n,n):he(be.get)?be.get.bind(n,n):Zt,it=!he(be)&&he(be.set)?be.set.bind(n):Zt,at=te({get:Ye,set:it});Object.defineProperty(s,X,{enumerable:!0,configurable:!0,get:()=>at.value,set:Ze=>at.value=Ze})}if(a)for(const X in a)Mc(a[X],s,n,X);if(l){const X=he(l)?l.call(n):l;Reflect.ownKeys(X).forEach(be=>{An(be,X[be])})}c&&ua(c,e,"c");function ge(X,be){le(be)?be.forEach(Ye=>X(Ye.bind(n))):be&&X(be.bind(n))}if(ge($f,f),ge(kn,p),ge(Ff,g),ge(Df,y),ge(Pf,S),ge(kf,E),ge(kc,W),ge(Vf,G),ge(Nf,O),ge(Ii,P),ge(Pc,A),ge(Mf,I),le(ne))if(ne.length){const X=e.exposed||(e.exposed={});ne.forEach(be=>{Object.defineProperty(X,be,{get:()=>n[be],set:Ye=>n[be]=Ye})})}else e.exposed||(e.exposed={});h&&e.render===Zt&&(e.render=h),q!=null&&(e.inheritAttrs=q),B&&(e.components=B),pe&&(e.directives=pe),I&&Rc(e)}function qf(e,t,n=Zt){le(e)&&(e=Jo(e));for(const s in e){const r=e[s];let o;De(r)?"default"in r?o=St(r.from||s,r.default,!0):o=St(r.from||s):o=St(r),He(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function ua(e,t,n){Ut(le(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Mc(e,t,n,s){let r=s.includes(".")?Yc(n,s):()=>n[s];if(ze(e)){const o=t[e];he(o)&&It(r,o)}else if(he(e))It(r,e.bind(n));else if(De(e))if(le(e))e.forEach(o=>Mc(o,t,n,s));else{const o=he(e.handler)?e.handler.bind(n):t[e.handler];he(o)&&It(r,o,e)}}function Nc(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!r.length&&!n&&!s?l=t:(l={},r.length&&r.forEach(d=>Rr(l,d,i,!0)),Rr(l,t,i)),De(t)&&o.set(t,l),l}function Rr(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Rr(e,o,n,!0),r&&r.forEach(i=>Rr(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const a=Bf[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Bf={data:da,props:fa,emits:fa,methods:As,computed:As,beforeCreate:gt,created:gt,beforeMount:gt,mounted:gt,beforeUpdate:gt,updated:gt,beforeDestroy:gt,beforeUnmount:gt,destroyed:gt,unmounted:gt,activated:gt,deactivated:gt,errorCaptured:gt,serverPrefetch:gt,components:As,directives:As,watch:Hf,provide:da,inject:Gf};function da(e,t){return t?e?function(){return tt(he(e)?e.call(this,this):e,he(t)?t.call(this,this):t)}:t:e}function Gf(e,t){return As(Jo(e),Jo(t))}function Jo(e){if(le(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function gt(e,t){return e?[...new Set([].concat(e,t))]:t}function As(e,t){return e?tt(Object.create(null),e,t):t}function fa(e,t){return e?le(e)&&le(t)?[...new Set([...e,...t])]:tt(Object.create(null),ca(e),ca(t??{})):t}function Hf(e,t){if(!e)return t;if(!t)return e;const n=tt(Object.create(null),e);for(const s in t)n[s]=gt(e[s],t[s]);return n}function Vc(){return{app:null,config:{isNativeTag:Id,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let zf=0;function Kf(e,t){return function(s,r=null){he(s)||(s=tt({},s)),r!=null&&!De(r)&&(r=null);const o=Vc(),i=new WeakSet,a=[];let l=!1;const d=o.app={_uid:zf++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Rh,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&he(c.install)?(i.add(c),c.install(d,...f)):he(c)&&(i.add(c),c(d,...f))),d},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),d},component(c,f){return f?(o.components[c]=f,d):o.components[c]},directive(c,f){return f?(o.directives[c]=f,d):o.directives[c]},mount(c,f,p){if(!l){const g=d._ceVNode||ae(s,r);return g.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(g,c,p),l=!0,d._container=c,c.__vue_app__=d,Qr(g.component)}},onUnmount(c){a.push(c)},unmount(){l&&(Ut(a,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(c,f){return o.provides[c]=f,d},runWithContext(c){const f=us;us=d;try{return c()}finally{us=f}}};return d}}let us=null;function An(e,t){if(rt){let n=rt.provides;const s=rt.parent&&rt.parent.provides;s===n&&(n=rt.provides=Object.create(s)),n[e]=t}}function St(e,t,n=!1){const s=rt||et;if(s||us){const r=us?us._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&he(t)?t.call(s&&s.proxy):t}}const Lc={},jc=()=>Object.create(Lc),Uc=e=>Object.getPrototypeOf(e)===Lc;function Wf(e,t,n,s=!1){const r={},o=jc();e.propsDefaults=Object.create(null),qc(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:hc(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Jf(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,a=Ae(r),[l]=e.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let p=c[f];if(Yr(e.emitsOptions,p))continue;const g=t[p];if(l)if(Pe(o,p))g!==o[p]&&(o[p]=g,d=!0);else{const y=Mt(p);r[y]=Yo(l,a,y,g,e,!1)}else g!==o[p]&&(o[p]=g,d=!0)}}}else{qc(e,t,r,o)&&(d=!0);let c;for(const f in a)(!t||!Pe(t,f)&&((c=Wn(f))===f||!Pe(t,c)))&&(l?n&&(n[f]!==void 0||n[c]!==void 0)&&(r[f]=Yo(l,a,f,void 0,e,!0)):delete r[f]);if(o!==a)for(const f in o)(!t||!Pe(t,f))&&(delete o[f],d=!0)}d&&cn(e.attrs,"set","")}function qc(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(Ts(l))continue;const d=t[l];let c;r&&Pe(r,c=Mt(l))?!o||!o.includes(c)?n[c]=d:(a||(a={}))[c]=d:Yr(e.emitsOptions,l)||(!(l in s)||d!==s[l])&&(s[l]=d,i=!0)}if(o){const l=Ae(n),d=a||Ne;for(let c=0;c<o.length;c++){const f=o[c];n[f]=Yo(r,l,f,d[f],e,!Pe(d,f))}}return i}function Yo(e,t,n,s,r,o){const i=e[n];if(i!=null){const a=Pe(i,"default");if(a&&s===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&he(l)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const c=Xs(r);s=d[n]=l.call(null,t),c()}}else s=l;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!a?s=!1:i[1]&&(s===""||s===Wn(n))&&(s=!0))}return s}const Yf=new WeakMap;function Bc(e,t,n=!1){const s=n?Yf:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},a=[];let l=!1;if(!he(e)){const c=f=>{l=!0;const[p,g]=Bc(f,t,!0);tt(i,p),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!l)return De(e)&&s.set(e,os),os;if(le(o))for(let c=0;c<o.length;c++){const f=Mt(o[c]);ha(f)&&(i[f]=Ne)}else if(o)for(const c in o){const f=Mt(c);if(ha(f)){const p=o[c],g=i[f]=le(p)||he(p)?{type:p}:tt({},p),y=g.type;let S=!1,E=!0;if(le(y))for(let C=0;C<y.length;++C){const P=y[C],D=he(P)&&P.name;if(D==="Boolean"){S=!0;break}else D==="String"&&(E=!1)}else S=he(y)&&y.name==="Boolean";g[0]=S,g[1]=E,(S||Pe(g,"default"))&&a.push(f)}}const d=[i,a];return De(e)&&s.set(e,d),d}function ha(e){return e[0]!=="$"&&!Ts(e)}const Gc=e=>e[0]==="_"||e==="$stable",$i=e=>le(e)?e.map(Yt):[Yt(e)],Zf=(e,t,n)=>{if(t._n)return t;const s=Ge((...r)=>$i(t(...r)),n);return s._c=!1,s},Hc=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Gc(r))continue;const o=e[r];if(he(o))t[r]=Zf(r,o,s);else if(o!=null){const i=$i(o);t[r]=()=>i}}},zc=(e,t)=>{const n=$i(t);e.slots.default=()=>n},Kc=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},Qf=(e,t,n)=>{const s=e.slots=jc();if(e.vnode.shapeFlag&32){const r=t._;r?(Kc(s,t,n),n&&Wl(s,"_",r,!0)):Hc(t,s)}else t&&zc(e,t)},Xf=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=Ne;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Kc(r,t,n):(o=!t.$stable,Hc(t,r)),i=t}else t&&(zc(e,t),i={default:1});if(o)for(const a in r)!Gc(a)&&i[a]==null&&delete r[a]},Tt=ph;function eh(e){return th(e)}function th(e,t){const n=Gr();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:a,createComment:l,setText:d,setElementText:c,parentNode:f,nextSibling:p,setScopeId:g=Zt,insertStaticContent:y}=e,S=(m,v,w,$=null,M=null,F=null,z=void 0,H=null,U=!!v.dynamicChildren)=>{if(m===v)return;m&&!Vn(m,v)&&($=T(m),Ze(m,M,F,!0),m=null),v.patchFlag===-2&&(U=!1,v.dynamicChildren=null);const{type:L,ref:ce,shapeFlag:J}=v;switch(L){case Zr:E(m,v,w,$);break;case vt:C(m,v,w,$);break;case pr:m==null&&P(v,w,$,z);break;case $e:B(m,v,w,$,M,F,z,H,U);break;default:J&1?h(m,v,w,$,M,F,z,H,U):J&6?pe(m,v,w,$,M,F,z,H,U):(J&64||J&128)&&L.process(m,v,w,$,M,F,z,H,U,ee)}ce!=null&&M&&Tr(ce,m&&m.ref,F,v||m,!v)},E=(m,v,w,$)=>{if(m==null)s(v.el=a(v.children),w,$);else{const M=v.el=m.el;v.children!==m.children&&d(M,v.children)}},C=(m,v,w,$)=>{m==null?s(v.el=l(v.children||""),w,$):v.el=m.el},P=(m,v,w,$)=>{[m.el,m.anchor]=y(m.children,v,w,$,m.el,m.anchor)},D=({el:m,anchor:v},w,$)=>{let M;for(;m&&m!==v;)M=p(m),s(m,w,$),m=M;s(v,w,$)},A=({el:m,anchor:v})=>{let w;for(;m&&m!==v;)w=p(m),r(m),m=w;r(v)},h=(m,v,w,$,M,F,z,H,U)=>{v.type==="svg"?z="svg":v.type==="math"&&(z="mathml"),m==null?G(v,w,$,M,F,z,H,U):I(m,v,M,F,z,H,U)},G=(m,v,w,$,M,F,z,H)=>{let U,L;const{props:ce,shapeFlag:J,transition:re,dirs:de}=m;if(U=m.el=i(m.type,F,ce&&ce.is,ce),J&8?c(U,m.children):J&16&&W(m.children,U,null,$,M,Ao(m,F),z,H),de&&In(m,null,$,"created"),O(U,m,m.scopeId,z,$),ce){for(const Fe in ce)Fe!=="value"&&!Ts(Fe)&&o(U,Fe,null,ce[Fe],F,$);"value"in ce&&o(U,"value",null,ce.value,F),(L=ce.onVnodeBeforeMount)&&zt(L,$,m)}de&&In(m,null,$,"beforeMount");const Ee=nh(M,re);Ee&&re.beforeEnter(U),s(U,v,w),((L=ce&&ce.onVnodeMounted)||Ee||de)&&Tt(()=>{L&&zt(L,$,m),Ee&&re.enter(U),de&&In(m,null,$,"mounted")},M)},O=(m,v,w,$,M)=>{if(w&&g(m,w),$)for(let F=0;F<$.length;F++)g(m,$[F]);if(M){let F=M.subTree;if(v===F||Qc(F.type)&&(F.ssContent===v||F.ssFallback===v)){const z=M.vnode;O(m,z,z.scopeId,z.slotScopeIds,M.parent)}}},W=(m,v,w,$,M,F,z,H,U=0)=>{for(let L=U;L<m.length;L++){const ce=m[L]=H?bn(m[L]):Yt(m[L]);S(null,ce,v,w,$,M,F,z,H)}},I=(m,v,w,$,M,F,z)=>{const H=v.el=m.el;let{patchFlag:U,dynamicChildren:L,dirs:ce}=v;U|=m.patchFlag&16;const J=m.props||Ne,re=v.props||Ne;let de;if(w&&$n(w,!1),(de=re.onVnodeBeforeUpdate)&&zt(de,w,v,m),ce&&In(v,m,w,"beforeUpdate"),w&&$n(w,!0),(J.innerHTML&&re.innerHTML==null||J.textContent&&re.textContent==null)&&c(H,""),L?ne(m.dynamicChildren,L,H,w,$,Ao(v,M),F):z||be(m,v,H,null,w,$,Ao(v,M),F,!1),U>0){if(U&16)q(H,J,re,w,M);else if(U&2&&J.class!==re.class&&o(H,"class",null,re.class,M),U&4&&o(H,"style",J.style,re.style,M),U&8){const Ee=v.dynamicProps;for(let Fe=0;Fe<Ee.length;Fe++){const Oe=Ee[Fe],pt=J[Oe],nt=re[Oe];(nt!==pt||Oe==="value")&&o(H,Oe,pt,nt,M,w)}}U&1&&m.children!==v.children&&c(H,v.children)}else!z&&L==null&&q(H,J,re,w,M);((de=re.onVnodeUpdated)||ce)&&Tt(()=>{de&&zt(de,w,v,m),ce&&In(v,m,w,"updated")},$)},ne=(m,v,w,$,M,F,z)=>{for(let H=0;H<v.length;H++){const U=m[H],L=v[H],ce=U.el&&(U.type===$e||!Vn(U,L)||U.shapeFlag&70)?f(U.el):w;S(U,L,ce,null,$,M,F,z,!0)}},q=(m,v,w,$,M)=>{if(v!==w){if(v!==Ne)for(const F in v)!Ts(F)&&!(F in w)&&o(m,F,v[F],null,M,$);for(const F in w){if(Ts(F))continue;const z=w[F],H=v[F];z!==H&&F!=="value"&&o(m,F,H,z,M,$)}"value"in w&&o(m,"value",v.value,w.value,M)}},B=(m,v,w,$,M,F,z,H,U)=>{const L=v.el=m?m.el:a(""),ce=v.anchor=m?m.anchor:a("");let{patchFlag:J,dynamicChildren:re,slotScopeIds:de}=v;de&&(H=H?H.concat(de):de),m==null?(s(L,w,$),s(ce,w,$),W(v.children||[],w,ce,M,F,z,H,U)):J>0&&J&64&&re&&m.dynamicChildren?(ne(m.dynamicChildren,re,w,M,F,z,H),(v.key!=null||M&&v===M.subTree)&&Wc(m,v,!0)):be(m,v,w,ce,M,F,z,H,U)},pe=(m,v,w,$,M,F,z,H,U)=>{v.slotScopeIds=H,m==null?v.shapeFlag&512?M.ctx.activate(v,w,$,z,U):Se(v,w,$,M,F,z,U):Me(m,v,U)},Se=(m,v,w,$,M,F,z)=>{const H=m.component=wh(m,$,M);if(Kr(m)&&(H.ctx.renderer=ee),Eh(H,!1,z),H.asyncDep){if(M&&M.registerDep(H,ge,z),!m.el){const U=H.subTree=ae(vt);C(null,U,v,w)}}else ge(H,m,v,w,M,F,z)},Me=(m,v,w)=>{const $=v.component=m.component;if(fh(m,v,w))if($.asyncDep&&!$.asyncResolved){X($,v,w);return}else $.next=v,$.update();else v.el=m.el,$.vnode=v},ge=(m,v,w,$,M,F,z)=>{const H=()=>{if(m.isMounted){let{next:J,bu:re,u:de,parent:Ee,vnode:Fe}=m;{const _=Jc(m);if(_){J&&(J.el=Fe.el,X(m,J,z)),_.asyncDep.then(()=>{m.isUnmounted||H()});return}}let Oe=J,pt;$n(m,!1),J?(J.el=Fe.el,X(m,J,z)):J=Fe,re&&hr(re),(pt=J.props&&J.props.onVnodeBeforeUpdate)&&zt(pt,Ee,J,Fe),$n(m,!0);const nt=ga(m),b=m.subTree;m.subTree=nt,S(b,nt,f(b.el),T(b),m,M,F),J.el=nt.el,Oe===null&&hh(m,nt.el),de&&Tt(de,M),(pt=J.props&&J.props.onVnodeUpdated)&&Tt(()=>zt(pt,Ee,J,Fe),M)}else{let J;const{el:re,props:de}=v,{bm:Ee,m:Fe,parent:Oe,root:pt,type:nt}=m,b=cs(v);$n(m,!1),Ee&&hr(Ee),!b&&(J=de&&de.onVnodeBeforeMount)&&zt(J,Oe,v),$n(m,!0);{pt.ce&&pt.ce._injectChildStyle(nt);const _=m.subTree=ga(m);S(null,_,w,$,m,M,F),v.el=_.el}if(Fe&&Tt(Fe,M),!b&&(J=de&&de.onVnodeMounted)){const _=v;Tt(()=>zt(J,Oe,_),M)}(v.shapeFlag&256||Oe&&cs(Oe.vnode)&&Oe.vnode.shapeFlag&256)&&m.a&&Tt(m.a,M),m.isMounted=!0,v=w=$=null}};m.scope.on();const U=m.effect=new Xl(H);m.scope.off();const L=m.update=U.run.bind(U),ce=m.job=U.runIfDirty.bind(U);ce.i=m,ce.id=m.uid,U.scheduler=()=>ki(ce),$n(m,!0),L()},X=(m,v,w)=>{v.component=m;const $=m.vnode.props;m.vnode=v,m.next=null,Jf(m,v.props,$,w),Xf(m,v.children,w),xn(),ia(m),Pn()},be=(m,v,w,$,M,F,z,H,U=!1)=>{const L=m&&m.children,ce=m?m.shapeFlag:0,J=v.children,{patchFlag:re,shapeFlag:de}=v;if(re>0){if(re&128){it(L,J,w,$,M,F,z,H,U);return}else if(re&256){Ye(L,J,w,$,M,F,z,H,U);return}}de&8?(ce&16&&Q(L,M,F),J!==L&&c(w,J)):ce&16?de&16?it(L,J,w,$,M,F,z,H,U):Q(L,M,F,!0):(ce&8&&c(w,""),de&16&&W(J,w,$,M,F,z,H,U))},Ye=(m,v,w,$,M,F,z,H,U)=>{m=m||os,v=v||os;const L=m.length,ce=v.length,J=Math.min(L,ce);let re;for(re=0;re<J;re++){const de=v[re]=U?bn(v[re]):Yt(v[re]);S(m[re],de,w,null,M,F,z,H,U)}L>ce?Q(m,M,F,!0,!1,J):W(v,w,$,M,F,z,H,U,J)},it=(m,v,w,$,M,F,z,H,U)=>{let L=0;const ce=v.length;let J=m.length-1,re=ce-1;for(;L<=J&&L<=re;){const de=m[L],Ee=v[L]=U?bn(v[L]):Yt(v[L]);if(Vn(de,Ee))S(de,Ee,w,null,M,F,z,H,U);else break;L++}for(;L<=J&&L<=re;){const de=m[J],Ee=v[re]=U?bn(v[re]):Yt(v[re]);if(Vn(de,Ee))S(de,Ee,w,null,M,F,z,H,U);else break;J--,re--}if(L>J){if(L<=re){const de=re+1,Ee=de<ce?v[de].el:$;for(;L<=re;)S(null,v[L]=U?bn(v[L]):Yt(v[L]),w,Ee,M,F,z,H,U),L++}}else if(L>re)for(;L<=J;)Ze(m[L],M,F,!0),L++;else{const de=L,Ee=L,Fe=new Map;for(L=Ee;L<=re;L++){const se=v[L]=U?bn(v[L]):Yt(v[L]);se.key!=null&&Fe.set(se.key,L)}let Oe,pt=0;const nt=re-Ee+1;let b=!1,_=0;const N=new Array(nt);for(L=0;L<nt;L++)N[L]=0;for(L=de;L<=J;L++){const se=m[L];if(pt>=nt){Ze(se,M,F,!0);continue}let ue;if(se.key!=null)ue=Fe.get(se.key);else for(Oe=Ee;Oe<=re;Oe++)if(N[Oe-Ee]===0&&Vn(se,v[Oe])){ue=Oe;break}ue===void 0?Ze(se,M,F,!0):(N[ue-Ee]=L+1,ue>=_?_=ue:b=!0,S(se,v[ue],w,null,M,F,z,H,U),pt++)}const Z=b?sh(N):os;for(Oe=Z.length-1,L=nt-1;L>=0;L--){const se=Ee+L,ue=v[se],oe=se+1<ce?v[se+1].el:$;N[L]===0?S(null,ue,w,oe,M,F,z,H,U):b&&(Oe<0||L!==Z[Oe]?at(ue,w,oe,2):Oe--)}}},at=(m,v,w,$,M=null)=>{const{el:F,type:z,transition:H,children:U,shapeFlag:L}=m;if(L&6){at(m.component.subTree,v,w,$);return}if(L&128){m.suspense.move(v,w,$);return}if(L&64){z.move(m,v,w,ee);return}if(z===$e){s(F,v,w);for(let J=0;J<U.length;J++)at(U[J],v,w,$);s(m.anchor,v,w);return}if(z===pr){D(m,v,w);return}if($!==2&&L&1&&H)if($===0)H.beforeEnter(F),s(F,v,w),Tt(()=>H.enter(F),M);else{const{leave:J,delayLeave:re,afterLeave:de}=H,Ee=()=>s(F,v,w),Fe=()=>{J(F,()=>{Ee(),de&&de()})};re?re(F,Ee,Fe):Fe()}else s(F,v,w)},Ze=(m,v,w,$=!1,M=!1)=>{const{type:F,props:z,ref:H,children:U,dynamicChildren:L,shapeFlag:ce,patchFlag:J,dirs:re,cacheIndex:de}=m;if(J===-2&&(M=!1),H!=null&&Tr(H,null,w,m,!0),de!=null&&(v.renderCache[de]=void 0),ce&256){v.ctx.deactivate(m);return}const Ee=ce&1&&re,Fe=!cs(m);let Oe;if(Fe&&(Oe=z&&z.onVnodeBeforeUnmount)&&zt(Oe,v,m),ce&6)gn(m.component,w,$);else{if(ce&128){m.suspense.unmount(w,$);return}Ee&&In(m,null,v,"beforeUnmount"),ce&64?m.type.remove(m,v,w,ee,$):L&&!L.hasOnce&&(F!==$e||J>0&&J&64)?Q(L,v,w,!1,!0):(F===$e&&J&384||!M&&ce&16)&&Q(U,v,w),$&&Xt(m)}(Fe&&(Oe=z&&z.onVnodeUnmounted)||Ee)&&Tt(()=>{Oe&&zt(Oe,v,m),Ee&&In(m,null,v,"unmounted")},w)},Xt=m=>{const{type:v,el:w,anchor:$,transition:M}=m;if(v===$e){Be(w,$);return}if(v===pr){A(m);return}const F=()=>{r(w),M&&!M.persisted&&M.afterLeave&&M.afterLeave()};if(m.shapeFlag&1&&M&&!M.persisted){const{leave:z,delayLeave:H}=M,U=()=>z(w,F);H?H(m.el,F,U):U()}else F()},Be=(m,v)=>{let w;for(;m!==v;)w=p(m),r(m),m=w;r(v)},gn=(m,v,w)=>{const{bum:$,scope:M,job:F,subTree:z,um:H,m:U,a:L}=m;pa(U),pa(L),$&&hr($),M.stop(),F&&(F.flags|=8,Ze(z,m,v,w)),H&&Tt(H,v),Tt(()=>{m.isUnmounted=!0},v),v&&v.pendingBranch&&!v.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===v.pendingId&&(v.deps--,v.deps===0&&v.resolve())},Q=(m,v,w,$=!1,M=!1,F=0)=>{for(let z=F;z<m.length;z++)Ze(m[z],v,w,$,M)},T=m=>{if(m.shapeFlag&6)return T(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const v=p(m.anchor||m.el),w=v&&v[Of];return w?p(w):v};let j=!1;const V=(m,v,w)=>{m==null?v._vnode&&Ze(v._vnode,null,null,!0):S(v._vnode||null,m,v,null,null,null,w),v._vnode=m,j||(j=!0,ia(),_c(),j=!1)},ee={p:S,um:Ze,m:at,r:Xt,mt:Se,mc:W,pc:be,pbc:ne,n:T,o:e};return{render:V,hydrate:void 0,createApp:Kf(V)}}function Ao({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function $n({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function nh(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Wc(e,t,n=!1){const s=e.children,r=t.children;if(le(s)&&le(r))for(let o=0;o<s.length;o++){const i=s[o];let a=r[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=r[o]=bn(r[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Wc(i,a)),a.type===Zr&&(a.el=i.el)}}function sh(e){const t=e.slice(),n=[0];let s,r,o,i,a;const l=e.length;for(s=0;s<l;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<d?o=a+1:i=a;d<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Jc(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Jc(t)}function pa(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const rh=Symbol.for("v-scx"),oh=()=>St(rh);function ih(e,t){return Fi(e,null,t)}function It(e,t,n){return Fi(e,t,n)}function Fi(e,t,n=Ne){const{immediate:s,deep:r,flush:o,once:i}=n,a=tt({},n),l=t&&s||!t&&o!=="post";let d;if(js){if(o==="sync"){const g=oh();d=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=Zt,g.resume=Zt,g.pause=Zt,g}}const c=rt;a.call=(g,y,S)=>Ut(g,c,y,S);let f=!1;o==="post"?a.scheduler=g=>{Tt(g,c&&c.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(g,y)=>{y?g():ki(g)}),a.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const p=Ef(e,t,a);return js&&(d?d.push(p):l&&p()),p}function ah(e,t,n){const s=this.proxy,r=ze(e)?e.includes(".")?Yc(s,e):()=>s[e]:e.bind(s,s);let o;he(t)?o=t:(o=t.handler,n=t);const i=Xs(this),a=Fi(r,o.bind(s),n);return i(),a}function Yc(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const lh=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Mt(t)}Modifiers`]||e[`${Wn(t)}Modifiers`];function ch(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Ne;let r=n;const o=t.startsWith("update:"),i=o&&lh(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>ze(c)?c.trim():c)),i.number&&(r=n.map(Er)));let a,l=s[a=yo(t)]||s[a=yo(Mt(t))];!l&&o&&(l=s[a=yo(Wn(t))]),l&&Ut(l,e,6,r);const d=s[a+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ut(d,e,6,r)}}function Zc(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},a=!1;if(!he(e)){const l=d=>{const c=Zc(d,t,!0);c&&(a=!0,tt(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(De(e)&&s.set(e,null),null):(le(o)?o.forEach(l=>i[l]=null):tt(i,o),De(e)&&s.set(e,i),i)}function Yr(e,t){return!e||!Ur(t)?!1:(t=t.slice(2).replace(/Once$/,""),Pe(e,t[0].toLowerCase()+t.slice(1))||Pe(e,Wn(t))||Pe(e,t))}function ga(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:a,emit:l,render:d,renderCache:c,props:f,data:p,setupState:g,ctx:y,inheritAttrs:S}=e,E=Or(e);let C,P;try{if(n.shapeFlag&4){const A=r||s,h=A;C=Yt(d.call(h,A,c,f,g,p,y)),P=a}else{const A=t;C=Yt(A.length>1?A(f,{attrs:a,slots:i,emit:l}):A(f,null)),P=t.props?a:uh(a)}}catch(A){ks.length=0,zr(A,e,1),C=ae(vt)}let D=C;if(P&&S!==!1){const A=Object.keys(P),{shapeFlag:h}=D;A.length&&h&7&&(o&&A.some(yi)&&(P=dh(P,o)),D=Rn(D,P,!1,!0))}return n.dirs&&(D=Rn(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&Ns(D,n.transition),C=D,Or(E),C}const uh=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ur(n))&&((t||(t={}))[n]=e[n]);return t},dh=(e,t)=>{const n={};for(const s in e)(!yi(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function fh(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:a,patchFlag:l}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?ma(s,i,d):!!i;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const p=c[f];if(i[p]!==s[p]&&!Yr(d,p))return!0}}}else return(r||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?ma(s,i,d):!0:!!i;return!1}function ma(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Yr(n,o))return!0}return!1}function hh({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Qc=e=>e.__isSuspense;function ph(e,t){t&&t.pendingBranch?le(e)?t.effects.push(...e):t.effects.push(e):Af(e)}const $e=Symbol.for("v-fgt"),Zr=Symbol.for("v-txt"),vt=Symbol.for("v-cmt"),pr=Symbol.for("v-stc"),ks=[];let kt=null;function R(e=!1){ks.push(kt=e?null:[])}function gh(){ks.pop(),kt=ks[ks.length-1]||null}let Vs=1;function va(e,t=!1){Vs+=e,e<0&&kt&&t&&(kt.hasOnce=!0)}function Xc(e){return e.dynamicChildren=Vs>0?kt||os:null,gh(),Vs>0&&kt&&kt.push(e),e}function x(e,t,n,s,r,o){return Xc(u(e,t,n,s,r,o,!0))}function On(e,t,n,s,r){return Xc(ae(e,t,n,s,r,!0))}function Ls(e){return e?e.__v_isVNode===!0:!1}function Vn(e,t){return e.type===t.type&&e.key===t.key}const eu=({key:e})=>e??null,gr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ze(e)||He(e)||he(e)?{i:et,r:e,k:t,f:!!n}:e:null);function u(e,t=null,n=null,s=0,r=null,o=e===$e?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&eu(t),ref:t&&gr(t),scopeId:wc,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:et};return a?(Di(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=ze(n)?8:16),Vs>0&&!i&&kt&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&kt.push(l),l}const ae=mh;function mh(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===$c)&&(e=vt),Ls(e)){const a=Rn(e,t,!0);return n&&Di(a,n),Vs>0&&!o&&kt&&(a.shapeFlag&6?kt[kt.indexOf(e)]=a:kt.push(a)),a.patchFlag=-2,a}if(Th(e)&&(e=e.__vccOpts),t){t=vh(t);let{class:a,style:l}=t;a&&!ze(a)&&(t.class=Te(a)),De(l)&&(Pi(l)&&!le(l)&&(l=tt({},l)),t.style=wi(l))}const i=ze(e)?1:Qc(e)?128:Ec(e)?64:De(e)?4:he(e)?2:0;return u(e,t,n,s,r,i,o,!0)}function vh(e){return e?Pi(e)||Uc(e)?tt({},e):e:null}function Rn(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:a,transition:l}=e,d=t?yh(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&eu(d),ref:t&&t.ref?n&&o?le(o)?o.concat(gr(t)):[o,gr(t)]:gr(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==$e?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Rn(e.ssContent),ssFallback:e.ssFallback&&Rn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&Ns(c,l.clone(c)),c}function _e(e=" ",t=0){return ae(Zr,null,e,t)}function hn(e,t){const n=ae(pr,null,e);return n.staticCount=t,n}function Y(e="",t=!1){return t?(R(),On(vt,null,e)):ae(vt,null,e)}function Yt(e){return e==null||typeof e=="boolean"?ae(vt):le(e)?ae($e,null,e.slice()):Ls(e)?bn(e):ae(Zr,null,String(e))}function bn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Rn(e)}function Di(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(le(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Di(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Uc(t)?t._ctx=et:r===3&&et&&(et.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else he(t)?(t={default:t,_ctx:et},n=32):(t=String(t),s&64?(n=16,t=[_e(t)]):n=8);e.children=t,e.shapeFlag|=n}function yh(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Te([t.class,s.class]));else if(r==="style")t.style=wi([t.style,s.style]);else if(Ur(r)){const o=t[r],i=s[r];i&&o!==i&&!(le(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function zt(e,t,n,s=null){Ut(e,t,7,[n,s])}const _h=Vc();let bh=0;function wh(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||_h,o={uid:bh++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ql(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Bc(s,r),emitsOptions:Zc(s,r),emit:null,emitted:null,propsDefaults:Ne,inheritAttrs:s.inheritAttrs,ctx:Ne,data:Ne,props:Ne,attrs:Ne,slots:Ne,refs:Ne,setupState:Ne,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ch.bind(null,o),e.ce&&e.ce(o),o}let rt=null;const Qs=()=>rt||et;let xr,Zo;{const e=Gr(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};xr=t("__VUE_INSTANCE_SETTERS__",n=>rt=n),Zo=t("__VUE_SSR_SETTERS__",n=>js=n)}const Xs=e=>{const t=rt;return xr(e),e.scope.on(),()=>{e.scope.off(),xr(t)}},ya=()=>{rt&&rt.scope.off(),xr(null)};function tu(e){return e.vnode.shapeFlag&4}let js=!1;function Eh(e,t=!1,n=!1){t&&Zo(t);const{props:s,children:r}=e.vnode,o=tu(e);Wf(e,s,o,t),Qf(e,r,n);const i=o?Sh(e,t):void 0;return t&&Zo(!1),i}function Sh(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,jf);const{setup:s}=n;if(s){xn();const r=e.setupContext=s.length>1?Ah(e):null,o=Xs(e),i=Ys(s,e,0,[e.props,r]),a=Hl(i);if(Pn(),o(),(a||e.sp)&&!cs(e)&&Rc(e),a){if(i.then(ya,ya),t)return i.then(l=>{_a(e,l)}).catch(l=>{zr(l,e,0)});e.asyncDep=i}else _a(e,i)}else nu(e)}function _a(e,t,n){he(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:De(t)&&(e.setupState=mc(t)),nu(e)}function nu(e,t,n){const s=e.type;e.render||(e.render=s.render||Zt);{const r=Xs(e);xn();try{Uf(e)}finally{Pn(),r()}}}const Ch={get(e,t){return ut(e,"get",""),e[t]}};function Ah(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ch),slots:e.slots,emit:e.emit,expose:t}}function Qr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(mc(hf(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ps)return Ps[n](e)},has(t,n){return n in t||n in Ps}})):e.proxy}function Oh(e,t=!0){return he(e)?e.displayName||e.name:e.name||t&&e.__name}function Th(e){return he(e)&&"__vccOpts"in e}const te=(e,t)=>bf(e,t,js);function Hn(e,t,n){const s=arguments.length;return s===2?De(t)&&!le(t)?Ls(t)?ae(e,null,[t]):ae(e,t):ae(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Ls(n)&&(n=[n]),ae(e,t,n))}const Rh="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Qo;const ba=typeof window<"u"&&window.trustedTypes;if(ba)try{Qo=ba.createPolicy("vue",{createHTML:e=>e})}catch{}const su=Qo?e=>Qo.createHTML(e):e=>e,xh="http://www.w3.org/2000/svg",Ph="http://www.w3.org/1998/Math/MathML",on=typeof document<"u"?document:null,wa=on&&on.createElement("template"),kh={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?on.createElementNS(xh,e):t==="mathml"?on.createElementNS(Ph,e):n?on.createElement(e,{is:n}):on.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>on.createTextNode(e),createComment:e=>on.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>on.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{wa.innerHTML=su(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=wa.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},mn="transition",ws="animation",Us=Symbol("_vtc"),ru={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ih=tt({},Sc,ru),$h=e=>(e.displayName="Transition",e.props=Ih,e),ou=$h((e,{slots:t})=>Hn(xf,Fh(e),t)),Fn=(e,t=[])=>{le(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ea=e=>e?le(e)?e.some(t=>t.length>1):e.length>1:!1;function Fh(e){const t={};for(const B in e)B in ru||(t[B]=e[B]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:d=i,appearToClass:c=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,y=Dh(r),S=y&&y[0],E=y&&y[1],{onBeforeEnter:C,onEnter:P,onEnterCancelled:D,onLeave:A,onLeaveCancelled:h,onBeforeAppear:G=C,onAppear:O=P,onAppearCancelled:W=D}=t,I=(B,pe,Se,Me)=>{B._enterCancelled=Me,Dn(B,pe?c:a),Dn(B,pe?d:i),Se&&Se()},ne=(B,pe)=>{B._isLeaving=!1,Dn(B,f),Dn(B,g),Dn(B,p),pe&&pe()},q=B=>(pe,Se)=>{const Me=B?O:P,ge=()=>I(pe,B,Se);Fn(Me,[pe,ge]),Sa(()=>{Dn(pe,B?l:o),tn(pe,B?c:a),Ea(Me)||Ca(pe,s,S,ge)})};return tt(t,{onBeforeEnter(B){Fn(C,[B]),tn(B,o),tn(B,i)},onBeforeAppear(B){Fn(G,[B]),tn(B,l),tn(B,d)},onEnter:q(!1),onAppear:q(!0),onLeave(B,pe){B._isLeaving=!0;const Se=()=>ne(B,pe);tn(B,f),B._enterCancelled?(tn(B,p),Ta()):(Ta(),tn(B,p)),Sa(()=>{B._isLeaving&&(Dn(B,f),tn(B,g),Ea(A)||Ca(B,s,E,Se))}),Fn(A,[B,Se])},onEnterCancelled(B){I(B,!1,void 0,!0),Fn(D,[B])},onAppearCancelled(B){I(B,!0,void 0,!0),Fn(W,[B])},onLeaveCancelled(B){ne(B),Fn(h,[B])}})}function Dh(e){if(e==null)return null;if(De(e))return[Oo(e.enter),Oo(e.leave)];{const t=Oo(e);return[t,t]}}function Oo(e){return Nd(e)}function tn(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Us]||(e[Us]=new Set)).add(t)}function Dn(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Us];n&&(n.delete(t),n.size||(e[Us]=void 0))}function Sa(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Mh=0;function Ca(e,t,n,s){const r=e._endId=++Mh,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:l}=Nh(e,t);if(!i)return s();const d=i+"end";let c=0;const f=()=>{e.removeEventListener(d,p),o()},p=g=>{g.target===e&&++c>=l&&f()};setTimeout(()=>{c<l&&f()},a+1),e.addEventListener(d,p)}function Nh(e,t){const n=window.getComputedStyle(e),s=y=>(n[y]||"").split(", "),r=s(`${mn}Delay`),o=s(`${mn}Duration`),i=Aa(r,o),a=s(`${ws}Delay`),l=s(`${ws}Duration`),d=Aa(a,l);let c=null,f=0,p=0;t===mn?i>0&&(c=mn,f=i,p=o.length):t===ws?d>0&&(c=ws,f=d,p=l.length):(f=Math.max(i,d),c=f>0?i>d?mn:ws:null,p=c?c===mn?o.length:l.length:0);const g=c===mn&&/\b(transform|all)(,|$)/.test(s(`${mn}Property`).toString());return{type:c,timeout:f,propCount:p,hasTransform:g}}function Aa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Oa(n)+Oa(e[s])))}function Oa(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ta(){return document.body.offsetHeight}function Vh(e,t,n){const s=e[Us];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Pr=Symbol("_vod"),iu=Symbol("_vsh"),Lh={beforeMount(e,{value:t},{transition:n}){e[Pr]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Es(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),Es(e,!0),s.enter(e)):s.leave(e,()=>{Es(e,!1)}):Es(e,t))},beforeUnmount(e,{value:t}){Es(e,t)}};function Es(e,t){e.style.display=t?e[Pr]:"none",e[iu]=!t}const jh=Symbol(""),Uh=/(^|;)\s*display\s*:/;function qh(e,t,n){const s=e.style,r=ze(n);let o=!1;if(n&&!r){if(t)if(ze(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&mr(s,a,"")}else for(const i in t)n[i]==null&&mr(s,i,"");for(const i in n)i==="display"&&(o=!0),mr(s,i,n[i])}else if(r){if(t!==n){const i=s[jh];i&&(n+=";"+i),s.cssText=n,o=Uh.test(n)}}else t&&e.removeAttribute("style");Pr in e&&(e[Pr]=o?s.display:"",e[iu]&&(s.display="none"))}const Ra=/\s*!important$/;function mr(e,t,n){if(le(n))n.forEach(s=>mr(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Bh(e,t);Ra.test(n)?e.setProperty(Wn(s),n.replace(Ra,""),"important"):e[s]=n}}const xa=["Webkit","Moz","ms"],To={};function Bh(e,t){const n=To[t];if(n)return n;let s=Mt(t);if(s!=="filter"&&s in e)return To[t]=s;s=Br(s);for(let r=0;r<xa.length;r++){const o=xa[r]+s;if(o in e)return To[t]=o}return t}const Pa="http://www.w3.org/1999/xlink";function ka(e,t,n,s,r,o=Bd(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Pa,t.slice(6,t.length)):e.setAttributeNS(Pa,t,n):n==null||o&&!Jl(n)?e.removeAttribute(t):e.setAttribute(t,o?"":jt(n)?String(n):n)}function Ia(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?su(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Jl(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function En(e,t,n,s){e.addEventListener(t,n,s)}function Gh(e,t,n,s){e.removeEventListener(t,n,s)}const $a=Symbol("_vei");function Hh(e,t,n,s,r=null){const o=e[$a]||(e[$a]={}),i=o[t];if(s&&i)i.value=s;else{const[a,l]=zh(t);if(s){const d=o[t]=Jh(s,r);En(e,a,d,l)}else i&&(Gh(e,a,i,l),o[t]=void 0)}}const Fa=/(?:Once|Passive|Capture)$/;function zh(e){let t;if(Fa.test(e)){t={};let s;for(;s=e.match(Fa);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Wn(e.slice(2)),t]}let Ro=0;const Kh=Promise.resolve(),Wh=()=>Ro||(Kh.then(()=>Ro=0),Ro=Date.now());function Jh(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ut(Yh(s,n.value),t,5,[s])};return n.value=e,n.attached=Wh(),n}function Yh(e,t){if(le(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Da=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Zh=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Vh(e,s,i):t==="style"?qh(e,n,s):Ur(t)?yi(t)||Hh(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Qh(e,t,s,i))?(Ia(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ka(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ze(s))?Ia(e,Mt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ka(e,t,s,i))};function Qh(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Da(t)&&he(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Da(t)&&ze(n)?!1:t in e}const fs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return le(t)?n=>hr(t,n):t};function Xh(e){e.target.composing=!0}function Ma(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const fn=Symbol("_assign"),Ke={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[fn]=fs(r);const o=s||r.props&&r.props.type==="number";En(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=Er(a)),e[fn](a)}),n&&En(e,"change",()=>{e.value=e.value.trim()}),t||(En(e,"compositionstart",Xh),En(e,"compositionend",Ma),En(e,"change",Ma))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[fn]=fs(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?Er(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===l)||(e.value=l))}},au={deep:!0,created(e,t,n){e[fn]=fs(n),En(e,"change",()=>{const s=e._modelValue,r=qs(e),o=e.checked,i=e[fn];if(le(s)){const a=Ei(s,r),l=a!==-1;if(o&&!l)i(s.concat(r));else if(!o&&l){const d=[...s];d.splice(a,1),i(d)}}else if(gs(s)){const a=new Set(s);o?a.add(r):a.delete(r),i(a)}else i(lu(e,o))})},mounted:Na,beforeUpdate(e,t,n){e[fn]=fs(n),Na(e,t,n)}};function Na(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(le(t))r=Ei(t,s.props.value)>-1;else if(gs(t))r=t.has(s.props.value);else{if(t===n)return;r=Js(t,lu(e,!0))}e.checked!==r&&(e.checked=r)}const xC={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=gs(t);En(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Er(qs(i)):qs(i));e[fn](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,xt(()=>{e._assigning=!1})}),e[fn]=fs(s)},mounted(e,{value:t}){Va(e,t)},beforeUpdate(e,t,n){e[fn]=fs(n)},updated(e,{value:t}){e._assigning||Va(e,t)}};function Va(e,t){const n=e.multiple,s=le(t);if(!(n&&!s&&!gs(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],a=qs(i);if(n)if(s){const l=typeof a;l==="string"||l==="number"?i.selected=t.some(d=>String(d)===String(a)):i.selected=Ei(t,a)>-1}else i.selected=t.has(a);else if(Js(qs(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function qs(e){return"_value"in e?e._value:e.value}function lu(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ep=["ctrl","shift","alt","meta"],tp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ep.some(n=>e[`${n}Key`]&&!t.includes(n))},cu=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const a=tp[t[i]];if(a&&a(r,t))return}return e(r,...o)})},np=tt({patchProp:Zh},kh);let La;function sp(){return La||(La=eh(np))}const rp=(...e)=>{const t=sp().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=ip(s);if(!r)return;const o=t._component;!he(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,op(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function op(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ip(e){return ze(e)?document.querySelector(e):e}function ap(){return uu().__VUE_DEVTOOLS_GLOBAL_HOOK__}function uu(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const lp=typeof Proxy=="function",cp="devtools-plugin:setup",up="plugin:settings:set";let Qn,Xo;function dp(){var e;return Qn!==void 0||(typeof window<"u"&&window.performance?(Qn=!0,Xo=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Qn=!0,Xo=globalThis.perf_hooks.performance):Qn=!1),Qn}function fp(){return dp()?Xo.now():Date.now()}class hp{constructor(t,n){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=n;const s={};if(t.settings)for(const i in t.settings){const a=t.settings[i];s[i]=a.defaultValue}const r=`__vue-devtools-plugin-settings__${t.id}`;let o=Object.assign({},s);try{const i=localStorage.getItem(r),a=JSON.parse(i);Object.assign(o,a)}catch{}this.fallbacks={getSettings(){return o},setSettings(i){try{localStorage.setItem(r,JSON.stringify(i))}catch{}o=i},now(){return fp()}},n&&n.on(up,(i,a)=>{i===this.plugin.id&&this.fallbacks.setSettings(a)}),this.proxiedOn=new Proxy({},{get:(i,a)=>this.target?this.target.on[a]:(...l)=>{this.onQueue.push({method:a,args:l})}}),this.proxiedTarget=new Proxy({},{get:(i,a)=>this.target?this.target[a]:a==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(a)?(...l)=>(this.targetQueue.push({method:a,args:l,resolve:()=>{}}),this.fallbacks[a](...l)):(...l)=>new Promise(d=>{this.targetQueue.push({method:a,args:l,resolve:d})})})}async setRealTarget(t){this.target=t;for(const n of this.onQueue)this.target.on[n.method](...n.args);for(const n of this.targetQueue)n.resolve(await this.target[n.method](...n.args))}}function pp(e,t){const n=e,s=uu(),r=ap(),o=lp&&n.enableEarlyProxy;if(r&&(s.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!o))r.emit(cp,e,t);else{const i=o?new hp(n,r):null;(s.__VUE_DEVTOOLS_PLUGINS__=s.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}}/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */var du="store";function er(e){return e===void 0&&(e=null),St(e!==null?e:du)}function ms(e,t){Object.keys(e).forEach(function(n){return t(e[n],n)})}function gp(e){return e!==null&&typeof e=="object"}function mp(e){return e&&typeof e.then=="function"}function vp(e,t){return function(){return e(t)}}function fu(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var s=t.indexOf(e);s>-1&&t.splice(s,1)}}function hu(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;Xr(e,n,[],e._modules.root,!0),Mi(e,n,t)}function Mi(e,t,n){var s=e._state,r=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var o=e._wrappedGetters,i={},a={},l=Hd(!0);l.run(function(){ms(o,function(d,c){i[c]=vp(d,e),a[c]=te(function(){return i[c]()}),Object.defineProperty(e.getters,c,{get:function(){return a[c].value},enumerable:!0})})}),e._state=Qt({data:t}),e._scope=l,e.strict&&Ep(e),s&&n&&e._withCommit(function(){s.data=null}),r&&r.stop()}function Xr(e,t,n,s,r){var o=!n.length,i=e._modules.getNamespace(n);if(s.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=s),!o&&!r){var a=Ni(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit(function(){a[l]=s.state})}var d=s.context=yp(e,i,n);s.forEachMutation(function(c,f){var p=i+f;_p(e,p,c,d)}),s.forEachAction(function(c,f){var p=c.root?f:i+f,g=c.handler||c;bp(e,p,g,d)}),s.forEachGetter(function(c,f){var p=i+f;wp(e,p,c,d)}),s.forEachChild(function(c,f){Xr(e,t,n.concat(f),c,r)})}function yp(e,t,n){var s=t==="",r={dispatch:s?e.dispatch:function(o,i,a){var l=kr(o,i,a),d=l.payload,c=l.options,f=l.type;return(!c||!c.root)&&(f=t+f),e.dispatch(f,d)},commit:s?e.commit:function(o,i,a){var l=kr(o,i,a),d=l.payload,c=l.options,f=l.type;(!c||!c.root)&&(f=t+f),e.commit(f,d,c)}};return Object.defineProperties(r,{getters:{get:s?function(){return e.getters}:function(){return pu(e,t)}},state:{get:function(){return Ni(e.state,n)}}}),r}function pu(e,t){if(!e._makeLocalGettersCache[t]){var n={},s=t.length;Object.keys(e.getters).forEach(function(r){if(r.slice(0,s)===t){var o=r.slice(s);Object.defineProperty(n,o,{get:function(){return e.getters[r]},enumerable:!0})}}),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function _p(e,t,n,s){var r=e._mutations[t]||(e._mutations[t]=[]);r.push(function(i){n.call(e,s.state,i)})}function bp(e,t,n,s){var r=e._actions[t]||(e._actions[t]=[]);r.push(function(i){var a=n.call(e,{dispatch:s.dispatch,commit:s.commit,getters:s.getters,state:s.state,rootGetters:e.getters,rootState:e.state},i);return mp(a)||(a=Promise.resolve(a)),e._devtoolHook?a.catch(function(l){throw e._devtoolHook.emit("vuex:error",l),l}):a})}function wp(e,t,n,s){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(o){return n(s.state,s.getters,o.state,o.getters)})}function Ep(e){It(function(){return e._state.data},function(){},{deep:!0,flush:"sync"})}function Ni(e,t){return t.reduce(function(n,s){return n[s]},e)}function kr(e,t,n){return gp(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var Sp="vuex bindings",ja="vuex:mutations",xo="vuex:actions",Xn="vuex",Cp=0;function Ap(e,t){pp({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[Sp]},function(n){n.addTimelineLayer({id:ja,label:"Vuex Mutations",color:Ua}),n.addTimelineLayer({id:xo,label:"Vuex Actions",color:Ua}),n.addInspector({id:Xn,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree(function(s){if(s.app===e&&s.inspectorId===Xn)if(s.filter){var r=[];yu(r,t._modules.root,s.filter,""),s.rootNodes=r}else s.rootNodes=[vu(t._modules.root,"")]}),n.on.getInspectorState(function(s){if(s.app===e&&s.inspectorId===Xn){var r=s.nodeId;pu(t,r),s.state=Rp(Pp(t._modules,r),r==="root"?t.getters:t._makeLocalGettersCache,r)}}),n.on.editInspectorState(function(s){if(s.app===e&&s.inspectorId===Xn){var r=s.nodeId,o=s.path;r!=="root"&&(o=r.split("/").filter(Boolean).concat(o)),t._withCommit(function(){s.set(t._state.data,o,s.state.value)})}}),t.subscribe(function(s,r){var o={};s.payload&&(o.payload=s.payload),o.state=r,n.notifyComponentUpdate(),n.sendInspectorTree(Xn),n.sendInspectorState(Xn),n.addTimelineEvent({layerId:ja,event:{time:Date.now(),title:s.type,data:o}})}),t.subscribeAction({before:function(s,r){var o={};s.payload&&(o.payload=s.payload),s._id=Cp++,s._time=Date.now(),o.state=r,n.addTimelineEvent({layerId:xo,event:{time:s._time,title:s.type,groupId:s._id,subtitle:"start",data:o}})},after:function(s,r){var o={},i=Date.now()-s._time;o.duration={_custom:{type:"duration",display:i+"ms",tooltip:"Action duration",value:i}},s.payload&&(o.payload=s.payload),o.state=r,n.addTimelineEvent({layerId:xo,event:{time:Date.now(),title:s.type,groupId:s._id,subtitle:"end",data:o}})}})})}var Ua=8702998,Op=6710886,Tp=16777215,gu={label:"namespaced",textColor:Tp,backgroundColor:Op};function mu(e){return e&&e!=="root"?e.split("/").slice(-2,-1)[0]:"Root"}function vu(e,t){return{id:t||"root",label:mu(t),tags:e.namespaced?[gu]:[],children:Object.keys(e._children).map(function(n){return vu(e._children[n],t+n+"/")})}}function yu(e,t,n,s){s.includes(n)&&e.push({id:s||"root",label:s.endsWith("/")?s.slice(0,s.length-1):s||"Root",tags:t.namespaced?[gu]:[]}),Object.keys(t._children).forEach(function(r){yu(e,t._children[r],n,s+r+"/")})}function Rp(e,t,n){t=n==="root"?t:t[n];var s=Object.keys(t),r={state:Object.keys(e.state).map(function(i){return{key:i,editable:!0,value:e.state[i]}})};if(s.length){var o=xp(t);r.getters=Object.keys(o).map(function(i){return{key:i.endsWith("/")?mu(i):i,editable:!1,value:ei(function(){return o[i]})}})}return r}function xp(e){var t={};return Object.keys(e).forEach(function(n){var s=n.split("/");if(s.length>1){var r=t,o=s.pop();s.forEach(function(i){r[i]||(r[i]={_custom:{value:{},display:i,tooltip:"Module",abstract:!0}}),r=r[i]._custom.value}),r[o]=ei(function(){return e[n]})}else t[n]=ei(function(){return e[n]})}),t}function Pp(e,t){var n=t.split("/").filter(function(s){return s});return n.reduce(function(s,r,o){var i=s[r];if(!i)throw new Error('Missing module "'+r+'" for path "'+t+'".');return o===n.length-1?i:i._children},t==="root"?e:e.root._children)}function ei(e){try{return e()}catch(t){return t}}var Gt=function(t,n){this.runtime=n,this._children=Object.create(null),this._rawModule=t;var s=t.state;this.state=(typeof s=="function"?s():s)||{}},_u={namespaced:{configurable:!0}};_u.namespaced.get=function(){return!!this._rawModule.namespaced};Gt.prototype.addChild=function(t,n){this._children[t]=n};Gt.prototype.removeChild=function(t){delete this._children[t]};Gt.prototype.getChild=function(t){return this._children[t]};Gt.prototype.hasChild=function(t){return t in this._children};Gt.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)};Gt.prototype.forEachChild=function(t){ms(this._children,t)};Gt.prototype.forEachGetter=function(t){this._rawModule.getters&&ms(this._rawModule.getters,t)};Gt.prototype.forEachAction=function(t){this._rawModule.actions&&ms(this._rawModule.actions,t)};Gt.prototype.forEachMutation=function(t){this._rawModule.mutations&&ms(this._rawModule.mutations,t)};Object.defineProperties(Gt.prototype,_u);var Jn=function(t){this.register([],t,!1)};Jn.prototype.get=function(t){return t.reduce(function(n,s){return n.getChild(s)},this.root)};Jn.prototype.getNamespace=function(t){var n=this.root;return t.reduce(function(s,r){return n=n.getChild(r),s+(n.namespaced?r+"/":"")},"")};Jn.prototype.update=function(t){bu([],this.root,t)};Jn.prototype.register=function(t,n,s){var r=this;s===void 0&&(s=!0);var o=new Gt(n,s);if(t.length===0)this.root=o;else{var i=this.get(t.slice(0,-1));i.addChild(t[t.length-1],o)}n.modules&&ms(n.modules,function(a,l){r.register(t.concat(l),a,s)})};Jn.prototype.unregister=function(t){var n=this.get(t.slice(0,-1)),s=t[t.length-1],r=n.getChild(s);r&&r.runtime&&n.removeChild(s)};Jn.prototype.isRegistered=function(t){var n=this.get(t.slice(0,-1)),s=t[t.length-1];return n?n.hasChild(s):!1};function bu(e,t,n){if(t.update(n),n.modules)for(var s in n.modules){if(!t.getChild(s))return;bu(e.concat(s),t.getChild(s),n.modules[s])}}function kp(e){return new Ot(e)}var Ot=function(t){var n=this;t===void 0&&(t={});var s=t.plugins;s===void 0&&(s=[]);var r=t.strict;r===void 0&&(r=!1);var o=t.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Jn(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=o;var i=this,a=this,l=a.dispatch,d=a.commit;this.dispatch=function(p,g){return l.call(i,p,g)},this.commit=function(p,g,y){return d.call(i,p,g,y)},this.strict=r;var c=this._modules.root.state;Xr(this,c,[],this._modules.root),Mi(this,c),s.forEach(function(f){return f(n)})},Vi={state:{configurable:!0}};Ot.prototype.install=function(t,n){t.provide(n||du,this),t.config.globalProperties.$store=this;var s=this._devtools!==void 0?this._devtools:!1;s&&Ap(t,this)};Vi.state.get=function(){return this._state.data};Vi.state.set=function(e){};Ot.prototype.commit=function(t,n,s){var r=this,o=kr(t,n,s),i=o.type,a=o.payload,l={type:i,payload:a},d=this._mutations[i];d&&(this._withCommit(function(){d.forEach(function(f){f(a)})}),this._subscribers.slice().forEach(function(c){return c(l,r.state)}))};Ot.prototype.dispatch=function(t,n){var s=this,r=kr(t,n),o=r.type,i=r.payload,a={type:o,payload:i},l=this._actions[o];if(l){try{this._actionSubscribers.slice().filter(function(c){return c.before}).forEach(function(c){return c.before(a,s.state)})}catch{}var d=l.length>1?Promise.all(l.map(function(c){return c(i)})):l[0](i);return new Promise(function(c,f){d.then(function(p){try{s._actionSubscribers.filter(function(g){return g.after}).forEach(function(g){return g.after(a,s.state)})}catch{}c(p)},function(p){try{s._actionSubscribers.filter(function(g){return g.error}).forEach(function(g){return g.error(a,s.state,p)})}catch{}f(p)})})}};Ot.prototype.subscribe=function(t,n){return fu(t,this._subscribers,n)};Ot.prototype.subscribeAction=function(t,n){var s=typeof t=="function"?{before:t}:t;return fu(s,this._actionSubscribers,n)};Ot.prototype.watch=function(t,n,s){var r=this;return It(function(){return t(r.state,r.getters)},n,Object.assign({},s))};Ot.prototype.replaceState=function(t){var n=this;this._withCommit(function(){n._state.data=t})};Ot.prototype.registerModule=function(t,n,s){s===void 0&&(s={}),typeof t=="string"&&(t=[t]),this._modules.register(t,n),Xr(this,this.state,t,this._modules.get(t),s.preserveState),Mi(this,this.state)};Ot.prototype.unregisterModule=function(t){var n=this;typeof t=="string"&&(t=[t]),this._modules.unregister(t),this._withCommit(function(){var s=Ni(n.state,t.slice(0,-1));delete s[t[t.length-1]]}),hu(this)};Ot.prototype.hasModule=function(t){return typeof t=="string"&&(t=[t]),this._modules.isRegistered(t)};Ot.prototype.hotUpdate=function(t){this._modules.update(t),hu(this,!0)};Ot.prototype._withCommit=function(t){var n=this._committing;this._committing=!0,t(),this._committing=n};Object.defineProperties(Ot.prototype,Vi);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ss=typeof document<"u";function wu(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ip(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&wu(e.default)}const xe=Object.assign;function Po(e,t){const n={};for(const s in t){const r=t[s];n[s]=qt(r)?r.map(e):e(r)}return n}const Is=()=>{},qt=Array.isArray,Eu=/#/g,$p=/&/g,Fp=/\//g,Dp=/=/g,Mp=/\?/g,Su=/\+/g,Np=/%5B/g,Vp=/%5D/g,Cu=/%5E/g,Lp=/%60/g,Au=/%7B/g,jp=/%7C/g,Ou=/%7D/g,Up=/%20/g;function Li(e){return encodeURI(""+e).replace(jp,"|").replace(Np,"[").replace(Vp,"]")}function qp(e){return Li(e).replace(Au,"{").replace(Ou,"}").replace(Cu,"^")}function ti(e){return Li(e).replace(Su,"%2B").replace(Up,"+").replace(Eu,"%23").replace($p,"%26").replace(Lp,"`").replace(Au,"{").replace(Ou,"}").replace(Cu,"^")}function Bp(e){return ti(e).replace(Dp,"%3D")}function Gp(e){return Li(e).replace(Eu,"%23").replace(Mp,"%3F")}function Hp(e){return e==null?"":Gp(e).replace(Fp,"%2F")}function Bs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const zp=/\/$/,Kp=e=>e.replace(zp,"");function ko(e,t,n="/"){let s,r={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),r=e(o)),a>-1&&(s=s||t.slice(0,a),i=t.slice(a,t.length)),s=Zp(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Bs(i)}}function Wp(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function qa(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Jp(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&hs(t.matched[s],n.matched[r])&&Tu(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function hs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Tu(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Yp(e[n],t[n]))return!1;return!0}function Yp(e,t){return qt(e)?Ba(e,t):qt(t)?Ba(t,e):e===t}function Ba(e,t){return qt(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Zp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,a;for(i=0;i<s.length;i++)if(a=s[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const vn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Gs;(function(e){e.pop="pop",e.push="push"})(Gs||(Gs={}));var $s;(function(e){e.back="back",e.forward="forward",e.unknown=""})($s||($s={}));function Qp(e){if(!e)if(ss){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Kp(e)}const Xp=/^[^#]+#/;function eg(e,t){return e.replace(Xp,"#")+t}function tg(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const eo=()=>({left:window.scrollX,top:window.scrollY});function ng(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=tg(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ga(e,t){return(history.state?history.state.position-t:-1)+e}const ni=new Map;function sg(e,t){ni.set(e,t)}function rg(e){const t=ni.get(e);return ni.delete(e),t}let og=()=>location.protocol+"//"+location.host;function Ru(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let a=r.includes(e.slice(o))?e.slice(o).length:1,l=r.slice(a);return l[0]!=="/"&&(l="/"+l),qa(l,"")}return qa(n,e)+s+r}function ig(e,t,n,s){let r=[],o=[],i=null;const a=({state:p})=>{const g=Ru(e,location),y=n.value,S=t.value;let E=0;if(p){if(n.value=g,t.value=p,i&&i===y){i=null;return}E=S?p.position-S.position:0}else s(g);r.forEach(C=>{C(n.value,y,{delta:E,type:Gs.pop,direction:E?E>0?$s.forward:$s.back:$s.unknown})})};function l(){i=n.value}function d(p){r.push(p);const g=()=>{const y=r.indexOf(p);y>-1&&r.splice(y,1)};return o.push(g),g}function c(){const{history:p}=window;p.state&&p.replaceState(xe({},p.state,{scroll:eo()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:d,destroy:f}}function Ha(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?eo():null}}function ag(e){const{history:t,location:n}=window,s={value:Ru(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,d,c){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:og()+e+l;try{t[c?"replaceState":"pushState"](d,"",p),r.value=d}catch(g){console.error(g),n[c?"replace":"assign"](p)}}function i(l,d){const c=xe({},t.state,Ha(r.value.back,l,r.value.forward,!0),d,{position:r.value.position});o(l,c,!0),s.value=l}function a(l,d){const c=xe({},r.value,t.state,{forward:l,scroll:eo()});o(c.current,c,!0);const f=xe({},Ha(s.value,l,null),{position:c.position+1},d);o(l,f,!1),s.value=l}return{location:s,state:r,push:a,replace:i}}function lg(e){e=Qp(e);const t=ag(e),n=ig(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=xe({location:"",base:e,go:s,createHref:eg.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function cg(e){return typeof e=="string"||e&&typeof e=="object"}function xu(e){return typeof e=="string"||typeof e=="symbol"}const Pu=Symbol("");var za;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(za||(za={}));function ps(e,t){return xe(new Error,{type:e,[Pu]:!0},t)}function nn(e,t){return e instanceof Error&&Pu in e&&(t==null||!!(e.type&t))}const Ka="[^/]+?",ug={sensitive:!1,strict:!1,start:!0,end:!0},dg=/[.+*?^${}()[\]/\\]/g;function fg(e,t){const n=xe({},ug,t),s=[];let r=n.start?"^":"";const o=[];for(const d of e){const c=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let f=0;f<d.length;f++){const p=d[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(dg,"\\$&"),g+=40;else if(p.type===1){const{value:y,repeatable:S,optional:E,regexp:C}=p;o.push({name:y,repeatable:S,optional:E});const P=C||Ka;if(P!==Ka){g+=10;try{new RegExp(`(${P})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${y}" (${P}): `+A.message)}}let D=S?`((?:${P})(?:/(?:${P}))*)`:`(${P})`;f||(D=E&&d.length<2?`(?:/${D})`:"/"+D),E&&(D+="?"),r+=D,g+=20,E&&(g+=-8),S&&(g+=-20),P===".*"&&(g+=-50)}c.push(g)}s.push(c)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function a(d){const c=d.match(i),f={};if(!c)return null;for(let p=1;p<c.length;p++){const g=c[p]||"",y=o[p-1];f[y.name]=g&&y.repeatable?g.split("/"):g}return f}function l(d){let c="",f=!1;for(const p of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const g of p)if(g.type===0)c+=g.value;else if(g.type===1){const{value:y,repeatable:S,optional:E}=g,C=y in d?d[y]:"";if(qt(C)&&!S)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const P=qt(C)?C.join("/"):C;if(!P)if(E)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);c+=P}}return c||"/"}return{re:i,score:s,keys:o,parse:a,stringify:l}}function hg(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ku(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=hg(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Wa(s))return 1;if(Wa(r))return-1}return r.length-s.length}function Wa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const pg={type:0,value:""},gg=/[a-zA-Z0-9_]/;function mg(e){if(!e)return[[]];if(e==="/")return[[pg]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${d}": ${g}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let a=0,l,d="",c="";function f(){d&&(n===0?o.push({type:0,value:d}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:d,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),d="")}function p(){d+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(d&&f(),i()):l===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:l==="("?n=2:gg.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),i(),r}function vg(e,t,n){const s=fg(mg(e.path),n),r=xe(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function yg(e,t){const n=[],s=new Map;t=Qa({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,p,g){const y=!g,S=Ya(f);S.aliasOf=g&&g.record;const E=Qa(t,f),C=[S];if("alias"in f){const A=typeof f.alias=="string"?[f.alias]:f.alias;for(const h of A)C.push(Ya(xe({},S,{components:g?g.record.components:S.components,path:h,aliasOf:g?g.record:S})))}let P,D;for(const A of C){const{path:h}=A;if(p&&h[0]!=="/"){const G=p.record.path,O=G[G.length-1]==="/"?"":"/";A.path=p.record.path+(h&&O+h)}if(P=vg(A,p,E),g?g.alias.push(P):(D=D||P,D!==P&&D.alias.push(P),y&&f.name&&!Za(P)&&i(f.name)),Iu(P)&&l(P),S.children){const G=S.children;for(let O=0;O<G.length;O++)o(G[O],P,g&&g.children[O])}g=g||P}return D?()=>{i(D)}:Is}function i(f){if(xu(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function l(f){const p=wg(f,n);n.splice(p,0,f),f.record.name&&!Za(f)&&s.set(f.record.name,f)}function d(f,p){let g,y={},S,E;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw ps(1,{location:f});E=g.record.name,y=xe(Ja(p.params,g.keys.filter(D=>!D.optional).concat(g.parent?g.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),f.params&&Ja(f.params,g.keys.map(D=>D.name))),S=g.stringify(y)}else if(f.path!=null)S=f.path,g=n.find(D=>D.re.test(S)),g&&(y=g.parse(S),E=g.record.name);else{if(g=p.name?s.get(p.name):n.find(D=>D.re.test(p.path)),!g)throw ps(1,{location:f,currentLocation:p});E=g.record.name,y=xe({},p.params,f.params),S=g.stringify(y)}const C=[];let P=g;for(;P;)C.unshift(P.record),P=P.parent;return{name:E,path:S,params:y,matched:C,meta:bg(C)}}e.forEach(f=>o(f));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:d,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:r}}function Ja(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Ya(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_g(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _g(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Za(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function bg(e){return e.reduce((t,n)=>xe(t,n.meta),{})}function Qa(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function wg(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;ku(e,t[o])<0?s=o:n=o+1}const r=Eg(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Eg(e){let t=e;for(;t=t.parent;)if(Iu(t)&&ku(e,t)===0)return t}function Iu({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sg(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Su," "),i=o.indexOf("="),a=Bs(i<0?o:o.slice(0,i)),l=i<0?null:Bs(o.slice(i+1));if(a in t){let d=t[a];qt(d)||(d=t[a]=[d]),d.push(l)}else t[a]=l}return t}function Xa(e){let t="";for(let n in e){const s=e[n];if(n=Bp(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(qt(s)?s.map(o=>o&&ti(o)):[s&&ti(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Cg(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=qt(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Ag=Symbol(""),el=Symbol(""),to=Symbol(""),ji=Symbol(""),si=Symbol("");function Ss(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function wn(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((a,l)=>{const d=p=>{p===!1?l(ps(4,{from:n,to:t})):p instanceof Error?l(p):cg(p)?l(ps(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),a())},c=o(()=>e.call(s&&s.instances[r],t,n,d));let f=Promise.resolve(c);e.length<3&&(f=f.then(d)),f.catch(p=>l(p))})}function Io(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(wu(l)){const c=(l.__vccOpts||l)[t];c&&o.push(wn(c,n,s,i,a,r))}else{let d=l();o.push(()=>d.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=Ip(c)?c.default:c;i.mods[a]=c,i.components[a]=f;const g=(f.__vccOpts||f)[t];return g&&wn(g,n,s,i,a,r)()}))}}return o}function tl(e){const t=St(to),n=St(ji),s=te(()=>{const l=Ce(e.to);return t.resolve(l)}),r=te(()=>{const{matched:l}=s.value,{length:d}=l,c=l[d-1],f=n.matched;if(!c||!f.length)return-1;const p=f.findIndex(hs.bind(null,c));if(p>-1)return p;const g=nl(l[d-2]);return d>1&&nl(c)===g&&f[f.length-1].path!==g?f.findIndex(hs.bind(null,l[d-2])):p}),o=te(()=>r.value>-1&&Pg(n.params,s.value.params)),i=te(()=>r.value>-1&&r.value===n.matched.length-1&&Tu(n.params,s.value.params));function a(l={}){if(xg(l)){const d=t[Ce(e.replace)?"replace":"push"](Ce(e.to)).catch(Is);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:te(()=>s.value.href),isActive:o,isExactActive:i,navigate:a}}function Og(e){return e.length===1?e[0]:e}const Tg=Zs({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:tl,setup(e,{slots:t}){const n=Qt(tl(e)),{options:s}=St(to),r=te(()=>({[sl(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[sl(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Og(t.default(n));return e.custom?o:Hn("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Rg=Tg;function xg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pg(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!qt(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function nl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const sl=(e,t,n)=>e??t??n,kg=Zs({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=St(si),r=te(()=>e.route||s.value),o=St(el,0),i=te(()=>{let d=Ce(o);const{matched:c}=r.value;let f;for(;(f=c[d])&&!f.components;)d++;return d}),a=te(()=>r.value.matched[i.value]);An(el,te(()=>i.value+1)),An(Ag,a),An(si,r);const l=ve();return It(()=>[l.value,a.value,e.name],([d,c,f],[p,g,y])=>{c&&(c.instances[f]=d,g&&g!==c&&d&&d===p&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),d&&c&&(!g||!hs(c,g)||!p)&&(c.enterCallbacks[f]||[]).forEach(S=>S(d))},{flush:"post"}),()=>{const d=r.value,c=e.name,f=a.value,p=f&&f.components[c];if(!p)return rl(n.default,{Component:p,route:d});const g=f.props[c],y=g?g===!0?d.params:typeof g=="function"?g(d):g:null,E=Hn(p,xe({},y,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return rl(n.default,{Component:E,route:d})||E}}});function rl(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Ig=kg;function $g(e){const t=yg(e.routes,e),n=e.parseQuery||Sg,s=e.stringifyQuery||Xa,r=e.history,o=Ss(),i=Ss(),a=Ss(),l=pc(vn);let d=vn;ss&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Po.bind(null,T=>""+T),f=Po.bind(null,Hp),p=Po.bind(null,Bs);function g(T,j){let V,ee;return xu(T)?(V=t.getRecordMatcher(T),ee=j):ee=T,t.addRoute(ee,V)}function y(T){const j=t.getRecordMatcher(T);j&&t.removeRoute(j)}function S(){return t.getRoutes().map(T=>T.record)}function E(T){return!!t.getRecordMatcher(T)}function C(T,j){if(j=xe({},j||l.value),typeof T=="string"){const w=ko(n,T,j.path),$=t.resolve({path:w.path},j),M=r.createHref(w.fullPath);return xe(w,$,{params:p($.params),hash:Bs(w.hash),redirectedFrom:void 0,href:M})}let V;if(T.path!=null)V=xe({},T,{path:ko(n,T.path,j.path).path});else{const w=xe({},T.params);for(const $ in w)w[$]==null&&delete w[$];V=xe({},T,{params:f(w)}),j.params=f(j.params)}const ee=t.resolve(V,j),we=T.hash||"";ee.params=c(p(ee.params));const m=Wp(s,xe({},T,{hash:qp(we),path:ee.path})),v=r.createHref(m);return xe({fullPath:m,hash:we,query:s===Xa?Cg(T.query):T.query||{}},ee,{redirectedFrom:void 0,href:v})}function P(T){return typeof T=="string"?ko(n,T,l.value.path):xe({},T)}function D(T,j){if(d!==T)return ps(8,{from:j,to:T})}function A(T){return O(T)}function h(T){return A(xe(P(T),{replace:!0}))}function G(T){const j=T.matched[T.matched.length-1];if(j&&j.redirect){const{redirect:V}=j;let ee=typeof V=="function"?V(T):V;return typeof ee=="string"&&(ee=ee.includes("?")||ee.includes("#")?ee=P(ee):{path:ee},ee.params={}),xe({query:T.query,hash:T.hash,params:ee.path!=null?{}:T.params},ee)}}function O(T,j){const V=d=C(T),ee=l.value,we=T.state,m=T.force,v=T.replace===!0,w=G(V);if(w)return O(xe(P(w),{state:typeof w=="object"?xe({},we,w.state):we,force:m,replace:v}),j||V);const $=V;$.redirectedFrom=j;let M;return!m&&Jp(s,ee,V)&&(M=ps(16,{to:$,from:ee}),at(ee,ee,!0,!1)),(M?Promise.resolve(M):ne($,ee)).catch(F=>nn(F)?nn(F,2)?F:it(F):be(F,$,ee)).then(F=>{if(F){if(nn(F,2))return O(xe({replace:v},P(F.to),{state:typeof F.to=="object"?xe({},we,F.to.state):we,force:m}),j||$)}else F=B($,ee,!0,v,we);return q($,ee,F),F})}function W(T,j){const V=D(T,j);return V?Promise.reject(V):Promise.resolve()}function I(T){const j=Be.values().next().value;return j&&typeof j.runWithContext=="function"?j.runWithContext(T):T()}function ne(T,j){let V;const[ee,we,m]=Fg(T,j);V=Io(ee.reverse(),"beforeRouteLeave",T,j);for(const w of ee)w.leaveGuards.forEach($=>{V.push(wn($,T,j))});const v=W.bind(null,T,j);return V.push(v),Q(V).then(()=>{V=[];for(const w of o.list())V.push(wn(w,T,j));return V.push(v),Q(V)}).then(()=>{V=Io(we,"beforeRouteUpdate",T,j);for(const w of we)w.updateGuards.forEach($=>{V.push(wn($,T,j))});return V.push(v),Q(V)}).then(()=>{V=[];for(const w of m)if(w.beforeEnter)if(qt(w.beforeEnter))for(const $ of w.beforeEnter)V.push(wn($,T,j));else V.push(wn(w.beforeEnter,T,j));return V.push(v),Q(V)}).then(()=>(T.matched.forEach(w=>w.enterCallbacks={}),V=Io(m,"beforeRouteEnter",T,j,I),V.push(v),Q(V))).then(()=>{V=[];for(const w of i.list())V.push(wn(w,T,j));return V.push(v),Q(V)}).catch(w=>nn(w,8)?w:Promise.reject(w))}function q(T,j,V){a.list().forEach(ee=>I(()=>ee(T,j,V)))}function B(T,j,V,ee,we){const m=D(T,j);if(m)return m;const v=j===vn,w=ss?history.state:{};V&&(ee||v?r.replace(T.fullPath,xe({scroll:v&&w&&w.scroll},we)):r.push(T.fullPath,we)),l.value=T,at(T,j,V,v),it()}let pe;function Se(){pe||(pe=r.listen((T,j,V)=>{if(!gn.listening)return;const ee=C(T),we=G(ee);if(we){O(xe(we,{replace:!0,force:!0}),ee).catch(Is);return}d=ee;const m=l.value;ss&&sg(Ga(m.fullPath,V.delta),eo()),ne(ee,m).catch(v=>nn(v,12)?v:nn(v,2)?(O(xe(P(v.to),{force:!0}),ee).then(w=>{nn(w,20)&&!V.delta&&V.type===Gs.pop&&r.go(-1,!1)}).catch(Is),Promise.reject()):(V.delta&&r.go(-V.delta,!1),be(v,ee,m))).then(v=>{v=v||B(ee,m,!1),v&&(V.delta&&!nn(v,8)?r.go(-V.delta,!1):V.type===Gs.pop&&nn(v,20)&&r.go(-1,!1)),q(ee,m,v)}).catch(Is)}))}let Me=Ss(),ge=Ss(),X;function be(T,j,V){it(T);const ee=ge.list();return ee.length?ee.forEach(we=>we(T,j,V)):console.error(T),Promise.reject(T)}function Ye(){return X&&l.value!==vn?Promise.resolve():new Promise((T,j)=>{Me.add([T,j])})}function it(T){return X||(X=!T,Se(),Me.list().forEach(([j,V])=>T?V(T):j()),Me.reset()),T}function at(T,j,V,ee){const{scrollBehavior:we}=e;if(!ss||!we)return Promise.resolve();const m=!V&&rg(Ga(T.fullPath,0))||(ee||!V)&&history.state&&history.state.scroll||null;return xt().then(()=>we(T,j,m)).then(v=>v&&ng(v)).catch(v=>be(v,T,j))}const Ze=T=>r.go(T);let Xt;const Be=new Set,gn={currentRoute:l,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:E,getRoutes:S,resolve:C,options:e,push:A,replace:h,go:Ze,back:()=>Ze(-1),forward:()=>Ze(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:ge.add,isReady:Ye,install(T){const j=this;T.component("RouterLink",Rg),T.component("RouterView",Ig),T.config.globalProperties.$router=j,Object.defineProperty(T.config.globalProperties,"$route",{enumerable:!0,get:()=>Ce(l)}),ss&&!Xt&&l.value===vn&&(Xt=!0,A(r.location).catch(we=>{}));const V={};for(const we in vn)Object.defineProperty(V,we,{get:()=>l.value[we],enumerable:!0});T.provide(to,j),T.provide(ji,hc(V)),T.provide(si,l);const ee=T.unmount;Be.add(T),T.unmount=function(){Be.delete(T),Be.size<1&&(d=vn,pe&&pe(),pe=null,l.value=vn,Xt=!1,X=!1),ee()}}};function Q(T){return T.reduce((j,V)=>j.then(()=>I(V)),Promise.resolve())}return gn}function Fg(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(d=>hs(d,a))?s.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(d=>hs(d,l))||r.push(l))}return[n,s,r]}function no(){return St(to)}function Ui(e){return St(ji)}const Je=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Dg={key:0,class:"global-loading"},Mg={class:"loading-container"},Ng={class:"loading-text"},Vg={__name:"GlobalLoading",props:{isLoading:{type:Boolean,default:!1},message:{type:String,default:""}},setup(e){return(t,n)=>(R(),On(ou,{name:"fade"},{default:Ge(()=>[e.isLoading?(R(),x("div",Dg,[u("div",Mg,[n[0]||(n[0]=u("div",{class:"spinner"},null,-1)),u("p",Ng,K(e.message||"Loading..."),1)])])):Y("",!0)]),_:1}))}},Lg=Je(Vg,[["__scopeId","data-v-548b1915"]]),jg={key:0,class:"error-boundary"},Ug={class:"error-container"},qg={class:"error-message"},Bg={key:0,class:"error-details"},Gg={__name:"ErrorBoundary",props:{fallback:{type:Function,default:null}},setup(e){const t=e,n=no(),s=ve(null),r=ve(null),o=ve(!1),i=te(()=>{var f,p,g,y,S;return s.value?(f=s.value.message)!=null&&f.includes("Network Error")?"Network error. Please check your internet connection and try again.":((p=s.value.response)==null?void 0:p.status)===404?"The requested resource was not found.":((g=s.value.response)==null?void 0:g.status)===403?"You do not have permission to access this resource.":((y=s.value.response)==null?void 0:y.status)===401?"Your session has expired. Please log in again.":((S=s.value.response)==null?void 0:S.status)>=500?"Server error. Please try again later.":s.value.message||"An unexpected error occurred.":""}),a=te(()=>{if(!s.value)return"";let f="";return s.value.stack&&(f+=`Error Stack:
${s.value.stack}

`),r.value&&(f+=`Component: ${r.value.component}
`,f+=`Props: ${JSON.stringify(r.value.props,null,2)}
`),s.value.response&&(f+=`Response Status: ${s.value.response.status}
`,f+=`Response Data: ${JSON.stringify(s.value.response.data,null,2)}
`),f}),l=()=>{o.value=!o.value},d=()=>{s.value=null,r.value=null,t.fallback&&typeof t.fallback=="function"?t.fallback():n.go(0)},c=()=>{s.value=null,r.value=null,n.push("/admin/dashboard")};return kc((f,p,g)=>{var y;return console.error("Error captured by boundary:",f),s.value=f,r.value={component:((y=p==null?void 0:p.$options)==null?void 0:y.name)||"Unknown",props:(p==null?void 0:p.$props)||{},info:g},!1}),An("errorBoundary",{setError:f=>{s.value=f},clearError:()=>{s.value=null,r.value=null}}),(f,p)=>(R(),x("div",null,[s.value?(R(),x("div",jg,[u("div",Ug,[p[2]||(p[2]=u("div",{class:"error-icon"},[u("i",{class:"fas fa-exclamation-triangle"})],-1)),p[3]||(p[3]=u("h2",{class:"error-title"},"Something went wrong",-1)),u("p",qg,K(i.value),1),u("div",{class:"error-actions"},[u("button",{class:"button is-primary",onClick:d},p[0]||(p[0]=[u("span",{class:"icon"},[u("i",{class:"fas fa-sync-alt"})],-1),u("span",null,"Try Again",-1)])),u("button",{class:"button is-light",onClick:c},p[1]||(p[1]=[u("span",{class:"icon"},[u("i",{class:"fas fa-home"})],-1),u("span",null,"Go to Dashboard",-1)]))]),o.value?(R(),x("div",Bg,[u("pre",null,K(a.value),1)])):Y("",!0),u("button",{class:"button is-small is-text",onClick:l},K(o.value?"Hide Details":"Show Details"),1)])])):Lf(f.$slots,"default",{key:1},void 0)]))}},Hg=Je(Gg,[["__scopeId","data-v-7bf65f90"]]);function $u(e,t){return function(){return e.apply(t,arguments)}}const{toString:zg}=Object.prototype,{getPrototypeOf:qi}=Object,{iterator:so,toStringTag:Fu}=Symbol,ro=(e=>t=>{const n=zg.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Ht=e=>(e=e.toLowerCase(),t=>ro(t)===e),oo=e=>t=>typeof t===e,{isArray:vs}=Array,Hs=oo("undefined");function Kg(e){return e!==null&&!Hs(e)&&e.constructor!==null&&!Hs(e.constructor)&&Ct(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Du=Ht("ArrayBuffer");function Wg(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Du(e.buffer),t}const Jg=oo("string"),Ct=oo("function"),Mu=oo("number"),io=e=>e!==null&&typeof e=="object",Yg=e=>e===!0||e===!1,vr=e=>{if(ro(e)!=="object")return!1;const t=qi(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Fu in e)&&!(so in e)},Zg=Ht("Date"),Qg=Ht("File"),Xg=Ht("Blob"),em=Ht("FileList"),tm=e=>io(e)&&Ct(e.pipe),nm=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ct(e.append)&&((t=ro(e))==="formdata"||t==="object"&&Ct(e.toString)&&e.toString()==="[object FormData]"))},sm=Ht("URLSearchParams"),[rm,om,im,am]=["ReadableStream","Request","Response","Headers"].map(Ht),lm=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function tr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),vs(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(s=0;s<i;s++)a=o[s],t.call(null,e[a],a,e)}}function Nu(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Ln=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Vu=e=>!Hs(e)&&e!==Ln;function ri(){const{caseless:e}=Vu(this)&&this||{},t={},n=(s,r)=>{const o=e&&Nu(t,r)||r;vr(t[o])&&vr(s)?t[o]=ri(t[o],s):vr(s)?t[o]=ri({},s):vs(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&tr(arguments[s],n);return t}const cm=(e,t,n,{allOwnKeys:s}={})=>(tr(t,(r,o)=>{n&&Ct(r)?e[o]=$u(r,n):e[o]=r},{allOwnKeys:s}),e),um=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),dm=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},fm=(e,t,n,s)=>{let r,o,i;const a={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&qi(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},hm=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},pm=e=>{if(!e)return null;if(vs(e))return e;let t=e.length;if(!Mu(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},gm=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&qi(Uint8Array)),mm=(e,t)=>{const s=(e&&e[so]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},vm=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},ym=Ht("HTMLFormElement"),_m=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),ol=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),bm=Ht("RegExp"),Lu=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};tr(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},wm=e=>{Lu(e,(t,n)=>{if(Ct(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Ct(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Em=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return vs(e)?s(e):s(String(e).split(t)),n},Sm=()=>{},Cm=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Am(e){return!!(e&&Ct(e.append)&&e[Fu]==="FormData"&&e[so])}const Om=e=>{const t=new Array(10),n=(s,r)=>{if(io(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=vs(s)?[]:{};return tr(s,(i,a)=>{const l=n(i,r+1);!Hs(l)&&(o[a]=l)}),t[r]=void 0,o}}return s};return n(e,0)},Tm=Ht("AsyncFunction"),Rm=e=>e&&(io(e)||Ct(e))&&Ct(e.then)&&Ct(e.catch),ju=((e,t)=>e?setImmediate:t?((n,s)=>(Ln.addEventListener("message",({source:r,data:o})=>{r===Ln&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),Ln.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ct(Ln.postMessage)),xm=typeof queueMicrotask<"u"?queueMicrotask.bind(Ln):typeof process<"u"&&process.nextTick||ju,Pm=e=>e!=null&&Ct(e[so]),k={isArray:vs,isArrayBuffer:Du,isBuffer:Kg,isFormData:nm,isArrayBufferView:Wg,isString:Jg,isNumber:Mu,isBoolean:Yg,isObject:io,isPlainObject:vr,isReadableStream:rm,isRequest:om,isResponse:im,isHeaders:am,isUndefined:Hs,isDate:Zg,isFile:Qg,isBlob:Xg,isRegExp:bm,isFunction:Ct,isStream:tm,isURLSearchParams:sm,isTypedArray:gm,isFileList:em,forEach:tr,merge:ri,extend:cm,trim:lm,stripBOM:um,inherits:dm,toFlatObject:fm,kindOf:ro,kindOfTest:Ht,endsWith:hm,toArray:pm,forEachEntry:mm,matchAll:vm,isHTMLForm:ym,hasOwnProperty:ol,hasOwnProp:ol,reduceDescriptors:Lu,freezeMethods:wm,toObjectSet:Em,toCamelCase:_m,noop:Sm,toFiniteNumber:Cm,findKey:Nu,global:Ln,isContextDefined:Vu,isSpecCompliantForm:Am,toJSONObject:Om,isAsyncFn:Tm,isThenable:Rm,setImmediate:ju,asap:xm,isIterable:Pm};function me(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}k.inherits(me,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:k.toJSONObject(this.config),code:this.code,status:this.status}}});const Uu=me.prototype,qu={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{qu[e]={value:e}});Object.defineProperties(me,qu);Object.defineProperty(Uu,"isAxiosError",{value:!0});me.from=(e,t,n,s,r,o)=>{const i=Object.create(Uu);return k.toFlatObject(e,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),me.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const km=null;function oi(e){return k.isPlainObject(e)||k.isArray(e)}function Bu(e){return k.endsWith(e,"[]")?e.slice(0,-2):e}function il(e,t,n){return e?e.concat(t).map(function(r,o){return r=Bu(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function Im(e){return k.isArray(e)&&!e.some(oi)}const $m=k.toFlatObject(k,{},null,function(t){return/^is[A-Z]/.test(t)});function ao(e,t,n){if(!k.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=k.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,E){return!k.isUndefined(E[S])});const s=n.metaTokens,r=n.visitor||c,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&k.isSpecCompliantForm(t);if(!k.isFunction(r))throw new TypeError("visitor must be a function");function d(y){if(y===null)return"";if(k.isDate(y))return y.toISOString();if(!l&&k.isBlob(y))throw new me("Blob is not supported. Use a Buffer instead.");return k.isArrayBuffer(y)||k.isTypedArray(y)?l&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,S,E){let C=y;if(y&&!E&&typeof y=="object"){if(k.endsWith(S,"{}"))S=s?S:S.slice(0,-2),y=JSON.stringify(y);else if(k.isArray(y)&&Im(y)||(k.isFileList(y)||k.endsWith(S,"[]"))&&(C=k.toArray(y)))return S=Bu(S),C.forEach(function(D,A){!(k.isUndefined(D)||D===null)&&t.append(i===!0?il([S],A,o):i===null?S:S+"[]",d(D))}),!1}return oi(y)?!0:(t.append(il(E,S,o),d(y)),!1)}const f=[],p=Object.assign($m,{defaultVisitor:c,convertValue:d,isVisitable:oi});function g(y,S){if(!k.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+S.join("."));f.push(y),k.forEach(y,function(C,P){(!(k.isUndefined(C)||C===null)&&r.call(t,C,k.isString(P)?P.trim():P,S,p))===!0&&g(C,S?S.concat(P):[P])}),f.pop()}}if(!k.isObject(e))throw new TypeError("data must be an object");return g(e),t}function al(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Bi(e,t){this._pairs=[],e&&ao(e,this,t)}const Gu=Bi.prototype;Gu.append=function(t,n){this._pairs.push([t,n])};Gu.toString=function(t){const n=t?function(s){return t.call(this,s,al)}:al;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function Fm(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Hu(e,t,n){if(!t)return e;const s=n&&n.encode||Fm;k.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=k.isURLSearchParams(t)?t.toString():new Bi(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class ll{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){k.forEach(this.handlers,function(s){s!==null&&t(s)})}}const zu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Dm=typeof URLSearchParams<"u"?URLSearchParams:Bi,Mm=typeof FormData<"u"?FormData:null,Nm=typeof Blob<"u"?Blob:null,Vm={isBrowser:!0,classes:{URLSearchParams:Dm,FormData:Mm,Blob:Nm},protocols:["http","https","file","blob","url","data"]},Gi=typeof window<"u"&&typeof document<"u",ii=typeof navigator=="object"&&navigator||void 0,Lm=Gi&&(!ii||["ReactNative","NativeScript","NS"].indexOf(ii.product)<0),jm=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Um=Gi&&window.location.href||"http://localhost",qm=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Gi,hasStandardBrowserEnv:Lm,hasStandardBrowserWebWorkerEnv:jm,navigator:ii,origin:Um},Symbol.toStringTag,{value:"Module"})),ft={...qm,...Vm};function Bm(e,t){return ao(e,new ft.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return ft.isNode&&k.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Gm(e){return k.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Hm(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function Ku(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=o>=n.length;return i=!i&&k.isArray(r)?r.length:i,l?(k.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!a):((!r[i]||!k.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&k.isArray(r[i])&&(r[i]=Hm(r[i])),!a)}if(k.isFormData(e)&&k.isFunction(e.entries)){const n={};return k.forEachEntry(e,(s,r)=>{t(Gm(s),r,n,0)}),n}return null}function zm(e,t,n){if(k.isString(e))try{return(t||JSON.parse)(e),k.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const nr={transitional:zu,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=k.isObject(t);if(o&&k.isHTMLForm(t)&&(t=new FormData(t)),k.isFormData(t))return r?JSON.stringify(Ku(t)):t;if(k.isArrayBuffer(t)||k.isBuffer(t)||k.isStream(t)||k.isFile(t)||k.isBlob(t)||k.isReadableStream(t))return t;if(k.isArrayBufferView(t))return t.buffer;if(k.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Bm(t,this.formSerializer).toString();if((a=k.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ao(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),zm(t)):t}],transformResponse:[function(t){const n=this.transitional||nr.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(k.isResponse(t)||k.isReadableStream(t))return t;if(t&&k.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?me.from(a,me.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ft.classes.FormData,Blob:ft.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};k.forEach(["delete","get","head","post","put","patch"],e=>{nr.headers[e]={}});const Km=k.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Wm=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&Km[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},cl=Symbol("internals");function Cs(e){return e&&String(e).trim().toLowerCase()}function yr(e){return e===!1||e==null?e:k.isArray(e)?e.map(yr):String(e)}function Jm(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Ym=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function $o(e,t,n,s,r){if(k.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!k.isString(t)){if(k.isString(s))return t.indexOf(s)!==-1;if(k.isRegExp(s))return s.test(t)}}function Zm(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Qm(e,t){const n=k.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}let At=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(a,l,d){const c=Cs(l);if(!c)throw new Error("header name must be a non-empty string");const f=k.findKey(r,c);(!f||r[f]===void 0||d===!0||d===void 0&&r[f]!==!1)&&(r[f||l]=yr(a))}const i=(a,l)=>k.forEach(a,(d,c)=>o(d,c,l));if(k.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(k.isString(t)&&(t=t.trim())&&!Ym(t))i(Wm(t),n);else if(k.isObject(t)&&k.isIterable(t)){let a={},l,d;for(const c of t){if(!k.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[d=c[0]]=(l=a[d])?k.isArray(l)?[...l,c[1]]:[l,c[1]]:c[1]}i(a,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=Cs(t),t){const s=k.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Jm(r);if(k.isFunction(n))return n.call(this,r,s);if(k.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Cs(t),t){const s=k.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||$o(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=Cs(i),i){const a=k.findKey(s,i);a&&(!n||$o(s,s[a],a,n))&&(delete s[a],r=!0)}}return k.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||$o(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return k.forEach(this,(r,o)=>{const i=k.findKey(s,o);if(i){n[i]=yr(r),delete n[o];return}const a=t?Zm(o):String(o).trim();a!==o&&delete n[o],n[a]=yr(r),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return k.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&k.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[cl]=this[cl]={accessors:{}}).accessors,r=this.prototype;function o(i){const a=Cs(i);s[a]||(Qm(r,i),s[a]=!0)}return k.isArray(t)?t.forEach(o):o(t),this}};At.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);k.reduceDescriptors(At.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});k.freezeMethods(At);function Fo(e,t){const n=this||nr,s=t||n,r=At.from(s.headers);let o=s.data;return k.forEach(e,function(a){o=a.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Wu(e){return!!(e&&e.__CANCEL__)}function ys(e,t,n){me.call(this,e??"canceled",me.ERR_CANCELED,t,n),this.name="CanceledError"}k.inherits(ys,me,{__CANCEL__:!0});function Ju(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new me("Request failed with status code "+n.status,[me.ERR_BAD_REQUEST,me.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Xm(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function ev(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const d=Date.now(),c=s[o];i||(i=d),n[r]=l,s[r]=d;let f=o,p=0;for(;f!==r;)p+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),d-i<t)return;const g=c&&d-c;return g?Math.round(p*1e3/g):void 0}}function tv(e,t){let n=0,s=1e3/t,r,o;const i=(d,c=Date.now())=>{n=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,d)};return[(...d)=>{const c=Date.now(),f=c-n;f>=s?i(d,c):(r=d,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const Ir=(e,t,n=3)=>{let s=0;const r=ev(50,250);return tv(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-s,d=r(l),c=i<=a;s=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:d||void 0,estimated:d&&a&&c?(a-i)/d:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},ul=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},dl=e=>(...t)=>k.asap(()=>e(...t)),nv=ft.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,ft.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(ft.origin),ft.navigator&&/(msie|trident)/i.test(ft.navigator.userAgent)):()=>!0,sv=ft.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];k.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),k.isString(s)&&i.push("path="+s),k.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function rv(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function ov(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Yu(e,t,n){let s=!rv(t);return e&&(s||n==!1)?ov(e,t):t}const fl=e=>e instanceof At?{...e}:e;function zn(e,t){t=t||{};const n={};function s(d,c,f,p){return k.isPlainObject(d)&&k.isPlainObject(c)?k.merge.call({caseless:p},d,c):k.isPlainObject(c)?k.merge({},c):k.isArray(c)?c.slice():c}function r(d,c,f,p){if(k.isUndefined(c)){if(!k.isUndefined(d))return s(void 0,d,f,p)}else return s(d,c,f,p)}function o(d,c){if(!k.isUndefined(c))return s(void 0,c)}function i(d,c){if(k.isUndefined(c)){if(!k.isUndefined(d))return s(void 0,d)}else return s(void 0,c)}function a(d,c,f){if(f in t)return s(d,c);if(f in e)return s(void 0,d)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(d,c,f)=>r(fl(d),fl(c),f,!0)};return k.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=l[c]||r,p=f(e[c],t[c],c);k.isUndefined(p)&&f!==a||(n[c]=p)}),n}const Zu=e=>{const t=zn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=At.from(i),t.url=Hu(Yu(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(k.isFormData(n)){if(ft.hasStandardBrowserEnv||ft.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[d,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([d||"multipart/form-data",...c].join("; "))}}if(ft.hasStandardBrowserEnv&&(s&&k.isFunction(s)&&(s=s(t)),s||s!==!1&&nv(t.url))){const d=r&&o&&sv.read(o);d&&i.set(r,d)}return t},iv=typeof XMLHttpRequest<"u",av=iv&&function(e){return new Promise(function(n,s){const r=Zu(e);let o=r.data;const i=At.from(r.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:d}=r,c,f,p,g,y;function S(){g&&g(),y&&y(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let E=new XMLHttpRequest;E.open(r.method.toUpperCase(),r.url,!0),E.timeout=r.timeout;function C(){if(!E)return;const D=At.from("getAllResponseHeaders"in E&&E.getAllResponseHeaders()),h={data:!a||a==="text"||a==="json"?E.responseText:E.response,status:E.status,statusText:E.statusText,headers:D,config:e,request:E};Ju(function(O){n(O),S()},function(O){s(O),S()},h),E=null}"onloadend"in E?E.onloadend=C:E.onreadystatechange=function(){!E||E.readyState!==4||E.status===0&&!(E.responseURL&&E.responseURL.indexOf("file:")===0)||setTimeout(C)},E.onabort=function(){E&&(s(new me("Request aborted",me.ECONNABORTED,e,E)),E=null)},E.onerror=function(){s(new me("Network Error",me.ERR_NETWORK,e,E)),E=null},E.ontimeout=function(){let A=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const h=r.transitional||zu;r.timeoutErrorMessage&&(A=r.timeoutErrorMessage),s(new me(A,h.clarifyTimeoutError?me.ETIMEDOUT:me.ECONNABORTED,e,E)),E=null},o===void 0&&i.setContentType(null),"setRequestHeader"in E&&k.forEach(i.toJSON(),function(A,h){E.setRequestHeader(h,A)}),k.isUndefined(r.withCredentials)||(E.withCredentials=!!r.withCredentials),a&&a!=="json"&&(E.responseType=r.responseType),d&&([p,y]=Ir(d,!0),E.addEventListener("progress",p)),l&&E.upload&&([f,g]=Ir(l),E.upload.addEventListener("progress",f),E.upload.addEventListener("loadend",g)),(r.cancelToken||r.signal)&&(c=D=>{E&&(s(!D||D.type?new ys(null,e,E):D),E.abort(),E=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const P=Xm(r.url);if(P&&ft.protocols.indexOf(P)===-1){s(new me("Unsupported protocol "+P+":",me.ERR_BAD_REQUEST,e));return}E.send(o||null)})},lv=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(d){if(!r){r=!0,a();const c=d instanceof Error?d:this.reason;s.abort(c instanceof me?c:new ys(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new me(`timeout ${t} of ms exceeded`,me.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(o):d.removeEventListener("abort",o)}),e=null)};e.forEach(d=>d.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>k.asap(a),l}},cv=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},uv=async function*(e,t){for await(const n of dv(e))yield*cv(n,t)},dv=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},hl=(e,t,n,s)=>{const r=uv(e,t);let o=0,i,a=l=>{i||(i=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:d,value:c}=await r.next();if(d){a(),l.close();return}let f=c.byteLength;if(n){let p=o+=f;n(p)}l.enqueue(new Uint8Array(c))}catch(d){throw a(d),d}},cancel(l){return a(l),r.return()}},{highWaterMark:2})},lo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qu=lo&&typeof ReadableStream=="function",fv=lo&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Xu=(e,...t)=>{try{return!!e(...t)}catch{return!1}},hv=Qu&&Xu(()=>{let e=!1;const t=new Request(ft.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),pl=64*1024,ai=Qu&&Xu(()=>k.isReadableStream(new Response("").body)),$r={stream:ai&&(e=>e.body)};lo&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!$r[t]&&($r[t]=k.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new me(`Response type '${t}' is not supported`,me.ERR_NOT_SUPPORT,s)})})})(new Response);const pv=async e=>{if(e==null)return 0;if(k.isBlob(e))return e.size;if(k.isSpecCompliantForm(e))return(await new Request(ft.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(k.isArrayBufferView(e)||k.isArrayBuffer(e))return e.byteLength;if(k.isURLSearchParams(e)&&(e=e+""),k.isString(e))return(await fv(e)).byteLength},gv=async(e,t)=>{const n=k.toFiniteNumber(e.getContentLength());return n??pv(t)},mv=lo&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:d,headers:c,withCredentials:f="same-origin",fetchOptions:p}=Zu(e);d=d?(d+"").toLowerCase():"text";let g=lv([r,o&&o.toAbortSignal()],i),y;const S=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let E;try{if(l&&hv&&n!=="get"&&n!=="head"&&(E=await gv(c,s))!==0){let h=new Request(t,{method:"POST",body:s,duplex:"half"}),G;if(k.isFormData(s)&&(G=h.headers.get("content-type"))&&c.setContentType(G),h.body){const[O,W]=ul(E,Ir(dl(l)));s=hl(h.body,pl,O,W)}}k.isString(f)||(f=f?"include":"omit");const C="credentials"in Request.prototype;y=new Request(t,{...p,signal:g,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:C?f:void 0});let P=await fetch(y);const D=ai&&(d==="stream"||d==="response");if(ai&&(a||D&&S)){const h={};["status","statusText","headers"].forEach(I=>{h[I]=P[I]});const G=k.toFiniteNumber(P.headers.get("content-length")),[O,W]=a&&ul(G,Ir(dl(a),!0))||[];P=new Response(hl(P.body,pl,O,()=>{W&&W(),S&&S()}),h)}d=d||"text";let A=await $r[k.findKey($r,d)||"text"](P,e);return!D&&S&&S(),await new Promise((h,G)=>{Ju(h,G,{data:A,headers:At.from(P.headers),status:P.status,statusText:P.statusText,config:e,request:y})})}catch(C){throw S&&S(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new me("Network Error",me.ERR_NETWORK,e,y),{cause:C.cause||C}):me.from(C,C&&C.code,e,y)}}),li={http:km,xhr:av,fetch:mv};k.forEach(li,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const gl=e=>`- ${e}`,vv=e=>k.isFunction(e)||e===null||e===!1,ed={getAdapter:e=>{e=k.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!vv(n)&&(s=li[(i=String(n)).toLowerCase()],s===void 0))throw new me(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(gl).join(`
`):" "+gl(o[0]):"as no adapter specified";throw new me("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:li};function Do(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ys(null,e)}function ml(e){return Do(e),e.headers=At.from(e.headers),e.data=Fo.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ed.getAdapter(e.adapter||nr.adapter)(e).then(function(s){return Do(e),s.data=Fo.call(e,e.transformResponse,s),s.headers=At.from(s.headers),s},function(s){return Wu(s)||(Do(e),s&&s.response&&(s.response.data=Fo.call(e,e.transformResponse,s.response),s.response.headers=At.from(s.response.headers))),Promise.reject(s)})}const td="1.9.0",co={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{co[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const vl={};co.transitional=function(t,n,s){function r(o,i){return"[Axios v"+td+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,a)=>{if(t===!1)throw new me(r(i," has been removed"+(n?" in "+n:"")),me.ERR_DEPRECATED);return n&&!vl[i]&&(vl[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};co.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function yv(e,t,n){if(typeof e!="object")throw new me("options must be an object",me.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const a=e[o],l=a===void 0||i(a,o,e);if(l!==!0)throw new me("option "+o+" must be "+l,me.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new me("Unknown option "+o,me.ERR_BAD_OPTION)}}const _r={assertOptions:yv,validators:co},Kt=_r.validators;let qn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new ll,response:new ll}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=zn(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&_r.assertOptions(s,{silentJSONParsing:Kt.transitional(Kt.boolean),forcedJSONParsing:Kt.transitional(Kt.boolean),clarifyTimeoutError:Kt.transitional(Kt.boolean)},!1),r!=null&&(k.isFunction(r)?n.paramsSerializer={serialize:r}:_r.assertOptions(r,{encode:Kt.function,serialize:Kt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),_r.assertOptions(n,{baseUrl:Kt.spelling("baseURL"),withXsrfToken:Kt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&k.merge(o.common,o[n.method]);o&&k.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=At.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(n)===!1||(l=l&&S.synchronous,a.unshift(S.fulfilled,S.rejected))});const d=[];this.interceptors.response.forEach(function(S){d.push(S.fulfilled,S.rejected)});let c,f=0,p;if(!l){const y=[ml.bind(this),void 0];for(y.unshift.apply(y,a),y.push.apply(y,d),p=y.length,c=Promise.resolve(n);f<p;)c=c.then(y[f++],y[f++]);return c}p=a.length;let g=n;for(f=0;f<p;){const y=a[f++],S=a[f++];try{g=y(g)}catch(E){S.call(this,E);break}}try{c=ml.call(this,g)}catch(y){return Promise.reject(y)}for(f=0,p=d.length;f<p;)c=c.then(d[f++],d[f++]);return c}getUri(t){t=zn(this.defaults,t);const n=Yu(t.baseURL,t.url,t.allowAbsoluteUrls);return Hu(n,t.params,t.paramsSerializer)}};k.forEach(["delete","get","head","options"],function(t){qn.prototype[t]=function(n,s){return this.request(zn(s||{},{method:t,url:n,data:(s||{}).data}))}});k.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,a){return this.request(zn(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}qn.prototype[t]=n(),qn.prototype[t+"Form"]=n(!0)});let _v=class nd{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(a=>{s.subscribe(a),o=a}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,a){s.reason||(s.reason=new ys(o,i,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new nd(function(r){t=r}),cancel:t}}};function bv(e){return function(n){return e.apply(null,n)}}function wv(e){return k.isObject(e)&&e.isAxiosError===!0}const ci={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ci).forEach(([e,t])=>{ci[t]=e});function sd(e){const t=new qn(e),n=$u(qn.prototype.request,t);return k.extend(n,qn.prototype,t,{allOwnKeys:!0}),k.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return sd(zn(e,r))},n}const ke=sd(nr);ke.Axios=qn;ke.CanceledError=ys;ke.CancelToken=_v;ke.isCancel=Wu;ke.VERSION=td;ke.toFormData=ao;ke.AxiosError=me;ke.Cancel=ke.CanceledError;ke.all=function(t){return Promise.all(t)};ke.spread=bv;ke.isAxiosError=wv;ke.mergeConfig=zn;ke.AxiosHeaders=At;ke.formToJSON=e=>Ku(k.isHTMLForm(e)?new FormData(e):e);ke.getAdapter=ed.getAdapter;ke.HttpStatusCode=ci;ke.default=ke;const{Axios:IC,AxiosError:$C,CanceledError:FC,isCancel:DC,CancelToken:MC,VERSION:NC,all:VC,Cancel:LC,isAxiosError:jC,spread:UC,toFormData:qC,AxiosHeaders:BC,HttpStatusCode:GC,formToJSON:HC,getAdapter:zC,mergeConfig:KC}=ke,ye=ke.create({baseURL:"/api",headers:{"Content-Type":"application/json"},timeout:3e4});ye.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));ye.interceptors.response.use(e=>(console.log(`API Response [${e.config.method.toUpperCase()}] ${e.config.url}:`,e.status),e),async e=>{if(console.error("API Error:",e),e.response){console.error(`API Error Response [${e.config.method.toUpperCase()}] ${e.config.url}:`,{status:e.response.status,statusText:e.response.statusText,data:e.response.data});const t=e.config;e.response.status===401&&!t._retry&&(t._retry=!0,localStorage.removeItem("user"),localStorage.removeItem("token"),window.location.href="/login")}else e.request?console.error("API Error: No response received",e.request):console.error("API Error: Request setup error",e.message);return Promise.reject(e)});class Ev{async getAll(t={}){try{return await ye.get("/categories/all",{params:t})}catch(n){if(console.error("Error fetching categories:",n),n.response&&n.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await ye.get("/admin/categories",{params:t});throw n}}async getAllRootCategories(t={}){try{return await ye.get("/categories/root",{params:t})}catch(n){if(console.error("Error fetching categories:",n),n.response&&n.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await ye.get("/admin/categories",{params:t});throw n}}async getSubCategories(t,n={}){try{return await ye.get(`/categories/${t}/subcategories`,{params:n})}catch(s){if(console.error("Error fetching subcategories:",s),s.response&&s.response.status===404)return console.warn("subcategories endpoint not found, trying admin endpoint"),await ye.get("/admin/categories",{params:n});throw s}}async getProducts(t,n={}){try{return await ye.get(`/categories/${t}/products`,{params:n})}catch(s){if(console.error("Error fetching categories:",s),s.response&&s.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await ye.get(`/admin/categories/${t}/products`,{params:n});throw s}}async getById(t){try{return await ye.get(`/categories/${t}`)}catch(n){if(console.error(`Error fetching category ${t}:`,n),n.response&&n.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await ye.get(`/admin/categories/${t}`);throw n}}async getBySlug(t){try{return await ye.get(`/categories/slug/${t}`)}catch(n){throw console.error(`Error fetching category ${t}:`,n),n}}async create(t){try{return await ye.post("/categories",t)}catch(n){if(console.error("Error creating category:",n),n.response&&n.response.status===404)return console.warn("Categories endpoint not found, trying admin endpoint"),await ye.post("/admin/categories",t);throw n}}async update(t,n){try{return await ye.put(`/categories/${t}`,n)}catch(s){if(console.error(`Error updating category ${t}:`,s),s.response&&s.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await ye.put(`/admin/categories/${t}`,n);throw s}}async delete(t){try{return await ye.delete(`/categories/${t}`)}catch(n){if(console.error(`Error deleting category ${t}:`,n),n.response&&n.response.status===404)return console.warn("Category endpoint not found, trying admin endpoint"),await ye.delete(`/admin/categories/${t}`);throw n}}async getStats(){try{return await ye.get("/categories/stats")}catch(t){if(console.error("Error fetching category stats:",t),t.response&&t.response.status===404)return console.warn("Category stats endpoint not found, trying admin endpoint"),await ye.get("/admin/categories/stats");throw t}}}const Sn=new Ev,Sv=[{id:1,name:"Ноутбуки та комп'ютери",slug:"laptops-computers",parentId:null,image:null},{id:2,name:"Смартфони, ТВ і електроніка",slug:"smartphones-tv-electronics",parentId:null,image:null},{id:3,name:"Побутова техніка",slug:"household-appliances",parentId:null,image:null},{id:4,name:"Товари для дому",slug:"home-goods",parentId:null,image:null},{id:5,name:"Дача, сад і город",slug:"garden",parentId:null,image:null},{id:6,name:"Спорт і хобі",slug:"sports-hobbies",parentId:null,image:null},{id:7,name:"Краса та здоров'я",slug:"beauty-health",parentId:null,image:null},{id:8,name:"Одяг, взуття та прикраси",slug:"clothing-shoes-jewelry",parentId:null,image:null},{id:101,name:"Ноутбуки",slug:"laptops",parentId:1,image:null},{id:102,name:"Комп'ютери",slug:"computers",parentId:1,image:null},{id:103,name:"Комплектуючі",slug:"components",parentId:1,image:null},{id:1001,name:"Apple Macbook",slug:"apple-macbook",parentId:101,image:null},{id:1002,name:"Acer",slug:"acer",parentId:101,image:null},{id:1003,name:"ASUS",slug:"asus",parentId:101,image:null},{id:1004,name:"Lenovo",slug:"lenovo",parentId:101,image:null},{id:1005,name:"HP (Hewlett Packard)",slug:"hp",parentId:101,image:null},{id:1006,name:"Dell",slug:"dell",parentId:101,image:null},{id:1007,name:"Всі ноутбуки",slug:"all-laptops",parentId:101,image:null},{id:2001,name:"Системні блоки (ПК)",slug:"desktop-pc",parentId:102,image:null},{id:2002,name:"Монітори",slug:"monitors",parentId:102,image:null},{id:2003,name:"Клавіатури та миші",slug:"keyboards-mice",parentId:102,image:null},{id:2004,name:"Комп'ютерні колонки",slug:"pc-speakers",parentId:102,image:null},{id:2005,name:"Програмне забезпечення",slug:"software",parentId:102,image:null},{id:201,name:"Смартфони",slug:"smartphones",parentId:2,image:null},{id:202,name:"Планшети",slug:"tablets",parentId:2,image:null},{id:203,name:"Телевізори",slug:"tvs",parentId:2,image:null},{id:3001,name:"Apple iPad",slug:"apple-ipad",parentId:202,image:null},{id:3002,name:"Samsung",slug:"samsung-tablets",parentId:202,image:null},{id:3003,name:"Lenovo",slug:"lenovo-tablets",parentId:202,image:null},{id:3004,name:"Xiaomi",slug:"xiaomi-tablets",parentId:202,image:null},{id:3005,name:"Усі планшети",slug:"all-tablets",parentId:202,image:null}],Cv={name:"CategoryMenu",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){const n=ve([]),s=ve(null),r=ve(!1),o=ve(null),i=async()=>{r.value=!0,o.value=null;try{const g={pageSize:152},y=await Sn.getAll(g);n.value=y.data.data||[],n.value.length>0&&!s.value&&(s.value=n.value[0])}catch(g){console.warn("Using mock category data as fallback:",g),n.value=Sv,n.value.length>0&&!s.value&&(s.value=n.value[0]),o.value=null}finally{r.value=!1}},a=te(()=>n.value.filter(g=>!g.parentId)),l=te(()=>s.value?n.value.filter(g=>g.parentId===s.value.id):[]),d=te(()=>{if(!s.value)return{};const g={};return n.value.filter(S=>S.parentId===s.value.id).forEach(S=>{const E=n.value.filter(C=>C.parentId===S.id);E.length>0&&(g[S.name]=E)}),g}),c=g=>{s.value=g},f=()=>{t("close")},p=g=>({"Ноутбуки та комп'ютери":"fas fa-laptop","Смартфони, ТВ і електроніка":"fas fa-mobile-alt","Побутова техніка":"fas fa-blender","Товари для дому":"fas fa-home","Дача, сад і город":"fas fa-seedling","Спорт і хобі":"fas fa-running",Спорт:"fas fa-running","Краса та здоров'я":"fas fa-heartbeat","Одяг, взуття та прикраси":"fas fa-tshirt",Одяг:"fas fa-tshirt"})[g.name]||"fas fa-folder";return kn(i),{categories:n,mainCategories:a,selectedCategory:s,subcategories:l,groupedSubcategories:d,isLoading:r,error:o,selectCategory:c,closeMenu:f,getCategoryIcon:p}}},Av={class:"category-menu-content"},Ov={class:"category-menu-sidebar"},Tv={class:"category-list"},Rv=["onMouseenter"],xv={class:"category-icon"},Pv=["href"],kv={class:"category-menu-subcategories"},Iv={key:0,class:"subcategories-container"},$v={class:"subcategory-group-title"},Fv={class:"subcategory-list"},Dv=["href"];function Mv(e,t,n,s,r,o){return n.isOpen?(R(),x("div",{key:0,class:"category-menu-overlay",onClick:t[2]||(t[2]=(...i)=>s.closeMenu&&s.closeMenu(...i))},[u("div",{class:"category-menu-container",onClick:t[1]||(t[1]=cu(()=>{},["stop"]))},[u("div",Av,[u("div",Ov,[u("ul",Tv,[(R(!0),x($e,null,ht(s.mainCategories,i=>(R(),x("li",{key:i.id,class:Te(["category-item",{active:s.selectedCategory===i}]),onMouseenter:a=>s.selectCategory(i)},[u("div",xv,[u("i",{class:Te(s.getCategoryIcon(i))},null,2)]),u("a",{href:`/catalog/${i.slug} `,class:"category-label"},[_e(K(i.name)+" ",1),t[3]||(t[3]=u("i",{class:"fas fa-chevron-right category-arrow"},null,-1))],8,Pv)],42,Rv))),128))])]),u("div",kv,[u("button",{class:"close-button",onClick:t[0]||(t[0]=(...i)=>s.closeMenu&&s.closeMenu(...i))},t[4]||(t[4]=[u("i",{class:"fas fa-times"},null,-1)])),s.selectedCategory?(R(),x("div",Iv,[(R(!0),x($e,null,ht(s.groupedSubcategories,(i,a)=>(R(),x("div",{key:a,class:"subcategory-group"},[u("h3",$v,K(a),1),u("ul",Fv,[(R(!0),x($e,null,ht(i,l=>(R(),x("li",{key:l.id,class:"subcategory-item"},[u("a",{href:`/catalog/${l.slug}`,class:"subcategory-link"},K(l.name),9,Dv)]))),128))])]))),128))])):Y("",!0)])])])])):Y("",!0)}const Nv=Je(Cv,[["render",Mv],["__scopeId","data-v-caf4cade"]]),Vv="data:image/svg+xml,%3csvg%20width='33'%20height='33'%20fill='none'%20viewBox='0%200%2033%2033'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M13.6248%202.45789H2.73926V13.3434H13.6248V2.45789Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M30.7306%202.45789H19.8451V13.3434H30.7306V2.45789Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M30.7306%2019.5637H19.8451V30.4492H30.7306V19.5637Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M13.6248%2019.5637H2.73926V30.4492H13.6248V19.5637Z'%20stroke='%23000000'%20stroke-width='3.73218'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",Lv={name:"CategoryMenuButton",components:{CategoryMenu:Nv},setup(){const e=ve(!1);return{isMenuOpen:e,toggleMenu:()=>{e.value=!e.value},closeMenu:()=>{e.value=!1}}}},jv={class:"category-menu-Button"};function Uv(e,t,n,s,r,o){const i=st("category-menu");return R(),x("div",jv,[u("button",{class:"catalog-btn",onClick:t[0]||(t[0]=(...a)=>s.toggleMenu&&s.toggleMenu(...a))},t[1]||(t[1]=[u("img",{src:Vv,alt:"Klondike",class:"catalog-pic"},null,-1),_e(" Каталог ")])),ae(i,{"is-open":s.isMenuOpen,onClose:s.closeMenu},null,8,["is-open","onClose"])])}const qv=Je(Lv,[["render",Uv],["__scopeId","data-v-c1d8e211"]]),Fr="/assets/logo-DSD9Se8B.svg",Bv={name:"App",components:{GlobalLoading:Lg,ErrorBoundary:Hg,CategoryMenuButton:qv},setup(){const e=er(),t=no(),n=Ui(),s=te(()=>e.getters["auth/isLoggedIn"]),r=te(()=>e.getters["auth/user"]),o=te(()=>e.getters["loading/isLoading"]),i=te(()=>e.getters["loading/loadingMessage"]),a=te(()=>!["Login","Register"].includes(n.name)),l=te(()=>!["Login","Register"].includes(n.name)),d=te(()=>{if(!r.value)return"/";const f=r.value.role;let p=!1;typeof f=="string"?p=f==="Admin":typeof f=="number"?p=f===4:f&&typeof f=="object"&&(f.hasOwnProperty("value")&&(p=f.value==="Admin"||f.value===4),f.hasOwnProperty("name")&&(p=f.name==="Admin"));const g=e.getters["auth/isAdmin"],y=p||g;return console.log("App.vue - User role:",r.value.role),console.log("App.vue - Is admin by role?",p),console.log("App.vue - Is admin by getter?",g),y?"/admin/dashboard":"/dashboard"});return{isLoggedIn:s,currentUser:r,dashboardLink:d,logout:async()=>{await e.dispatch("auth/logout"),t.push("/login")},showHeader:a,showFooter:l,isLoading:o,loadingMessage:i}}},Gv={class:"app-container"},Hv={key:0,class:"header"},zv={class:"header-content"},Kv={class:"logo-container"},Wv={class:"header-left"},Jv={class:"header-actions"},Yv={key:1,class:"footer"};function Zv(e,t,n,s,r,o){const i=st("global-loading"),a=st("router-link"),l=st("category-menu-button"),d=st("router-view"),c=st("error-boundary");return R(),x("div",Gv,[ae(i,{"is-loading":s.isLoading,message:s.loadingMessage},null,8,["is-loading","message"]),s.showHeader?(R(),x("header",Hv,[u("div",zv,[u("div",Kv,[ae(a,{to:"/"},{default:Ge(()=>t[0]||(t[0]=[u("img",{src:Fr,alt:"Klondike",class:"logo"},null,-1)])),_:1})]),u("div",Wv,[ae(l)]),t[5]||(t[5]=u("div",{class:"search-container"},[u("input",{type:"text",class:"search-input",placeholder:"Пошук"}),u("button",{class:"search-btn"},[u("i",{class:"fas fa-search"})])],-1)),u("div",Jv,[ae(a,{to:"/wishlist",class:"header-action-btn"},{default:Ge(()=>t[1]||(t[1]=[u("i",{class:"far fa-heart"},null,-1)])),_:1}),s.isLoggedIn?(R(),On(a,{key:0,to:"/user/profile",class:"header-action-btn"},{default:Ge(()=>t[2]||(t[2]=[u("i",{class:"far fa-user"},null,-1)])),_:1})):(R(),On(a,{key:1,to:"/login",class:"header-action-btn"},{default:Ge(()=>t[3]||(t[3]=[u("i",{class:"far fa-user"},null,-1)])),_:1})),ae(a,{to:"/cart",class:"header-action-btn cart-btn"},{default:Ge(()=>t[4]||(t[4]=[u("i",{class:"fas fa-shopping-cart"},null,-1)])),_:1})])])])):Y("",!0),ae(c,null,{default:Ge(()=>[ae(d,null,{default:Ge(({Component:f})=>[ae(ou,{name:"fade",mode:"out-in"},{default:Ge(()=>[(R(),On(Jr(f),{key:e.$route.fullPath}))]),_:2},1024)]),_:1})]),_:1}),s.showFooter?(R(),x("footer",Yv,t[6]||(t[6]=[hn('<div class="footer-content"><div class="footer-logo"><img src="'+Fr+'" alt="Klondike" class="logo"></div><div class="footer-section"><h3 class="footer-title">Інформація про компанію</h3><ul class="footer-links"><li><a href="#">Про нас</a></li><li><a href="#">Контакти</a></li><li><a href="#">Магазини</a></li><li><a href="#">Вакансії</a></li></ul></div><div class="footer-section"><h3 class="footer-title">Допомога покупцеві</h3><ul class="footer-links"><li><a href="#">Центр допомоги клієнтам</a></li><li><a href="#">Доставка та оплата</a></li><li><a href="#">Обмін і повернення товару</a></li><li><a href="#">Гарантії</a></li><li><a href="#">Сервісні центри</a></li></ul></div><div class="footer-section"><h3 class="footer-title">Номер телефону</h3><ul class="footer-links"><li><a href="#">Пошта</a></li></ul><div class="social-links"><a href="#" class="social-link"><i class="fab fa-telegram"></i></a><a href="#" class="social-link"><i class="fab fa-youtube"></i></a><a href="#" class="social-link"><i class="fab fa-instagram"></i></a><a href="#" class="social-link"><i class="fab fa-facebook"></i></a></div></div></div><div class="copyright"> Всі права захищені </div>',2)]))):Y("",!0)])}const Qv=Je(Bv,[["render",Zv]]),Xv="modulepreload",ey=function(e){return"/"+e},yl={},Ie=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let i=function(d){return Promise.all(d.map(c=>Promise.resolve(c).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),l=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));r=i(n.map(d=>{if(d=ey(d),d in yl)return;yl[d]=!0;const c=d.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${f}`))return;const p=document.createElement("link");if(p.rel=c?"stylesheet":Xv,c||(p.as="script"),p.crossOrigin="",p.href=d,l&&p.setAttribute("nonce",l),document.head.appendChild(p),c)return new Promise((g,y)=>{p.addEventListener("load",g),p.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${d}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return r.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})},Nt=new Map,ty=(e,t)=>`${t}:${e}`,ny=()=>{Nt.forEach((e,t)=>{e.abort(),Nt.delete(t)})},sy=e=>{Nt.forEach((t,n)=>{n.includes(e)&&(t.abort(),Nt.delete(n))})},ry=async(e,t,n={})=>{const s=n.method||"GET",r=ty(t,s);Nt.has(r)&&(Nt.get(r).abort(),Nt.delete(r));const o=new AbortController;Nt.set(r,o);try{const i=await e(t,{...n,signal:o.signal});return Nt.delete(r),i}catch(i){if(Nt.delete(r),!ke.isCancel(i)&&i.name!=="AbortError")throw i;return{aborted:!0}}},Ve={cancelAllRequests:ny,cancelRequestsForRoute:sy,createCancellableRequest:ry,pendingRequests:Nt},an=ke.create({baseURL:"http://localhost:5296",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4});an.interceptors.request.use(e=>{Qe.dispatch("loading/startRequest");const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(Qe.dispatch("loading/finishRequest"),Promise.reject(e)));an.interceptors.response.use(e=>(Qe.dispatch("loading/finishRequest"),e),e=>{var t,n,s;return Qe.dispatch("loading/finishRequest"),ke.isCancel(e)?(console.log("Request cancelled:",e.message),Promise.reject(e)):(e.response&&e.response.status===401&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.pathname!=="/login"&&(window.location.href="/login")),e.response&&e.response.status===403&&console.error("Permission denied:",e.response.data.message||"You do not have permission to perform this action"),e.response&&e.response.status===404&&console.error("Resource not found:",e.response.data.message||"The requested resource was not found"),e.response&&e.response.status===422&&console.error("Validation error:",e.response.data.errors||e.response.data.message||"Validation failed"),e.response&&e.response.status>=500&&console.error("Server error:",e.response.data.message||"An unexpected server error occurred"),e.message==="Network Error"&&console.error("Network error: Please check your internet connection"),e.code==="ECONNABORTED"&&(console.error("Request timeout: The server took too long to respond"),e.isTimeout=!0),console.error("API Error:",{url:(t=e.config)==null?void 0:t.url,method:(n=e.config)==null?void 0:n.method,status:(s=e.response)==null?void 0:s.status,message:e.message,code:e.code}),Promise.reject(e))});const Xe={async get(e,t={}){try{const n=new AbortController,s={...t,signal:n.signal},r=`GET:${e}`;Ve.pendingRequests.has(r)&&Ve.pendingRequests.get(r).abort(),Ve.pendingRequests.set(r,n);const o=await an.get(e,s);return Ve.pendingRequests.delete(r),o}catch(n){throw ke.isCancel(n)||console.error(`GET ${e} error:`,n),n}},async post(e,t={},n={}){try{const s=new AbortController,r={...n,signal:s.signal},o=`POST:${e}`;Ve.pendingRequests.has(o)&&Ve.pendingRequests.get(o).abort(),Ve.pendingRequests.set(o,s);const i=await an.post(e,t,r);return Ve.pendingRequests.delete(o),i}catch(s){throw ke.isCancel(s)||console.error(`POST ${e} error:`,s),s}},async put(e,t={},n={}){try{const s=new AbortController,r={...n,signal:s.signal},o=`PUT:${e}`;Ve.pendingRequests.has(o)&&Ve.pendingRequests.get(o).abort(),Ve.pendingRequests.set(o,s);const i=await an.put(e,t,r);return Ve.pendingRequests.delete(o),i}catch(s){throw ke.isCancel(s)||console.error(`PUT ${e} error:`,s),s}},async patch(e,t={},n={}){try{const s=new AbortController,r={...n,signal:s.signal},o=`PATCH:${e}`;Ve.pendingRequests.has(o)&&Ve.pendingRequests.get(o).abort(),Ve.pendingRequests.set(o,s);const i=await an.patch(e,t,r);return Ve.pendingRequests.delete(o),i}catch(s){throw ke.isCancel(s)||console.error(`PATCH ${e} error:`,s),s}},async delete(e,t={}){try{const n=new AbortController,s={...t,signal:n.signal},r=`DELETE:${e}`;Ve.pendingRequests.has(r)&&Ve.pendingRequests.get(r).abort(),Ve.pendingRequests.set(r,n);const o=await an.delete(e,s);return Ve.pendingRequests.delete(r),o}catch(n){throw ke.isCancel(n)||console.error(`DELETE ${e} error:`,n),n}},async upload(e,t,n=null,s={}){try{const r=new AbortController,o={headers:{"Content-Type":"multipart/form-data"},...s,signal:r.signal};n&&(o.onUploadProgress=n);const i=`UPLOAD:${e}`;Ve.pendingRequests.has(i)&&Ve.pendingRequests.get(i).abort(),Ve.pendingRequests.set(i,r);const a=await an.post(e,t,o);return Ve.pendingRequests.delete(i),a}catch(r){throw ke.isCancel(r)||console.error(`UPLOAD ${e} error:`,r),r}},cancelAllRequests(){Ve.cancelAllRequests()},cancelRequestsForRoute(e){Ve.cancelRequestsForRoute(e)}},oy=Object.freeze(Object.defineProperty({__proto__:null,api:an,default:Xe},Symbol.toStringTag,{value:"Module"})),Mo="/api/auth/";class iy{async login(t,n){try{console.log("Attempting login with email:",t);const s=await Xe.post(Mo+"login",{email:t,password:n});if(console.log("Full login response:",s),console.log("Response data:",s.data),!s||!s.data)throw console.error("Empty response or no data"),new Error("Empty response from server");const r=s.data;let o,i;if(r.success&&r.data?(o=r.data.token,i=r.data.user,console.log("Found token and user in ApiResponse.data format")):(o=r.token,i=r.user,console.log("Found token and user in direct format")),!o)throw console.error("No token in response:",r),new Error("No authentication token received");if(!i)throw console.error("No user data in response:",r),new Error("No user data received");return console.log("User data from login:",i),console.log("User role:",i.role),localStorage.setItem("user",JSON.stringify(i)),localStorage.setItem("token",o),{token:o,user:i}}catch(s){throw console.error("Login error:",s),s.response&&(console.error("Error response data:",s.response.data),console.error("Error response status:",s.response.status)),localStorage.removeItem("user"),localStorage.removeItem("token"),s}}async googleLogin(t){try{console.log("Attempting Google login with token");const n=await Xe.post(Mo+"google-login",{idToken:t});if(console.log("Full Google login response:",n),console.log("Google login response data:",n.data),!n||!n.data)throw console.error("Empty response or no data from Google login"),new Error("Empty response from server");const s=n.data;let r,o;if(s.success&&s.data?(r=s.data.token,o=s.data.user,console.log("Found token and user in ApiResponse.data format for Google login")):(r=s.token,o=s.user,console.log("Found token and user in direct format for Google login")),!r)throw console.error("No token in Google login response:",s),new Error("No authentication token received");if(!o)throw console.error("No user data in Google login response:",s),new Error("No user data received");return console.log("User data from Google login:",o),console.log("User role from Google login:",o.role),localStorage.setItem("user",JSON.stringify(o)),localStorage.setItem("token",r),{token:r,user:o}}catch(n){throw console.error("Google login error:",n),n.response&&(console.error("Error response data:",n.response.data),console.error("Error response status:",n.response.status)),localStorage.removeItem("user"),localStorage.removeItem("token"),n}}logout(){localStorage.removeItem("user"),localStorage.removeItem("token")}async register(t){return console.log("Registering user:",t),Xe.post(Mo+"register",{username:t.username||t.email.split("@")[0],email:t.email,password:t.password})}}const lr=new iy,_l=JSON.parse(localStorage.getItem("user")),ay=_l?{status:{loggedIn:!0},user:_l}:{status:{loggedIn:!1},user:null},ly={namespaced:!0,state:ay,getters:{isLoggedIn:e=>e.status.loggedIn,user:e=>e.user,isAdmin:e=>{if(!e.user)return!1;const t=e.user.role;if(console.log("Role in isAdmin getter:",t),console.log("Role type:",typeof t),typeof t=="string")return t==="Admin";if(typeof t=="number")return t===4;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Admin"||t.value===4;if(t.hasOwnProperty("name"))return t.name==="Admin"}return!1},isModerator:e=>{if(!e.user)return!1;const t=e.user.role;if(typeof t=="string")return t==="Moderator";if(typeof t=="number")return t===3;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Moderator"||t.value===3;if(t.hasOwnProperty("name"))return t.name==="Moderator"}return!1},isAdminOrModerator:e=>{if(!e.user)return!1;const t=e.user.role;if(typeof t=="string")return t==="Admin"||t==="Moderator";if(typeof t=="number")return t===3||t===4;if(t&&typeof t=="object"){if(t.hasOwnProperty("value"))return t.value==="Admin"||t.value==="Moderator"||t.value===3||t.value===4;if(t.hasOwnProperty("name"))return t.name==="Admin"||t.name==="Moderator"}return!1}},actions:{async login({commit:e},{username:t,password:n}){try{const s=t;if(!s||!n)throw new Error("Email and password are required");const r=await lr.login(s,n);if(console.log("Auth data received from service:",r),!r||!r.token||!r.user)throw console.error("Invalid auth data from service:",r),e("loginFailure"),new Error("Authentication failed");return e("loginSuccess",r),Promise.resolve(r)}catch(s){return console.error("Login action error:",s),e("loginFailure"),Promise.reject(s)}},async googleLogin({commit:e},t){try{if(!t)throw new Error("Google ID token is required");console.log("Attempting Google login with token in Vuex");const n=await lr.googleLogin(t);if(console.log("Google auth data received from service:",n),!n||!n.token||!n.user)throw console.error("Invalid Google auth data from service:",n),e("loginFailure"),new Error("Google authentication failed");return e("loginSuccess",n),Promise.resolve(n)}catch(n){return console.error("Google login action error:",n),e("loginFailure"),Promise.reject(n)}},async register({commit:e},t){try{if(console.log("Register action in Vuex with user data:",t),!t.email||!t.password)throw new Error("Email and password are required");t.username||(t.username=t.email.split("@")[0]);const n=await lr.register(t);return console.log("Register response in Vuex:",n),e("registerSuccess"),Promise.resolve(n.data)}catch(n){return console.error("Register error in Vuex:",n),n.response&&(console.error("Error response in Vuex:",n.response),console.error("Error response data in Vuex:",n.response.data)),e("registerFailure"),Promise.reject(n)}},logout({commit:e}){lr.logout(),e("logout")}},mutations:{loginSuccess(e,t){if(console.log("Login success mutation with data:",t),!t||!t.user){console.error("Missing user data in loginSuccess mutation");return}e.status.loggedIn=!0,e.user=t.user,console.log("Updated auth state:",e)},loginFailure(e){e.status.loggedIn=!1,e.user=null},registerSuccess(e){e.status.loggedIn=!1},registerFailure(e){e.status.loggedIn=!1},logout(e){e.status.loggedIn=!1,e.user=null}}},cy={namespaced:!0,state:{isLoading:!1,loadingMessage:"",pendingRequests:0,routeChanging:!1},getters:{isLoading:e=>e.isLoading||e.routeChanging,loadingMessage:e=>e.loadingMessage,hasPendingRequests:e=>e.pendingRequests>0,apiService:()=>Xe},mutations:{SET_LOADING(e,t){e.isLoading=t},SET_LOADING_MESSAGE(e,t){e.loadingMessage=t},INCREMENT_PENDING_REQUESTS(e){e.pendingRequests++},DECREMENT_PENDING_REQUESTS(e){e.pendingRequests=Math.max(0,e.pendingRequests-1)},RESET_PENDING_REQUESTS(e){e.pendingRequests=0},SET_ROUTE_CHANGING(e,t){e.routeChanging=t}},actions:{startLoading({commit:e},t=""){e("SET_LOADING",!0),e("SET_LOADING_MESSAGE",t)},stopLoading({commit:e}){e("SET_LOADING",!1),e("SET_LOADING_MESSAGE","")},startRequest({commit:e,state:t}){e("INCREMENT_PENDING_REQUESTS"),t.pendingRequests===1&&e("SET_LOADING",!0)},finishRequest({commit:e,state:t}){e("DECREMENT_PENDING_REQUESTS"),t.pendingRequests===0&&e("SET_LOADING",!1)},resetRequests({commit:e}){e("RESET_PENDING_REQUESTS"),e("SET_LOADING",!1)},startRouteChange({commit:e},t="Loading page..."){e("SET_ROUTE_CHANGING",!0),e("SET_LOADING_MESSAGE",t)},finishRouteChange({commit:e}){e("SET_ROUTE_CHANGING",!1),e("SET_LOADING_MESSAGE","")}}},fe={categories:null,categoryTree:null,categoryDetails:{},lastFetched:{categories:null,categoryTree:null},cacheTimeout:5*60*1e3},bl=e=>fe.lastFetched[e]?new Date().getTime()-fe.lastFetched[e]<fe.cacheTimeout:!1,es={async getCategories(e={}){const t=Object.keys(e).length>0;if(!t&&bl("categories")&&fe.categories)return fe.categories;try{try{console.log("Fetching categories with params:",e);const n=await Xe.get("/api/categories/all",{params:e});console.log("Categories API response:",n.data);let s=[],r=0;n.data&&(n.data.data&&Array.isArray(n.data.data)?(s=n.data.data,r=n.data.total||n.data.totalItems||n.data.data.length):Array.isArray(n.data)?(s=n.data,r=n.data.length):n.data.items&&Array.isArray(n.data.items)&&(s=n.data.items,r=n.data.total||n.data.totalItems||n.data.items.length));const o={data:s,total:r,categories:s,totalCount:r};return t||(fe.categories=o,fe.lastFetched.categories=new Date().getTime()),o}catch(n){console.warn("Admin categories endpoint failed, falling back to public endpoint:",n.message);try{const s=await Xe.get("/api/categories/all",{params:e});console.log("Fallback categories API response:",s.data);let r=[],o=0;s.data&&(s.data.data&&Array.isArray(s.data.data)?(r=s.data.data,o=s.data.total||s.data.totalItems||s.data.data.length):Array.isArray(s.data)&&(r=s.data,o=s.data.length));const i={data:r,total:o,categories:r,totalCount:o};return t||(fe.categories=i,fe.lastFetched.categories=new Date().getTime()),i}catch(s){return console.error("Both admin and public categories endpoints failed:",s.message),{data:[],total:0,categories:[],totalCount:0}}}}catch(n){throw console.error("Error fetching categories:",n),n}},async getCategoryById(e){if(fe.categoryDetails[e])return fe.categoryDetails[e];try{const t=await Xe.get(`/api/admin/categories/${e}`);return fe.categoryDetails[e]=t.data,t.data}catch(t){throw console.error(`Error fetching category ${e}:`,t),t}},async createCategory(e){try{const t=await Xe.post("/api/admin/categories",e);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,t.data}catch(t){throw console.error("Error creating category:",t),t}},async updateCategory(e,t){try{const n=await Xe.put(`/api/admin/categories/${e}`,t);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&(fe.categoryDetails[e]=n.data.category||n.data),n.data}catch(n){throw console.error(`Error updating category ${e}:`,n),n}},async deleteCategory(e){try{const t=await Xe.delete(`/api/admin/categories/${e}`);return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&delete fe.categoryDetails[e],t.data}catch(t){throw console.error(`Error deleting category ${e}:`,t),t}},async uploadCategoryImage(e,t){try{const n=new FormData;n.append("image",t);const s=await Xe.post(`/api/admin/categories/${e}/image`,n,{headers:{"Content-Type":"multipart/form-data"}});return fe.categoryDetails[e]&&delete fe.categoryDetails[e],s.data}catch(n){throw console.error(`Error uploading image for category ${e}:`,n),n}},async getCategoryProducts(e,t={}){try{return(await Xe.get(`/api/admin/categories/${e}/products`,{params:t})).data}catch(n){throw console.error(`Error fetching products for category ${e}:`,n),n}},async getCategoryTree(){var e,t,n;if(bl("categoryTree")&&fe.categoryTree)return fe.categoryTree;try{try{const s=await Xe.get("/api/admin/categories/tree");return fe.categoryTree=s.data,fe.lastFetched.categoryTree=new Date().getTime(),s.data}catch(s){console.warn("Admin categories tree endpoint failed, building tree from regular categories:",s.message);const o=(await this.getCategories()).categories||[],i=this.buildCategoryTree(o);return fe.categoryTree=i,fe.lastFetched.categoryTree=new Date().getTime(),i}}catch(s){console.error("Error fetching category tree:",s);const r=((e=s.response)==null?void 0:e.status)===404?"Category tree endpoint not found. Please check API configuration.":((t=s.response)==null?void 0:t.status)===403?"You do not have permission to access the category tree.":((n=s.response)==null?void 0:n.status)===401?"Authentication required. Please log in again.":s.message||"Failed to fetch category tree",o=new Error(r);throw o.originalError=s,o}},buildCategoryTree(e){const t={};e.forEach(s=>{t[s.id]={...s,children:[]}});const n=[];return e.forEach(s=>{const r=t[s.id];s.parentId&&t[s.parentId]?t[s.parentId].children.push(r):n.push(r)}),n},async moveCategory(e,t){try{const n=await Xe.patch(`/api/admin/categories/${e}/move`,{parentId:t});return fe.categories=null,fe.categoryTree=null,fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null,fe.categoryDetails[e]&&delete fe.categoryDetails[e],n.data}catch(n){throw console.error(`Error moving category ${e}:`,n),n}},clearCache(){fe.categories=null,fe.categoryTree=null,fe.categoryDetails={},fe.lastFetched.categories=null,fe.lastFetched.categoryTree=null}},uy={namespaced:!0,state:{categories:[],categoryTree:[],categoryMap:{},loading:!1,error:null,lastFetched:null,cacheTimeout:5*60*1e3},getters:{allCategories:e=>e.categories,categoryById:e=>t=>e.categoryMap[t]||null,categoryTree:e=>e.categoryTree,rootCategories:e=>e.categories.filter(t=>!t.parentId),isLoading:e=>e.loading,hasError:e=>!!e.error,errorMessage:e=>e.error,isCacheValid:e=>e.lastFetched?new Date().getTime()-e.lastFetched<e.cacheTimeout:!1},mutations:{SET_CATEGORIES(e,t){e.categories=t,e.categoryMap={},t.forEach(n=>{e.categoryMap[n.id]=n}),e.lastFetched=new Date().getTime()},SET_CATEGORY_TREE(e,t){e.categoryTree=t},ADD_CATEGORY(e,t){e.categories.push(t),e.categoryMap[t.id]=t},UPDATE_CATEGORY(e,t){const n=e.categories.findIndex(s=>s.id===t.id);n!==-1&&(e.categories.splice(n,1,t),e.categoryMap[t.id]=t)},REMOVE_CATEGORY(e,t){e.categories=e.categories.filter(n=>n.id!==t),delete e.categoryMap[t]},SET_LOADING(e,t){e.loading=t},SET_ERROR(e,t){e.error=t},CLEAR_ERROR(e){e.error=null},INVALIDATE_CACHE(e){e.lastFetched=null}},actions:{async fetchCategories({commit:e,state:t,getters:n},s={}){if(!(Object.keys(s).length>0)&&n.isCacheValid&&t.categories.length>0)return{categories:t.categories};e("SET_LOADING",!0),e("CLEAR_ERROR");try{const o=await es.getCategories(s);return o&&o.categories&&e("SET_CATEGORIES",o.categories),o}catch(o){const i=o.message||"Failed to fetch categories";e("SET_ERROR",i);const a=new Error(i);throw a.originalError=o.originalError||o,a}finally{e("SET_LOADING",!1)}},async fetchCategoryTree({commit:e,state:t,getters:n}){if(n.isCacheValid&&t.categoryTree.length>0)return t.categoryTree;e("SET_LOADING",!0),e("CLEAR_ERROR");try{const s=await es.getCategoryTree();return e("SET_CATEGORY_TREE",s),s}catch(s){const r=s.message||"Failed to fetch category tree";e("SET_ERROR",r);const o=new Error(r);throw o.originalError=s.originalError||s,o}finally{e("SET_LOADING",!1)}},async fetchCategoryById({commit:e,getters:t},n){const s=t.categoryById(n);if(s)return s;e("SET_LOADING",!0),e("CLEAR_ERROR");try{const r=await es.getCategoryById(n);return e("UPDATE_CATEGORY",r),r}catch(r){throw e("SET_ERROR",r.message||`Failed to fetch category ${n}`),r}finally{e("SET_LOADING",!1)}},async createCategory({commit:e},t){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const n=await es.createCategory(t);return n.success&&n.category&&(e("ADD_CATEGORY",n.category),e("INVALIDATE_CACHE")),n}catch(n){throw e("SET_ERROR",n.message||"Failed to create category"),n}finally{e("SET_LOADING",!1)}},async updateCategory({commit:e},{id:t,categoryData:n}){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const s=await es.updateCategory(t,n);return s.success&&s.category&&(e("UPDATE_CATEGORY",s.category),e("INVALIDATE_CACHE")),s}catch(s){throw e("SET_ERROR",s.message||`Failed to update category ${t}`),s}finally{e("SET_LOADING",!1)}},async deleteCategory({commit:e},t){e("SET_LOADING",!0),e("CLEAR_ERROR");try{const n=await es.deleteCategory(t);return n.success&&(e("REMOVE_CATEGORY",t),e("INVALIDATE_CACHE")),n}catch(n){throw e("SET_ERROR",n.message||`Failed to delete category ${t}`),n}finally{e("SET_LOADING",!1)}}}},Qe=kp({modules:{auth:ly,loading:cy,categories:uy}}),dy="/assets/spring-banner-WHY_UVRU.jpg",fy={props:{products:{type:Array,default:()=>[]}}},hy={class:"top-products-section"},py={class:"container"},gy={class:"products-grid"},my={key:0,class:"product-badge"},vy={class:"product-image"},yy=["src","alt"],_y={class:"product-info"},by={class:"product-name"},wy={key:0,class:"product-availability"},Ey={key:1,class:"product-unavailability"},Sy={class:"product-price"},Cy={key:0,class:"price-old"},Ay={key:1,class:"price-discount"},Oy={class:"price-current"};function Ty(e,t,n,s,r,o){return R(),x("section",hy,[u("div",py,[t[3]||(t[3]=u("h2",{class:"section-title"},"ТОП товари",-1)),u("div",gy,[(R(!0),x($e,null,ht(n.products,i=>(R(),x("div",{key:i.id,class:"product-card"},[i.badge?(R(),x("div",my,K(i.badge),1)):Y("",!0),u("div",vy,[u("img",{src:i.image,alt:i.name},null,8,yy)]),u("div",_y,[u("h3",by,K(i.name),1),i.stock>0?(R(),x("div",wy,t[0]||(t[0]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),i.stock==0?(R(),x("div",Ey,t[1]||(t[1]=[u("span",{class:"availability-icon"},"✖",-1),u("span",{class:"availability-text"},"Немає в наявності",-1)]))):Y("",!0),u("div",Sy,[i.oldPrice?(R(),x("div",Cy,K(i.oldPrice)+" ₴",1)):Y("",!0),i.discount?(R(),x("div",Ay,"-"+K(i.discount)+"%",1)):Y("",!0)]),u("div",Oy,K(Math.round(i.priceAmount))+" ₴",1)]),t[2]||(t[2]=hn('<div class="product-actions" data-v-c6459617><button class="wishlist-btn" data-v-c6459617><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-c6459617><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" data-v-c6459617></path></svg></button><button class="cart-btn" data-v-c6459617><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-c6459617><circle cx="9" cy="21" r="1" data-v-c6459617></circle><circle cx="20" cy="21" r="1" data-v-c6459617></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6" data-v-c6459617></path></svg></button></div>',1))]))),128))])])])}const Ry=Je(fy,[["render",Ty],["__scopeId","data-v-c6459617"]]);class xy{async getCart(t={}){try{return await ye.get("/users/me/cart",{params:t})}catch(n){throw console.error("Error fetching cart:",n),n}}async addToCart(t,n={}){try{return await ye.post("/users/me/cart/items",n={productId:t})}catch(s){throw console.error("Error adding item:",s),s}}async changeItemCount(t,n,s={}){try{return await ye.put(`/users/me/cart/items/${t}`,s={quantity:n})}catch(r){throw console.error("Error increasing item count:",r),r}}async deleteItem(t){try{return await ye.delete(`/users/me/cart/items/${t}`)}catch(n){throw console.error("Error increasing item count:",n),n}}async deleteCart(){try{return await ye.delete("/users/me/cart")}catch(t){throw console.error("Error deleting cart:",t),t}}async checkout(){try{return await ye.post("/users/me/cart/checkout")}catch(t){throw console.error("Error initiating payment:",t),t}}}const ln=new xy;class Py{async getWishlist(t={}){try{return await ye.get("/users/me/wishlist",{params:t})}catch(n){throw console.error("Error fetching Wishlist:",n),n}}async addToWishlist(t,n={}){try{return await ye.post("/users/me/wishlist/items",n={productId:t})}catch(s){throw console.error("Error adding item:",s),s}}async deleteWishlist(t){try{return await ye.delete(`/users/me/wishlist/${t}`)}catch(n){throw console.error("Error deleting Wishlist:",n),n}}}const uo=new Py,ky={props:{products:{type:Array,default:()=>[]}},methods:{async addToCart(e){await ln.addToCart(e)},async addToWishlist(e){await uo.addToWishlist(e)}}},Iy={class:"recommended-products-section"},$y={class:"products-grid"},Fy={key:0,class:"product-badge"},Dy={class:"product-image"},My=["src","alt"],Ny={class:"product-info"},Vy={class:"product-name"},Ly={key:0,class:"product-availability"},jy={key:1,class:"product-unavailability"},Uy={class:"product-price"},qy={key:0,class:"price-old"},By={key:1,class:"price-discount"},Gy={class:"price-current"},Hy={class:"product-actions"},zy=["onClick"],Ky=["onClick"];function Wy(e,t,n,s,r,o){return R(),x("section",Iy,[t[4]||(t[4]=u("h2",{class:"section-title"},"Рекомендації на основі ваших переглядів",-1)),t[5]||(t[5]=_e("\\ ")),u("div",$y,[(R(!0),x($e,null,ht(n.products,i=>(R(),x("div",{key:i.id,class:"product-card"},[i.badge?(R(),x("div",Fy,K(i.badge),1)):Y("",!0),u("div",Dy,[u("img",{src:i.image,alt:i.name},null,8,My)]),u("div",Ny,[u("h3",Vy,K(i.name),1),i.stock>0?(R(),x("div",Ly,t[0]||(t[0]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),i.stock==0?(R(),x("div",jy,t[1]||(t[1]=[u("span",{class:"availability-icon"},"✖",-1),u("span",{class:"availability-text"},"Немає в наявності",-1)]))):Y("",!0),u("div",Uy,[i.oldPrice?(R(),x("div",qy,K(i.oldPrice)+" ₴",1)):Y("",!0),i.discount?(R(),x("div",By,"-"+K(i.discount)+"%",1)):Y("",!0)]),u("div",Gy,K(Math.round(i.priceAmount))+" ₴",1)]),u("div",Hy,[u("button",{class:"wishlist-btn",onClick:a=>o.addToWishlist(i.id)},t[2]||(t[2]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})],-1)]),8,zy),u("button",{class:"cart-btn",onClick:a=>o.addToCart(i.id)},t[3]||(t[3]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("circle",{cx:"9",cy:"21",r:"1"}),u("circle",{cx:"20",cy:"21",r:"1"}),u("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})],-1)]),8,Ky)])]))),128))])])}const rd=Je(ky,[["render",Wy],["__scopeId","data-v-03fad485"]]),Jy={props:{products:{type:Array,default:()=>[]}},methods:{async addToCart(e){await ln.addToCart(e)},async addToWishlist(e){await uo.addToWishlist(e)}}},Yy={class:"household-appliances-section"},Zy={class:"products-grid"},Qy={key:0,class:"product-badge"},Xy={class:"product-image"},e0=["src","alt"],t0={class:"product-info"},n0={class:"product-name"},s0={key:0,class:"product-availability"},r0={key:1,class:"product-unavailability"},o0={class:"product-price"},i0={key:0,class:"price-old"},a0={key:1,class:"price-discount"},l0={class:"price-current"},c0={class:"product-actions"},u0=["onClick"],d0=["onClick"];function f0(e,t,n,s,r,o){return R(),x("section",Yy,[t[4]||(t[4]=u("div",{class:"section-header"},[u("h2",{class:"section-title"},"Побутова техніка"),u("a",{href:"/catalog/category-2",class:"view-all"},"Більше товарів з категорії")],-1)),u("div",Zy,[(R(!0),x($e,null,ht(n.products,i=>(R(),x("div",{key:i.id,class:"product-card"},[i.badge?(R(),x("div",Qy,K(i.badge),1)):Y("",!0),u("div",Xy,[u("img",{src:i.image,alt:i.name},null,8,e0)]),u("div",t0,[u("h3",n0,K(i.name),1),i.stock>0?(R(),x("div",s0,t[0]||(t[0]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),i.stock==0?(R(),x("div",r0,t[1]||(t[1]=[u("span",{class:"availability-icon"},"✖",-1),u("span",{class:"availability-text"},"Немає в наявності",-1)]))):Y("",!0),u("div",o0,[i.oldPrice?(R(),x("div",i0,K(i.oldPrice)+" ₴",1)):Y("",!0),i.discount?(R(),x("div",a0,"-"+K(i.discount)+"%",1)):Y("",!0)]),u("div",l0,K(Math.round(i.priceAmount))+" ₴",1)]),u("div",c0,[u("button",{class:"wishlist-btn",onClick:a=>o.addToWishlist(i.id)},t[2]||(t[2]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})],-1)]),8,u0),u("button",{class:"cart-btn",onClick:a=>o.addToCart(i.id)},t[3]||(t[3]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("circle",{cx:"9",cy:"21",r:"1"}),u("circle",{cx:"20",cy:"21",r:"1"}),u("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})],-1)]),8,d0)])]))),128))])])}const h0=Je(Jy,[["render",f0],["__scopeId","data-v-156cbdd2"]]),p0={props:{categories:{type:Array,default:()=>[]}}},g0={class:"categories-section"},m0={class:"container"},v0={key:0,class:"categories-grid"},y0={class:"category-icon"},_0=["src","alt"],b0=["href"],w0={key:1};function E0(e,t,n,s,r,o){return R(),x("section",g0,[u("div",m0,[n.categories?(R(),x("div",v0,[(R(!0),x($e,null,ht(n.categories,i=>(R(),x("div",{key:i.id,class:"category-item"},[u("div",y0,[i.image?(R(),x("img",{key:0,src:i.image||"@assets/images/icons/placeholder-icon.svg",alt:i.name},null,8,_0)):Y("",!0)]),i.name?(R(),x("a",{key:0,href:`/catalog/${i.slug}`,class:"category-name"},K(i.name),9,b0)):Y("",!0)]))),128))])):(R(),x("div",w0,"Loading categories..."))])])}const S0=Je(p0,[["render",E0],["__scopeId","data-v-483768f0"]]);class C0{async getAll(t={}){return await ye.get("/products",{params:t})}async getById(t){return await ye.get(`/products/${t}`)}async create(t){return await ye.post("/products",t)}async update(t,n){return await ye.put(`/products/${t}`,n)}async delete(t){return await ye.delete(`/products/${t}`)}async uploadImage(t,n){return await ye.post(`/productimages/${t}`,n,{headers:{"Content-Type":"multipart/form-data"}})}async deleteImage(t){return await ye.delete(`/productimages/${t}`)}async getStats(){return await ye.get("/products/stats")}async getFilters(t){return await ye.get(`/products/filters/${t}`)}}const Hi=new C0,A0={class:"home-page"},O0={class:"categories-section"},T0={class:"container"},R0={class:"featured-section"},x0={class:"container"},P0={class:"appliances-section"},k0={class:"container"},I0={class:"recommended-section"},$0={class:"container"},F0={data(){return{categories:[],topProducts:[],householdProducts:[],recommendedProducts:[],error:null}},async mounted(){await this.fetchCategories(),await this.fetchTopProducts(),await this.fetchHouseholdProducts(),await this.fetchRecommendedProducts()},computed:{filteredTopProducts(){return this.topProducts.filter(e=>e.status==1)},filteredHouseholdProducts(){return this.householdProducts.filter(e=>e.status==1)},filteredRecommendedProducts(){return this.recommendedProducts.filter(e=>e.status==1)}},methods:{async fetchCategories(e={pageSize:18}){try{const t=await Sn.getAllRootCategories(e);console.log(t),this.categories=t.data.data,this.error=null}catch(t){this.error="Failed to load categories. Please try again.",console.error(t)}},async fetchHouseholdProducts(e={}){try{const t=await Sn.getProducts("category-5",e);this.householdProducts=t.data.data,this.error=null}catch(t){this.error="Failed to load household products. Please try again.",console.error(t)}},async fetchTopProducts(e={}){try{const t=await Hi.getAll(e);this.topProducts=t.data.data,this.error=null}catch(t){this.error="Failed to load top products. Please try again.",console.error(t)}},async fetchRecommendedProducts(e={}){try{const t=await Sn.getProducts("category-0",e);this.recommendedProducts=t.data.data,this.error=null}catch(t){this.error="Failed to load recommended products. Please try again.",console.error(t)}}}},D0=Object.assign(F0,{__name:"HomePage",setup(e){return(t,n)=>(R(),x("div",A0,[n[1]||(n[1]=hn('<div class="hero-banner" data-v-66c03a3a><div class="banner-container" data-v-66c03a3a><img src="'+dy+'" alt="Spring Banner" class="banner-image" data-v-66c03a3a><div class="banner-text" data-v-66c03a3a><h2 data-v-66c03a3a>ЗУСТРІЧАЙ ВЕШНЮ ПРАВИЛЬНО</h2></div><button class="banner-nav-btn banner-next" data-v-66c03a3a><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-66c03a3a><path d="M9 18l6-6-6-6" data-v-66c03a3a></path></svg></button></div></div>',1)),u("section",O0,[u("div",T0,[n[0]||(n[0]=u("h2",{class:"section-title"},"Розділи на сервісі",-1)),ae(S0,{categories:t.categories},null,8,["categories"])])]),u("section",R0,[u("div",x0,[ae(Ry,{products:t.filteredTopProducts},null,8,["products"])])]),u("section",P0,[u("div",k0,[ae(h0,{products:t.filteredHouseholdProducts},null,8,["products"])])]),u("section",I0,[u("div",$0,[ae(rd,{products:t.filteredRecommendedProducts},null,8,["products"])])])]))}}),M0=Je(D0,[["__scopeId","data-v-66c03a3a"]]);/**
  * vee-validate v4.15.0
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */function ot(e){return typeof e=="function"}function od(e){return e==null}const Kn=e=>e!==null&&!!e&&typeof e=="object"&&!Array.isArray(e);function zi(e){return Number(e)>=0}function N0(e){const t=parseFloat(e);return isNaN(t)?e:t}function V0(e){return typeof e=="object"&&e!==null}function L0(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}function wl(e){if(!V0(e)||L0(e)!=="[object Object]")return!1;if(Object.getPrototypeOf(e)===null)return!0;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function zs(e,t){return Object.keys(t).forEach(n=>{if(wl(t[n])&&wl(e[n])){e[n]||(e[n]={}),zs(e[n],t[n]);return}e[n]=t[n]}),e}function Os(e){const t=e.split(".");if(!t.length)return"";let n=String(t[0]);for(let s=1;s<t.length;s++){if(zi(t[s])){n+=`[${t[s]}]`;continue}n+=`.${t[s]}`}return n}const j0={};function U0(e){return j0[e]}function El(e,t,n){typeof n.value=="object"&&(n.value=Re(n.value)),!n.enumerable||n.get||n.set||!n.configurable||!n.writable||t==="__proto__"?Object.defineProperty(e,t,n):e[t]=n.value}function Re(e){if(typeof e!="object")return e;var t=0,n,s,r,o=Object.prototype.toString.call(e);if(o==="[object Object]"?r=Object.create(e.__proto__||null):o==="[object Array]"?r=Array(e.length):o==="[object Set]"?(r=new Set,e.forEach(function(i){r.add(Re(i))})):o==="[object Map]"?(r=new Map,e.forEach(function(i,a){r.set(Re(a),Re(i))})):o==="[object Date]"?r=new Date(+e):o==="[object RegExp]"?r=new RegExp(e.source,e.flags):o==="[object DataView]"?r=new e.constructor(Re(e.buffer)):o==="[object ArrayBuffer]"?r=e.slice(0):o.slice(-6)==="Array]"&&(r=new e.constructor(e)),r){for(s=Object.getOwnPropertySymbols(e);t<s.length;t++)El(r,s[t],Object.getOwnPropertyDescriptor(e,s[t]));for(t=0,s=Object.getOwnPropertyNames(e);t<s.length;t++)Object.hasOwnProperty.call(r,n=s[t])&&r[n]===e[n]||El(r,n,Object.getOwnPropertyDescriptor(e,n))}return r||e}const fo=Symbol("vee-validate-form"),q0=Symbol("vee-validate-form-context"),B0=Symbol("vee-validate-field-instance"),Dr=Symbol("Default empty value"),G0=typeof window<"u";function ui(e){return ot(e)&&!!e.__locatorRef}function Vt(e){return!!e&&ot(e.parse)&&e.__type==="VVTypedSchema"}function Mr(e){return!!e&&ot(e.validate)}function sr(e){return e==="checkbox"||e==="radio"}function H0(e){return Kn(e)||Array.isArray(e)}function z0(e){return Array.isArray(e)?e.length===0:Kn(e)&&Object.keys(e).length===0}function ho(e){return/^\[.+\]$/i.test(e)}function K0(e){return id(e)&&e.multiple}function id(e){return e.tagName==="SELECT"}function W0(e,t){const n=![!1,null,void 0,0].includes(t.multiple)&&!Number.isNaN(t.multiple);return e==="select"&&"multiple"in t&&n}function J0(e,t){return!W0(e,t)&&t.type!=="file"&&!sr(t.type)}function ad(e){return Ki(e)&&e.target&&"submit"in e.target}function Ki(e){return e?!!(typeof Event<"u"&&ot(Event)&&e instanceof Event||e&&e.srcElement):!1}function Sl(e,t){return t in e&&e[t]!==Dr}function yt(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var n,s,r;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(s=n;s--!==0;)if(!yt(e[s],t[s]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(s of e.entries())if(!t.has(s[0]))return!1;for(s of e.entries())if(!yt(s[1],t.get(s[0])))return!1;return!0}if(Al(e)&&Al(t))return!(e.size!==t.size||e.name!==t.name||e.lastModified!==t.lastModified||e.type!==t.type);if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(s of e.entries())if(!t.has(s[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(n=e.length,n!=t.length)return!1;for(s=n;s--!==0;)if(e[s]!==t[s])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(r=Object.keys(e),n=r.length-Cl(e,r),n!==Object.keys(t).length-Cl(t,Object.keys(t)))return!1;for(s=n;s--!==0;)if(!Object.prototype.hasOwnProperty.call(t,r[s]))return!1;for(s=n;s--!==0;){var o=r[s];if(!yt(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function Cl(e,t){let n=0;for(let r=t.length;r--!==0;){var s=t[r];e[s]===void 0&&n++}return n}function Al(e){return G0?e instanceof File:!1}function Wi(e){return ho(e)?e.replace(/\[|\]/gi,""):e}function Et(e,t,n){return e?ho(t)?e[Wi(t)]:(t||"").split(/\.|\[(\d+)\]/).filter(Boolean).reduce((r,o)=>H0(r)&&o in r?r[o]:n,e):n}function rn(e,t,n){if(ho(t)){e[Wi(t)]=n;return}const s=t.split(/\.|\[(\d+)\]/).filter(Boolean);let r=e;for(let o=0;o<s.length;o++){if(o===s.length-1){r[s[o]]=n;return}(!(s[o]in r)||od(r[s[o]]))&&(r[s[o]]=zi(s[o+1])?[]:{}),r=r[s[o]]}}function No(e,t){if(Array.isArray(e)&&zi(t)){e.splice(Number(t),1);return}Kn(e)&&delete e[t]}function Ol(e,t){if(ho(t)){delete e[Wi(t)];return}const n=t.split(/\.|\[(\d+)\]/).filter(Boolean);let s=e;for(let o=0;o<n.length;o++){if(o===n.length-1){No(s,n[o]);break}if(!(n[o]in s)||od(s[n[o]]))break;s=s[n[o]]}const r=n.map((o,i)=>Et(e,n.slice(0,i).join(".")));for(let o=r.length-1;o>=0;o--)if(z0(r[o])){if(o===0){No(e,n[0]);continue}No(r[o-1],n[o-1])}}function Pt(e){return Object.keys(e)}function ld(e,t=void 0){const n=Qs();return(n==null?void 0:n.provides[e])||St(e,t)}function Tl(e,t,n){if(Array.isArray(e)){const s=[...e],r=s.findIndex(o=>yt(o,t));return r>=0?s.splice(r,1):s.push(t),s}return yt(e,t)?n:t}function Rl(e,t=0){let n=null,s=[];return function(...r){return n&&clearTimeout(n),n=setTimeout(()=>{const o=e(...r);s.forEach(i=>i(o)),s=[]},t),new Promise(o=>s.push(o))}}function Y0(e,t){return Kn(t)&&t.number?N0(e):e}function di(e,t){let n;return async function(...r){const o=e(...r);n=o;const i=await o;return o!==n?i:(n=void 0,t(i,r))}}function fi(e){return Array.isArray(e)?e:e?[e]:[]}function cr(e,t){const n={};for(const s in e)t.includes(s)||(n[s]=e[s]);return n}function Z0(e){let t=null,n=[];return function(...s){const r=xt(()=>{if(t!==r)return;const o=e(...s);n.forEach(i=>i(o)),n=[],t=null});return t=r,new Promise(o=>n.push(o))}}function Ji(e,t,n){return t.slots.default?typeof e=="string"||!e?t.slots.default(n()):{default:()=>{var s,r;return(r=(s=t.slots).default)===null||r===void 0?void 0:r.call(s,n())}}:t.slots.default}function Vo(e){if(cd(e))return e._value}function cd(e){return"_value"in e}function Q0(e){return e.type==="number"||e.type==="range"?Number.isNaN(e.valueAsNumber)?e.value:e.valueAsNumber:e.value}function Nr(e){if(!Ki(e))return e;const t=e.target;if(sr(t.type)&&cd(t))return Vo(t);if(t.type==="file"&&t.files){const n=Array.from(t.files);return t.multiple?n:n[0]}if(K0(t))return Array.from(t.options).filter(n=>n.selected&&!n.disabled).map(Vo);if(id(t)){const n=Array.from(t.options).find(s=>s.selected);return n?Vo(n):t.value}return Q0(t)}function ud(e){const t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?Kn(e)&&e._$$isNormalized?e:Kn(e)?Object.keys(e).reduce((n,s)=>{const r=X0(e[s]);return e[s]!==!1&&(n[s]=xl(r)),n},t):typeof e!="string"?t:e.split("|").reduce((n,s)=>{const r=e_(s);return r.name&&(n[r.name]=xl(r.params)),n},t):t}function X0(e){return e===!0?[]:Array.isArray(e)||Kn(e)?e:[e]}function xl(e){const t=n=>typeof n=="string"&&n[0]==="@"?t_(n.slice(1)):n;return Array.isArray(e)?e.map(t):e instanceof RegExp?[e]:Object.keys(e).reduce((n,s)=>(n[s]=t(e[s]),n),{})}const e_=e=>{let t=[];const n=e.split(":")[0];return e.includes(":")&&(t=e.split(":").slice(1).join(":").split(",")),{name:n,params:t}};function t_(e){const t=n=>{var s;return(s=Et(n,e))!==null&&s!==void 0?s:n[e]};return t.__locatorRef=e,t}function n_(e){return Array.isArray(e)?e.filter(ui):Pt(e).filter(t=>ui(e[t])).map(t=>e[t])}const s_={generateMessage:({field:e})=>`${e} is not valid.`,bails:!0,validateOnBlur:!0,validateOnChange:!0,validateOnInput:!1,validateOnModelUpdate:!0};let r_=Object.assign({},s_);const jn=()=>r_;async function dd(e,t,n={}){const s=n==null?void 0:n.bails,r={name:(n==null?void 0:n.name)||"{field}",rules:t,label:n==null?void 0:n.label,bails:s??!0,formData:(n==null?void 0:n.values)||{}},o=await o_(r,e);return Object.assign(Object.assign({},o),{valid:!o.errors.length})}async function o_(e,t){const n=e.rules;if(Vt(n)||Mr(n))return a_(t,Object.assign(Object.assign({},e),{rules:n}));if(ot(n)||Array.isArray(n)){const a={field:e.label||e.name,name:e.name,label:e.label,form:e.formData,value:t},l=Array.isArray(n)?n:[n],d=l.length,c=[];for(let f=0;f<d;f++){const p=l[f],g=await p(t,a);if(!(typeof g!="string"&&!Array.isArray(g)&&g)){if(Array.isArray(g))c.push(...g);else{const S=typeof g=="string"?g:hd(a);c.push(S)}if(e.bails)return{errors:c}}}return{errors:c}}const s=Object.assign(Object.assign({},e),{rules:ud(n)}),r=[],o=Object.keys(s.rules),i=o.length;for(let a=0;a<i;a++){const l=o[a],d=await l_(s,t,{name:l,params:s.rules[l]});if(d.error&&(r.push(d.error),e.bails))return{errors:r}}return{errors:r}}function i_(e){return!!e&&e.name==="ValidationError"}function fd(e){return{__type:"VVTypedSchema",async parse(n,s){var r;try{return{output:await e.validate(n,{abortEarly:!1,context:(s==null?void 0:s.formData)||{}}),errors:[]}}catch(o){if(!i_(o))throw o;if(!(!((r=o.inner)===null||r===void 0)&&r.length)&&o.errors.length)return{errors:[{path:o.path,errors:o.errors}]};const i=o.inner.reduce((a,l)=>{const d=l.path||"";return a[d]||(a[d]={errors:[],path:d}),a[d].errors.push(...l.errors),a},{});return{errors:Object.values(i)}}}}}async function a_(e,t){const s=await(Vt(t.rules)?t.rules:fd(t.rules)).parse(e,{formData:t.formData}),r=[];for(const o of s.errors)o.errors.length&&r.push(...o.errors);return{value:s.value,errors:r}}async function l_(e,t,n){const s=U0(n.name);if(!s)throw new Error(`No such validator '${n.name}' exists.`);const r=c_(n.params,e.formData),o={field:e.label||e.name,name:e.name,label:e.label,value:t,form:e.formData,rule:Object.assign(Object.assign({},n),{params:r})},i=await s(t,r,o);return typeof i=="string"?{error:i}:{error:i?void 0:hd(o)}}function hd(e){const t=jn().generateMessage;return t?t(e):"Field is invalid"}function c_(e,t){const n=s=>ui(s)?s(t):s;return Array.isArray(e)?e.map(n):Object.keys(e).reduce((s,r)=>(s[r]=n(e[r]),s),{})}async function u_(e,t){const s=await(Vt(e)?e:fd(e)).parse(Re(t),{formData:Re(t)}),r={},o={};for(const i of s.errors){const a=i.errors,l=(i.path||"").replace(/\["(\d+)"\]/g,(d,c)=>`[${c}]`);r[l]={valid:!a.length,errors:a},a.length&&(o[l]=a[0])}return{valid:!s.errors.length,results:r,errors:o,values:s.value,source:"schema"}}async function d_(e,t,n){const r=Pt(e).map(async d=>{var c,f,p;const g=(c=n==null?void 0:n.names)===null||c===void 0?void 0:c[d],y=await dd(Et(t,d),e[d],{name:(g==null?void 0:g.name)||d,label:g==null?void 0:g.label,values:t,bails:(p=(f=n==null?void 0:n.bailsMap)===null||f===void 0?void 0:f[d])!==null&&p!==void 0?p:!0});return Object.assign(Object.assign({},y),{path:d})});let o=!0;const i=await Promise.all(r),a={},l={};for(const d of i)a[d.path]={valid:d.valid,errors:d.errors},d.valid||(o=!1,l[d.path]=d.errors[0]);return{valid:o,results:a,errors:l,source:"schema"}}let Pl=0;function f_(e,t){const{value:n,initialValue:s,setInitialValue:r}=h_(e,t.modelValue,t.form);if(!t.form){let l=function(g){var y;"value"in g&&(n.value=g.value),"errors"in g&&c(g.errors),"touched"in g&&(p.touched=(y=g.touched)!==null&&y!==void 0?y:p.touched),"initialValue"in g&&r(g.initialValue)};const{errors:d,setErrors:c}=m_(),f=Pl>=Number.MAX_SAFE_INTEGER?0:++Pl,p=g_(n,s,d,t.schema);return{id:f,path:e,value:n,initialValue:s,meta:p,flags:{pendingUnmount:{[f]:!1},pendingReset:!1},errors:d,setState:l}}const o=t.form.createPathState(e,{bails:t.bails,label:t.label,type:t.type,validate:t.validate,schema:t.schema}),i=te(()=>o.errors);function a(l){var d,c,f;"value"in l&&(n.value=l.value),"errors"in l&&((d=t.form)===null||d===void 0||d.setFieldError(Ce(e),l.errors)),"touched"in l&&((c=t.form)===null||c===void 0||c.setFieldTouched(Ce(e),(f=l.touched)!==null&&f!==void 0?f:!1)),"initialValue"in l&&r(l.initialValue)}return{id:Array.isArray(o.id)?o.id[o.id.length-1]:o.id,path:e,value:n,errors:i,meta:o,initialValue:s,flags:o.__flags,setState:a}}function h_(e,t,n){const s=ve(Ce(t));function r(){return n?Et(n.initialValues.value,Ce(e),Ce(s)):Ce(s)}function o(d){if(!n){s.value=d;return}n.setFieldInitialValue(Ce(e),d,!0)}const i=te(r);if(!n)return{value:ve(r()),initialValue:i,setInitialValue:o};const a=p_(t,n,i,e);return n.stageInitialValue(Ce(e),a,!0),{value:te({get(){return Et(n.values,Ce(e))},set(d){n.setFieldValue(Ce(e),d,!1)}}),initialValue:i,setInitialValue:o}}function p_(e,t,n,s){return He(e)?Ce(e):e!==void 0?e:Et(t.values,Ce(s),Ce(n))}function g_(e,t,n,s){const r=te(()=>{var i,a,l;return(l=(a=(i=ie(s))===null||i===void 0?void 0:i.describe)===null||a===void 0?void 0:a.call(i).required)!==null&&l!==void 0?l:!1}),o=Qt({touched:!1,pending:!1,valid:!0,required:r,validated:!!Ce(n).length,initialValue:te(()=>Ce(t)),dirty:te(()=>!yt(Ce(e),Ce(t)))});return It(n,i=>{o.valid=!i.length},{immediate:!0,flush:"sync"}),o}function m_(){const e=ve([]);return{errors:e,setErrors:t=>{e.value=fi(t)}}}function Ks(e,t,n){return sr(n==null?void 0:n.type)?y_(e,t,n):pd(e,t,n)}function pd(e,t,n){const{initialValue:s,validateOnMount:r,bails:o,type:i,checkedValue:a,label:l,validateOnValueUpdate:d,uncheckedValue:c,controlled:f,keepValueOnUnmount:p,syncVModel:g,form:y}=v_(n),S=f?ld(fo):void 0,E=y||S,C=te(()=>Os(ie(e))),P=te(()=>{if(ie(E==null?void 0:E.schema))return;const T=Ce(t);return Mr(T)||Vt(T)||ot(T)||Array.isArray(T)?T:ud(T)}),D=!ot(P.value)&&Vt(ie(t)),{id:A,value:h,initialValue:G,meta:O,setState:W,errors:I,flags:ne}=f_(C,{modelValue:s,form:E,bails:o,label:l,type:i,validate:P.value?ge:void 0,schema:D?t:void 0}),q=te(()=>I.value[0]);g&&__({value:h,prop:g,handleChange:X,shouldValidate:()=>d&&!ne.pendingReset});const B=(Q,T=!1)=>{O.touched=!0,T&&Se()};async function pe(Q){var T,j;if(E!=null&&E.validateSchema){const{results:V}=await E.validateSchema(Q);return(T=V[ie(C)])!==null&&T!==void 0?T:{valid:!0,errors:[]}}return P.value?dd(h.value,P.value,{name:ie(C),label:ie(l),values:(j=E==null?void 0:E.values)!==null&&j!==void 0?j:{},bails:o}):{valid:!0,errors:[]}}const Se=di(async()=>(O.pending=!0,O.validated=!0,pe("validated-only")),Q=>(ne.pendingUnmount[Be.id]||(W({errors:Q.errors}),O.pending=!1,O.valid=Q.valid),Q)),Me=di(async()=>pe("silent"),Q=>(O.valid=Q.valid,Q));function ge(Q){return(Q==null?void 0:Q.mode)==="silent"?Me():Se()}function X(Q,T=!0){const j=Nr(Q);at(j,T)}kn(()=>{if(r)return Se();(!E||!E.validateSchema)&&Me()});function be(Q){O.touched=Q}function Ye(Q){var T;const j=Q&&"value"in Q?Q.value:G.value;W({value:Re(j),initialValue:Re(j),touched:(T=Q==null?void 0:Q.touched)!==null&&T!==void 0?T:!1,errors:(Q==null?void 0:Q.errors)||[]}),O.pending=!1,O.validated=!1,Me()}const it=Qs();function at(Q,T=!0){h.value=it&&g?Y0(Q,it.props.modelModifiers):Q,(T?Se:Me)()}function Ze(Q){W({errors:Array.isArray(Q)?Q:[Q]})}const Xt=te({get(){return h.value},set(Q){at(Q,d)}}),Be={id:A,name:C,label:l,value:Xt,meta:O,errors:I,errorMessage:q,type:i,checkedValue:a,uncheckedValue:c,bails:o,keepValueOnUnmount:p,resetField:Ye,handleReset:()=>Ye(),validate:ge,handleChange:X,handleBlur:B,setState:W,setTouched:be,setErrors:Ze,setValue:at};if(An(B0,Be),He(t)&&typeof Ce(t)!="function"&&It(t,(Q,T)=>{yt(Q,T)||(O.validated?Se():Me())},{deep:!0}),!E)return Be;const gn=te(()=>{const Q=P.value;return!Q||ot(Q)||Mr(Q)||Vt(Q)||Array.isArray(Q)?{}:Object.keys(Q).reduce((T,j)=>{const V=n_(Q[j]).map(ee=>ee.__locatorRef).reduce((ee,we)=>{const m=Et(E.values,we)||E.values[we];return m!==void 0&&(ee[we]=m),ee},{});return Object.assign(T,V),T},{})});return It(gn,(Q,T)=>{if(!Object.keys(Q).length)return;!yt(Q,T)&&(O.validated?Se():Me())}),Ii(()=>{var Q;const T=(Q=ie(Be.keepValueOnUnmount))!==null&&Q!==void 0?Q:ie(E.keepValuesOnUnmount),j=ie(C);if(T||!E||ne.pendingUnmount[Be.id]){E==null||E.removePathState(j,A);return}ne.pendingUnmount[Be.id]=!0;const V=E.getPathState(j);if(Array.isArray(V==null?void 0:V.id)&&(V!=null&&V.multiple)?V!=null&&V.id.includes(Be.id):(V==null?void 0:V.id)===Be.id){if(V!=null&&V.multiple&&Array.isArray(V.value)){const we=V.value.findIndex(m=>yt(m,ie(Be.checkedValue)));if(we>-1){const m=[...V.value];m.splice(we,1),E.setFieldValue(j,m)}Array.isArray(V.id)&&V.id.splice(V.id.indexOf(Be.id),1)}else E.unsetPathValue(ie(C));E.removePathState(j,A)}}),Be}function v_(e){const t=()=>({initialValue:void 0,validateOnMount:!1,bails:!0,label:void 0,validateOnValueUpdate:!0,keepValueOnUnmount:void 0,syncVModel:!1,controlled:!0}),n=!!(e!=null&&e.syncVModel),s=typeof(e==null?void 0:e.syncVModel)=="string"?e.syncVModel:(e==null?void 0:e.modelPropName)||"modelValue",r=n&&!("initialValue"in(e||{}))?hi(Qs(),s):e==null?void 0:e.initialValue;if(!e)return Object.assign(Object.assign({},t()),{initialValue:r});const o="valueProp"in e?e.valueProp:e.checkedValue,i="standalone"in e?!e.standalone:e.controlled,a=(e==null?void 0:e.modelPropName)||(e==null?void 0:e.syncVModel)||!1;return Object.assign(Object.assign(Object.assign({},t()),e||{}),{initialValue:r,controlled:i??!0,checkedValue:o,syncVModel:a})}function y_(e,t,n){const s=n!=null&&n.standalone?void 0:ld(fo),r=n==null?void 0:n.checkedValue,o=n==null?void 0:n.uncheckedValue;function i(a){const l=a.handleChange,d=te(()=>{const f=ie(a.value),p=ie(r);return Array.isArray(f)?f.findIndex(g=>yt(g,p))>=0:yt(p,f)});function c(f,p=!0){var g,y;if(d.value===((g=f==null?void 0:f.target)===null||g===void 0?void 0:g.checked)){p&&a.validate();return}const S=ie(e),E=s==null?void 0:s.getPathState(S),C=Nr(f);let P=(y=ie(r))!==null&&y!==void 0?y:C;s&&(E!=null&&E.multiple)&&E.type==="checkbox"?P=Tl(Et(s.values,S)||[],P,void 0):(n==null?void 0:n.type)==="checkbox"&&(P=Tl(ie(a.value),P,ie(o))),l(P,p)}return Object.assign(Object.assign({},a),{checked:d,checkedValue:r,uncheckedValue:o,handleChange:c})}return i(pd(e,t,n))}function __({prop:e,value:t,handleChange:n,shouldValidate:s}){const r=Qs();if(!r||!e)return;const o=typeof e=="string"?e:"modelValue",i=`update:${o}`;o in r.props&&(It(t,a=>{yt(a,hi(r,o))||r.emit(i,a)}),It(()=>hi(r,o),a=>{if(a===Dr&&t.value===void 0)return;const l=a===Dr?void 0:a;yt(l,t.value)||n(l,s())}))}function hi(e,t){if(e)return e.props[t]}const b_=Zs({name:"Field",inheritAttrs:!1,props:{as:{type:[String,Object],default:void 0},name:{type:String,required:!0},rules:{type:[Object,String,Function],default:void 0},validateOnMount:{type:Boolean,default:!1},validateOnBlur:{type:Boolean,default:void 0},validateOnChange:{type:Boolean,default:void 0},validateOnInput:{type:Boolean,default:void 0},validateOnModelUpdate:{type:Boolean,default:void 0},bails:{type:Boolean,default:()=>jn().bails},label:{type:String,default:void 0},uncheckedValue:{type:null,default:void 0},modelValue:{type:null,default:Dr},modelModifiers:{type:null,default:()=>({})},"onUpdate:modelValue":{type:null,default:void 0},standalone:{type:Boolean,default:!1},keepValue:{type:Boolean,default:void 0}},setup(e,t){const n=Nn(e,"rules"),s=Nn(e,"name"),r=Nn(e,"label"),o=Nn(e,"uncheckedValue"),i=Nn(e,"keepValue"),{errors:a,value:l,errorMessage:d,validate:c,handleChange:f,handleBlur:p,setTouched:g,resetField:y,handleReset:S,meta:E,checked:C,setErrors:P,setValue:D}=Ks(s,n,{validateOnMount:e.validateOnMount,bails:e.bails,standalone:e.standalone,type:t.attrs.type,initialValue:E_(e,t),checkedValue:t.attrs.value,uncheckedValue:o,label:r,validateOnValueUpdate:e.validateOnModelUpdate,keepValueOnUnmount:i,syncVModel:!0}),A=function(ne,q=!0){f(ne,q)},h=te(()=>{const{validateOnInput:I,validateOnChange:ne,validateOnBlur:q,validateOnModelUpdate:B}=w_(e);function pe(X){p(X,q),ot(t.attrs.onBlur)&&t.attrs.onBlur(X)}function Se(X){A(X,I),ot(t.attrs.onInput)&&t.attrs.onInput(X)}function Me(X){A(X,ne),ot(t.attrs.onChange)&&t.attrs.onChange(X)}const ge={name:e.name,onBlur:pe,onInput:Se,onChange:Me};return ge["onUpdate:modelValue"]=X=>A(X,B),ge}),G=te(()=>{const I=Object.assign({},h.value);sr(t.attrs.type)&&C&&(I.checked=C.value);const ne=kl(e,t);return J0(ne,t.attrs)&&(I.value=l.value),I}),O=te(()=>Object.assign(Object.assign({},h.value),{modelValue:l.value}));function W(){return{field:G.value,componentField:O.value,value:l.value,meta:E,errors:a.value,errorMessage:d.value,validate:c,resetField:y,handleChange:A,handleInput:I=>A(I,!1),handleReset:S,handleBlur:h.value.onBlur,setTouched:g,setErrors:P,setValue:D}}return t.expose({value:l,meta:E,errors:a,errorMessage:d,setErrors:P,setTouched:g,setValue:D,reset:y,validate:c,handleChange:f}),()=>{const I=Jr(kl(e,t)),ne=Ji(I,t,W);return I?Hn(I,Object.assign(Object.assign({},t.attrs),G.value),ne):ne}}});function kl(e,t){let n=e.as||"";return!e.as&&!t.slots.default&&(n="input"),n}function w_(e){var t,n,s,r;const{validateOnInput:o,validateOnChange:i,validateOnBlur:a,validateOnModelUpdate:l}=jn();return{validateOnInput:(t=e.validateOnInput)!==null&&t!==void 0?t:o,validateOnChange:(n=e.validateOnChange)!==null&&n!==void 0?n:i,validateOnBlur:(s=e.validateOnBlur)!==null&&s!==void 0?s:a,validateOnModelUpdate:(r=e.validateOnModelUpdate)!==null&&r!==void 0?r:l}}function E_(e,t){return sr(t.attrs.type)?Sl(e,"modelValue")?e.modelValue:void 0:Sl(e,"modelValue")?e.modelValue:t.attrs.value}const gd=b_;let S_=0;const ur=["bails","fieldsCount","id","multiple","type","validate"];function md(e){const t=(e==null?void 0:e.initialValues)||{},n=Object.assign({},ie(t)),s=Ce(e==null?void 0:e.validationSchema);return s&&Vt(s)&&ot(s.cast)?Re(s.cast(n)||{}):Re(n)}function C_(e){var t;const n=S_++,s=(e==null?void 0:e.name)||"Form";let r=0;const o=ve(!1),i=ve(!1),a=ve(0),l=[],d=Qt(md(e)),c=ve([]),f=ve({}),p=ve({}),g=Z0(()=>{p.value=c.value.reduce((b,_)=>(b[Os(ie(_.path))]=_,b),{})});function y(b,_){const N=X(b);if(!N){typeof b=="string"&&(f.value[Os(b)]=fi(_));return}if(typeof b=="string"){const Z=Os(b);f.value[Z]&&delete f.value[Z]}N.errors=fi(_),N.valid=!N.errors.length}function S(b){Pt(b).forEach(_=>{y(_,b[_])})}e!=null&&e.initialErrors&&S(e.initialErrors);const E=te(()=>{const b=c.value.reduce((_,N)=>(N.errors.length&&(_[ie(N.path)]=N.errors),_),{});return Object.assign(Object.assign({},f.value),b)}),C=te(()=>Pt(E.value).reduce((b,_)=>{const N=E.value[_];return N!=null&&N.length&&(b[_]=N[0]),b},{})),P=te(()=>c.value.reduce((b,_)=>(b[ie(_.path)]={name:ie(_.path)||"",label:_.label||""},b),{})),D=te(()=>c.value.reduce((b,_)=>{var N;return b[ie(_.path)]=(N=_.bails)!==null&&N!==void 0?N:!0,b},{})),A=Object.assign({},(e==null?void 0:e.initialErrors)||{}),h=(t=e==null?void 0:e.keepValuesOnUnmount)!==null&&t!==void 0?t:!1,{initialValues:G,originalInitialValues:O,setInitialValues:W}=O_(c,d,e),I=A_(c,d,O,C),ne=te(()=>c.value.reduce((b,_)=>{const N=Et(d,ie(_.path));return rn(b,ie(_.path),N),b},{})),q=e==null?void 0:e.validationSchema;function B(b,_){var N,Z;const se=te(()=>Et(G.value,ie(b))),ue=p.value[ie(b)],oe=(_==null?void 0:_.type)==="checkbox"||(_==null?void 0:_.type)==="radio";if(ue&&oe){ue.multiple=!0;const $t=r++;return Array.isArray(ue.id)?ue.id.push($t):ue.id=[ue.id,$t],ue.fieldsCount++,ue.__flags.pendingUnmount[$t]=!1,ue}const Ue=te(()=>Et(d,ie(b))),We=ie(b),lt=Ye.findIndex($t=>$t===We);lt!==-1&&Ye.splice(lt,1);const je=te(()=>{var $t,_s,po,go;const mo=ie(q);if(Vt(mo))return(_s=($t=mo.describe)===null||$t===void 0?void 0:$t.call(mo,ie(b)).required)!==null&&_s!==void 0?_s:!1;const vo=ie(_==null?void 0:_.schema);return Vt(vo)&&(go=(po=vo.describe)===null||po===void 0?void 0:po.call(vo).required)!==null&&go!==void 0?go:!1}),ct=r++,_t=Qt({id:ct,path:b,touched:!1,pending:!1,valid:!0,validated:!!(!((N=A[We])===null||N===void 0)&&N.length),required:je,initialValue:se,errors:pc([]),bails:(Z=_==null?void 0:_.bails)!==null&&Z!==void 0?Z:!1,label:_==null?void 0:_.label,type:(_==null?void 0:_.type)||"default",value:Ue,multiple:!1,__flags:{pendingUnmount:{[ct]:!1},pendingReset:!1},fieldsCount:1,validate:_==null?void 0:_.validate,dirty:te(()=>!yt(Ce(Ue),Ce(se)))});return c.value.push(_t),p.value[We]=_t,g(),C.value[We]&&!A[We]&&xt(()=>{U(We,{mode:"silent"})}),He(b)&&It(b,$t=>{g();const _s=Re(Ue.value);p.value[$t]=_t,xt(()=>{rn(d,$t,_s)})}),_t}const pe=Rl(re,5),Se=Rl(re,5),Me=di(async b=>await(b==="silent"?pe():Se()),(b,[_])=>{const N=Pt(T.errorBag.value),se=[...new Set([...Pt(b.results),...c.value.map(ue=>ue.path),...N])].sort().reduce((ue,oe)=>{var Ue;const We=oe,lt=X(We)||be(We),je=((Ue=b.results[We])===null||Ue===void 0?void 0:Ue.errors)||[],ct=ie(lt==null?void 0:lt.path)||We,_t=T_({errors:je,valid:!je.length},ue.results[ct]);return ue.results[ct]=_t,_t.valid||(ue.errors[ct]=_t.errors[0]),lt&&f.value[ct]&&delete f.value[ct],lt?(lt.valid=_t.valid,_==="silent"||_==="validated-only"&&!lt.validated||y(lt,_t.errors),ue):(y(ct,je),ue)},{valid:b.valid,results:{},errors:{},source:b.source});return b.values&&(se.values=b.values,se.source=b.source),Pt(se.results).forEach(ue=>{var oe;const Ue=X(ue);Ue&&_!=="silent"&&(_==="validated-only"&&!Ue.validated||y(Ue,(oe=se.results[ue])===null||oe===void 0?void 0:oe.errors))}),se});function ge(b){c.value.forEach(b)}function X(b){const _=typeof b=="string"?Os(b):b;return typeof _=="string"?p.value[_]:_}function be(b){return c.value.filter(N=>b.startsWith(ie(N.path))).reduce((N,Z)=>N?Z.path.length>N.path.length?Z:N:Z,void 0)}let Ye=[],it;function at(b){return Ye.push(b),it||(it=xt(()=>{[...Ye].sort().reverse().forEach(N=>{Ol(d,N)}),Ye=[],it=null})),it}function Ze(b){return function(N,Z){return function(ue){return ue instanceof Event&&(ue.preventDefault(),ue.stopPropagation()),ge(oe=>oe.touched=!0),o.value=!0,a.value++,H().then(oe=>{const Ue=Re(d);if(oe.valid&&typeof N=="function"){const We=Re(ne.value);let lt=b?We:Ue;return oe.values&&(lt=oe.source==="schema"?oe.values:Object.assign({},lt,oe.values)),N(lt,{evt:ue,controlledValues:We,setErrors:S,setFieldError:y,setTouched:M,setFieldTouched:m,setValues:ee,setFieldValue:j,resetForm:z,resetField:F})}!oe.valid&&typeof Z=="function"&&Z({values:Ue,evt:ue,errors:oe.errors,results:oe.results})}).then(oe=>(o.value=!1,oe),oe=>{throw o.value=!1,oe})}}}const Be=Ze(!1);Be.withControlled=Ze(!0);function gn(b,_){const N=c.value.findIndex(se=>se.path===b&&(Array.isArray(se.id)?se.id.includes(_):se.id===_)),Z=c.value[N];if(!(N===-1||!Z)){if(xt(()=>{U(b,{mode:"silent",warn:!1})}),Z.multiple&&Z.fieldsCount&&Z.fieldsCount--,Array.isArray(Z.id)){const se=Z.id.indexOf(_);se>=0&&Z.id.splice(se,1),delete Z.__flags.pendingUnmount[_]}(!Z.multiple||Z.fieldsCount<=0)&&(c.value.splice(N,1),L(b),g(),delete p.value[b])}}function Q(b){Pt(p.value).forEach(_=>{_.startsWith(b)&&delete p.value[_]}),c.value=c.value.filter(_=>!_.path.startsWith(b)),xt(()=>{g()})}const T={name:s,formId:n,values:d,controlledValues:ne,errorBag:E,errors:C,schema:q,submitCount:a,meta:I,isSubmitting:o,isValidating:i,fieldArrays:l,keepValuesOnUnmount:h,validateSchema:Ce(q)?Me:void 0,validate:H,setFieldError:y,validateField:U,setFieldValue:j,setValues:ee,setErrors:S,setFieldTouched:m,setTouched:M,resetForm:z,resetField:F,handleSubmit:Be,useFieldModel:Fe,defineInputBinds:Oe,defineComponentBinds:pt,defineField:Ee,stageInitialValue:ce,unsetInitialValue:L,setFieldInitialValue:J,createPathState:B,getPathState:X,unsetPathValue:at,removePathState:gn,initialValues:G,getAllPathStates:()=>c.value,destroyPath:Q,isFieldTouched:v,isFieldDirty:w,isFieldValid:$};function j(b,_,N=!0){const Z=Re(_),se=typeof b=="string"?b:b.path;X(se)||B(se),rn(d,se,Z),N&&U(se)}function V(b,_=!0){Pt(d).forEach(N=>{delete d[N]}),Pt(b).forEach(N=>{j(N,b[N],!1)}),_&&H()}function ee(b,_=!0){zs(d,b),l.forEach(N=>N&&N.reset()),_&&H()}function we(b,_){const N=X(ie(b))||B(b);return te({get(){return N.value},set(Z){var se;const ue=ie(b);j(ue,Z,(se=ie(_))!==null&&se!==void 0?se:!1)}})}function m(b,_){const N=X(b);N&&(N.touched=_)}function v(b){const _=X(b);return _?_.touched:c.value.filter(N=>N.path.startsWith(b)).some(N=>N.touched)}function w(b){const _=X(b);return _?_.dirty:c.value.filter(N=>N.path.startsWith(b)).some(N=>N.dirty)}function $(b){const _=X(b);return _?_.valid:c.value.filter(N=>N.path.startsWith(b)).every(N=>N.valid)}function M(b){if(typeof b=="boolean"){ge(_=>{_.touched=b});return}Pt(b).forEach(_=>{m(_,!!b[_])})}function F(b,_){var N;const Z=_&&"value"in _?_.value:Et(G.value,b),se=X(b);se&&(se.__flags.pendingReset=!0),J(b,Re(Z),!0),j(b,Z,!1),m(b,(N=_==null?void 0:_.touched)!==null&&N!==void 0?N:!1),y(b,(_==null?void 0:_.errors)||[]),xt(()=>{se&&(se.__flags.pendingReset=!1)})}function z(b,_){let N=Re(b!=null&&b.values?b.values:O.value);N=_!=null&&_.force?N:zs(O.value,N),N=Vt(q)&&ot(q.cast)?q.cast(N):N,W(N,{force:_==null?void 0:_.force}),ge(Z=>{var se;Z.__flags.pendingReset=!0,Z.validated=!1,Z.touched=((se=b==null?void 0:b.touched)===null||se===void 0?void 0:se[ie(Z.path)])||!1,j(ie(Z.path),Et(N,ie(Z.path)),!1),y(ie(Z.path),void 0)}),_!=null&&_.force?V(N,!1):ee(N,!1),S((b==null?void 0:b.errors)||{}),a.value=(b==null?void 0:b.submitCount)||0,xt(()=>{H({mode:"silent"}),ge(Z=>{Z.__flags.pendingReset=!1})})}async function H(b){const _=(b==null?void 0:b.mode)||"force";if(_==="force"&&ge(oe=>oe.validated=!0),T.validateSchema)return T.validateSchema(_);i.value=!0;const N=await Promise.all(c.value.map(oe=>oe.validate?oe.validate(b).then(Ue=>({key:ie(oe.path),valid:Ue.valid,errors:Ue.errors,value:Ue.value})):Promise.resolve({key:ie(oe.path),valid:!0,errors:[],value:void 0})));i.value=!1;const Z={},se={},ue={};for(const oe of N)Z[oe.key]={valid:oe.valid,errors:oe.errors},oe.value&&rn(ue,oe.key,oe.value),oe.errors.length&&(se[oe.key]=oe.errors[0]);return{valid:N.every(oe=>oe.valid),results:Z,errors:se,values:ue,source:"fields"}}async function U(b,_){var N;const Z=X(b);if(Z&&(_==null?void 0:_.mode)!=="silent"&&(Z.validated=!0),q){const{results:se}=await Me((_==null?void 0:_.mode)||"validated-only");return se[b]||{errors:[],valid:!0}}return Z!=null&&Z.validate?Z.validate(_):(!Z&&(N=_==null?void 0:_.warn),Promise.resolve({errors:[],valid:!0}))}function L(b){Ol(G.value,b)}function ce(b,_,N=!1){J(b,_),rn(d,b,_),N&&!(e!=null&&e.initialValues)&&rn(O.value,b,Re(_))}function J(b,_,N=!1){rn(G.value,b,Re(_)),N&&rn(O.value,b,Re(_))}async function re(){const b=Ce(q);if(!b)return{valid:!0,results:{},errors:{},source:"none"};i.value=!0;const _=Mr(b)||Vt(b)?await u_(b,d):await d_(b,d,{names:P.value,bailsMap:D.value});return i.value=!1,_}const de=Be((b,{evt:_})=>{ad(_)&&_.target.submit()});kn(()=>{if(e!=null&&e.initialErrors&&S(e.initialErrors),e!=null&&e.initialTouched&&M(e.initialTouched),e!=null&&e.validateOnMount){H();return}T.validateSchema&&T.validateSchema("silent")}),He(q)&&It(q,()=>{var b;(b=T.validateSchema)===null||b===void 0||b.call(T,"validated-only")}),An(fo,T);function Ee(b,_){const N=ot(_)||_==null?void 0:_.label,Z=X(ie(b))||B(b,{label:N}),se=()=>ot(_)?_(cr(Z,ur)):_||{};function ue(){var je;Z.touched=!0,((je=se().validateOnBlur)!==null&&je!==void 0?je:jn().validateOnBlur)&&U(ie(Z.path))}function oe(){var je;((je=se().validateOnInput)!==null&&je!==void 0?je:jn().validateOnInput)&&xt(()=>{U(ie(Z.path))})}function Ue(){var je;((je=se().validateOnChange)!==null&&je!==void 0?je:jn().validateOnChange)&&xt(()=>{U(ie(Z.path))})}const We=te(()=>{const je={onChange:Ue,onInput:oe,onBlur:ue};return ot(_)?Object.assign(Object.assign({},je),_(cr(Z,ur)).props||{}):_!=null&&_.props?Object.assign(Object.assign({},je),_.props(cr(Z,ur))):je});return[we(b,()=>{var je,ct,_t;return(_t=(je=se().validateOnModelUpdate)!==null&&je!==void 0?je:(ct=jn())===null||ct===void 0?void 0:ct.validateOnModelUpdate)!==null&&_t!==void 0?_t:!0}),We]}function Fe(b){return Array.isArray(b)?b.map(_=>we(_,!0)):we(b)}function Oe(b,_){const[N,Z]=Ee(b,_);function se(){Z.value.onBlur()}function ue(Ue){const We=Nr(Ue);j(ie(b),We,!1),Z.value.onInput()}function oe(Ue){const We=Nr(Ue);j(ie(b),We,!1),Z.value.onChange()}return te(()=>Object.assign(Object.assign({},Z.value),{onBlur:se,onInput:ue,onChange:oe,value:N.value}))}function pt(b,_){const[N,Z]=Ee(b,_),se=X(ie(b));function ue(oe){N.value=oe}return te(()=>{const oe=ot(_)?_(cr(se,ur)):_||{};return Object.assign({[oe.model||"modelValue"]:N.value,[`onUpdate:${oe.model||"modelValue"}`]:ue},Z.value)})}const nt=Object.assign(Object.assign({},T),{values:Ri(d),handleReset:()=>z(),submitForm:de});return An(q0,nt),nt}function A_(e,t,n,s){const r={touched:"some",pending:"some",valid:"every"},o=te(()=>!yt(t,Ce(n)));function i(){const l=e.value;return Pt(r).reduce((d,c)=>{const f=r[c];return d[c]=l[f](p=>p[c]),d},{})}const a=Qt(i());return ih(()=>{const l=i();a.touched=l.touched,a.valid=l.valid,a.pending=l.pending}),te(()=>Object.assign(Object.assign({initialValues:Ce(n)},a),{valid:a.valid&&!Pt(s.value).length,dirty:o.value}))}function O_(e,t,n){const s=md(n),r=ve(s),o=ve(Re(s));function i(a,l){l!=null&&l.force?(r.value=Re(a),o.value=Re(a)):(r.value=zs(Re(r.value)||{},Re(a)),o.value=zs(Re(o.value)||{},Re(a))),l!=null&&l.updateFields&&e.value.forEach(d=>{if(d.touched)return;const f=Et(r.value,ie(d.path));rn(t,ie(d.path),Re(f))})}return{initialValues:r,originalInitialValues:o,setInitialValues:i}}function T_(e,t){return t?{valid:e.valid&&t.valid,errors:[...e.errors,...t.errors]}:e}const R_=Zs({name:"Form",inheritAttrs:!1,props:{as:{type:null,default:"form"},validationSchema:{type:Object,default:void 0},initialValues:{type:Object,default:void 0},initialErrors:{type:Object,default:void 0},initialTouched:{type:Object,default:void 0},validateOnMount:{type:Boolean,default:!1},onSubmit:{type:Function,default:void 0},onInvalidSubmit:{type:Function,default:void 0},keepValues:{type:Boolean,default:!1},name:{type:String,default:"Form"}},setup(e,t){const n=Nn(e,"validationSchema"),s=Nn(e,"keepValues"),{errors:r,errorBag:o,values:i,meta:a,isSubmitting:l,isValidating:d,submitCount:c,controlledValues:f,validate:p,validateField:g,handleReset:y,resetForm:S,handleSubmit:E,setErrors:C,setFieldError:P,setFieldValue:D,setValues:A,setFieldTouched:h,setTouched:G,resetField:O}=C_({validationSchema:n.value?n:void 0,initialValues:e.initialValues,initialErrors:e.initialErrors,initialTouched:e.initialTouched,validateOnMount:e.validateOnMount,keepValuesOnUnmount:s,name:e.name}),W=E((ge,{evt:X})=>{ad(X)&&X.target.submit()},e.onInvalidSubmit),I=e.onSubmit?E(e.onSubmit,e.onInvalidSubmit):W;function ne(ge){Ki(ge)&&ge.preventDefault(),y(),typeof t.attrs.onReset=="function"&&t.attrs.onReset()}function q(ge,X){return E(typeof ge=="function"&&!X?ge:X,e.onInvalidSubmit)(ge)}function B(){return Re(i)}function pe(){return Re(a.value)}function Se(){return Re(r.value)}function Me(){return{meta:a.value,errors:r.value,errorBag:o.value,values:i,isSubmitting:l.value,isValidating:d.value,submitCount:c.value,controlledValues:f.value,validate:p,validateField:g,handleSubmit:q,handleReset:y,submitForm:W,setErrors:C,setFieldError:P,setFieldValue:D,setValues:A,setFieldTouched:h,setTouched:G,resetForm:S,resetField:O,getValues:B,getMeta:pe,getErrors:Se}}return t.expose({setFieldError:P,setErrors:C,setFieldValue:D,setValues:A,setFieldTouched:h,setTouched:G,resetForm:S,validate:p,validateField:g,resetField:O,getValues:B,getMeta:pe,getErrors:Se,values:i,meta:a,errors:r}),function(){const X=e.as==="form"?e.as:e.as?Jr(e.as):null,be=Ji(X,t,Me);return X?Hn(X,Object.assign(Object.assign(Object.assign({},X==="form"?{novalidate:!0}:{}),t.attrs),{onSubmit:I,onReset:ne}),be):be}}}),vd=R_,x_=Zs({name:"ErrorMessage",props:{as:{type:String,default:void 0},name:{type:String,required:!0}},setup(e,t){const n=St(fo,void 0),s=te(()=>n==null?void 0:n.errors.value[e.name]);function r(){return{message:s.value}}return()=>{if(!s.value)return;const o=e.as?Jr(e.as):e.as,i=Ji(o,t,r),a=Object.assign({role:"alert"},t.attrs);return!o&&(Array.isArray(i)||!i)&&(i!=null&&i.length)?i:(Array.isArray(i)||!i)&&!(i!=null&&i.length)?Hn(o||"span",a,s.value):Hn(o,a,i)}}}),yd=x_;var WC=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function P_(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Lo,Il;function k_(){if(Il)return Lo;Il=1;function e(C){this._maxSize=C,this.clear()}e.prototype.clear=function(){this._size=0,this._values=Object.create(null)},e.prototype.get=function(C){return this._values[C]},e.prototype.set=function(C,P){return this._size>=this._maxSize&&this.clear(),C in this._values||this._size++,this._values[C]=P};var t=/[^.^\]^[]+|(?=\[\]|\.\.)/g,n=/^\d+$/,s=/^\d/,r=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,o=/^\s*(['"]?)(.*?)(\1)\s*$/,i=512,a=new e(i),l=new e(i),d=new e(i);Lo={Cache:e,split:f,normalizePath:c,setter:function(C){var P=c(C);return l.get(C)||l.set(C,function(A,h){for(var G=0,O=P.length,W=A;G<O-1;){var I=P[G];if(I==="__proto__"||I==="constructor"||I==="prototype")return A;W=W[P[G++]]}W[P[G]]=h})},getter:function(C,P){var D=c(C);return d.get(C)||d.set(C,function(h){for(var G=0,O=D.length;G<O;)if(h!=null||!P)h=h[D[G++]];else return;return h})},join:function(C){return C.reduce(function(P,D){return P+(g(D)||n.test(D)?"["+D+"]":(P?".":"")+D)},"")},forEach:function(C,P,D){p(Array.isArray(C)?C:f(C),P,D)}};function c(C){return a.get(C)||a.set(C,f(C).map(function(P){return P.replace(o,"$2")}))}function f(C){return C.match(t)||[""]}function p(C,P,D){var A=C.length,h,G,O,W;for(G=0;G<A;G++)h=C[G],h&&(E(h)&&(h='"'+h+'"'),W=g(h),O=!W&&/^\d+$/.test(h),P.call(D,h,W,O,G,C))}function g(C){return typeof C=="string"&&C&&["'",'"'].indexOf(C.charAt(0))!==-1}function y(C){return C.match(s)&&!C.match(n)}function S(C){return r.test(C)}function E(C){return!g(C)&&(y(C)||S(C))}return Lo}var Bn=k_(),jo,$l;function I_(){if($l)return jo;$l=1;const e=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,t=c=>c.match(e)||[],n=c=>c[0].toUpperCase()+c.slice(1),s=(c,f)=>t(c).join(f).toLowerCase(),r=c=>t(c).reduce((f,p)=>`${f}${f?p[0].toUpperCase()+p.slice(1).toLowerCase():p.toLowerCase()}`,"");return jo={words:t,upperFirst:n,camelCase:r,pascalCase:c=>n(r(c)),snakeCase:c=>s(c,"_"),kebabCase:c=>s(c,"-"),sentenceCase:c=>n(s(c," ")),titleCase:c=>t(c).map(n).join(" ")},jo}var Uo=I_(),dr={exports:{}},Fl;function $_(){if(Fl)return dr.exports;Fl=1,dr.exports=function(r){return e(t(r),r)},dr.exports.array=e;function e(r,o){var i=r.length,a=new Array(i),l={},d=i,c=n(o),f=s(r);for(o.forEach(function(g){if(!f.has(g[0])||!f.has(g[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});d--;)l[d]||p(r[d],d,new Set);return a;function p(g,y,S){if(S.has(g)){var E;try{E=", node was:"+JSON.stringify(g)}catch{E=""}throw new Error("Cyclic dependency"+E)}if(!f.has(g))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(g));if(!l[y]){l[y]=!0;var C=c.get(g)||new Set;if(C=Array.from(C),y=C.length){S.add(g);do{var P=C[--y];p(P,f.get(P),S)}while(y);S.delete(g)}a[--i]=g}}}function t(r){for(var o=new Set,i=0,a=r.length;i<a;i++){var l=r[i];o.add(l[0]),o.add(l[1])}return Array.from(o)}function n(r){for(var o=new Map,i=0,a=r.length;i<a;i++){var l=r[i];o.has(l[0])||o.set(l[0],new Set),o.has(l[1])||o.set(l[1],new Set),o.get(l[0]).add(l[1])}return o}function s(r){for(var o=new Map,i=0,a=r.length;i<a;i++)o.set(r[i],i);return o}return dr.exports}var F_=$_();const D_=P_(F_),M_=Object.prototype.toString,N_=Error.prototype.toString,V_=RegExp.prototype.toString,L_=typeof Symbol<"u"?Symbol.prototype.toString:()=>"",j_=/^Symbol\((.*)\)(.*)$/;function U_(e){return e!=+e?"NaN":e===0&&1/e<0?"-0":""+e}function Dl(e,t=!1){if(e==null||e===!0||e===!1)return""+e;const n=typeof e;if(n==="number")return U_(e);if(n==="string")return t?`"${e}"`:e;if(n==="function")return"[Function "+(e.name||"anonymous")+"]";if(n==="symbol")return L_.call(e).replace(j_,"Symbol($1)");const s=M_.call(e).slice(8,-1);return s==="Date"?isNaN(e.getTime())?""+e:e.toISOString(e):s==="Error"||e instanceof Error?"["+N_.call(e)+"]":s==="RegExp"?V_.call(e):null}function Tn(e,t){let n=Dl(e,t);return n!==null?n:JSON.stringify(e,function(s,r){let o=Dl(this[s],t);return o!==null?o:r},2)}function _d(e){return e==null?[]:[].concat(e)}let bd,wd,Ed,q_=/\$\{\s*(\w+)\s*\}/g;bd=Symbol.toStringTag;class Ml{constructor(t,n,s,r){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[bd]="Error",this.name="ValidationError",this.value=n,this.path=s,this.type=r,this.errors=[],this.inner=[],_d(t).forEach(o=>{if(wt.isError(o)){this.errors.push(...o.errors);const i=o.inner.length?o.inner:[o];this.inner.push(...i)}else this.errors.push(o)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}wd=Symbol.hasInstance;Ed=Symbol.toStringTag;class wt extends Error{static formatError(t,n){const s=n.label||n.path||"this";return n=Object.assign({},n,{path:s,originalPath:n.path}),typeof t=="string"?t.replace(q_,(r,o)=>Tn(n[o])):typeof t=="function"?t(n):t}static isError(t){return t&&t.name==="ValidationError"}constructor(t,n,s,r,o){const i=new Ml(t,n,s,r);if(o)return i;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[Ed]="Error",this.name=i.name,this.message=i.message,this.type=i.type,this.value=i.value,this.path=i.path,this.errors=i.errors,this.inner=i.inner,Error.captureStackTrace&&Error.captureStackTrace(this,wt)}static[wd](t){return Ml[Symbol.hasInstance](t)||super[Symbol.hasInstance](t)}}let Jt={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:e,type:t,value:n,originalValue:s})=>{const r=s!=null&&s!==n?` (cast from the value \`${Tn(s,!0)}\`).`:".";return t!=="mixed"?`${e} must be a \`${t}\` type, but the final value was: \`${Tn(n,!0)}\``+r:`${e} must match the configured type. The validated value was: \`${Tn(n,!0)}\``+r}},bt={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},B_={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},pi={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},gi={isValue:"${path} field must be ${value}"},br={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},G_={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},H_={notType:e=>{const{path:t,value:n,spec:s}=e,r=s.types.length;if(Array.isArray(n)){if(n.length<r)return`${t} tuple value has too few items, expected a length of ${r} but got ${n.length} for value: \`${Tn(n,!0)}\``;if(n.length>r)return`${t} tuple value has too many items, expected a length of ${r} but got ${n.length} for value: \`${Tn(n,!0)}\``}return wt.formatError(Jt.notType,e)}};Object.assign(Object.create(null),{mixed:Jt,string:bt,number:B_,date:pi,object:br,array:G_,boolean:gi,tuple:H_});const Yi=e=>e&&e.__isYupSchema__;class Vr{static fromOptions(t,n){if(!n.then&&!n.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:s,then:r,otherwise:o}=n,i=typeof s=="function"?s:(...a)=>a.every(l=>l===s);return new Vr(t,(a,l)=>{var d;let c=i(...a)?r:o;return(d=c==null?void 0:c(l))!=null?d:l})}constructor(t,n){this.fn=void 0,this.refs=t,this.refs=t,this.fn=n}resolve(t,n){let s=this.refs.map(o=>o.getValue(n==null?void 0:n.value,n==null?void 0:n.parent,n==null?void 0:n.context)),r=this.fn(s,t,n);if(r===void 0||r===t)return t;if(!Yi(r))throw new TypeError("conditions must return a schema object");return r.resolve(n)}}const fr={context:"$",value:"."};class Yn{constructor(t,n={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,typeof t!="string")throw new TypeError("ref must be a string, got: "+t);if(this.key=t.trim(),t==="")throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===fr.context,this.isValue=this.key[0]===fr.value,this.isSibling=!this.isContext&&!this.isValue;let s=this.isContext?fr.context:this.isValue?fr.value:"";this.path=this.key.slice(s.length),this.getter=this.path&&Bn.getter(this.path,!0),this.map=n.map}getValue(t,n,s){let r=this.isContext?s:this.isValue?t:n;return this.getter&&(r=this.getter(r||{})),this.map&&(r=this.map(r)),r}cast(t,n){return this.getValue(t,n==null?void 0:n.parent,n==null?void 0:n.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(t){return t&&t.__isYupRef}}Yn.prototype.__isYupRef=!0;const dn=e=>e==null;function ts(e){function t({value:n,path:s="",options:r,originalValue:o,schema:i},a,l){const{name:d,test:c,params:f,message:p,skipAbsent:g}=e;let{parent:y,context:S,abortEarly:E=i.spec.abortEarly,disableStackTrace:C=i.spec.disableStackTrace}=r;function P(q){return Yn.isRef(q)?q.getValue(n,y,S):q}function D(q={}){const B=Object.assign({value:n,originalValue:o,label:i.spec.label,path:q.path||s,spec:i.spec,disableStackTrace:q.disableStackTrace||C},f,q.params);for(const Se of Object.keys(B))B[Se]=P(B[Se]);const pe=new wt(wt.formatError(q.message||p,B),n,B.path,q.type||d,B.disableStackTrace);return pe.params=B,pe}const A=E?a:l;let h={path:s,parent:y,type:d,from:r.from,createError:D,resolve:P,options:r,originalValue:o,schema:i};const G=q=>{wt.isError(q)?A(q):q?l(null):A(D())},O=q=>{wt.isError(q)?A(q):a(q)};if(g&&dn(n))return G(!0);let I;try{var ne;if(I=c.call(h,n,h),typeof((ne=I)==null?void 0:ne.then)=="function"){if(r.sync)throw new Error(`Validation test of type: "${h.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(I).then(G,O)}}catch(q){O(q);return}G(I)}return t.OPTIONS=e,t}function z_(e,t,n,s=n){let r,o,i;return t?(Bn.forEach(t,(a,l,d)=>{let c=l?a.slice(1,a.length-1):a;e=e.resolve({context:s,parent:r,value:n});let f=e.type==="tuple",p=d?parseInt(c,10):0;if(e.innerType||f){if(f&&!d)throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${i}" must contain an index to the tuple element, e.g. "${i}[0]"`);if(n&&p>=n.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${a}, in the path: ${t}. because there is no value at that index. `);r=n,n=n&&n[p],e=f?e.spec.types[p]:e.innerType}if(!d){if(!e.fields||!e.fields[c])throw new Error(`The schema does not contain the path: ${t}. (failed at: ${i} which is a type: "${e.type}")`);r=n,n=n&&n[c],e=e.fields[c]}o=c,i=l?"["+a+"]":"."+a}),{schema:e,parent:r,parentPath:o}):{parent:r,parentPath:t,schema:e}}class Lr extends Set{describe(){const t=[];for(const n of this.values())t.push(Yn.isRef(n)?n.describe():n);return t}resolveAll(t){let n=[];for(const s of this.values())n.push(t(s));return n}clone(){return new Lr(this.values())}merge(t,n){const s=this.clone();return t.forEach(r=>s.add(r)),n.forEach(r=>s.delete(r)),s}}function rs(e,t=new Map){if(Yi(e)||!e||typeof e!="object")return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime()),t.set(e,n);else if(e instanceof RegExp)n=new RegExp(e),t.set(e,n);else if(Array.isArray(e)){n=new Array(e.length),t.set(e,n);for(let s=0;s<e.length;s++)n[s]=rs(e[s],t)}else if(e instanceof Map){n=new Map,t.set(e,n);for(const[s,r]of e.entries())n.set(s,rs(r,t))}else if(e instanceof Set){n=new Set,t.set(e,n);for(const s of e)n.add(rs(s,t))}else if(e instanceof Object){n={},t.set(e,n);for(const[s,r]of Object.entries(e))n[s]=rs(r,t)}else throw Error(`Unable to clone ${e}`);return n}class Bt{constructor(t){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new Lr,this._blacklist=new Lr,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(Jt.notType)}),this.type=t.type,this._typeCheck=t.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},t==null?void 0:t.spec),this.withMutation(n=>{n.nonNullable()})}get _type(){return this.type}clone(t){if(this._mutate)return t&&Object.assign(this.spec,t),this;const n=Object.create(Object.getPrototypeOf(this));return n.type=this.type,n._typeCheck=this._typeCheck,n._whitelist=this._whitelist.clone(),n._blacklist=this._blacklist.clone(),n.internalTests=Object.assign({},this.internalTests),n.exclusiveTests=Object.assign({},this.exclusiveTests),n.deps=[...this.deps],n.conditions=[...this.conditions],n.tests=[...this.tests],n.transforms=[...this.transforms],n.spec=rs(Object.assign({},this.spec,t)),n}label(t){let n=this.clone();return n.spec.label=t,n}meta(...t){if(t.length===0)return this.spec.meta;let n=this.clone();return n.spec.meta=Object.assign(n.spec.meta||{},t[0]),n}withMutation(t){let n=this._mutate;this._mutate=!0;let s=t(this);return this._mutate=n,s}concat(t){if(!t||t===this)return this;if(t.type!==this.type&&this.type!=="mixed")throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${t.type}`);let n=this,s=t.clone();const r=Object.assign({},n.spec,s.spec);return s.spec=r,s.internalTests=Object.assign({},n.internalTests,s.internalTests),s._whitelist=n._whitelist.merge(t._whitelist,t._blacklist),s._blacklist=n._blacklist.merge(t._blacklist,t._whitelist),s.tests=n.tests,s.exclusiveTests=n.exclusiveTests,s.withMutation(o=>{t.tests.forEach(i=>{o.test(i.OPTIONS)})}),s.transforms=[...n.transforms,...s.transforms],s}isType(t){return t==null?!!(this.spec.nullable&&t===null||this.spec.optional&&t===void 0):this._typeCheck(t)}resolve(t){let n=this;if(n.conditions.length){let s=n.conditions;n=n.clone(),n.conditions=[],n=s.reduce((r,o)=>o.resolve(r,t),n),n=n.resolve(t)}return n}resolveOptions(t){var n,s,r,o;return Object.assign({},t,{from:t.from||[],strict:(n=t.strict)!=null?n:this.spec.strict,abortEarly:(s=t.abortEarly)!=null?s:this.spec.abortEarly,recursive:(r=t.recursive)!=null?r:this.spec.recursive,disableStackTrace:(o=t.disableStackTrace)!=null?o:this.spec.disableStackTrace})}cast(t,n={}){let s=this.resolve(Object.assign({value:t},n)),r=n.assert==="ignore-optionality",o=s._cast(t,n);if(n.assert!==!1&&!s.isType(o)){if(r&&dn(o))return o;let i=Tn(t),a=Tn(o);throw new TypeError(`The value of ${n.path||"field"} could not be cast to a value that satisfies the schema type: "${s.type}". 

attempted value: ${i} 
`+(a!==i?`result of cast: ${a}`:""))}return o}_cast(t,n){let s=t===void 0?t:this.transforms.reduce((r,o)=>o.call(this,r,t,this),t);return s===void 0&&(s=this.getDefault(n)),s}_validate(t,n={},s,r){let{path:o,originalValue:i=t,strict:a=this.spec.strict}=n,l=t;a||(l=this._cast(l,Object.assign({assert:!1},n)));let d=[];for(let c of Object.values(this.internalTests))c&&d.push(c);this.runTests({path:o,value:l,originalValue:i,options:n,tests:d},s,c=>{if(c.length)return r(c,l);this.runTests({path:o,value:l,originalValue:i,options:n,tests:this.tests},s,r)})}runTests(t,n,s){let r=!1,{tests:o,value:i,originalValue:a,path:l,options:d}=t,c=S=>{r||(r=!0,n(S,i))},f=S=>{r||(r=!0,s(S,i))},p=o.length,g=[];if(!p)return f([]);let y={value:i,originalValue:a,path:l,options:d,schema:this};for(let S=0;S<o.length;S++){const E=o[S];E(y,c,function(P){P&&(Array.isArray(P)?g.push(...P):g.push(P)),--p<=0&&f(g)})}}asNestedTest({key:t,index:n,parent:s,parentPath:r,originalParent:o,options:i}){const a=t??n;if(a==null)throw TypeError("Must include `key` or `index` for nested validations");const l=typeof a=="number";let d=s[a];const c=Object.assign({},i,{strict:!0,parent:s,value:d,originalValue:o[a],key:void 0,[l?"index":"key"]:a,path:l||a.includes(".")?`${r||""}[${l?a:`"${a}"`}]`:(r?`${r}.`:"")+t});return(f,p,g)=>this.resolve(c)._validate(d,c,p,g)}validate(t,n){var s;let r=this.resolve(Object.assign({},n,{value:t})),o=(s=n==null?void 0:n.disableStackTrace)!=null?s:r.spec.disableStackTrace;return new Promise((i,a)=>r._validate(t,n,(l,d)=>{wt.isError(l)&&(l.value=d),a(l)},(l,d)=>{l.length?a(new wt(l,d,void 0,void 0,o)):i(d)}))}validateSync(t,n){var s;let r=this.resolve(Object.assign({},n,{value:t})),o,i=(s=n==null?void 0:n.disableStackTrace)!=null?s:r.spec.disableStackTrace;return r._validate(t,Object.assign({},n,{sync:!0}),(a,l)=>{throw wt.isError(a)&&(a.value=l),a},(a,l)=>{if(a.length)throw new wt(a,t,void 0,void 0,i);o=l}),o}isValid(t,n){return this.validate(t,n).then(()=>!0,s=>{if(wt.isError(s))return!1;throw s})}isValidSync(t,n){try{return this.validateSync(t,n),!0}catch(s){if(wt.isError(s))return!1;throw s}}_getDefault(t){let n=this.spec.default;return n==null?n:typeof n=="function"?n.call(this,t):rs(n)}getDefault(t){return this.resolve(t||{})._getDefault(t)}default(t){return arguments.length===0?this._getDefault():this.clone({default:t})}strict(t=!0){return this.clone({strict:t})}nullability(t,n){const s=this.clone({nullable:t});return s.internalTests.nullable=ts({message:n,name:"nullable",test(r){return r===null?this.schema.spec.nullable:!0}}),s}optionality(t,n){const s=this.clone({optional:t});return s.internalTests.optionality=ts({message:n,name:"optionality",test(r){return r===void 0?this.schema.spec.optional:!0}}),s}optional(){return this.optionality(!0)}defined(t=Jt.defined){return this.optionality(!1,t)}nullable(){return this.nullability(!0)}nonNullable(t=Jt.notNull){return this.nullability(!1,t)}required(t=Jt.required){return this.clone().withMutation(n=>n.nonNullable(t).defined(t))}notRequired(){return this.clone().withMutation(t=>t.nullable().optional())}transform(t){let n=this.clone();return n.transforms.push(t),n}test(...t){let n;if(t.length===1?typeof t[0]=="function"?n={test:t[0]}:n=t[0]:t.length===2?n={name:t[0],test:t[1]}:n={name:t[0],message:t[1],test:t[2]},n.message===void 0&&(n.message=Jt.default),typeof n.test!="function")throw new TypeError("`test` is a required parameters");let s=this.clone(),r=ts(n),o=n.exclusive||n.name&&s.exclusiveTests[n.name]===!0;if(n.exclusive&&!n.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return n.name&&(s.exclusiveTests[n.name]=!!n.exclusive),s.tests=s.tests.filter(i=>!(i.OPTIONS.name===n.name&&(o||i.OPTIONS.test===r.OPTIONS.test))),s.tests.push(r),s}when(t,n){!Array.isArray(t)&&typeof t!="string"&&(n=t,t=".");let s=this.clone(),r=_d(t).map(o=>new Yn(o));return r.forEach(o=>{o.isSibling&&s.deps.push(o.key)}),s.conditions.push(typeof n=="function"?new Vr(r,n):Vr.fromOptions(r,n)),s}typeError(t){let n=this.clone();return n.internalTests.typeError=ts({message:t,name:"typeError",skipAbsent:!0,test(s){return this.schema._typeCheck(s)?!0:this.createError({params:{type:this.schema.type}})}}),n}oneOf(t,n=Jt.oneOf){let s=this.clone();return t.forEach(r=>{s._whitelist.add(r),s._blacklist.delete(r)}),s.internalTests.whiteList=ts({message:n,name:"oneOf",skipAbsent:!0,test(r){let o=this.schema._whitelist,i=o.resolveAll(this.resolve);return i.includes(r)?!0:this.createError({params:{values:Array.from(o).join(", "),resolved:i}})}}),s}notOneOf(t,n=Jt.notOneOf){let s=this.clone();return t.forEach(r=>{s._blacklist.add(r),s._whitelist.delete(r)}),s.internalTests.blacklist=ts({message:n,name:"notOneOf",test(r){let o=this.schema._blacklist,i=o.resolveAll(this.resolve);return i.includes(r)?this.createError({params:{values:Array.from(o).join(", "),resolved:i}}):!0}}),s}strip(t=!0){let n=this.clone();return n.spec.strip=t,n}describe(t){const n=(t?this.resolve(t):this).clone(),{label:s,meta:r,optional:o,nullable:i}=n.spec;return{meta:r,label:s,optional:o,nullable:i,default:n.getDefault(t),type:n.type,oneOf:n._whitelist.describe(),notOneOf:n._blacklist.describe(),tests:n.tests.map(l=>({name:l.OPTIONS.name,params:l.OPTIONS.params})).filter((l,d,c)=>c.findIndex(f=>f.name===l.name)===d)}}}Bt.prototype.__isYupSchema__=!0;for(const e of["validate","validateSync"])Bt.prototype[`${e}At`]=function(t,n,s={}){const{parent:r,parentPath:o,schema:i}=z_(this,t,n,s.context);return i[e](r&&r[o],Object.assign({},s,{parent:r,path:t}))};for(const e of["equals","is"])Bt.prototype[e]=Bt.prototype.oneOf;for(const e of["not","nope"])Bt.prototype[e]=Bt.prototype.notOneOf;function Sd(){return new Cd}class Cd extends Bt{constructor(){super({type:"boolean",check(t){return t instanceof Boolean&&(t=t.valueOf()),typeof t=="boolean"}}),this.withMutation(()=>{this.transform((t,n,s)=>{if(s.spec.coerce&&!s.isType(t)){if(/^(true|1)$/i.test(String(t)))return!0;if(/^(false|0)$/i.test(String(t)))return!1}return t})})}isTrue(t=gi.isValue){return this.test({message:t,name:"is-value",exclusive:!0,params:{value:"true"},test(n){return dn(n)||n===!0}})}isFalse(t=gi.isValue){return this.test({message:t,name:"is-value",exclusive:!0,params:{value:"false"},test(n){return dn(n)||n===!1}})}default(t){return super.default(t)}defined(t){return super.defined(t)}optional(){return super.optional()}required(t){return super.required(t)}notRequired(){return super.notRequired()}nullable(){return super.nullable()}nonNullable(t){return super.nonNullable(t)}strip(t){return super.strip(t)}}Sd.prototype=Cd.prototype;const K_=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function W_(e){const t=mi(e);if(!t)return Date.parse?Date.parse(e):Number.NaN;if(t.z===void 0&&t.plusMinus===void 0)return new Date(t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond).valueOf();let n=0;return t.z!=="Z"&&t.plusMinus!==void 0&&(n=t.hourOffset*60+t.minuteOffset,t.plusMinus==="+"&&(n=0-n)),Date.UTC(t.year,t.month,t.day,t.hour,t.minute+n,t.second,t.millisecond)}function mi(e){var t,n;const s=K_.exec(e);return s?{year:sn(s[1]),month:sn(s[2],1)-1,day:sn(s[3],1),hour:sn(s[4]),minute:sn(s[5]),second:sn(s[6]),millisecond:s[7]?sn(s[7].substring(0,3)):0,precision:(t=(n=s[7])==null?void 0:n.length)!=null?t:void 0,z:s[8]||void 0,plusMinus:s[9]||void 0,hourOffset:sn(s[10]),minuteOffset:sn(s[11])}:null}function sn(e,t=0){return Number(e)||t}let J_=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Y_=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,Z_=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Q_="^\\d{4}-\\d{2}-\\d{2}",X_="\\d{2}:\\d{2}:\\d{2}",eb="(([+-]\\d{2}(:?\\d{2})?)|Z)",tb=new RegExp(`${Q_}T${X_}(\\.\\d+)?${eb}$`),nb=e=>dn(e)||e===e.trim(),sb={}.toString();function ds(){return new Ad}class Ad extends Bt{constructor(){super({type:"string",check(t){return t instanceof String&&(t=t.valueOf()),typeof t=="string"}}),this.withMutation(()=>{this.transform((t,n,s)=>{if(!s.spec.coerce||s.isType(t)||Array.isArray(t))return t;const r=t!=null&&t.toString?t.toString():t;return r===sb?t:r})})}required(t){return super.required(t).withMutation(n=>n.test({message:t||Jt.required,name:"required",skipAbsent:!0,test:s=>!!s.length}))}notRequired(){return super.notRequired().withMutation(t=>(t.tests=t.tests.filter(n=>n.OPTIONS.name!=="required"),t))}length(t,n=bt.length){return this.test({message:n,name:"length",exclusive:!0,params:{length:t},skipAbsent:!0,test(s){return s.length===this.resolve(t)}})}min(t,n=bt.min){return this.test({message:n,name:"min",exclusive:!0,params:{min:t},skipAbsent:!0,test(s){return s.length>=this.resolve(t)}})}max(t,n=bt.max){return this.test({name:"max",exclusive:!0,message:n,params:{max:t},skipAbsent:!0,test(s){return s.length<=this.resolve(t)}})}matches(t,n){let s=!1,r,o;return n&&(typeof n=="object"?{excludeEmptyString:s=!1,message:r,name:o}=n:r=n),this.test({name:o||"matches",message:r||bt.matches,params:{regex:t},skipAbsent:!0,test:i=>i===""&&s||i.search(t)!==-1})}email(t=bt.email){return this.matches(J_,{name:"email",message:t,excludeEmptyString:!0})}url(t=bt.url){return this.matches(Y_,{name:"url",message:t,excludeEmptyString:!0})}uuid(t=bt.uuid){return this.matches(Z_,{name:"uuid",message:t,excludeEmptyString:!1})}datetime(t){let n="",s,r;return t&&(typeof t=="object"?{message:n="",allowOffset:s=!1,precision:r=void 0}=t:n=t),this.matches(tb,{name:"datetime",message:n||bt.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:n||bt.datetime_offset,params:{allowOffset:s},skipAbsent:!0,test:o=>{if(!o||s)return!0;const i=mi(o);return i?!!i.z:!1}}).test({name:"datetime_precision",message:n||bt.datetime_precision,params:{precision:r},skipAbsent:!0,test:o=>{if(!o||r==null)return!0;const i=mi(o);return i?i.precision===r:!1}})}ensure(){return this.default("").transform(t=>t===null?"":t)}trim(t=bt.trim){return this.transform(n=>n!=null?n.trim():n).test({message:t,name:"trim",test:nb})}lowercase(t=bt.lowercase){return this.transform(n=>dn(n)?n:n.toLowerCase()).test({message:t,name:"string_case",exclusive:!0,skipAbsent:!0,test:n=>dn(n)||n===n.toLowerCase()})}uppercase(t=bt.uppercase){return this.transform(n=>dn(n)?n:n.toUpperCase()).test({message:t,name:"string_case",exclusive:!0,skipAbsent:!0,test:n=>dn(n)||n===n.toUpperCase()})}}ds.prototype=Ad.prototype;let rb=new Date(""),ob=e=>Object.prototype.toString.call(e)==="[object Date]";class Zi extends Bt{constructor(){super({type:"date",check(t){return ob(t)&&!isNaN(t.getTime())}}),this.withMutation(()=>{this.transform((t,n,s)=>!s.spec.coerce||s.isType(t)||t===null?t:(t=W_(t),isNaN(t)?Zi.INVALID_DATE:new Date(t)))})}prepareParam(t,n){let s;if(Yn.isRef(t))s=t;else{let r=this.cast(t);if(!this._typeCheck(r))throw new TypeError(`\`${n}\` must be a Date or a value that can be \`cast()\` to a Date`);s=r}return s}min(t,n=pi.min){let s=this.prepareParam(t,"min");return this.test({message:n,name:"min",exclusive:!0,params:{min:t},skipAbsent:!0,test(r){return r>=this.resolve(s)}})}max(t,n=pi.max){let s=this.prepareParam(t,"max");return this.test({message:n,name:"max",exclusive:!0,params:{max:t},skipAbsent:!0,test(r){return r<=this.resolve(s)}})}}Zi.INVALID_DATE=rb;function ib(e,t=[]){let n=[],s=new Set,r=new Set(t.map(([i,a])=>`${i}-${a}`));function o(i,a){let l=Bn.split(i)[0];s.add(l),r.has(`${a}-${l}`)||n.push([a,l])}for(const i of Object.keys(e)){let a=e[i];s.add(i),Yn.isRef(a)&&a.isSibling?o(a.path,i):Yi(a)&&"deps"in a&&a.deps.forEach(l=>o(l,i))}return D_.array(Array.from(s),n).reverse()}function Nl(e,t){let n=1/0;return e.some((s,r)=>{var o;if((o=t.path)!=null&&o.includes(s))return n=r,!0}),n}function Od(e){return(t,n)=>Nl(e,t)-Nl(e,n)}const ab=(e,t,n)=>{if(typeof e!="string")return e;let s=e;try{s=JSON.parse(e)}catch{}return n.isType(s)?s:e};function wr(e){if("fields"in e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=wr(s);return e.setFields(t)}if(e.type==="array"){const t=e.optional();return t.innerType&&(t.innerType=wr(t.innerType)),t}return e.type==="tuple"?e.optional().clone({types:e.spec.types.map(wr)}):"optional"in e?e.optional():e}const lb=(e,t)=>{const n=[...Bn.normalizePath(t)];if(n.length===1)return n[0]in e;let s=n.pop(),r=Bn.getter(Bn.join(n),!0)(e);return!!(r&&s in r)};let Vl=e=>Object.prototype.toString.call(e)==="[object Object]";function Ll(e,t){let n=Object.keys(e.fields);return Object.keys(t).filter(s=>n.indexOf(s)===-1)}const cb=Od([]);function Qi(e){return new Td(e)}class Td extends Bt{constructor(t){super({type:"object",check(n){return Vl(n)||typeof n=="function"}}),this.fields=Object.create(null),this._sortErrors=cb,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{t&&this.shape(t)})}_cast(t,n={}){var s;let r=super._cast(t,n);if(r===void 0)return this.getDefault(n);if(!this._typeCheck(r))return r;let o=this.fields,i=(s=n.stripUnknown)!=null?s:this.spec.noUnknown,a=[].concat(this._nodes,Object.keys(r).filter(f=>!this._nodes.includes(f))),l={},d=Object.assign({},n,{parent:l,__validating:n.__validating||!1}),c=!1;for(const f of a){let p=o[f],g=f in r;if(p){let y,S=r[f];d.path=(n.path?`${n.path}.`:"")+f,p=p.resolve({value:S,context:n.context,parent:l});let E=p instanceof Bt?p.spec:void 0,C=E==null?void 0:E.strict;if(E!=null&&E.strip){c=c||f in r;continue}y=!n.__validating||!C?p.cast(r[f],d):r[f],y!==void 0&&(l[f]=y)}else g&&!i&&(l[f]=r[f]);(g!==f in l||l[f]!==r[f])&&(c=!0)}return c?l:r}_validate(t,n={},s,r){let{from:o=[],originalValue:i=t,recursive:a=this.spec.recursive}=n;n.from=[{schema:this,value:i},...o],n.__validating=!0,n.originalValue=i,super._validate(t,n,s,(l,d)=>{if(!a||!Vl(d)){r(l,d);return}i=i||d;let c=[];for(let f of this._nodes){let p=this.fields[f];!p||Yn.isRef(p)||c.push(p.asNestedTest({options:n,key:f,parent:d,parentPath:n.path,originalParent:i}))}this.runTests({tests:c,value:d,originalValue:i,options:n},s,f=>{r(f.sort(this._sortErrors).concat(l),d)})})}clone(t){const n=super.clone(t);return n.fields=Object.assign({},this.fields),n._nodes=this._nodes,n._excludedEdges=this._excludedEdges,n._sortErrors=this._sortErrors,n}concat(t){let n=super.concat(t),s=n.fields;for(let[r,o]of Object.entries(this.fields)){const i=s[r];s[r]=i===void 0?o:i}return n.withMutation(r=>r.setFields(s,[...this._excludedEdges,...t._excludedEdges]))}_getDefault(t){if("default"in this.spec)return super._getDefault(t);if(!this._nodes.length)return;let n={};return this._nodes.forEach(s=>{var r;const o=this.fields[s];let i=t;(r=i)!=null&&r.value&&(i=Object.assign({},i,{parent:i.value,value:i.value[s]})),n[s]=o&&"getDefault"in o?o.getDefault(i):void 0}),n}setFields(t,n){let s=this.clone();return s.fields=t,s._nodes=ib(t,n),s._sortErrors=Od(Object.keys(t)),n&&(s._excludedEdges=n),s}shape(t,n=[]){return this.clone().withMutation(s=>{let r=s._excludedEdges;return n.length&&(Array.isArray(n[0])||(n=[n]),r=[...s._excludedEdges,...n]),s.setFields(Object.assign(s.fields,t),r)})}partial(){const t={};for(const[n,s]of Object.entries(this.fields))t[n]="optional"in s&&s.optional instanceof Function?s.optional():s;return this.setFields(t)}deepPartial(){return wr(this)}pick(t){const n={};for(const s of t)this.fields[s]&&(n[s]=this.fields[s]);return this.setFields(n,this._excludedEdges.filter(([s,r])=>t.includes(s)&&t.includes(r)))}omit(t){const n=[];for(const s of Object.keys(this.fields))t.includes(s)||n.push(s);return this.pick(n)}from(t,n,s){let r=Bn.getter(t,!0);return this.transform(o=>{if(!o)return o;let i=o;return lb(o,t)&&(i=Object.assign({},o),s||delete i[t],i[n]=r(o)),i})}json(){return this.transform(ab)}exact(t){return this.test({name:"exact",exclusive:!0,message:t||br.exact,test(n){if(n==null)return!0;const s=Ll(this.schema,n);return s.length===0||this.createError({params:{properties:s.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(t=!0,n=br.noUnknown){typeof t!="boolean"&&(n=t,t=!0);let s=this.test({name:"noUnknown",exclusive:!0,message:n,test(r){if(r==null)return!0;const o=Ll(this.schema,r);return!t||o.length===0||this.createError({params:{unknown:o.join(", ")}})}});return s.spec.noUnknown=t,s}unknown(t=!0,n=br.noUnknown){return this.noUnknown(!t,n)}transformKeys(t){return this.transform(n=>{if(!n)return n;const s={};for(const r of Object.keys(n))s[t(r)]=n[r];return s})}camelCase(){return this.transformKeys(Uo.camelCase)}snakeCase(){return this.transformKeys(Uo.snakeCase)}constantCase(){return this.transformKeys(t=>Uo.snakeCase(t).toUpperCase())}describe(t){const n=(t?this.resolve(t):this).clone(),s=super.describe(t);s.fields={};for(const[o,i]of Object.entries(n.fields)){var r;let a=t;(r=a)!=null&&r.value&&(a=Object.assign({},a,{parent:a.value,value:a.value[o]})),s.fields[o]=i.describe(a)}return s}}Qi.prototype=Td.prototype;const ub="*************-1gid4thgk1lhs4l3e883qk0asumgb7nv.apps.googleusercontent.com";function db(){return new Promise((e,t)=>{if(window.google&&window.google.accounts){console.log("Google Identity Services already loaded"),e(window.google.accounts);return}const n=document.createElement("script");n.src="https://accounts.google.com/gsi/client",n.async=!0,n.defer=!0,n.onload=()=>{console.log("Google Identity Services script loaded successfully"),window.google&&window.google.accounts?e(window.google.accounts):(console.error("Google Identity Services loaded but API not available"),t(new Error("Google Identity Services API not available")))},n.onerror=s=>{console.error("Failed to load Google Identity Services script",s),t(new Error("Failed to load Google Identity Services script"))},document.head.appendChild(n)})}function jr(e){return new Promise(async(t,n)=>{try{const s=await db();s.id.initialize({client_id:ub,callback:e,auto_select:!1,cancel_on_tap_outside:!0,context:"signin",allowed_parent_origin:["http://localhost:3000","http://localhost:3001","http://localhost:5000","http://localhost:5001",window.location.origin]}),console.log("Google Sign-In initialized successfully"),t(s)}catch(s){console.error("Failed to initialize Google Sign-In:",s),n(s)}})}function Rd(e=3e4){return new Promise((t,n)=>{if(!window.google||!window.google.accounts||!window.google.accounts.id){const r=new Error("Google Identity Services not loaded");console.error(r),n(r);return}try{window.google.accounts.id.prompt(r=>{if(clearTimeout(s),r.isNotDisplayed()){const o=r.getNotDisplayedReason();console.warn("Google Sign-In prompt not displayed:",o),n(o==="browser_not_supported"?new Error("Your browser does not support Google Sign-In. Please try a different browser."):o==="invalid_client"?new Error("Invalid Google client configuration. Please contact support."):o==="missing_client_id"?new Error("Google client ID is missing. Please contact support."):o==="third_party_cookies_blocked"?new Error("Third-party cookies are blocked in your browser. Please enable them or use a different browser."):new Error(`Google Sign-In not available (${o}). Please try again later.`))}else if(r.isSkippedMoment()){const o=r.getSkippedReason();console.warn("Google Sign-In moment skipped:",o),n(new Error("Google Sign-In was skipped. Please try again."))}else if(r.isDismissedMoment()){const o=r.getDismissedReason();console.warn("Google Sign-In prompt dismissed:",o),o==="credential_returned"?t():n(o==="cancel_called"?new Error("Google Sign-In was cancelled."):o==="user_cancel"?new Error("Google Sign-In was cancelled by user."):new Error("Google Sign-In was dismissed. Please try again."))}else t()})}catch(r){clearTimeout(s),console.error("Error prompting Google Sign-In:",r),n(r)}const s=setTimeout(()=>{console.warn("Google Sign-In prompt timed out after",e,"ms"),n(new Error("Google Sign-In timed out. Please try again."))},e)})}const xd="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%2048%2048'%20width='48px'%20height='48px'%3e%3cpath%20fill='%23FFC107'%20d='M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z'/%3e%3cpath%20fill='%23FF3D00'%20d='M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z'/%3e%3cpath%20fill='%234CAF50'%20d='M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z'/%3e%3cpath%20fill='%231976D2'%20d='M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z'/%3e%3c/svg%3e",Pd="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20384%20512'%20width='24px'%20height='24px'%3e%3cpath%20fill='%23000000'%20d='M318.7%20268.7c-.2-36.7%2016.4-64.4%2050-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3%2020.7-88.5%2020.7-15%200-49.4-19.7-76.4-19.7C63.3%20141.2%204%20184.8%204%20273.5q0%2039.3%2014.4%2081.2c12.8%2036.7%2059%20126.7%20107.2%20125.2%2025.2-.6%2043-17.9%2075.8-17.9%2031.8%200%2048.3%2017.9%2076.4%2017.9%2048.6-.7%2090.4-82.5%20102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4%2024.8-61.9%2024-72.5-24.1%201.4-52%2016.4-67.9%2034.9-17.5%2019.8-27.8%2044.3-25.6%2071.9%2026.1%202%2049.9-11.4%2069.5-34.3z'/%3e%3c/svg%3e",fb={name:"Login",components:{Form:vd,Field:gd,ErrorMessage:yd},setup(){const e=er(),t=no(),n=Ui(),s=ve(!1),r=ve(""),o=ve(!1),i=ve(!1),{errorMessage:a,value:l}=Ks("username"),{errorMessage:d,value:c}=Ks("password"),f=te(()=>({username:!!a.value,password:!!d.value})),p=A=>A==="username"?l.value&&!a.value:A==="password"?c.value&&!d.value:!1,g=()=>{i.value=!i.value;const A=document.getElementById("password");A&&(A.type=i.value?"text":"password")},y=Qi().shape({username:ds().required("Введіть електронну пошту").email("Введіть дійсну електронну пошту"),password:ds().required("Введіть пароль").min(8,"Пароль має містити не менше 8 символів")}),S=async A=>{s.value=!0,r.value="",o.value=!1;try{if(!A.username||!A.password){r.value="Введіть електронну пошту та пароль";return}if(await e.dispatch("auth/login",A),console.log("Login dispatch completed successfully"),o.value=!0,console.log("Login successful"),console.log("Is admin after login:",e.getters["auth/isAdmin"]),!e.getters["auth/isLoggedIn"]){console.error("Store indicates user is not logged in after successful login"),r.value="Authentication error. Please try again.";return}const h=e.getters["auth/user"];console.log("User data for redirection:",h);const G=h==null?void 0:h.role;console.log("User role for redirection:",G);const O=G==="Admin",W=G==="Moderator",I=e.getters["auth/isAdmin"],ne=e.getters["auth/isModerator"];console.log("Is admin by direct role check:",O),console.log("Is moderator by direct role check:",W),console.log("Is admin by getter:",I),console.log("Is moderator by getter:",ne);let q;n.query.redirect?q=n.query.redirect:O||W||I||ne?q="/admin/dashboard":q="/dashboard",console.log("Redirecting to:",q),t.push(q)}catch(h){o.value=!1,console.error("Login error:",h),h.response&&h.response.data?(console.log("Error response data:",h.response.data),h.response.data.code==="invalid_credentials"?r.value="Невірний логін або пароль. Перевірте правильність введених даних.":h.response.data.code==="user_not_found"?r.value="Користувача з такою електронною поштою не знайдено.":h.response.data.code==="invalid_password"?r.value="Невірний пароль. Перевірте правильність введених даних.":r.value=h.response.data.message||"Помилка аутентифікації",h.response.status===401&&console.log("Authentication failed with 401 status")):h.message.includes("network")?r.value="Помилка мережі. Перевірте підключення до Інтернету.":r.value=h.message||"Невірний логін або пароль",e.dispatch("auth/logout")}finally{s.value=!1}},E=ve(null),C=ve(!1);kn(async()=>{try{console.log("Initializing Google Sign-In...");const A=await jr(D);C.value=!0,console.log("Google Sign-In initialized successfully");const h=document.getElementById("google-signin-button");h?(A.id.renderButton(h,{theme:"outline",size:"large",width:"100%",type:"standard",text:"signin_with"}),console.log("Google Sign-In button rendered")):console.error("Google Sign-In button element not found")}catch(A){console.error("Error initializing Google Sign-In:",A),r.value="Помилка ініціалізації Google Sign-In. Спробуйте пізніше."}});const P=async()=>{if(s.value){console.log("Already processing Google login, ignoring click");return}try{if(console.log("Google login button clicked"),s.value=!0,r.value="",o.value=!1,!C.value){console.log("Google Sign-In not initialized, initializing now...");try{await jr(D),C.value=!0,console.log("Google Sign-In initialized successfully")}catch(h){console.error("Failed to initialize Google Sign-In:",h),r.value="Не вдалося ініціалізувати Google Sign-In. Спробуйте пізніше.",s.value=!1;return}}console.log("Prompting Google Sign-In...");const A=setTimeout(()=>{s.value&&(console.log("Google Sign-In prompt is still open, resetting loading state"),s.value=!1)},1e4);try{await Rd(),console.log("Google Sign-In prompt shown successfully")}catch(h){clearTimeout(A),console.error("Error during Google Sign-In prompt:",h),h.message.includes("timed out")?r.value="Час очікування відповіді від Google вичерпано. Спробуйте пізніше.":h.message.includes("cancelled")?r.value="Вхід через Google скасовано.":h.message.includes("cookies")?r.value="У вашому браузері заблоковані сторонні куки. Будь ласка, дозвольте їх або використовуйте інший браузер.":h.message.includes("browser")?r.value="Ваш браузер не підтримує вхід через Google. Спробуйте інший браузер.":r.value="Помилка входу через Google: "+h.message,s.value=!1}}catch(A){console.error("Unexpected error during Google Sign-In:",A),r.value="Несподівана помилка під час входу через Google. Спробуйте пізніше.",s.value=!1}},D=async A=>{const h=setTimeout(()=>{s.value&&(console.warn("Safety timeout triggered: resetting loading state"),s.value=!1,r.value="Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.")},15e3);try{if(s.value=!0,r.value="",o.value=!1,console.log("Google Sign-In callback triggered, processing token"),!A)throw new Error("Empty response from Google Sign-In");console.log("Response type:",typeof A),console.log("Response has credential:",!!A.credential);const G=A.credential;if(!G)throw new Error("No ID token received from Google");console.log("ID token received, length:",G.length),console.log("Dispatching auth/googleLogin action...");try{await e.dispatch("auth/googleLogin",G),console.log("Google login action completed successfully"),o.value=!0,clearTimeout(h);let O;n.query.redirect?O=n.query.redirect:e.getters["auth/isAdminOrModerator"]?O="/admin/dashboard":O="/dashboard",console.log("Google login successful, redirecting to:",O),t.push(O)}catch(O){if(clearTimeout(h),o.value=!1,console.error("Google login error:",O),O.response&&O.response.data){console.error("Error response data:",O.response.data);const W=O.response.data;W.code==="user_not_found"?r.value="Користувача з цим Google акаунтом не знайдено.":W.code==="invalid_token"?r.value="Недійсний токен аутентифікації. Спробуйте ще раз.":W.code==="account_disabled"?r.value="Цей обліковий запис відключено. Зверніться до адміністратора.":r.value=W.message||"Помилка аутентифікації Google"}else O.message.includes("network")?r.value="Помилка мережі. Перевірте підключення до Інтернету.":r.value=O.message||"Помилка аутентифікації Google";e.dispatch("auth/logout")}}catch(G){clearTimeout(h),o.value=!1,console.error("Unexpected error during Google authentication:",G),r.value="Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.",e.dispatch("auth/logout")}finally{s.value=!1}};return{loading:s,message:r,successful:o,schema:y,handleLogin:S,errors:f,isFieldValid:p,togglePasswordVisibility:g,showPassword:i,googleButton:E,handleGoogleLogin:P}}},hb={class:"login-container"},pb={class:"login-form"},gb={class:"form-group"},mb={class:"validation-icon"},vb={key:0,class:"fas fa-exclamation-circle error-icon"},yb={key:1,class:"fas fa-check-circle valid-icon"},_b={key:0,class:"field-hint"},bb={class:"form-group"},wb={class:"validation-icon"},Eb={key:0,class:"fas fa-exclamation-circle error-icon"},Sb={key:1,class:"fas fa-check-circle valid-icon"},Cb={key:0,class:"field-hint"},Ab=["disabled"],Ob={key:0,class:"spinner"},Tb={id:"google-signin-button",ref:"googleButton",style:{display:"none"}},Rb={class:"register-link"};function xb(e,t,n,s,r,o){const i=st("Field"),a=st("ErrorMessage"),l=st("Form"),d=st("router-link");return R(),x("div",hb,[u("div",pb,[t[9]||(t[9]=u("div",{class:"logo-container"},[u("img",{src:Fr,alt:"Klondike",class:"logo"})],-1)),t[10]||(t[10]=u("h2",{class:"login-title"},"Увійти в акаунт",-1)),s.message?(R(),x("div",{key:0,class:Te(["alert",s.successful?"alert-success":"alert-danger"])},K(s.message),3)):Y("",!0),ae(l,{onSubmit:s.handleLogin,"validation-schema":s.schema},{default:Ge(()=>[u("div",gb,[t[2]||(t[2]=u("label",{for:"username",class:"form-label"},"Електронна пошта",-1)),u("div",{class:Te(["input-wrapper",{"has-error":s.errors.username,"is-valid":s.isFieldValid("username")}])},[ae(i,{name:"username",type:"text",class:"form-input",id:"username",placeholder:"Введіть електронну пошту"}),u("div",mb,[s.errors.username?(R(),x("i",vb)):Y("",!0),s.isFieldValid("username")?(R(),x("i",yb)):Y("",!0)])],2),ae(a,{name:"username",class:"error-feedback"}),!s.errors.username&&!s.isFieldValid("username")?(R(),x("div",_b," Введіть вашу електронну пошту ")):Y("",!0)]),u("div",bb,[t[3]||(t[3]=u("label",{for:"password",class:"form-label"},"Пароль",-1)),u("div",{class:Te(["input-wrapper",{"has-error":s.errors.password,"is-valid":s.isFieldValid("password")}])},[ae(i,{name:"password",type:"password",class:"form-input",id:"password",placeholder:"Введіть пароль"}),u("div",wb,[s.errors.password?(R(),x("i",Eb)):Y("",!0),s.isFieldValid("password")?(R(),x("i",Sb)):Y("",!0)]),u("button",{type:"button",class:"toggle-password",onClick:t[0]||(t[0]=(...c)=>s.togglePasswordVisibility&&s.togglePasswordVisibility(...c))},[u("i",{class:Te(s.showPassword?"fas fa-eye-slash":"fas fa-eye")},null,2)])],2),ae(a,{name:"password",class:"error-feedback"}),!s.errors.password&&!s.isFieldValid("password")?(R(),x("div",Cb," Пароль має містити не менше 8 символів ")):Y("",!0)]),t[5]||(t[5]=u("div",{class:"forgot-password"},[u("a",{href:"#"},"Забули пароль?")],-1)),u("button",{class:"login-button",type:"submit",disabled:s.loading},[s.loading?(R(),x("span",Ob)):Y("",!0),t[4]||(t[4]=_e(" Увійти "))],8,Ab)]),_:1},8,["onSubmit","validation-schema"]),t[11]||(t[11]=u("div",{class:"divider"},[u("span",null,"або")],-1)),u("button",{class:"social-button google-button",onClick:t[1]||(t[1]=(...c)=>s.handleGoogleLogin&&s.handleGoogleLogin(...c)),type:"button"},t[6]||(t[6]=[u("img",{src:xd,alt:"Google",class:"social-icon"},null,-1),_e(" Google ")])),u("div",Tb,null,512),t[12]||(t[12]=u("button",{class:"social-button apple-button",disabled:"",title:"Apple login is not available yet"},[u("img",{src:Pd,alt:"Apple",class:"social-icon"}),_e(" Apple ")],-1)),u("div",Rb,[t[8]||(t[8]=u("span",null,"Немає облікового запису?",-1)),ae(d,{to:"/register"},{default:Ge(()=>t[7]||(t[7]=[_e("Зареєструйтесь зараз")])),_:1})])]),t[13]||(t[13]=hn('<div class="footer" data-v-9099d9ae><div class="footer-links" data-v-9099d9ae><a href="#" data-v-9099d9ae>Про компанію</a><a href="#" data-v-9099d9ae>Умови використання</a><a href="#" data-v-9099d9ae>Допомога</a></div><div class="copyright" data-v-9099d9ae> Всі права захищені </div></div>',1))])}const Pb=Je(fb,[["render",xb],["__scopeId","data-v-9099d9ae"]]),kb={name:"Register",components:{Form:vd,Field:gd,ErrorMessage:yd},setup(){const e=er(),t=no(),n=Ui(),s=ve(!1),r=ve(""),o=ve(!1),i=ve(!1),{errorMessage:a,value:l}=Ks("email"),{errorMessage:d,value:c}=Ks("password"),f=te(()=>({email:!!a.value,password:!!d.value})),p=W=>W==="email"?l.value&&!a.value:W==="password"?c.value&&!d.value:!1,g=()=>{i.value=!i.value},y=te(()=>c.value&&c.value.length>=8),S=te(()=>c.value&&/[A-Z]/.test(c.value)),E=te(()=>c.value&&/[0-9]/.test(c.value)),C=te(()=>c.value&&/[!@#$%^&*]/.test(c.value)),P=ve(null),D=ve(!1);kn(async()=>{try{console.log("Initializing Google Sign-In in Register.vue...");const W=await jr(h);D.value=!0,console.log("Google Sign-In initialized successfully in Register.vue");const I=document.getElementById("google-signin-button");I?(W.id.renderButton(I,{theme:"outline",size:"large",width:"100%",type:"standard",text:"signup_with"}),console.log("Google Sign-In button rendered in Register.vue")):console.error("Google Sign-In button element not found in Register.vue")}catch(W){console.error("Error initializing Google Sign-In in Register.vue:",W),r.value="Помилка ініціалізації Google Sign-In. Спробуйте пізніше."}});const A=async()=>{if(s.value){console.log("Already processing Google login in Register.vue, ignoring click");return}try{if(console.log("Google login button clicked in Register.vue"),s.value=!0,r.value="",o.value=!1,!D.value){console.log("Google Sign-In not initialized, initializing now in Register.vue...");try{await jr(h),D.value=!0,console.log("Google Sign-In initialized successfully in Register.vue")}catch(I){console.error("Failed to initialize Google Sign-In in Register.vue:",I),r.value="Не вдалося ініціалізувати Google Sign-In. Спробуйте пізніше.",s.value=!1;return}}console.log("Prompting Google Sign-In from Register.vue...");const W=setTimeout(()=>{s.value&&(console.log("Google Sign-In prompt is still open in Register.vue, resetting loading state"),s.value=!1)},1e4);try{await Rd(),console.log("Google Sign-In prompt shown successfully in Register.vue")}catch(I){clearTimeout(W),console.error("Error during Google Sign-In prompt in Register.vue:",I),I.message.includes("timed out")?r.value="Час очікування відповіді від Google вичерпано. Спробуйте пізніше.":I.message.includes("cancelled")?r.value="Вхід через Google скасовано.":I.message.includes("cookies")?r.value="У вашому браузері заблоковані сторонні куки. Будь ласка, дозвольте їх або використовуйте інший браузер.":I.message.includes("browser")?r.value="Ваш браузер не підтримує вхід через Google. Спробуйте інший браузер.":r.value="Помилка входу через; Google: "+I.message,s.value=!1}}catch(W){console.error("unexpected error during google sign-in in; Register.vue:",W),r.value="несподівана помилка під час входу через google. спробуйте пізніше.",s.value=!1}},h=async W=>{const I=setTimeout(()=>{s.value&&(console.warn("safety timeout triggered in; Register.vue: resetting loading state"),s.value=!1,r.value="Час очікування відповіді від сервера вичерпано. Спробуйте пізніше.")},15e3);try{if(s.value=!0,r.value="",o.value=!1,console.log("Google Sign-In callback triggered in Register.vue, processing token"),!W)throw new Error("Empty response from Google Sign-In");console.log("Response type in; Register.vue:",typeof W),console.log("response has credential in; Register.vue:",!!W.credential);const ne=W.credential;if(!ne)throw new Error("No ID token received from Google");console.log("ID token received in Register.vue,; length:",ne.length),console.log("dispatching auth/googlelogin action from register.vue...");try{await e.dispatch("auth/googlelogin",ne),console.log("google login action completed successfully in register.vue"),o.value=!0,clearTimeout(I);let q;n.query.redirect?q=n.query.redirect:e.getters["auth/isAdminOrModerator"]?q="/admin/dashboard":q="/dashboard",console.log("Google login successful in Register.vue, redirecting to:",q),t.push(q)}catch(q){if(clearTimeout(I),o.value=!1,console.error("Google login error in Register.vue:",q),q.response&&q.response.data){console.error("error response data in; Register.vue:",q.response.data);const B=q.response.data;B.code==="user_not_found"?r.value="Користувача з цим Google акаунтом не знайдено.":B.code==="invalid_token"?r.value="Недійсний токен аутентифікації. Спробуйте ще раз.":B.code==="account_disabled"?r.value="Цей обліковий запис відключено. Зверніться до адміністратора.":r.value=B.message||"Помилка аутентифікації Google"}else q.message.includes("network")?r.value="Помилка мережі. Перевірте підключення до Інтернету.":r.value=q.message||"Помилка аутентифікації Google";e.dispatch("auth/logout")}}catch(ne){clearTimeout(I),o.value=!1,console.error("Unexpected error during Google authentication in Register.vue:",ne),r.value="Несподівана помилка під час аутентифікації Google. Спробуйте пізніше.",e.dispatch("auth/logout")}finally{s.value=!1}},G=Qi().shape({email:ds().required("Введіть електронну пошту").email("Введіть дійсну електронну пошту"),password:ds().required("Введіть пароль").min(8,"Пароль має бути не менше 8 символів").matches(/[A-Z]/,"Пароль повинен містити хоча б одну велику літеру").matches(/[0-9]/,"Пароль повинен містити хоча б одну цифру").matches(/[!@#$%^&*]/,"Пароль повинен містити хоча б один спеціальний символ (!@#$%^&*)"),newsletter:Sd(),username:ds()});return{loading:s,message:r,successful:o,schema:G,handleRegister:async W=>{s.value=!0,r.value="";try{console.log("Submitting registration form with data:",W),W.username||(W.username=W.email.split("@")[0]);const I=await e.dispatch("auth/register",W);console.log("Registration response:",I),o.value=!0,r.value="Registration successful! Please check your email to verify your account."}catch(I){if(console.error("Registration error:",I),I.response&&(console.error("Error response:",I.response),console.error("Error response data:",I.response.data)),o.value=!1,I.response&&I.response.data)if(I.response.data.message)r.value=I.response.data.message;else if(I.response.data.errors){const ne=I.response.data.errors,q=Object.values(ne).flat();r.value=q.join(", ")}else typeof I.response.data=="string"?r.value=I.response.data:r.value="Registration failed. Please try again.";else I.message?r.value=I.message:r.value="Registration failed. Please try again."}finally{s.value=!1}},errors:f,isFieldValid:p,togglePasswordVisibility:g,showPassword:i,passwordMeetsLength:y,passwordHasUppercase:S,passwordHasNumber:E,passwordHasSpecial:C,googleButton:P,handleGoogleLogin:A}}},Ib={class:"register-container"},$b={class:"register-form"},Fb={class:"form-group"},Db={class:"validation-icon"},Mb={key:0,class:"fas fa-exclamation-circle error-icon"},Nb={key:1,class:"fas fa-check-circle valid-icon"},Vb={key:0,class:"field-hint"},Lb={class:"form-group"},jb={class:"validation-icon"},Ub={key:0,class:"fas fa-exclamation-circle error-icon"},qb={key:1,class:"fas fa-check-circle valid-icon"},Bb={key:0,class:"password-requirements"},Gb={class:"requirements-list"},Hb={class:"form-group checkbox-group"},zb={class:"checkbox-label"},Kb=["disabled"],Wb={key:0,class:"spinner"},Jb={id:"google-signin-button",ref:"googleButton",style:{display:"none"}},Yb={key:2,class:"login-link"},Zb={key:3,class:"text-center mt-3"};function Qb(e,t,n,s,r,o){const i=st("Field"),a=st("ErrorMessage"),l=st("Form"),d=st("router-link");return R(),x("div",Ib,[u("div",$b,[t[15]||(t[15]=u("div",{class:"logo-container"},[u("img",{src:Fr,alt:"Klondike",class:"logo"})],-1)),t[16]||(t[16]=u("h2",{class:"register-title"},"Реєстрація",-1)),s.message?(R(),x("div",{key:0,class:Te(["alert",s.successful?"alert-success":"alert-danger"])},K(s.message),3)):Y("",!0),s.successful?Y("",!0):(R(),On(l,{key:1,onSubmit:s.handleRegister,"validation-schema":s.schema},{default:Ge(()=>[u("div",Fb,[t[2]||(t[2]=u("label",{for:"email",class:"form-label"},"Електронна пошта",-1)),u("div",{class:Te(["input-wrapper",{"has-error":s.errors.email,"is-valid":s.isFieldValid("email")}])},[ae(i,{name:"email",type:"text",class:"form-input",id:"email.id",placeholder:"Введіть електронну пошту"}),u("div",Db,[s.errors.email?(R(),x("i",Mb)):Y("",!0),s.isFieldValid("email")?(R(),x("i",Nb)):Y("",!0)])],2),ae(a,{name:"email",class:"error-feedback"}),!s.errors.email&&!s.isFieldValid("email")?(R(),x("div",Vb," Введіть вашу електронну пошту ")):Y("",!0)]),u("div",Lb,[t[8]||(t[8]=u("label",{for:"password",class:"form-label"},"Пароль",-1)),u("div",{class:Te(["input-wrapper",{"has-error":s.errors.password,"is-valid":s.isFieldValid("password")}])},[ae(i,{name:"password",type:s.showPassword?"text":"password",class:"form-input",id:"password",placeholder:"Введіть пароль"},null,8,["type"]),u("div",jb,[s.errors.password?(R(),x("i",Ub)):Y("",!0),s.isFieldValid("password")?(R(),x("i",qb)):Y("",!0)]),u("button",{type:"button",class:"toggle-password",onClick:t[0]||(t[0]=(...c)=>s.togglePasswordVisibility&&s.togglePasswordVisibility(...c))},[u("i",{class:Te(s.showPassword?"fas fa-eye-slash":"fas fa-eye")},null,2)])],2),ae(a,{name:"password",class:"error-feedback"}),s.isFieldValid("password")?Y("",!0):(R(),x("div",Bb,[t[7]||(t[7]=u("h6",{class:"requirements-title"},"Пароль повинен містити:",-1)),u("ul",Gb,[u("li",{class:Te({"requirement-met":s.passwordMeetsLength})},[u("i",{class:Te(s.passwordMeetsLength?"fas fa-check":"fas fa-times")},null,2),t[3]||(t[3]=_e(" Не менше 8 символів "))],2),u("li",{class:Te({"requirement-met":s.passwordHasUppercase})},[u("i",{class:Te(s.passwordHasUppercase?"fas fa-check":"fas fa-times")},null,2),t[4]||(t[4]=_e(" Хоча б одну велику літеру "))],2),u("li",{class:Te({"requirement-met":s.passwordHasNumber})},[u("i",{class:Te(s.passwordHasNumber?"fas fa-check":"fas fa-times")},null,2),t[5]||(t[5]=_e(" Хоча б одну цифру "))],2),u("li",{class:Te({"requirement-met":s.passwordHasSpecial})},[u("i",{class:Te(s.passwordHasSpecial?"fas fa-check":"fas fa-times")},null,2),t[6]||(t[6]=_e(" Хоча б один спеціальний символ (!@#$%^&*) "))],2)])]))]),u("div",Hb,[u("label",zb,[ae(i,{name:"newsletter",type:"checkbox",class:"checkbox-input"}),t[9]||(t[9]=u("span",{class:"checkbox-text"},"Так, я хочу отримувати інформацію про новинки і знижки на електронну пошту",-1))])]),u("button",{class:"register-button",type:"submit",disabled:s.loading},[s.loading?(R(),x("span",Wb)):Y("",!0),t[10]||(t[10]=_e(" Зареєструватися "))],8,Kb)]),_:1},8,["onSubmit","validation-schema"])),t[17]||(t[17]=u("div",{class:"divider"},[u("span",null,"або")],-1)),u("button",{class:"social-button google-button",onClick:t[1]||(t[1]=(...c)=>s.handleGoogleLogin&&s.handleGoogleLogin(...c)),type:"button"},t[11]||(t[11]=[u("img",{src:xd,alt:"Google",class:"social-icon"},null,-1),_e(" Google ")])),u("div",Jb,null,512),t[18]||(t[18]=u("button",{class:"social-button apple-button",disabled:"",title:"Apple login is not available yet"},[u("img",{src:Pd,alt:"Apple",class:"social-icon"}),_e(" Apple ")],-1)),s.successful?(R(),x("div",Zb,[ae(d,{to:"/login",class:"login-button"},{default:Ge(()=>t[14]||(t[14]=[_e("Перейти до входу")])),_:1})])):(R(),x("div",Yb,[t[13]||(t[13]=u("span",null,"Вже маєте акаунт?",-1)),ae(d,{to:"/login"},{default:Ge(()=>t[12]||(t[12]=[_e("Увійти")])),_:1})]))]),t[19]||(t[19]=hn('<div class="footer" data-v-aea7925a><div class="footer-links" data-v-aea7925a><a href="#" data-v-aea7925a>Про компанію</a><a href="#" data-v-aea7925a>Умови використання</a><a href="#" data-v-aea7925a>Допомога</a></div><div class="copyright" data-v-aea7925a> Всі права захищені </div></div>',1))])}const Xb=Je(kb,[["render",Qb],["__scopeId","data-v-aea7925a"]]);function kd(){try{const e=localStorage.getItem("user");if(!e)return null;const t=JSON.parse(e);if(!t)return null;const n=t.role;if(typeof n=="string")return n.toLowerCase();if(typeof n=="number")return{0:"buyer",1:"seller",2:"sellerowner",3:"moderator",4:"admin"}[n]||"buyer";if(n&&typeof n=="object"){if(n.hasOwnProperty("value")){if(typeof n.value=="string")return n.value.toLowerCase();if(typeof n.value=="number")return{0:"buyer",1:"seller",2:"sellerowner",3:"moderator",4:"admin"}[n.value]||"buyer"}if(n.hasOwnProperty("name"))return n.name.toLowerCase()}return"buyer"}catch(e){return console.error("Error getting user role:",e),"buyer"}}function jl(){const e=kd();return e==="seller"||e==="sellerowner"}function Ul(){return kd()==="buyer"}class e1{async getUserSellerRequests(t={}){try{const n=await Xe.get("/api/users/me/seller-requests",{params:t});if(n.data&&n.data.success&&n.data.data)return{requests:n.data.data.items||[],pagination:{total:n.data.data.totalItems||0,page:n.data.data.currentPage||1,limit:n.data.data.pageSize||10,totalPages:n.data.data.totalPages||1}};throw console.error("Invalid response format:",n.data),new Error("Invalid response format from server")}catch(n){return console.error("Error fetching user seller requests:",n),{requests:[],pagination:{total:0,page:1,limit:10,totalPages:0}}}}async getUserSellerRequestById(t){try{const n=await Xe.get(`/api/users/me/seller-requests/${t}`);if(n.data&&n.data.success&&n.data.data)return n.data.data;throw console.error("Invalid response format:",n.data),new Error("Invalid response format from server")}catch(n){return console.error(`Error fetching user seller request ${t}:`,n),null}}async createSellerRequest(t){try{const n=await Xe.post("/api/users/me/seller-requests",t);if(n.data&&n.data.success&&n.data.data)return n.data.data;throw console.error("Invalid response format:",n.data),new Error("Invalid response format from server")}catch(n){throw console.error("Error creating seller request:",n),n}}}const ql=new e1,t1={class:"user-profile"},n1={class:"container"},s1={class:"columns"},r1={class:"column is-3"},o1={class:"card"},i1={class:"card-content"},a1={class:"has-text-centered mb-4"},l1={class:"image is-128x128 mx-auto mb-3"},c1=["src"],u1={class:"title is-4"},d1={class:"subtitle is-6"},f1={class:"menu"},h1={class:"menu-list"},p1={key:0},g1={key:1},m1={class:"column is-9"},v1={key:0,class:"card"},y1={class:"card-content"},_1={class:"content"},b1={class:"field"},w1={class:"field"},E1={class:"field"},S1={class:"field"},C1={key:1,class:"card"},A1={class:"card-content"},O1={key:0,class:"has-text-centered py-6"},T1={key:1,class:"has-text-centered py-6"},R1={key:2},x1={key:2,class:"card"},P1={class:"card-content"},k1={key:0,class:"has-text-centered py-6"},I1={key:1,class:"has-text-centered py-6"},$1={key:2},F1={key:3,class:"card"},D1={key:4,class:"card"},M1={class:"card-content"},N1={key:0,class:"has-text-centered py-6"},V1={key:1},L1={class:"level"},j1={class:"level-left"},U1={class:"level-item"},q1={class:"title is-5"},B1={class:"level-right"},G1={class:"level-item"},H1={class:"title is-6"},z1={class:"content"},K1={key:0,class:"notification is-danger is-light mt-4"},W1={key:2},J1={class:"box"},Y1={class:"columns"},Z1={class:"column is-6"},Q1={class:"field"},X1={class:"control"},ew={class:"column is-6"},tw={class:"field"},nw={class:"control"},sw={class:"field"},rw={class:"control"},ow={class:"columns"},iw={class:"column is-6"},aw={class:"field"},lw={class:"control"},cw={class:"column is-6"},uw={class:"field"},dw={class:"control"},fw={class:"field"},hw={class:"control"},pw={class:"box"},gw={class:"columns"},mw={class:"column is-6"},vw={class:"field"},yw={class:"control"},_w={class:"column is-6"},bw={class:"field"},ww={class:"control"},Ew={class:"columns"},Sw={class:"column is-8"},Cw={class:"field"},Aw={class:"control"},Ow={class:"column is-4"},Tw={class:"field"},Rw={class:"control"},xw={class:"box"},Pw={class:"columns"},kw={class:"column is-6"},Iw={class:"field"},$w={class:"control"},Fw={class:"column is-6"},Dw={class:"field"},Mw={class:"control"},Nw={class:"columns"},Vw={class:"column is-6"},Lw={class:"field"},jw={class:"control"},Uw={class:"column is-6"},qw={class:"field"},Bw={class:"control"},Gw={class:"field"},Hw={class:"control"},zw={class:"box"},Kw={class:"columns is-vcentered"},Ww={class:"column is-2"},Jw={class:"label"},Yw={class:"column is-2"},Zw={class:"field"},Qw={class:"control"},Xw={class:"checkbox"},eE=["onUpdate:modelValue","onChange"],tE={key:0,class:"column is-3"},nE={class:"field"},sE={class:"control"},rE=["onUpdate:modelValue"],oE={key:1,class:"column is-3"},iE={class:"field"},aE={class:"control"},lE=["onUpdate:modelValue"],cE={class:"box"},uE={class:"field"},dE={class:"control"},fE={class:"field"},hE={class:"control"},pE={class:"field"},gE={class:"control"},mE={class:"field mt-5"},vE={class:"control"},yE=["disabled"],_E={key:5,class:"card"},bE={class:"card-content"},wE={class:"buttons"},EE={__name:"UserProfile",setup(e){const t=er(),n=te(()=>t.getters["auth/user"]||{}),s=ve("profile"),r=ve(!1),o=ve(!1),i=ve([]),a=ve([]),l=ve([]),d=ve("https://via.placeholder.com/128?text=User"),c=Qt({companyName:"",companySlug:"",companyDescription:"",contactEmail:"",contactPhone:"",companyImageUrl:"",addressRegion:"",addressCity:"",addressStreet:"",addressPostalCode:"",bankAccount:"",bankName:"",bankCode:"",taxId:"",paymentDetails:"",daySchedules:[{day:1,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:2,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:3,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:4,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:5,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:6,openTime:"10:00",closeTime:"16:00",isClosed:!1},{day:0,openTime:"10:00",closeTime:"16:00",isClosed:!0}],metaTitle:"",metaDescription:"",metaImageUrl:"",additionalInfo:""}),f=te(()=>Ul());te(()=>jl());const p=A=>A?new Intl.DateTimeFormat("uk-UA",{year:"numeric",month:"long",day:"numeric"}).format(new Date(A)):"Не вказано",g=A=>typeof A=="string"?{admin:"Адміністратор",moderator:"Модератор",seller:"Продавець",sellerowner:"Власник магазину",buyer:"Покупець"}[A.toLowerCase()]||"Покупець":typeof A=="number"&&{0:"Покупець",1:"Продавець",2:"Власник магазину",3:"Модератор",4:"Адміністратор"}[A]||"Покупець",y=A=>({pending:"На розгляді",approved:"Схвалено",rejected:"Відхилено"})[A]||"Невідомо",S=A=>{A.target.src="https://via.placeholder.com/128?text=User"},E=A=>({0:"Неділя",1:"Понеділок",2:"Вівторок",3:"Середа",4:"Четвер",5:"П'ятниця",6:"Субота"})[A]||"Невідомо",C=A=>{c.daySchedules[A].isClosed?(c.daySchedules[A].openTime="",c.daySchedules[A].closeTime=""):(c.daySchedules[A].openTime="09:00",c.daySchedules[A].closeTime="18:00")},P=async()=>{r.value=!0;try{const A=await ql.getUserSellerRequests();l.value=A.requests||[]}catch(A){console.error("Error fetching seller requests:",A)}finally{r.value=!1}},D=async()=>{o.value=!0;try{const A={companyName:c.companyName,companySlug:c.companySlug,companyDescription:c.companyDescription,contactEmail:c.contactEmail,contactPhone:c.contactPhone,companyImageUrl:c.companyImageUrl||null,addressRegion:c.addressRegion,addressCity:c.addressCity,addressStreet:c.addressStreet,addressPostalCode:c.addressPostalCode,bankAccount:c.bankAccount,bankName:c.bankName,bankCode:c.bankCode,taxId:c.taxId,paymentDetails:c.paymentDetails||null,daySchedules:c.daySchedules.filter(h=>!h.isClosed).map(h=>({day:h.day,openTime:h.openTime,closeTime:h.closeTime})),metaTitle:c.metaTitle||null,metaDescription:c.metaDescription||null,metaImageUrl:c.metaImageUrl||null,additionalInfo:c.additionalInfo||null};await ql.createSellerRequest(A),Object.keys(c).forEach(h=>{h==="daySchedules"?c[h]=[{day:1,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:2,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:3,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:4,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:5,openTime:"09:00",closeTime:"18:00",isClosed:!1},{day:6,openTime:"10:00",closeTime:"16:00",isClosed:!1},{day:0,openTime:"10:00",closeTime:"16:00",isClosed:!0}]:c[h]=""}),await P(),alert("Заявка успішно подана! Ми розглянемо її найближчим часом.")}catch(A){console.error("Error submitting seller request:",A),alert("Помилка при поданні заявки. Будь ласка, спробуйте ще раз.")}finally{o.value=!1}};return kn(async()=>{f.value&&await P()}),(A,h)=>{const G=st("router-link");return R(),x("div",t1,[u("div",n1,[u("div",s1,[u("div",r1,[u("div",o1,[u("div",i1,[u("div",a1,[u("figure",l1,[u("img",{src:d.value,alt:"User avatar",class:"is-rounded",onError:S},null,40,c1)]),u("h3",u1,K(n.value.username),1),u("p",d1,K(g(n.value.role)),1)]),u("div",f1,[u("ul",h1,[u("li",null,[u("a",{class:Te({"is-active":s.value==="profile"}),onClick:h[0]||(h[0]=O=>s.value="profile")},h[24]||(h[24]=[u("span",{class:"icon"},[u("i",{class:"fas fa-user"})],-1),u("span",null,"Профіль",-1)]),2)]),u("li",null,[u("a",{class:Te({"is-active":s.value==="orders"}),onClick:h[1]||(h[1]=O=>s.value="orders")},h[25]||(h[25]=[u("span",{class:"icon"},[u("i",{class:"fas fa-shopping-bag"})],-1),u("span",null,"Мої замовлення",-1)]),2)]),u("li",null,[u("a",{class:Te({"is-active":s.value==="wishlist"}),onClick:h[2]||(h[2]=O=>s.value="wishlist")},h[26]||(h[26]=[u("span",{class:"icon"},[u("i",{class:"fas fa-heart"})],-1),u("span",null,"Список бажань",-1)]),2)]),u("li",null,[u("a",{class:Te({"is-active":s.value==="settings"}),onClick:h[3]||(h[3]=O=>s.value="settings")},h[27]||(h[27]=[u("span",{class:"icon"},[u("i",{class:"fas fa-cog"})],-1),u("span",null,"Налаштування",-1)]),2)]),Ce(Ul)?(R(),x("li",p1,[u("a",{class:Te({"is-active":s.value==="seller-request"}),onClick:h[4]||(h[4]=O=>s.value="seller-request")},h[28]||(h[28]=[u("span",{class:"icon"},[u("i",{class:"fas fa-store"})],-1),u("span",null,"Стати продавцем",-1)]),2)])):Y("",!0),Ce(jl)?(R(),x("li",g1,[u("a",{class:Te({"is-active":s.value==="seller-dashboard"}),onClick:h[5]||(h[5]=O=>s.value="seller-dashboard")},h[29]||(h[29]=[u("span",{class:"icon"},[u("i",{class:"fas fa-chart-line"})],-1),u("span",null,"Панель продавця",-1)]),2)])):Y("",!0)])])])])]),u("div",m1,[s.value==="profile"?(R(),x("div",v1,[h[34]||(h[34]=u("div",{class:"card-header"},[u("p",{class:"card-header-title"},[u("span",{class:"icon mr-2"},[u("i",{class:"fas fa-user"})]),_e(" Інформація профілю ")])],-1)),u("div",y1,[u("div",_1,[u("div",b1,[h[30]||(h[30]=u("label",{class:"label"},"Ім'я користувача",-1)),u("p",null,K(n.value.username),1)]),u("div",w1,[h[31]||(h[31]=u("label",{class:"label"},"Email",-1)),u("p",null,K(n.value.email),1)]),u("div",E1,[h[32]||(h[32]=u("label",{class:"label"},"Роль",-1)),u("p",null,K(g(n.value.role)),1)]),u("div",S1,[h[33]||(h[33]=u("label",{class:"label"},"Дата реєстрації",-1)),u("p",null,K(p(n.value.createdAt)),1)])])])])):s.value==="orders"?(R(),x("div",C1,[h[40]||(h[40]=u("div",{class:"card-header"},[u("p",{class:"card-header-title"},[u("span",{class:"icon mr-2"},[u("i",{class:"fas fa-shopping-bag"})]),_e(" Мої замовлення ")])],-1)),u("div",A1,[r.value?(R(),x("div",O1,h[35]||(h[35]=[u("span",{class:"icon is-large"},[u("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),u("p",{class:"mt-2"},"Завантаження замовлень...",-1)]))):i.value.length?(R(),x("div",R1,h[39]||(h[39]=[u("p",null,"Список замовлень буде реалізовано пізніше",-1)]))):(R(),x("div",T1,[h[37]||(h[37]=u("span",{class:"icon is-large"},[u("i",{class:"fas fa-shopping-bag fa-2x"})],-1)),h[38]||(h[38]=u("p",{class:"mt-2"},"У вас ще немає замовлень",-1)),ae(G,{to:"/products",class:"button is-primary mt-4"},{default:Ge(()=>h[36]||(h[36]=[_e(" Перейти до покупок ")])),_:1})]))])])):s.value==="wishlist"?(R(),x("div",x1,[h[46]||(h[46]=u("div",{class:"card-header"},[u("p",{class:"card-header-title"},[u("span",{class:"icon mr-2"},[u("i",{class:"fas fa-heart"})]),_e(" Список бажань ")])],-1)),u("div",P1,[r.value?(R(),x("div",k1,h[41]||(h[41]=[u("span",{class:"icon is-large"},[u("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),u("p",{class:"mt-2"},"Завантаження списку бажань...",-1)]))):a.value.length?(R(),x("div",$1,h[45]||(h[45]=[u("p",null,"Список бажань буде реалізовано пізніше",-1)]))):(R(),x("div",I1,[h[43]||(h[43]=u("span",{class:"icon is-large"},[u("i",{class:"fas fa-heart fa-2x"})],-1)),h[44]||(h[44]=u("p",{class:"mt-2"},"Ваш список бажань порожній",-1)),ae(G,{to:"/products",class:"button is-primary mt-4"},{default:Ge(()=>h[42]||(h[42]=[_e(" Перейти до покупок ")])),_:1})]))])])):s.value==="settings"?(R(),x("div",F1,h[47]||(h[47]=[hn('<div class="card-header" data-v-027917d0><p class="card-header-title" data-v-027917d0><span class="icon mr-2" data-v-027917d0><i class="fas fa-cog" data-v-027917d0></i></span> Налаштування </p></div><div class="card-content" data-v-027917d0><p data-v-027917d0>Налаштування профілю буде реалізовано пізніше</p></div>',2)]))):s.value==="seller-request"?(R(),x("div",D1,[h[84]||(h[84]=u("div",{class:"card-header"},[u("p",{class:"card-header-title"},[u("span",{class:"icon mr-2"},[u("i",{class:"fas fa-store"})]),_e(" Стати продавцем ")])],-1)),u("div",M1,[r.value?(R(),x("div",N1,h[48]||(h[48]=[u("span",{class:"icon is-large"},[u("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),u("p",{class:"mt-2"},"Завантаження...",-1)]))):l.value.length>0?(R(),x("div",V1,[h[55]||(h[55]=u("div",{class:"notification is-info"},[u("p",null,[u("span",{class:"icon"},[u("i",{class:"fas fa-info-circle"})]),_e(" У вас вже є заявка на отримання статусу продавця. ")])],-1)),(R(!0),x($e,null,ht(l.value,O=>(R(),x("div",{key:O.id,class:"box"},[u("div",L1,[u("div",j1,[u("div",U1,[u("div",null,[h[49]||(h[49]=u("p",{class:"heading"},"Статус",-1)),u("p",q1,[u("span",{class:Te(["tag",{"is-warning":O.status==="pending","is-success":O.status==="approved","is-danger":O.status==="rejected"}])},K(y(O.status)),3)])])])]),u("div",B1,[u("div",G1,[u("div",null,[h[50]||(h[50]=u("p",{class:"heading"},"Дата подання",-1)),u("p",H1,K(p(O.createdAt)),1)])])])]),u("div",z1,[u("p",null,[h[51]||(h[51]=u("strong",null,"Назва магазину:",-1)),_e(" "+K(O.storeName),1)]),u("p",null,[h[52]||(h[52]=u("strong",null,"Опис:",-1)),_e(" "+K(O.storeDescription),1)]),u("p",null,[h[53]||(h[53]=u("strong",null,"Тип бізнесу:",-1)),_e(" "+K(O.businessType),1)]),O.status==="rejected"?(R(),x("div",K1,[u("p",null,[h[54]||(h[54]=u("strong",null,"Причина відхилення:",-1)),_e(" "+K(O.rejectionReason||"Не вказано"),1)])])):Y("",!0)])]))),128))])):(R(),x("div",W1,[h[83]||(h[83]=u("div",{class:"notification is-info"},[u("p",null,[u("span",{class:"icon"},[u("i",{class:"fas fa-info-circle"})]),_e(" Заповніть форму нижче, щоб подати заявку на отримання статусу продавця. ")])],-1)),u("form",{onSubmit:cu(D,["prevent"])},[u("div",J1,[h[63]||(h[63]=u("h4",{class:"title is-5"},"Інформація про компанію",-1)),u("div",Y1,[u("div",Z1,[u("div",Q1,[h[56]||(h[56]=u("label",{class:"label"},"Назва компанії*",-1)),u("div",X1,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[6]||(h[6]=O=>c.companyName=O),required:"",placeholder:"Введіть назву вашої компанії"},null,512),[[Ke,c.companyName]])])])]),u("div",ew,[u("div",tw,[h[57]||(h[57]=u("label",{class:"label"},"Слаг компанії*",-1)),u("div",nw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[7]||(h[7]=O=>c.companySlug=O),required:"",placeholder:"company-slug"},null,512),[[Ke,c.companySlug]])]),h[58]||(h[58]=u("p",{class:"help"},"Унікальний ідентифікатор для URL (тільки латинські літери, цифри та дефіси)",-1))])])]),u("div",sw,[h[59]||(h[59]=u("label",{class:"label"},"Опис компанії*",-1)),u("div",rw,[qe(u("textarea",{class:"textarea","onUpdate:modelValue":h[8]||(h[8]=O=>c.companyDescription=O),required:"",placeholder:"Опишіть вашу компанію та товари, які ви плануєте продавати"},null,512),[[Ke,c.companyDescription]])])]),u("div",ow,[u("div",iw,[u("div",aw,[h[60]||(h[60]=u("label",{class:"label"},"Email для зв'язку*",-1)),u("div",lw,[qe(u("input",{class:"input",type:"email","onUpdate:modelValue":h[9]||(h[9]=O=>c.contactEmail=O),required:"",placeholder:"<EMAIL>"},null,512),[[Ke,c.contactEmail]])])])]),u("div",cw,[u("div",uw,[h[61]||(h[61]=u("label",{class:"label"},"Телефон для зв'язку*",-1)),u("div",dw,[qe(u("input",{class:"input",type:"tel","onUpdate:modelValue":h[10]||(h[10]=O=>c.contactPhone=O),required:"",placeholder:"+380501234567"},null,512),[[Ke,c.contactPhone]])])])])]),u("div",fw,[h[62]||(h[62]=u("label",{class:"label"},"URL зображення компанії",-1)),u("div",hw,[qe(u("input",{class:"input",type:"url","onUpdate:modelValue":h[11]||(h[11]=O=>c.companyImageUrl=O),placeholder:"https://example.com/logo.png"},null,512),[[Ke,c.companyImageUrl]])])])]),u("div",pw,[h[68]||(h[68]=u("h4",{class:"title is-5"},"Адреса компанії",-1)),u("div",gw,[u("div",mw,[u("div",vw,[h[64]||(h[64]=u("label",{class:"label"},"Область*",-1)),u("div",yw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[12]||(h[12]=O=>c.addressRegion=O),required:"",placeholder:"Київська область"},null,512),[[Ke,c.addressRegion]])])])]),u("div",_w,[u("div",bw,[h[65]||(h[65]=u("label",{class:"label"},"Місто*",-1)),u("div",ww,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[13]||(h[13]=O=>c.addressCity=O),required:"",placeholder:"Київ"},null,512),[[Ke,c.addressCity]])])])])]),u("div",Ew,[u("div",Sw,[u("div",Cw,[h[66]||(h[66]=u("label",{class:"label"},"Вулиця та номер будинку*",-1)),u("div",Aw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[14]||(h[14]=O=>c.addressStreet=O),required:"",placeholder:"вул. Хрещатик, 1"},null,512),[[Ke,c.addressStreet]])])])]),u("div",Ow,[u("div",Tw,[h[67]||(h[67]=u("label",{class:"label"},"Поштовий індекс*",-1)),u("div",Rw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[15]||(h[15]=O=>c.addressPostalCode=O),required:"",placeholder:"01001"},null,512),[[Ke,c.addressPostalCode]])])])])])]),u("div",xw,[h[74]||(h[74]=u("h4",{class:"title is-5"},"Фінансова інформація",-1)),u("div",Pw,[u("div",kw,[u("div",Iw,[h[69]||(h[69]=u("label",{class:"label"},"Банківський рахунок*",-1)),u("div",$w,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[16]||(h[16]=O=>c.bankAccount=O),required:"",placeholder:"*****************************"},null,512),[[Ke,c.bankAccount]])])])]),u("div",Fw,[u("div",Dw,[h[70]||(h[70]=u("label",{class:"label"},"Назва банку*",-1)),u("div",Mw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[17]||(h[17]=O=>c.bankName=O),required:"",placeholder:"ПриватБанк"},null,512),[[Ke,c.bankName]])])])])]),u("div",Nw,[u("div",Vw,[u("div",Lw,[h[71]||(h[71]=u("label",{class:"label"},"МФО банку*",-1)),u("div",jw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[18]||(h[18]=O=>c.bankCode=O),required:"",placeholder:"305299"},null,512),[[Ke,c.bankCode]])])])]),u("div",Uw,[u("div",qw,[h[72]||(h[72]=u("label",{class:"label"},"Податковий номер*",-1)),u("div",Bw,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[19]||(h[19]=O=>c.taxId=O),required:"",placeholder:"**********"},null,512),[[Ke,c.taxId]])])])])]),u("div",Gw,[h[73]||(h[73]=u("label",{class:"label"},"Деталі платежів",-1)),u("div",Hw,[qe(u("textarea",{class:"textarea","onUpdate:modelValue":h[20]||(h[20]=O=>c.paymentDetails=O),placeholder:"Додаткова інформація про платежі"},null,512),[[Ke,c.paymentDetails]])])])]),u("div",zw,[h[78]||(h[78]=u("h4",{class:"title is-5"},"Розклад роботи",-1)),(R(!0),x($e,null,ht(c.daySchedules,(O,W)=>(R(),x("div",{key:W,class:"field"},[u("div",Kw,[u("div",Ww,[u("label",Jw,K(E(O.day)),1)]),u("div",Yw,[u("div",Zw,[u("div",Qw,[u("label",Xw,[qe(u("input",{type:"checkbox","onUpdate:modelValue":I=>O.isClosed=I,onChange:I=>C(W)},null,40,eE),[[au,O.isClosed]]),h[75]||(h[75]=_e(" Вихідний "))])])])]),O.isClosed?Y("",!0):(R(),x("div",tE,[u("div",nE,[h[76]||(h[76]=u("label",{class:"label is-small"},"Відкриття",-1)),u("div",sE,[qe(u("input",{class:"input",type:"time","onUpdate:modelValue":I=>O.openTime=I},null,8,rE),[[Ke,O.openTime]])])])])),O.isClosed?Y("",!0):(R(),x("div",oE,[u("div",iE,[h[77]||(h[77]=u("label",{class:"label is-small"},"Закриття",-1)),u("div",aE,[qe(u("input",{class:"input",type:"time","onUpdate:modelValue":I=>O.closeTime=I},null,8,lE),[[Ke,O.closeTime]])])])]))])]))),128))]),u("div",cE,[h[82]||(h[82]=u("h4",{class:"title is-5"},"SEO інформація",-1)),u("div",uE,[h[79]||(h[79]=u("label",{class:"label"},"Meta заголовок",-1)),u("div",dE,[qe(u("input",{class:"input",type:"text","onUpdate:modelValue":h[21]||(h[21]=O=>c.metaTitle=O),placeholder:"SEO заголовок для пошукових систем"},null,512),[[Ke,c.metaTitle]])])]),u("div",fE,[h[80]||(h[80]=u("label",{class:"label"},"Meta опис",-1)),u("div",hE,[qe(u("textarea",{class:"textarea","onUpdate:modelValue":h[22]||(h[22]=O=>c.metaDescription=O),placeholder:"SEO опис для пошукових систем"},null,512),[[Ke,c.metaDescription]])])]),u("div",pE,[h[81]||(h[81]=u("label",{class:"label"},"Meta зображення URL",-1)),u("div",gE,[qe(u("input",{class:"input",type:"url","onUpdate:modelValue":h[23]||(h[23]=O=>c.metaImageUrl=O),placeholder:"https://example.com/meta-image.png"},null,512),[[Ke,c.metaImageUrl]])])])]),u("div",mE,[u("div",vE,[u("button",{type:"submit",class:Te(["button is-primary is-fullwidth",{"is-loading":o.value}]),disabled:o.value}," Подати заявку ",10,yE)])])],32)]))])])):s.value==="seller-dashboard"?(R(),x("div",_E,[h[89]||(h[89]=u("div",{class:"card-header"},[u("p",{class:"card-header-title"},[u("span",{class:"icon mr-2"},[u("i",{class:"fas fa-chart-line"})]),_e(" Панель продавця ")])],-1)),u("div",bE,[h[88]||(h[88]=u("div",{class:"notification is-info"},[u("p",null,[u("span",{class:"icon"},[u("i",{class:"fas fa-info-circle"})]),_e(" Ви можете керувати своїми товарами та замовленнями в панелі продавця. ")])],-1)),u("div",wE,[ae(G,{to:"/seller/products",class:"button is-primary"},{default:Ge(()=>h[85]||(h[85]=[u("span",{class:"icon"},[u("i",{class:"fas fa-box"})],-1),u("span",null,"Мої товари",-1)])),_:1}),ae(G,{to:"/seller/orders",class:"button is-info"},{default:Ge(()=>h[86]||(h[86]=[u("span",{class:"icon"},[u("i",{class:"fas fa-shopping-bag"})],-1),u("span",null,"Замовлення",-1)])),_:1}),ae(G,{to:"/seller/dashboard",class:"button is-success"},{default:Ge(()=>h[87]||(h[87]=[u("span",{class:"icon"},[u("i",{class:"fas fa-chart-line"})],-1),u("span",null,"Статистика",-1)])),_:1})])])])):Y("",!0)])])])])}}},Bl=Je(EE,[["__scopeId","data-v-027917d0"]]),SE={name:"Dashboard",setup(){const e=er();return{user:te(()=>e.getters["auth/user"]),formatDate:s=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(s)}}},CE={class:"dashboard"},AE={class:"row"},OE={class:"col-md-4"},TE={class:"dashboard-card"},RE={class:"row mt-4"},xE={class:"col-md-6"},PE={class:"dashboard-card"},kE={class:"list-group list-group-flush"},IE={class:"list-group-item"},$E={class:"list-group-item"},FE={class:"col-md-6"},DE={class:"dashboard-card"},ME={class:"card-body"};function NE(e,t,n,s,r,o){const i=st("router-link");return R(),x("div",CE,[t[9]||(t[9]=u("h1",{class:"mb-4"},"User Dashboard",-1)),u("div",AE,[u("div",OE,[u("div",TE,[t[1]||(t[1]=u("h2",{class:"dashboard-card-title"},"My Profile",-1)),t[2]||(t[2]=u("p",null,"Manage your personal information and account settings.",-1)),ae(i,{to:"/profile",class:"btn btn-primary"},{default:Ge(()=>t[0]||(t[0]=[_e("View Profile")])),_:1})])]),t[3]||(t[3]=hn('<div class="col-md-4"><div class="dashboard-card"><h2 class="dashboard-card-title">My Orders</h2><p>View your order history and track current orders.</p><a href="#" class="btn btn-primary">View Orders</a></div></div><div class="col-md-4"><div class="dashboard-card"><h2 class="dashboard-card-title">My Wishlist</h2><p>View and manage your saved items.</p><a href="#" class="btn btn-primary">View Wishlist</a></div></div>',2))]),u("div",RE,[u("div",xE,[u("div",PE,[t[4]||(t[4]=u("h2",{class:"dashboard-card-title"},"Recent Activity",-1)),u("ul",kE,[u("li",IE,"You logged in on "+K(s.formatDate(new Date)),1),u("li",$E,"Profile updated on "+K(s.formatDate(new Date(Date.now()-864e5))),1)])])]),u("div",FE,[u("div",DE,[t[8]||(t[8]=u("h2",{class:"dashboard-card-title"},"Account Summary",-1)),u("div",ME,[u("p",null,[t[5]||(t[5]=u("strong",null,"Username:",-1)),_e(" "+K(s.user.username),1)]),u("p",null,[t[6]||(t[6]=u("strong",null,"Email:",-1)),_e(" "+K(s.user.email),1)]),u("p",null,[t[7]||(t[7]=u("strong",null,"Member Since:",-1)),_e(" "+K(s.formatDate(new Date)),1)])])])])])])}const VE=Je(SE,[["render",NE]]),LE={props:{products:{type:Array,default:()=>[]}},methods:{async addToCart(e){await ln.addToCart(e)},async addToWishlist(e){await uo.addToWishlist(e)}}},jE={class:"catalog-section"},UE={class:"products-grid"},qE={key:0,class:"product-badge"},BE={class:"product-image"},GE=["src","alt"],HE={class:"product-info"},zE={class:"product-name"},KE={key:0,class:"product-availability"},WE={key:1,class:"product-unavailability"},JE={class:"product-price"},YE={key:0,class:"price-old"},ZE={key:1,class:"price-discount"},QE={class:"price-current"},XE={class:"product-actions"},eS=["onClick"],tS=["onClick"];function nS(e,t,n,s,r,o){return R(),x("section",jE,[u("div",UE,[(R(!0),x($e,null,ht(n.products,i=>(R(),x("div",{key:i.id,class:"product-card"},[i.badge?(R(),x("div",qE,K(i.badge),1)):Y("",!0),u("div",BE,[u("img",{src:i.image,alt:i.name},null,8,GE)]),u("div",HE,[u("h3",zE,K(i.name),1),i.stock>0?(R(),x("div",KE,t[0]||(t[0]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),i.stock==0?(R(),x("div",WE,t[1]||(t[1]=[u("span",{class:"availability-icon"},"✖",-1),u("span",{class:"availability-text"},"Немає в наявності",-1)]))):Y("",!0),u("div",JE,[i.oldPrice?(R(),x("div",YE,K(i.oldPrice)+" ₴",1)):Y("",!0),i.discount?(R(),x("div",ZE,"-"+K(i.discount)+"%",1)):Y("",!0)]),u("div",QE,K(Math.round(i.priceAmount))+" ₴",1)]),u("div",XE,[u("button",{class:"wishlist-btn",onClick:a=>o.addToWishlist(i.id)},t[2]||(t[2]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})],-1)]),8,eS),u("button",{class:"cart-btn",onClick:a=>o.addToCart(i.id)},t[3]||(t[3]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("circle",{cx:"9",cy:"21",r:"1"}),u("circle",{cx:"20",cy:"21",r:"1"}),u("path",{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"})],-1)]),8,tS)])]))),128))])])}const sS=Je(LE,[["render",nS],["__scopeId","data-v-f392e6ac"]]),rS={name:"FiltersSidebar",props:{filterList:{type:Object,default:()=>({})}},data(){return{openFilters:{},selectedFilters:{},searchTerms:{},error:null}},created(){Object.keys(this.filterList).forEach(e=>{this.$set(this.openFilters,e,!0),this.$set(this.selectedFilters,e,[]),this.$set(this.searchTerms,e,"")})},methods:{toggleFilter(e){this.$set(this.openFilters,e,!this.openFilters[e])},isFilterOpen(e){return this.openFilters[e]===!0},shouldShowSearch(e){return Object.keys(this.filterList[e]||{}).length>5},filterOptionsBySearch(e,t){var r;const n=((r=this.searchTerms[t])==null?void 0:r.toLowerCase())||"";if(!n)return e;const s={};return Object.entries(e).forEach(([o,i])=>{o.toLowerCase().includes(n)&&(s[o]=i)}),s},emitFilterChange(){const e={};Object.entries(this.selectedFilters).forEach(([t,n])=>{n.length>0&&(e[t]=n)}),this.$emit("filter-change",e)}}},oS={class:"filters-sidebar"},iS=["onClick"],aS={class:"filter-title"},lS={class:"filter-content"},cS={key:0,class:"filter-search"},uS=["placeholder","onUpdate:modelValue"],dS={class:"filter-options"},fS=["value","onUpdate:modelValue"],hS={class:"option-name"},pS={class:"option-count"};function gS(e,t,n,s,r,o){return R(),x("div",oS,[(R(!0),x($e,null,ht(n.filterList,(i,a)=>(R(),x("div",{key:a,class:"filter-group"},[u("div",{class:"filter-header",onClick:l=>o.toggleFilter(a)},[u("h3",aS,K(a),1),(R(),x("svg",{class:Te(["filter-arrow",{rotated:o.isFilterOpen(a)}]),width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},t[1]||(t[1]=[u("path",{d:"M6 9l6 6 6-6"},null,-1)]),2))],8,iS),qe(u("div",lS,[o.shouldShowSearch(a)?(R(),x("div",cS,[qe(u("input",{type:"text",placeholder:"Пошук в "+a,"onUpdate:modelValue":l=>r.searchTerms[a]=l},null,8,uS),[[Ke,r.searchTerms[a]]])])):Y("",!0),u("div",dS,[(R(!0),x($e,null,ht(o.filterOptionsBySearch(i,a),(l,d)=>(R(),x("label",{key:d,class:"filter-option"},[qe(u("input",{type:"checkbox",value:d,"onUpdate:modelValue":c=>r.selectedFilters[a]=c,onChange:t[0]||(t[0]=(...c)=>o.emitFilterChange&&o.emitFilterChange(...c))},null,40,fS),[[au,r.selectedFilters[a]]]),u("span",hS,K(d),1),u("span",pS,K(l),1)]))),128))])],512),[[Lh,o.isFilterOpen(a)]])]))),128))])}const mS=Je(rS,[["render",gS],["__scopeId","data-v-f3a83ac7"]]),vS={name:"Pagination",props:{currentPage:{type:Number,required:!0},totalItems:{type:Number,required:!0},itemsPerPage:{type:Number,default:10},maxVisiblePages:{type:Number,default:5}},computed:{totalPages(){return Math.ceil(this.totalItems/this.itemsPerPage)||1},visiblePages(){const e=[],t=Math.floor(this.maxVisiblePages/2);let n=Math.max(1,this.currentPage-t);const s=Math.min(this.totalPages,n+this.maxVisiblePages-1);s-n+1<this.maxVisiblePages&&(n=Math.max(1,s-this.maxVisiblePages+1));for(let r=n;r<=s;r++)e.push(r);return e}},methods:{goToPage(e){e>=1&&e<=this.totalPages&&e!==this.currentPage&&(this.$emit("page-changed",e),console.log("emitted update:currentPage"))}}},yS={class:"pagination","aria-label":"Pagination"},_S=["disabled"],bS=["aria-label"],wS=["aria-label"],ES=["onClick","aria-current","aria-label"],SS=["aria-label"],CS=["aria-label"],AS=["disabled"];function OS(e,t,n,s,r,o){return R(),x("nav",yS,[u("button",{class:"pagination-btn-prev disable-styles",disabled:n.currentPage===1,onClick:t[0]||(t[0]=i=>o.goToPage(n.currentPage-1)),"aria-label":"Previous page"},t[6]||(t[6]=[u("span",{class:"fas fa-arrow-left"},null,-1)]),8,_S),o.visiblePages.indexOf(1)==-1?(R(),x("button",{key:0,class:"pagination-btn",onClick:t[1]||(t[1]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`}," 1 ",8,bS)):Y("",!0),o.visiblePages.indexOf(1)==-1?(R(),x("button",{key:1,class:"pagination-btn",onClick:t[2]||(t[2]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`},t[7]||(t[7]=[u("span",null,"...",-1)]),8,wS)):Y("",!0),(R(!0),x($e,null,ht(o.visiblePages,i=>(R(),x("button",{key:i,class:Te(["pagination-btn",{active:i===n.currentPage}]),onClick:a=>o.goToPage(i),"aria-current":i===n.currentPage?"page":null,"aria-label":`Go to page ${i}`},K(i),11,ES))),128)),o.visiblePages.indexOf(o.totalPages)==-1?(R(),x("button",{key:2,class:"pagination-btn",onClick:t[3]||(t[3]=i=>o.goToPage(1)),"aria-label":`Go to page ${e.page}`},t[8]||(t[8]=[u("span",null,"...",-1)]),8,SS)):Y("",!0),o.visiblePages.indexOf(o.totalPages)==-1?(R(),x("button",{key:3,class:"pagination-btn",onClick:t[4]||(t[4]=i=>o.goToPage(o.totalPages)),"aria-label":`Go to page ${e.page}`},K(o.totalPages),9,CS)):Y("",!0),u("button",{class:"pagination-btn-next disable-styles",disabled:n.currentPage===o.totalPages,onClick:t[5]||(t[5]=i=>o.goToPage(n.currentPage+1)),"aria-label":"Next page"},t[9]||(t[9]=[u("span",{class:"fas fa-arrow-right"},null,-1)]),8,AS)])}const TS=Je(vS,[["render",OS],["__scopeId","data-v-2d6a37ac"]]),RS={props:{reviews:{type:Array,default:()=>[]}},methods:{calculateAvgRating(e){return e?((e.service+e.deliveryTime+e.accuracy)/3).toFixed(1):0},formatDate(e){return e?new Date(e).toLocaleDateString("uk-UA"):""}}},xS={class:"category-reviews"},PS={key:0,class:"no-reviews"},kS={key:1,class:"reviews-list"},IS={class:"review-header"},$S={class:"product-info"},FS={class:"rating"},DS={class:"rating-value"},MS={class:"review-content"},NS={class:"review-footer"},VS={class:"review-date"};function LS(e,t,n,s,r,o){return R(),x("div",xS,[t[1]||(t[1]=u("h3",{class:"reviews-title"},"Відгуки покупців",-1)),n.reviews.length===0?(R(),x("div",PS," Немає відгуків для товарів цієї категорії ")):(R(),x("div",kS,[(R(!0),x($e,null,ht(n.reviews,i=>(R(),x("div",{key:i.id,class:"review-card"},[u("div",IS,[u("div",$S,[u("strong",null,K(i.productName),1)]),u("div",FS,[t[0]||(t[0]=u("span",{class:"stars"},"★★★★★",-1)),u("span",DS,K(o.calculateAvgRating(i.rating))+"/5",1)])]),u("div",MS,K(i.comment),1),u("div",NS,[u("span",VS,K(o.formatDate(i.createdAt)),1)])]))),128))]))])}const jS=Je(RS,[["render",LS],["__scopeId","data-v-d71ec2e7"]]);class US{getCategoryReviews(t,n={}){return Xe.get(`/categories/${t}/reviews`,{params:n})}}const qS=new US,BS={class:"electronics-page"},GS={class:"breadcrumbs"},HS={key:0,href:"/"},zS={key:1},KS={key:0,class:"page-title"},WS={class:"page-stats"},JS={key:0},YS={class:"content-container"},ZS={class:"products-grid"},QS={class:"recommended-products-section"},XS={class:"container"},e2={class:"category-reviews-section"},t2={class:"container"},n2={data(){return{filters:[],products:[],recommendedProducts:[],categoryReviews:[],currentPage:1,itemsPerPage:24,totalCount:0,categorySlug:this.$route.params.value,categoryName:"",description:"",error:null}},async mounted(){await this.fetchProducts(),await this.fetchAllCount(),await this.fetchFilters(),await this.fetchCategoryInfo(),await this.fetchRecommendedProducts(),await this.fetchCategoryReviews()},methods:{async fetchAllCount(){try{const e=await Sn.getProducts(this.categorySlug);this.totalCount=e.data.data.filter(t=>t.status===1).length,this.error=null}catch(e){this.error="Failed to load product count. Please try again.",console.error(e)}},async fetchCategoryInfo(e){try{const t=await Sn.getBySlug(this.categorySlug,e);this.categoryName=t.data.name,this.description=t.data.description,this.error=null}catch(t){this.error="Failed to load category info. Please try again.",console.error(t)}},async fetchProducts(e={page:1,pageSize:this.itemsPerPage}){try{const t=await Sn.getProducts(this.categorySlug,e);this.products=t.data.data||[],this.currentPage=t.data.currentPage,this.error=null}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}},async fetchFilters(){try{const e=await Hi.getFilters(this.categorySlug);console.log(e),this.filters=e.data.data||[],console.log(filters),this.error=null}catch(e){this.error="Failed to load filters. Please try again.",console.error(e)}},async fetchRecommendedProducts(e={page:1,pageSize:4}){try{const t=await Sn.getProducts(this.categorySlug,e);this.recommendedProducts=t.data.data,this.error=null}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}},handlePageChange(e){this.fetchProducts({page:e,pageSize:this.itemsPerPage})},async fetchCategoryReviews(e={page:1,pageSize:5}){try{const t=await qS.getCategoryReviews(this.categorySlug,e);this.categoryReviews=t.data.data||[],this.error=null}catch(t){this.error="Failed to load category reviews. Please try again.",console.error(t)}}}},s2=Object.assign(n2,{__name:"CatalogPage",setup(e){return(t,n)=>(R(),x("div",BS,[u("div",GS,[t.description?(R(),x("a",HS,K(t.description),1)):Y("",!0),n[0]||(n[0]=_e(" / ")),t.categoryName?(R(),x("span",zS,K(t.categoryName),1)):Y("",!0)]),t.categoryName?(R(),x("h1",KS,K(t.categoryName),1)):Y("",!0),u("div",WS,[t.totalCount?(R(),x("span",JS,"Знайдено "+K(t.totalCount)+" товарів",1)):Y("",!0),n[1]||(n[1]=u("div",{class:"sort-container"},[u("span",null,"За популярністю"),u("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[u("path",{d:"M6 9l6 6 6-6"})])],-1))]),u("div",YS,[ae(mS,{filterList:t.filters},null,8,["filterList"]),u("div",ZS,[ae(sS,{products:t.products},null,8,["products"]),ae(TS,{currentPage:t.currentPage,"total-items":t.totalCount,"items-per-page":t.itemsPerPage,"max-visible-pages":10,onPageChanged:t.handlePageChange},null,8,["currentPage","total-items","items-per-page","onPageChanged"])])]),u("section",QS,[u("div",XS,[ae(rd,{products:t.recommendedProducts},null,8,["products"])])]),u("section",e2,[u("div",t2,[ae(jS,{reviews:t.categoryReviews},null,8,["reviews"])])])]))}}),r2=Je(s2,[["__scopeId","data-v-710bddfb"]]),o2={name:"Cart",data(){return{cartItems:[],recommendedProducts:[],error:null}},computed:{totalItems(){return this.cartItems.reduce((e,t)=>e+t.quantity,0)},subtotal(){return Number.isNaN(Number(this.cartItems.reduce((e,t)=>e+t.total,0)))?this.originalTotal:this.cartItems.reduce((e,t)=>e+t.total,0)},originalTotal(){return this.cartItems.reduce((e,t)=>e+Math.round(t.totalPrice),0)},discount(){return this.subtotal==this.originalTotal?0:this.orginalTotal-this.subtotal},discountToPercent(){return this.discount==0?0:100*(this.orginalTotal-this.subtotal)/this.originalTotal}},async mounted(){await this.fetchCart(),await this.fetchProducts()},methods:{async fetchProducts(e){try{const t=await Hi.getAll(e={});this.products=t.data.data,this.error=null}catch(t){this.error="Failed to load products. Please try again.",console.error(t)}},async fetchCart(){try{const e=await ln.getCart();console.log(e),this.cartItems=e.data.data.items,this.error=null}catch{this.error="Failed to load cart. Please try again."}},async increaseQuantity(e){e.quantity<e.productStock&&await ln.changeItemCount(e.id,e.quantity+1),await this.fetchCart()},async decreaseQuantity(e){e.quantity>1?await ln.changeItemCount(e.id,e.quantity-1):await ln.deleteItem(e.id),await this.fetchCart()},async removeItem(e){await ln.deleteItem(e),await this.fetchCart()},async clearCart(){ln.deleteCart()},async addToWishlist(e){await uo.addToWishlist(e)},addToFavorites(e){console.log(`Added item ${e} to favorites`)},applyPromoCode(e){console.log(`Applied promo code: ${e}`)},proceedToCheckout(){console.log("Proceeding to checkout")}}},i2={class:"cart-page"},a2={class:"cart-header"},l2={class:"cart-title"},c2={class:"cart-count"},u2={key:0,class:"cart-count"},d2={class:"cart-content"},f2={class:"cart-items"},h2={class:"cart-item"},p2={class:"item-details"},g2={class:"item-image"},m2=["src","alt"],v2={class:"item-info"},y2={key:0,class:"item-availability"},_2={key:1,class:"item-name"},b2={key:2,class:"item-code"},w2={class:"item-actions"},E2=["onClick"],S2={class:"item-price-controls"},C2={class:"price-container"},A2={key:0,class:"price-current"},O2={key:1,class:"price-original"},T2={class:"quantity-controls"},R2=["onClick"],x2=["value"],P2=["onClick"],k2={class:"cart-summary"},I2={class:"summary-row"},$2={class:"summary-value"},F2={class:"summary-row"},D2={class:"summary-value discount"},M2={class:"summary-row total"},N2={class:"summary-value"},V2={class:"recommended-products"},L2={class:"container"},j2={class:"products-grid"},U2={key:0,class:"product-badge"},q2={class:"product-image"},B2=["src","alt"],G2={class:"product-info"},H2={class:"product-name"},z2={key:0,class:"product-availability"},K2={key:1,class:"product-unavailability"},W2={class:"product-price"},J2={key:0,class:"price-old"},Y2={key:1,class:"price-discount"},Z2={class:"price-current"};function Q2(e,t,n,s,r,o){const i=st("router-link");return R(),x("div",i2,[u("div",a2,[ae(i,{to:"/",class:"back-link"},{default:Ge(()=>t[0]||(t[0]=[u("i",{class:"fas fa-arrow-left"},null,-1)])),_:1}),u("h1",l2,[t[1]||(t[1]=_e("Кошик ")),u("span",c2,K(o.totalItems)+" товари ",1),o.discountToPercent!=0?(R(),x("span",u2,"(-"+K(o.discountToPercent)+"%)",1)):Y("",!0)])]),u("div",d2,[u("div",f2,[t[7]||(t[7]=u("button",{class:"clear-cart-btn"},[u("i",{class:"fas fa-trash"}),_e(" Видалити все ")],-1)),(R(!0),x($e,null,ht(r.cartItems,a=>(R(),x("div",h2,[t[6]||(t[6]=u("button",{class:"remove-item"},[u("i",{class:"fas fa-times"})],-1)),u("div",p2,[u("div",g2,[a.productImage?(R(),x("img",{key:0,src:a.productImage||"@assets/images/icons/placeholder-icon.svg",alt:a.productName},null,8,m2)):Y("",!0)]),u("div",v2,[a.productStock>0?(R(),x("div",y2,t[2]||(t[2]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),a.productName?(R(),x("h3",_2,K(a.productName),1)):Y("",!0),a.id?(R(),x("p",b2,"Код товару: "+K(a.id),1)):Y("",!0),u("div",w2,[u("button",{class:"add-to-favorites",onClick:l=>o.addToWishlist(a.productId)},t[3]||(t[3]=[u("span",{class:"add-to-favorites-heart"},[u("i",{class:"far fa-heart"})],-1),u("span",{class:"add-to-favirites-text"},"В обрані",-1)]),8,E2)])]),u("div",S2,[u("div",C2,[a.totalPrice?(R(),x("div",A2,K(Math.round(a.totalPrice))+" ₴",1)):Y("",!0),a.oldPrice?(R(),x("div",O2,K(a.oldPrice)+" ₴",1)):Y("",!0)]),u("div",T2,[u("button",{class:"quantity-btn minus",onClick:l=>o.decreaseQuantity(a)},t[4]||(t[4]=[u("i",{class:"fas fa-minus"},null,-1)]),8,R2),u("input",{type:"number",class:"quantity-input",value:a.quantity,min:"1"},null,8,x2),u("button",{class:"quantity-btn plus",onClick:l=>o.increaseQuantity(a)},t[5]||(t[5]=[u("i",{class:"fas fa-plus"},null,-1)]),8,P2)])])])]))),256))]),u("div",k2,[t[10]||(t[10]=u("h2",{class:"summary-title"},"Разом",-1)),u("div",I2,[u("span",null,K(o.totalItems)+" товари на суму",1),u("span",$2,K(o.originalTotal)+" ₴",1)]),u("div",F2,[t[8]||(t[8]=u("span",null,"Знижка",-1)),u("span",D2,K(o.discount)+" ₴",1)]),t[11]||(t[11]=u("div",{class:"summary-row"},[u("span",null,"Вартість доставки"),u("span",{class:"summary-value free"},"Безкоштовно")],-1)),t[12]||(t[12]=u("div",{class:"summary-divider"},null,-1)),u("div",M2,[t[9]||(t[9]=u("span",null,"До оплати",-1)),u("span",N2,K(o.subtotal)+" ₴",1)]),t[13]||(t[13]=hn('<button class="checkout-btn" data-v-4800e765>Перейти до оформлення</button><div class="promo-code" data-v-4800e765><span data-v-4800e765>Промокод</span><div class="promo-input-container" data-v-4800e765><input type="text" class="promo-input" placeholder="Введіть промокод" data-v-4800e765><button class="apply-promo-btn" data-v-4800e765>Додати</button></div></div>',2))])]),u("div",V2,[t[17]||(t[17]=u("h2",{class:"section-title"},"Рекомендовані товари",-1)),u("div",L2,[u("div",j2,[(R(!0),x($e,null,ht(r.recommendedProducts,a=>(R(),x("div",{key:a.id,class:"product-card"},[a.badge?(R(),x("div",U2,K(a.badge),1)):Y("",!0),u("div",q2,[u("img",{src:a.image,alt:a.name},null,8,B2)]),u("div",G2,[u("h3",H2,K(a.name),1),a.stock>0?(R(),x("div",z2,t[14]||(t[14]=[u("span",{class:"availability-icon"},"✓",-1),u("span",{class:"availability-text"},"В наявності",-1)]))):Y("",!0),a.stock==0?(R(),x("div",K2,t[15]||(t[15]=[u("span",{class:"availability-icon"},"✖",-1),u("span",{class:"availability-text"},"Немає в наявності",-1)]))):Y("",!0),u("div",W2,[a.oldPrice?(R(),x("div",J2,K(a.oldPrice)+" ₴",1)):Y("",!0),e.cartItem.discount?(R(),x("div",Y2,"-"+K(a.discount)+"%",1)):Y("",!0)]),u("div",Z2,K(a.priceAmount)+" ₴",1)]),t[16]||(t[16]=hn('<div class="product-actions" data-v-4800e765><button class="wishlist-btn" data-v-4800e765><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-4800e765><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" data-v-4800e765></path></svg></button><button class="cart-btn" data-v-4800e765><svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-4800e765><circle cx="9" cy="21" r="1" data-v-4800e765></circle><circle cx="20" cy="21" r="1" data-v-4800e765></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6" data-v-4800e765></path></svg></button></div>',1))]))),128))])])])])}const X2=Je(o2,[["render",Q2],["__scopeId","data-v-4800e765"]]),eC=()=>Ie(()=>import("./NotFoundPage-BsHt-cUw.js"),__vite__mapDeps([0,1])),tC=()=>Ie(()=>import("./AdminLayout-Cv8tSEUJ.js"),__vite__mapDeps([2,3])),nC=()=>Ie(()=>import("./Dashboard-CbicfS87.js"),__vite__mapDeps([4,5,6,7,8])),sC=()=>Ie(()=>import("./UserList-DESvC1RO.js"),__vite__mapDeps([9,10,11,12,13,14,15])),rC=()=>Ie(()=>import("./UserDetail-CC-AwMLs.js"),__vite__mapDeps([16,10,13,14,17,7])),oC=()=>Ie(()=>import("./Products-CxqKb4pE.js"),__vite__mapDeps([18,19,20,6,7,21,11,12,13,14,22])),iC=()=>Ie(()=>import("./ProductView-B32do9Ws.js"),__vite__mapDeps([23,21,24,25])),aC=()=>Ie(()=>import("./ProductEdit-DMGe_nnb.js"),__vite__mapDeps([26,21,24,27])),lC=()=>Ie(()=>import("./ProductCreate-BZ5OEql0.js"),__vite__mapDeps([28,26,21,24,27])),cC=()=>Ie(()=>import("./CategoryList-C9tCzFrW.js"),__vite__mapDeps([29,13,14,30])),uC=()=>Ie(()=>import("./CategoryDetail-GiN5JLA4.js"),__vite__mapDeps([31,13,14,32])),Gl=()=>Ie(()=>import("./CategoryForm-BFA-MzER.js"),__vite__mapDeps([33,34])),dC=()=>Ie(()=>import("./OrderList-D_PaRh9V.js"),__vite__mapDeps([35,36,6,7,11,12,37])),fC=()=>Ie(()=>import("./OrderDetail-MMuuH13h.js"),__vite__mapDeps([38,36,6,7,39])),hC=()=>Ie(()=>import("./SellerRequestList-zSoqrLS1.js"),__vite__mapDeps([40,41,6,7,11,12,13,14,42])),pC=()=>Ie(()=>import("./SellerRequestDetail-BTolo_nV.js"),__vite__mapDeps([43,41,6,7,13,14,44])),gC=()=>Ie(()=>import("./CompanyList-K-yE_voF.js"),__vite__mapDeps([45,24,19,20,11,12,46])),mC=()=>Ie(()=>import("./CompanyDetail-DAB2A9Cu.js"),__vite__mapDeps([47,24,48])),vC=()=>Ie(()=>import("./CompanyEdit-PevIfUrT.js"),__vite__mapDeps([49,24,21,50])),yC=()=>Ie(()=>import("./ReviewList-W5LUiEy0.js"),__vite__mapDeps([51,52,19,20,53,54,55])),_C=()=>Ie(()=>import("./ReviewDetail-sshVlUxY.js"),__vite__mapDeps([56,52,57])),bC=()=>Ie(()=>import("./RatingList-BbFziP1Z.js"),__vite__mapDeps([58,19,20,53,54,59])),wC=()=>Ie(()=>import("./ChatList-BgqtKI14.js"),__vite__mapDeps([60,61,62,63,53,54,64])),EC=()=>Ie(()=>import("./ChatDetail-DSqpAgF1.js"),__vite__mapDeps([65,61,66])),SC=()=>Ie(()=>import("./AddressList-V2kbPOK0.js"),__vite__mapDeps([67,62,63,53,54,68])),CC=()=>Ie(()=>import("./ApiTest-CDWWdvvg.js"),__vite__mapDeps([69,21,70])),AC=()=>Ie(()=>import("./Settings-D8Q_UYEa.js"),__vite__mapDeps([71,72])),OC=()=>Ie(()=>import("./Security-WXIWPOVF.js"),__vite__mapDeps([73,74])),TC=()=>Ie(()=>import("./Reports-Drunj1GZ.js"),__vite__mapDeps([75,5,76])),RC=[{path:"/",name:"Home",component:M0},{path:"/login",name:"Login",component:Pb,meta:{guestOnly:!0}},{path:"/register",name:"Register",component:Xb,meta:{guestOnly:!0}},{path:"/dashboard",name:"Dashboard",component:VE,meta:{requiresAuth:!0}},{path:"/cart",name:"Cart",component:X2,meta:{requiresAuth:!0}},{path:"/catalog/:value([a-zA-Z-0-9]+)",name:"Catalog",component:r2,meta:{requiresAuth:!1}},{path:"/profile",name:"Profile",component:Bl,meta:{requiresAuth:!0}},{path:"/user/profile",name:"UserProfile",component:Bl,meta:{requiresAuth:!0}},{path:"/admin",component:tC,meta:{requiresAuth:!0,requiresAdminOrModerator:!0},children:[{path:"dashboard",name:"AdminDashboard",component:nC},{path:"users",name:"AdminUsers",component:sC},{path:"users/:id",name:"AdminUserDetail",component:rC},{path:"products",name:"AdminProducts",component:oC},{path:"products/create",name:"AdminProductCreate",component:lC},{path:"products/:id/view",name:"AdminProductView",component:iC},{path:"products/:id/edit",name:"AdminProductEdit",component:aC},{path:"categories",name:"AdminCategories",component:cC},{path:"categories/create",name:"AdminCategoryCreate",component:Gl},{path:"categories/:id",name:"AdminCategoryDetail",component:uC},{path:"categories/:id/edit",name:"AdminCategoryEdit",component:Gl},{path:"orders",name:"AdminOrders",component:dC},{path:"orders/:id",name:"AdminOrderDetail",component:fC},{path:"seller-requests",name:"AdminSellerRequests",component:hC},{path:"seller-requests/:id",name:"AdminSellerRequestDetail",component:pC},{path:"companies",name:"AdminCompanies",component:gC},{path:"companies/:id",name:"AdminCompanyDetail",component:mC},{path:"companies/:id/edit",name:"AdminCompanyEdit",component:vC},{path:"reviews",name:"AdminReviews",component:yC},{path:"reviews/:id",name:"AdminReviewDetail",component:_C},{path:"ratings",name:"AdminRatings",component:bC},{path:"chats",name:"AdminChats",component:wC},{path:"chats/:id",name:"AdminChatDetail",component:EC},{path:"addresses",name:"AdminAddresses",component:SC},{path:"test",name:"ApiTest",component:CC},{path:"settings",name:"AdminSettings",component:AC,meta:{requiresAdmin:!0}},{path:"security",name:"AdminSecurity",component:OC,meta:{requiresAdmin:!0}},{path:"reports",name:"AdminReports",component:TC},{path:"",redirect:{name:"AdminDashboard"}},{path:":pathMatch(.*)*",redirect:{name:"AdminDashboard"}}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:eC}],Xi=$g({history:lg(),routes:RC});Xi.beforeEach(async(e,t,n)=>{Qe.dispatch("loading/startRouteChange","Navigating..."),t.path&&(await Ie(async()=>{const{default:a}=await Promise.resolve().then(()=>oy);return{default:a}},void 0)).default.cancelRequestsForRoute(t.path);const s=Qe.getters["auth/isLoggedIn"],r=Qe.getters["auth/isAdmin"],o=Qe.getters["auth/isModerator"];if(s){const i=Qe.getters["auth/user"];console.log("Current user:",i),console.log("User role:",i==null?void 0:i.role),console.log("Is admin?",r),console.log("Trying to access:",e.fullPath);const a=(i==null?void 0:i.role)==="Admin";console.log("Is admin by direct check?",a)}if(e.matched.some(i=>i.meta.requiresAuth))if(!s)console.log("Not logged in, redirecting to login"),Qe.dispatch("loading/finishRouteChange"),n({name:"Login",query:{redirect:e.fullPath}});else if(e.matched.some(i=>i.meta.requiresAdminOrModerator)){const i=Qe.getters["auth/user"],a=i==null?void 0:i.role;let l=!1;typeof a=="string"?l=a==="Admin"||a==="Moderator":typeof a=="number"&&(l=a===3||a===4),!r&&!o&&!l?(console.log("Not admin or moderator, redirecting to dashboard"),Qe.dispatch("loading/finishRouteChange"),n({name:"Dashboard"})):e.matched.some(d=>d.meta.requiresAdmin)?r?n():(console.log("Admin-only route, but user is not admin, redirecting to admin dashboard"),Qe.dispatch("loading/finishRouteChange"),n({name:"AdminDashboard"})):n()}else if(e.matched.some(i=>i.meta.requiresAdmin)){const i=Qe.getters["auth/user"],a=i==null?void 0:i.role;console.log("User role in router guard:",a),console.log("Role type:",typeof a);let l=!1;typeof a=="string"?l=a==="Admin":typeof a=="number"?l=a===4:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(l=a.value==="Admin"||a.value===4),a.hasOwnProperty("name")&&(l=a.name==="Admin")),console.log("Is admin by direct role check:",l),console.log("Is admin by getter:",r),!r&&!l?(console.log("Not admin, redirecting to dashboard"),Qe.dispatch("loading/finishRouteChange"),n({name:"Dashboard"})):(console.log("Admin access granted"),n())}else n();else if(e.matched.some(i=>i.meta.guestOnly))if(s){const i=Qe.getters["auth/user"],a=i==null?void 0:i.role;let l=!1;typeof a=="string"?l=a==="Admin":typeof a=="number"?l=a===4:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(l=a.value==="Admin"||a.value===4),a.hasOwnProperty("name")&&(l=a.name==="Admin"));let d=!1;typeof a=="string"?d=a==="Moderator":typeof a=="number"?d=a===3:a&&typeof a=="object"&&(a.hasOwnProperty("value")&&(d=a.value==="Moderator"||a.value===3),a.hasOwnProperty("name")&&(d=a.name==="Moderator"));const c=r||l||o||d;console.log("Guest route, user is logged in"),console.log("Should redirect to admin?",c),Qe.dispatch("loading/finishRouteChange"),n({name:c?"AdminDashboard":"Dashboard"})}else n();else n()});Xi.afterEach(()=>{Qe.dispatch("loading/finishRouteChange")});const ea=document.createElement("link");ea.rel="stylesheet";ea.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css";document.head.appendChild(ea);const ta=rp(Qv);ta.use(Xi);ta.use(Qe);ta.mount("#app");export{qe as A,Ke as B,xC as C,Ce as D,Hg as E,$e as F,Lg as G,hn as H,es as I,xt as J,St as K,au as L,WC as M,Lf as N,ou as T,Je as _,u as a,ae as b,x as c,_e as d,no as e,Ui as f,ve as g,te as h,kn as i,Pc as j,Y as k,Fr as l,cu as m,Te as n,R as o,ht as p,Xe as q,st as r,On as s,K as t,er as u,Jr as v,Ge as w,It as x,wi as y,Qt as z};
