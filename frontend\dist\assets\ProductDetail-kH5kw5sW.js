import{_ as T,g as r,h as C,f as V,i as L,c as l,a as s,b as u,w as f,r as R,d as g,t as a,k as A,F as D,p as I,n as h,e as z,o as i}from"./index-C1YYMYJd.js";import{p as N}from"./products-DEnweLaU.js";import{S as H}from"./StatusBadge-7HpbPXqn.js";import{C as K}from"./ConfirmDialog-hpHY5nH2.js";/* empty css                                                                    */const W={class:"product-detail"},Y={class:"level"},j={class:"level-right"},q={class:"level-item"},G={key:0,class:"has-text-centered py-6"},J={key:1,class:"notification is-danger"},O={key:2,class:"notification is-warning"},Q={key:3},X={class:"card mb-4"},Z={class:"card-content"},ss={class:"columns"},ts={class:"column is-8"},es={class:"product-title"},os={class:"product-sku"},as={class:"column is-4 has-text-right"},ls={class:"buttons is-right"},is={class:"columns"},ns={class:"column is-4"},cs={class:"card"},ds={class:"card-content"},rs={key:0,class:"product-images"},us={class:"main-image"},vs=["src","alt"],ms={key:0,class:"image-thumbnails"},_s=["onClick"],ps=["src","alt"],fs={key:1,class:"no-image"},gs={class:"column is-8"},hs={class:"card"},ys={class:"card-content"},ks={class:"columns"},bs={class:"column is-6"},Ps={class:"info-group"},Cs={class:"info-value"},As={class:"column is-6"},Ds={class:"info-group"},Is={class:"info-value"},Ns={class:"columns"},ws={class:"column is-6"},xs={class:"info-group"},Fs={class:"info-value"},Ss={class:"column is-6"},Bs={class:"info-group"},Es={class:"info-value"},$s={class:"info-group"},Us={class:"content"},Ms={key:0,class:"info-group"},Ts={class:"tags"},Vs={class:"card mt-4"},Ls={class:"card-content"},Rs={class:"columns is-multiline"},zs={class:"column is-6"},Hs={class:"info-group"},Ks={class:"info-value"},Ws={class:"column is-6"},Ys={class:"info-group"},js={class:"info-value"},qs={class:"column is-6"},Gs={class:"info-group"},Js={class:"info-value"},Os={class:"column is-6"},Qs={class:"info-group"},Xs={class:"info-value"},Zs={class:"icon"},st={class:"column is-6"},tt={class:"info-group"},et={class:"info-value"},ot={class:"column is-6"},at={class:"info-group"},lt={class:"info-value"},it={__name:"ProductDetail",setup(nt){const w=V(),x=z(),m=r(!0),n=r(null),e=r({}),y=r(0),v=r(!1),k=C(()=>w.params.id),F=C(()=>{if(!e.value.images||e.value.images.length===0)return"https://via.placeholder.com/500?text=No+Image";const o=e.value.images.find(t=>t.isMain);return o?o.url:e.value.images[y.value].url}),S=async()=>{m.value=!0,n.value=null;try{const o=await N.getProductById(k.value);e.value=o,Array.isArray(e.value.images)||(e.value.images=[]),typeof e.value.tags=="string"&&(e.value.tags=e.value.tags.split(",").map(t=>t.trim()).filter(t=>t))}catch(o){console.error("Error fetching product:",o),n.value="Failed to load product data. Please try again."}finally{m.value=!1}},_=o=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(o),b=o=>o?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(o)):"N/A",B=o=>o<=0?"is-danger":o<=5?"is-warning":"is-success",P=o=>{o.target.src="https://via.placeholder.com/500?text=No+Image"},E=o=>{y.value=o},$=()=>{v.value=!0},U=async()=>{try{await N.deleteProduct(k.value),x.push("/admin/products")}catch(o){console.error("Error deleting product:",o),n.value="Failed to delete product. Please try again."}finally{v.value=!1}},M=()=>{v.value=!1};return L(()=>{S()}),(o,t)=>{const p=R("router-link");return i(),l("div",W,[s("div",Y,[t[2]||(t[2]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Product Details")])],-1)),s("div",j,[s("div",q,[u(p,{to:"/admin/products",class:"button is-light"},{default:f(()=>t[1]||(t[1]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Products",-1)])),_:1})])])]),m.value?(i(),l("div",G,t[3]||(t[3]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading product details...",-1)]))):n.value?(i(),l("div",J,[s("button",{class:"delete",onClick:t[0]||(t[0]=c=>n.value=null)}),g(" "+a(n.value),1)])):e.value.id?(i(),l("div",Q,[s("div",X,[s("div",Z,[s("div",ss,[s("div",ts,[s("h2",es,a(e.value.name),1),s("p",os,"SKU: "+a(e.value.sku||"N/A"),1)]),s("div",as,[s("div",ls,[u(p,{to:`/admin/products/${e.value.id}/edit`,class:"button is-primary"},{default:f(()=>t[6]||(t[6]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit",-1)])),_:1},8,["to"]),s("button",{class:"button is-danger",onClick:$},t[7]||(t[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete",-1)]))])])])])]),s("div",is,[s("div",ns,[s("div",cs,[t[9]||(t[9]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Product Images")],-1)),s("div",ds,[e.value.images&&e.value.images.length>0?(i(),l("div",rs,[s("div",us,[s("img",{src:F.value,alt:e.value.name,onError:P},null,40,vs)]),e.value.images.length>1?(i(),l("div",ms,[(i(!0),l(D,null,I(e.value.images,(c,d)=>(i(),l("div",{key:d,class:h(["thumbnail",{"is-active":c.isMain}]),onClick:ct=>E(d)},[s("img",{src:c.url,alt:`${e.value.name} - Image ${d+1}`,onError:P},null,40,ps)],10,_s))),128))])):A("",!0)])):(i(),l("div",fs,t[8]||(t[8]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-image fa-3x"})],-1),s("p",{class:"mt-2"},"No images available",-1)])))])])]),s("div",gs,[s("div",hs,[t[16]||(t[16]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Product Information")],-1)),s("div",ys,[s("div",ks,[s("div",bs,[s("div",Ps,[t[10]||(t[10]=s("h3",{class:"info-label"},"Price",-1)),s("p",Cs,a(_(e.value.price)),1)])]),s("div",As,[s("div",Ds,[t[11]||(t[11]=s("h3",{class:"info-label"},"Status",-1)),s("p",Is,[u(H,{status:e.value.status,type:"product"},null,8,["status"])])])])]),s("div",Ns,[s("div",ws,[s("div",xs,[t[12]||(t[12]=s("h3",{class:"info-label"},"Category",-1)),s("p",Fs,a(e.value.categoryName||"N/A"),1)])]),s("div",Ss,[s("div",Bs,[t[13]||(t[13]=s("h3",{class:"info-label"},"Stock",-1)),s("p",Es,[s("span",{class:h(["tag",B(e.value.stock)])},a(e.value.stock),3)])])])]),s("div",$s,[t[14]||(t[14]=s("h3",{class:"info-label"},"Description",-1)),s("div",Us,[s("p",null,a(e.value.description),1)])]),e.value.tags&&e.value.tags.length>0?(i(),l("div",Ms,[t[15]||(t[15]=s("h3",{class:"info-label"},"Tags",-1)),s("div",Ts,[(i(!0),l(D,null,I(e.value.tags,(c,d)=>(i(),l("span",{key:d,class:"tag is-primary"},a(c),1))),128))])])):A("",!0)])]),s("div",Vs,[t[23]||(t[23]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Additional Details")],-1)),s("div",Ls,[s("div",Rs,[s("div",zs,[s("div",Hs,[t[17]||(t[17]=s("h3",{class:"info-label"},"Compare at Price",-1)),s("p",Ks,a(e.value.compareAtPrice?_(e.value.compareAtPrice):"N/A"),1)])]),s("div",Ws,[s("div",Ys,[t[18]||(t[18]=s("h3",{class:"info-label"},"Cost Price",-1)),s("p",js,a(e.value.costPrice?_(e.value.costPrice):"N/A"),1)])]),s("div",qs,[s("div",Gs,[t[19]||(t[19]=s("h3",{class:"info-label"},"Weight",-1)),s("p",Js,a(e.value.weight?`${e.value.weight} kg`:"N/A"),1)])]),s("div",Os,[s("div",Qs,[t[20]||(t[20]=s("h3",{class:"info-label"},"Featured",-1)),s("p",Xs,[s("span",Zs,[s("i",{class:h(["fas",e.value.isFeatured?"fa-check has-text-success":"fa-times has-text-danger"])},null,2)]),g(" "+a(e.value.isFeatured?"Yes":"No"),1)])])]),s("div",st,[s("div",tt,[t[21]||(t[21]=s("h3",{class:"info-label"},"Created At",-1)),s("p",et,a(b(e.value.createdAt)),1)])]),s("div",ot,[s("div",at,[t[22]||(t[22]=s("h3",{class:"info-label"},"Last Updated",-1)),s("p",lt,a(b(e.value.updatedAt)),1)])])])])])])])])):(i(),l("div",O,[t[5]||(t[5]=s("p",null,"Product not found.",-1)),u(p,{to:"/admin/products",class:"button is-primary mt-4"},{default:f(()=>t[4]||(t[4]=[g(" Back to Products ")])),_:1})])),u(K,{"is-open":v.value,title:"Delete Product",message:`Are you sure you want to delete '${e.value.name}'? This action cannot be undone.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:U,onCancel:M},null,8,["is-open","message"])])}}},_t=T(it,[["__scopeId","data-v-7feebe7f"]]);export{_t as default};
