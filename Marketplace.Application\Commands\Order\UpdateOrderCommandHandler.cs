﻿using AutoMapper;
using Marketplace.Application.Commands.Notification;
using Marketplace.Application.Services.Auth;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Application.Commands.Order;

public class UpdateOrderCommandHandler : IRequestHandler<UpdateOrderCommand, UpdateOrderResponse>
{
    private readonly INotificationRepository? _notificationRepository;
    private readonly IOrderRepository _repository;
    private readonly IMapper _mapper;
    private readonly IEmailService? _emailService;
    private readonly IUserRepository _userRepository;

    public UpdateOrderCommandHandler(
        IOrderRepository repository,
        IMapper mapper,
        IUserRepository userRepository,
        INotificationRepository? notificationRepository = null,
        IEmailService? emailService = null)
    {
        _repository = repository;
        _mapper = mapper;
        _userRepository = userRepository;
        _notificationRepository = notificationRepository;
        _emailService = emailService;
    }

    public async Task<UpdateOrderResponse> Handle(UpdateOrderCommand command, CancellationToken cancellationToken)
    {
        try
        {
            var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
            if (item == null)
            {
                return new UpdateOrderResponse
                {
                    Success = false,
                    Message = "Order not found",
                    OrderId = command.Id
                };
            }

            // Send notification if status changed
            if (command.Status.HasValue && item.Status != command.Status)
            {
                string oldOrderStatus = GetOrderStatusDisplayName(item.Status);
                string newOrderStatus = GetOrderStatusDisplayName(command.Status.Value);

                // Create in-app notification
                if (_notificationRepository != null)
                {
                    var notification = new StoreNotificationCommand(
                        item.CustomerId,
                        "Змінено статус вашого замовлення!",
                        "Вітання! Один з наших адміністраторів змінив статус вашого замолення з " + oldOrderStatus + " на " + newOrderStatus + ".");

                    var notificationItem = _mapper.Map<Domain.Entities.Notification>(notification);
                    await _notificationRepository.AddAsync(notificationItem, cancellationToken);
                }

                // Send email notification
                if (_emailService != null)
                {
                    try
                    {
                        var user = await _userRepository.GetByIdAsync(item.CustomerId, cancellationToken);
                        if (user != null && user.EmailConfirmed)
                        {
                            var emailSubject = "Оновлення статусу замовлення";
                            var emailBody = CreateOrderStatusEmailBody(item.Id, oldOrderStatus, newOrderStatus);

                            await _emailService.SendEmailAsync(
                                user.Email.Value,
                                emailSubject,
                                emailBody,
                                cancellationToken);
                        }
                    }
                    catch (Exception)
                    {
                        // Ігноруємо помилку email, але не перериваємо процес оновлення
                        // В production середовищі тут можна додати логування помилки
                    }
                }
            }

            // Update order properties
            if (command.Status.HasValue)
            {
                item.Status = command.Status.Value;
            }

            // Note: PaymentStatus is handled through Payment entity, not Order
            // Note: UpdatedAt is not available in Order entity

            await _repository.UpdateAsync(item, cancellationToken);

            return new UpdateOrderResponse
            {
                Success = true,
                Message = "Order updated successfully",
                OrderId = item.Id
            };
        }
        catch (Exception ex)
        {
            return new UpdateOrderResponse
            {
                Success = false,
                Message = $"Error updating order: {ex.Message}",
                OrderId = command.Id
            };
        }
    }

    private static string GetOrderStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "Очікує обробки",
            OrderStatus.Shipped => "Відправлено",
            OrderStatus.Delivered => "Доставлено",
            OrderStatus.Cancelled => "Скасовано",
            _ => status.ToString()
        };
    }

    private static string CreateOrderStatusEmailBody(Guid orderId, string oldStatus, string newStatus)
    {
        return $@"
            <html>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>
                        Оновлення статусу замовлення
                    </h2>

                    <p>Вітаємо!</p>

                    <p>Статус вашого замовлення <strong>#{orderId}</strong> було змінено:</p>

                    <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                        <p style='margin: 0;'>
                            <strong>Попередній статус:</strong> <span style='color: #e74c3c;'>{oldStatus}</span><br>
                            <strong>Новий статус:</strong> <span style='color: #27ae60;'>{newStatus}</span>
                        </p>
                    </div>

                    <p>Ви можете переглянути деталі замовлення в особистому кабінеті на нашому сайті.</p>

                    <p style='margin-top: 30px;'>
                        З повагою,<br>
                        <strong>Команда Marketplace</strong>
                    </p>

                    <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                    <p style='font-size: 12px; color: #666; text-align: center;'>
                        Це автоматичне повідомлення. Будь ласка, не відповідайте на цей email.
                    </p>
                </div>
            </body>
            </html>";
    }
}

