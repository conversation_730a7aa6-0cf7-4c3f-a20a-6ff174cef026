﻿using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Category;

public class GetAllCategoryQueryHandler :
    PaginatedQueryHandler<GetAllCategoryQuery, Domain.Entities.Category, CategoryResponse>,
    IRequestHandler<GetAllCategoryQuery, PaginatedResponse<CategoryResponse>>
{
    private readonly ICategoryRepository _repository;
    private readonly IProductRepository _productRepository;

    public GetAllCategoryQueryHandler(
        ICategoryRepository repository,
        IProductRepository productRepository,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
        _productRepository = productRepository;
    }

    public async Task<PaginatedResponse<CategoryResponse>> Handle(GetAllCategoryQuery request, CancellationToken cancellationToken)
    {
        // Створюємо фільтр
        Expression<Func<Domain.Entities.Category, bool>>? filter = null;

        if (!string.IsNullOrEmpty(request.Filter))
        {
            filter = a => a.Name.Contains(request.Filter) ||
                    a.Slug.Value.Contains(request.Filter);
        }

        // Отримуємо пагіновані дані
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy,
            descending: request.Descending,
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 15,
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Category, object>>[] {
                c => c.Parent,
                c => c.Children
            }
        );

        // Мапимо категорії та додаємо кількість продуктів
        var categoryResponses = new List<CategoryResponse>();
        foreach (var category in pagedResult.Items)
        {
            var categoryResponse = Mapper.Map<CategoryResponse>(category);
            categoryResponse.ProductCount = await _productRepository.CountByCategoryAsync(category.Id, cancellationToken);
            categoryResponses.Add(categoryResponse);
        }

        // Створюємо пагіновану відповідь вручну з правильними даними
        int page = pagedResult.Page;
        int pageSize = pagedResult.PageSize;
        int total = pagedResult.TotalCount;
        int lastPage = pagedResult.TotalPages;
        int from = total == 0 ? 0 : ((page - 1) * pageSize) + 1;
        int to = Math.Min(from + pageSize - 1, total);

        var queryParams = new Dictionary<string, string?>
        {
            { "filter", request.Filter },
            { "orderBy", request.OrderBy },
            { "descending", request.Descending.ToString().ToLower() },
            { "pageSize", pageSize.ToString() }
        };

        string BuildUrl(int pageNum) =>
            $"{BaseUrl}/api/categories/all?{string.Join("&", queryParams
                .Where(kvp => kvp.Value != null)
                .Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value ?? string.Empty)}"))}&page={pageNum}";

        return new PaginatedResponse<CategoryResponse>
        {
            Total = total,
            PerPage = pageSize,
            CurrentPage = page,
            LastPage = lastPage,
            FirstPageUrl = BuildUrl(1),
            LastPageUrl = BuildUrl(lastPage),
            NextPageUrl = page < lastPage ? BuildUrl(page + 1) : null,
            PrevPageUrl = page > 1 ? BuildUrl(page - 1) : null,
            Path = $"{BaseUrl}/api/categories/all",
            From = from,
            To = to,
            Data = categoryResponses
        };
    }
}
