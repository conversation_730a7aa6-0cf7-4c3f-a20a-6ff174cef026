﻿﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Responses;

public class OrderAnalyticsResponse
{
    public int TotalOrders { get; set; }
    public int ProcessingOrders { get; set; }
    public int PendingOrders { get; set; }
    public int ShippedOrders { get; set; }
    public int DeliveredOrders { get; set; }
    public int CancelledOrders { get; set; }
    public Money TotalRevenue { get; set; }
    public Money AverageOrderValue { get; set; }
    public Dictionary<string, int> OrdersByStatus { get; set; }
    public Dictionary<string, Money> RevenueByStatus { get; set; }
    public Dictionary<string, int> OrdersByMonth { get; set; }
    public Dictionary<string, Money> RevenueByMonth { get; set; }
    public List<TopCustomerResponse> TopCustomers { get; set; }
}
