<template>
  <div class="admin-products">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Products</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="createProduct">
            <span class="icon"><i class="fas fa-plus"></i></span>
            <span>Create Product</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Products"
      search-placeholder="Search by name, description, or category..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="products"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading products...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchData">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Products Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <product-table
            :products="items"
            :categories="categories"
            :loading="loading"
            @view="viewProduct"
            @edit="editProduct"
            @delete="confirmDeleteProduct" />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-wrapper" v-if="totalPages > 1 || totalItems > 0">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>

    <!-- Add/Edit Product Modal -->
    <product-form-modal
      :is-open="isProductModalOpen"
      :product="selectedProduct"
      @close="closeProductModal"
      @save="saveProduct" />

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="isDeleteModalOpen"
      :title="'Delete Product'"
      :message="'Are you sure you want to delete this product? This action cannot be undone.'"
      @confirm="deleteProduct"
      @cancel="isDeleteModalOpen = false" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import ProductTable from '@/admin/components/products/ProductTable.vue';
import ProductFormModal from '@/admin/components/products/ProductFormModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Router
const router = useRouter();

// Filter configuration
const filterFields = ref([
  {
    key: 'categoryId',
    label: 'Category',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Categories',
    options: [] // Will be populated dynamically
  },
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Statuses',
    options: [
      { value: '0', label: 'Pending' },
      { value: '1', label: 'Approved' },
      { value: '2', label: 'Rejected' }
    ]
  },
  {
    key: 'stock',
    label: 'Stock Level',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Stock Levels',
    options: [
      { value: 'in-stock', label: 'In Stock (>10)' },
      { value: 'low-stock', label: 'Low Stock (1-10)' },
      { value: 'out-of-stock', label: 'Out of Stock (0)' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false,
    options: [
      { value: 'Name', label: 'Name' },
      { value: 'CreatedAt', label: 'Created Date' },
      { value: 'Stock', label: 'Stock' },
      { value: 'Status', label: 'Status' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
]);

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: productsService.getProducts,
  defaultFilters: {
    search: '',
    categoryId: '',
    status: '',
    stock: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 20, // Збільшуємо розмір сторінки для кращого відображення
  clientSideSearch: false
});

// Modal state
const isProductModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const selectedProduct = ref(null);

// Categories state
const categories = ref([]);

// Load categories for filter
const loadCategories = async () => {
  try {
    console.log('🗂️ Loading categories...');
    const response = await categoriesService.getCategories();
    console.log('📋 Categories service response:', response);

    const categoriesData = response.data || response.categories || [];
    console.log('📊 Categories data:', categoriesData);

    // Store categories for the table component
    categories.value = categoriesData;
    console.log('✅ Categories stored for table:', categories.value.length, 'categories');

    // Flatten categories and subcategories for the filter
    const flattenCategories = (cats, prefix = '') => {
      let result = [];
      cats.forEach(category => {
        const label = prefix ? `${prefix} > ${category.name}` : category.name;
        result.push({
          value: category.id,
          label: label
        });

        // Add subcategories if they exist
        if (category.children && category.children.length > 0) {
          result = result.concat(flattenCategories(category.children, label));
        }
      });
      return result;
    };

    const flattenedCategories = flattenCategories(categoriesData);
    console.log('🔄 Flattened categories for filter:', flattenedCategories);

    // Update the category filter options
    const categoryFilter = filterFields.value.find(field => field.key === 'categoryId');
    if (categoryFilter) {
      categoryFilter.options = flattenedCategories;
      console.log('✅ Category filter updated with', flattenedCategories.length, 'options');
      console.log('📋 Sample categories:', flattenedCategories.slice(0, 3));

      // Force reactivity update
      filterFields.value = [...filterFields.value];
    } else {
      console.error('❌ Category filter field not found');
    }
  } catch (error) {
    console.error('❌ Error loading categories:', error);
  }
};

// Subcategories removed - not available in current data structure

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  console.log(`🔄 Filter changed: ${filterKey} = "${filterValue}"`);

  // Special handling for category filter to include subcategories
  if (filterKey === 'categoryId' && filterValue) {
    const selectedCategoryIds = getCategoryAndSubcategoryIds(filterValue);
    console.log(`📂 Selected category includes subcategories:`, selectedCategoryIds);

    // Send all category IDs to backend
    filters.categoryIds = selectedCategoryIds;
    filters[filterKey] = filterValue; // Keep original for UI
  } else if (filterKey === 'categoryId' && !filterValue) {
    // Clear category filter
    delete filters.categoryIds;
    filters[filterKey] = filterValue;
  } else {
    filters[filterKey] = filterValue;
  }

  console.log('📊 Current filters state:', { ...filters });
};

// Helper function to get category and all its subcategory IDs
const getCategoryAndSubcategoryIds = (categoryId) => {
  const ids = [categoryId];

  const collectSubcategoryIds = (cats, targetId) => {
    for (const category of cats) {
      if (category.id === targetId) {
        // Found the category, now collect all subcategory IDs
        const collectAllChildren = (cat) => {
          if (cat.children && cat.children.length > 0) {
            cat.children.forEach(child => {
              ids.push(child.id);
              collectAllChildren(child); // Recursive for nested subcategories
            });
          }
        };
        collectAllChildren(category);
        return true;
      }

      if (category.children && category.children.length > 0) {
        if (collectSubcategoryIds(category.children, targetId)) {
          return true;
        }
      }
    }
    return false;
  };

  collectSubcategoryIds(categories.value, categoryId);
  return ids;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else if (key === 'sortBy') {
      filters[key] = 'CreatedAt';
    } else if (key === 'sortOrder') {
      filters[key] = 'desc';
    } else {
      filters[key] = '';
    }
  });

  // Clear categoryIds as well
  delete filters.categoryIds;

  // Викликаємо fetchData після скидання фільтрів
  fetchData(1);
};

// Open add product modal
const openAddProductModal = () => {
  selectedProduct.value = null; // Reset selected product for adding new
  isProductModalOpen.value = true;
};

// Create product
const createProduct = () => {
  // Navigate to product create page
  router.push('/admin/products/create');
};

// View product
const viewProduct = (product) => {
  // Navigate to product view page
  router.push(`/admin/products/${product.id}/view`);
};

// Edit product
const editProduct = (product) => {
  // Navigate to product edit page
  router.push(`/admin/products/${product.id}/edit`);
};

// Close product modal
const closeProductModal = () => {
  isProductModalOpen.value = false;
  selectedProduct.value = null;
};

// Save product (create or update)
const saveProduct = async (productData) => {
  try {
    if (productData.id) {
      await productsService.updateProduct(productData.id, productData);
    } else {
      await productsService.createProduct(productData);
    }
    closeProductModal();
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error saving product:', error);
  }
};

// Confirm delete product
const confirmDeleteProduct = (product) => {
  selectedProduct.value = product;
  isDeleteModalOpen.value = true;
};

// Delete product
const deleteProduct = async () => {
  if (!selectedProduct.value) return;

  try {
    await productsService.deleteProduct(selectedProduct.value.id);
    isDeleteModalOpen.value = false;
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error deleting product:', error);
  }
};

// Initialize component
onMounted(async () => {
  await loadCategories();
  // Fetch initial data
  fetchData(1);
});
</script>

<style scoped>
.admin-products {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
