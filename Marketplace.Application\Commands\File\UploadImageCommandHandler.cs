﻿using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.Services;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Marketplace.Application.Commands.File;

/// <summary>
/// Обробник команди для завантаження зображення для Category, Company
/// </summary>
public class UploadImageCommandHandler : IRequestHandler<UploadImageCommand, FileUploadResult>
{
    private readonly string _baseUrl;
    private readonly ICategoryRepository _categoryRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IFileService _fileService;
    private readonly ILogger<UploadImageCommandHandler> _logger;

    public UploadImageCommandHandler(
        IConfiguration configuration,
        ICategoryRepository categoryRepository,
        ICompanyRepository companyRepository,
        IFileService fileService,
        ILogger<UploadImageCommandHandler> logger)
    {
        _baseUrl = configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
        _categoryRepository = categoryRepository;
        _companyRepository = companyRepository;
        _fileService = fileService;
        _logger = logger;
    }

    public async Task<FileUploadResult> Handle(UploadImageCommand request, CancellationToken cancellationToken)
    {
        // Завантажуємо файл
        var file = await _fileService.SaveFileAsync(
            request.File.OpenReadStream(),
            request.File.FileName,
            request.File.ContentType,
            $"images/{request.EntityType.ToLower()}/{request.EntityId}",
            cancellationToken);

        var fileUrl = $"{_baseUrl}/uploads/{file}";
        // Оновлюємо  в залежності від типу сутності
        switch (request.EntityType.ToLower())
        {
            case "category":
                await UpdateCategoryImage(request.EntityId, fileUrl, cancellationToken);
                break;
            case "company":
                await UpdateCompanyImage(request.EntityId, fileUrl, cancellationToken);
                break;
            default:
                throw new InvalidOperationException($"Невідомий тип сутності: {request.EntityType}");
        }

        // Повертаємо результат
        return new FileUploadResult
        {
            FileUrl = fileUrl,
            FileName = request.File.FileName,
            ContentType = request.File.ContentType,
            Size = request.File.Length
        };
    }

    private async Task UpdateCategoryImage(Guid categoryId, string imageUrl, CancellationToken cancellationToken)
    {
        var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
        if (category == null)
            throw new InvalidOperationException($"Категорію з ID {categoryId} не знайдено.");

        // Оновлюємо 
        category.UpdateImage(imageUrl);

        // Зберігаємо зміни
        await _categoryRepository.UpdateAsync(category, cancellationToken);
    }

    private async Task UpdateCompanyImage(Guid companyId, string imageUrl, CancellationToken cancellationToken)
    {
        var company = await _companyRepository.GetByIdAsync(companyId, cancellationToken);
        if (company == null)
            throw new InvalidOperationException($"Компанію з ID {companyId} не знайдено.");

        // Оновлюємо 
        company.UpdateImage(imageUrl);

        // Зберігаємо зміни
        await _companyRepository.UpdateAsync(company, cancellationToken);
    }
}
