<template>
  <div class="order-detail">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Order Details</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/orders" class="button is-light">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Orders</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading order details...</p>
    </div>
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>
    <div v-else-if="!order || !order.id" class="notification is-warning">
      <p>Order not found.</p>
      <router-link to="/admin/orders" class="button is-primary mt-4">
        Back to Orders
      </router-link>
    </div>
    <div v-else>
      <!-- Order Header -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-8">
              <h2 class="order-title">Order #{{ order.id }}</h2>
              <p class="order-date">{{ formatDate(order.createdAt) }}</p>
            </div>
            <div class="column is-4 has-text-right">
              <div class="buttons is-right">
                <button
                  class="button is-primary"
                  @click="$router.push(`/admin/orders/${orderId}/edit`)">
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                  <span>Edit Order</span>
                </button>
                <button
                  class="button is-info"
                  @click="openStatusModal">
                  <span class="icon">
                    <i class="fas fa-cog"></i>
                  </span>
                  <span>Update Status</span>
                </button>
                <button 
                  class="button is-info" 
                  @click="printOrder">
                  <span class="icon">
                    <i class="fas fa-print"></i>
                  </span>
                  <span>Print</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="columns">
        <!-- Order Status and Customer Info -->
        <div class="column is-4">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Order Status</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Status</h3>
                <p class="info-value">
                  <status-badge 
                    :status="order.status" 
                    type="order" />
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Payment Status</h3>
                <p class="info-value">
                  <status-badge 
                    :status="order.paymentStatus" 
                    type="payment" />
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Payment Method</h3>
                <p class="info-value">{{ order.paymentMethod || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Shipping Method</h3>
                <p class="info-value">{{ order.shippingMethod || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Customer Information</p>
            </div>
            <div class="card-content">
              <div class="info-group">
                <h3 class="info-label">Customer</h3>
                <p class="info-value">{{ order.customerName || order.userName || 'N/A' }}</p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Email</h3>
                <p class="info-value">
                  <a :href="`mailto:${order.customerEmail || order.email}`">{{ order.customerEmail || order.email || 'N/A' }}</a>
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Phone</h3>
                <p class="info-value">
                  {{ order.shippingAddress?.phone || 'N/A' }}
                </p>
              </div>

              <div class="info-group">
                <h3 class="info-label">Customer Account</h3>
                <p class="info-value">
                  <router-link 
                    v-if="order.userId" 
                    :to="`/admin/users/${order.userId}`">
                    View Customer Profile
                  </router-link>
                  <span v-else>Guest Checkout</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Items and Totals -->
        <div class="column is-8">
          <div class="card">
            <div class="card-header">
              <p class="card-header-title">Order Items</p>
            </div>
            <div class="card-content">
              <div class="table-container">
                <table class="table is-fullwidth">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Price</th>
                      <th>Quantity</th>
                      <th class="has-text-right">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in order.items" :key="item.id">
                      <td>
                        <div class="product-info">
                          <figure class="image is-48x48 mr-2">
                            <img 
                              :src="item.image || 'https://via.placeholder.com/48'" 
                              :alt="item.productName"
                              @error="handleImageError">
                          </figure>
                          <div>
                            <p class="product-name">{{ item.productName }}</p>
                            <p class="product-id">ID: {{ item.productId }}</p>
                          </div>
                        </div>
                      </td>
                      <td>{{ formatCurrency(item.price) }}</td>
                      <td>{{ item.quantity }}</td>
                      <td class="has-text-right">{{ formatCurrency(item.total) }}</td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="3" class="has-text-right"><strong>Subtotal:</strong></td>
                      <td class="has-text-right">{{ formatCurrency(order.subtotal || order.totalPriceAmount || 0) }}</td>
                    </tr>
                    <tr v-if="order.discount">
                      <td colspan="3" class="has-text-right"><strong>Discount:</strong></td>
                      <td class="has-text-right">-{{ formatCurrency(order.discount) }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="has-text-right"><strong>Shipping:</strong></td>
                      <td class="has-text-right">{{ formatCurrency(order.shipping || 0) }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="has-text-right"><strong>Tax:</strong></td>
                      <td class="has-text-right">{{ formatCurrency(order.tax || 0) }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="has-text-right"><strong>Total:</strong></td>
                      <td class="has-text-right total-cell">{{ formatCurrency(order.total || order.totalPriceAmount || 0) }}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>

          <div class="columns mt-4">
            <div class="column is-6">
              <div class="card">
                <div class="card-header">
                  <p class="card-header-title">Shipping Address</p>
                </div>
                <div class="card-content">
                  <address class="address">
                    <p>{{ order.shippingAddress?.firstName }} {{ order.shippingAddress?.lastName }}</p>
                    <p>{{ order.shippingAddress?.address1 }}</p>
                    <p v-if="order.shippingAddress?.address2">{{ order.shippingAddress?.address2 }}</p>
                    <p>{{ order.shippingAddress?.city }}, {{ order.shippingAddress?.state }} {{ order.shippingAddress?.postalCode }}</p>
                    <p>{{ order.shippingAddress?.country }}</p>
                    <p v-if="order.shippingAddress?.phone">Phone: {{ order.shippingAddress?.phone }}</p>
                  </address>
                </div>
              </div>
            </div>
            <div class="column is-6">
              <div class="card">
                <div class="card-header">
                  <p class="card-header-title">Billing Address</p>
                </div>
                <div class="card-content">
                  <address class="address">
                    <p>{{ order.billingAddress?.firstName }} {{ order.billingAddress?.lastName }}</p>
                    <p>{{ order.billingAddress?.address1 }}</p>
                    <p v-if="order.billingAddress?.address2">{{ order.billingAddress?.address2 }}</p>
                    <p>{{ order.billingAddress?.city }}, {{ order.billingAddress?.state }} {{ order.billingAddress?.postalCode }}</p>
                    <p>{{ order.billingAddress?.country }}</p>
                    <p v-if="order.billingAddress?.phone">Phone: {{ order.billingAddress?.phone }}</p>
                  </address>
                </div>
              </div>
            </div>
          </div>

          <!-- Order Notes -->
          <div class="card mt-4">
            <div class="card-header">
              <p class="card-header-title">Order Notes</p>
            </div>
            <div class="card-content">
              <div v-if="loadingNotes" class="has-text-centered py-4">
                <span class="icon">
                  <i class="fas fa-spinner fa-pulse"></i>
                </span>
                <p class="mt-2">Loading notes...</p>
              </div>
              <div v-else-if="!orderNotes.length" class="has-text-centered py-4">
                <p>No notes for this order</p>
              </div>
              <div v-else class="notes-list">
                <div v-for="note in orderNotes" :key="note.id" class="note-item">
                  <div class="note-header">
                    <span class="note-author">{{ note.createdBy }}</span>
                    <span class="note-date">{{ formatDate(note.createdAt) }}</span>
                  </div>
                  <div class="note-content">
                    {{ note.content }}
                  </div>
                </div>
              </div>
              
              <div class="add-note mt-4">
                <div class="field">
                  <label class="label">Add Note</label>
                  <div class="control">
                    <textarea 
                      class="textarea" 
                      v-model="newNote" 
                      placeholder="Add a note about this order">
                    </textarea>
                  </div>
                </div>
                <div class="field">
                  <div class="control">
                    <button 
                      class="button is-primary" 
                      @click="addNote"
                      :class="{ 'is-loading': addingNote }"
                      :disabled="!newNote.trim()">
                      Add Note
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal" :class="{ 'is-active': showStatusModal }">
      <div class="modal-background" @click="closeStatusModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Update Order Status</p>
          <button class="delete" aria-label="close" @click="closeStatusModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="content">
            <p><strong>Order ID:</strong> {{ order.id }}</p>
            <p><strong>Current Status:</strong> 
              <status-badge 
                :status="order.status" 
                type="order" />
            </p>
            
            <div class="field">
              <label class="label">New Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newStatus">
                    <option :value="0">Processing</option>
                    <option :value="1">Pending</option>
                    <option :value="2">Paid</option>
                    <option :value="3">Shipped</option>
                    <option :value="4">Delivered</option>
                    <option :value="5">Cancelled</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label">Payment Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newPaymentStatus">
                    <option :value="0">Pending</option>
                    <option :value="1">Completed</option>
                    <option :value="2">Failed</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Note (Optional)</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  v-model="statusNote" 
                  placeholder="Add a note about this status change">
                </textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-primary" 
            @click="updateOrderStatus"
            :class="{ 'is-loading': updatingStatus }">
            Update Status
          </button>
          <button class="button" @click="closeStatusModal">Cancel</button>
        </footer>
      </div>
    </div>

    <!-- Refund Modal -->
    <div class="modal" :class="{ 'is-active': showRefundModal }">
      <div class="modal-background" @click="closeRefundModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Process Refund</p>
          <button class="delete" aria-label="close" @click="closeRefundModal"></button>
        </header>
        <section class="modal-card-body">
          <div class="content">
            <p><strong>Order ID:</strong> {{ order.id }}</p>
            <p><strong>Order Total:</strong> {{ formatCurrency(order.total || order.totalPriceAmount || 0) }}</p>

            <div class="field">
              <label class="label">Refund Amount</label>
              <div class="control has-icons-left">
                <input
                  class="input"
                  type="number"
                  v-model="refundAmount"
                  min="0"
                  :max="order.total || order.totalPriceAmount || 0"
                  step="0.01">
                <span class="icon is-small is-left">
                  <i class="fas fa-hryvnia"></i>
                </span>
              </div>
              <p class="help">
                <a href="#" @click.prevent="refundAmount = order.total || order.totalPriceAmount || 0">Refund full amount</a>
              </p>
            </div>
            
            <div class="field">
              <label class="label">Reason for Refund</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  v-model="refundReason" 
                  placeholder="Enter reason for refund">
                </textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-danger" 
            @click="processRefund"
            :class="{ 'is-loading': processingRefund }"
            :disabled="!refundAmount || !refundReason.trim()">
            Process Refund
          </button>
          <button class="button" @click="closeRefundModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ordersService } from '@/admin/services/orders';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const loadingNotes = ref(false);
const error = ref(null);
const order = ref({});
const orderNotes = ref([]);
const newNote = ref('');
const addingNote = ref(false);

// Status modal state
const showStatusModal = ref(false);
const newStatus = ref('');
const newPaymentStatus = ref('');
const statusNote = ref('');
const updatingStatus = ref(false);

// Refund modal state
const showRefundModal = ref(false);
const refundAmount = ref(0);
const refundReason = ref('');
const processingRefund = ref(false);

// Computed properties
const orderId = computed(() => route.params.id);

// Fetch order data
const fetchOrder = async () => {
  loading.value = true;
  error.value = null;

  try {
    console.log('Fetching order with ID:', orderId.value);
    const data = await ordersService.getOrderById(orderId.value);
    console.log('Received order data:', data);

    if (data === null) {
      // Order not found
      order.value = null;
      error.value = 'Order not found.';
    } else {
      order.value = data;

      // Set initial values for status modal
      newStatus.value = data.status;
      newPaymentStatus.value = data.paymentStatus;
    }
  } catch (err) {
    console.error('Error fetching order:', err);
    error.value = 'Failed to load order data. Please try again.';
    order.value = null;
  } finally {
    loading.value = false;
  }
};

// Fetch order notes
const fetchOrderNotes = async () => {
  loadingNotes.value = true;
  
  try {
    const notes = await ordersService.getOrderNotes(orderId.value);
    orderNotes.value = notes;
  } catch (err) {
    console.error('Error fetching order notes:', err);
  } finally {
    loadingNotes.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/48?text=No+Image';
};

// Add note
const addNote = async () => {
  if (!newNote.trim()) return;
  
  addingNote.value = true;
  
  try {
    const response = await ordersService.addOrderNote(orderId.value, newNote);
    
    if (response.note) {
      orderNotes.value.unshift(response.note);
      newNote.value = '';
    }
  } catch (err) {
    console.error('Error adding note:', err);
  } finally {
    addingNote.value = false;
  }
};

// Open status modal
const openStatusModal = () => {
  newStatus.value = order.value.status;
  newPaymentStatus.value = order.value.paymentStatus;
  statusNote.value = '';
  showStatusModal.value = true;
};

// Close status modal
const closeStatusModal = () => {
  showStatusModal.value = false;
};

// Update order status
const updateOrderStatus = async () => {
  updatingStatus.value = true;
  
  try {
    // Update order status
    if (newStatus.value !== order.value.status) {
      await ordersService.updateOrderStatus(orderId.value, newStatus.value);
      order.value.status = newStatus.value;
    }
    
    // Update payment status
    if (newPaymentStatus.value !== order.value.paymentStatus) {
      await ordersService.updatePaymentStatus(orderId.value, newPaymentStatus.value);
      order.value.paymentStatus = newPaymentStatus.value;
    }
    
    // Add note if provided
    if (statusNote.value.trim()) {
      const response = await ordersService.addOrderNote(orderId.value, statusNote.value);
      if (response.note) {
        orderNotes.value.unshift(response.note);
      }
    }
    
    // Close modal
    closeStatusModal();
  } catch (err) {
    console.error('Error updating order status:', err);
  } finally {
    updatingStatus.value = false;
  }
};

// Open refund modal
const openRefundModal = () => {
  refundAmount.value = order.value.total || order.value.totalPriceAmount || 0;
  refundReason.value = '';
  showRefundModal.value = true;
};

// Close refund modal
const closeRefundModal = () => {
  showRefundModal.value = false;
};

// Process refund
const processRefund = async () => {
  if (!refundAmount.value || !refundReason.value.trim()) return;
  
  processingRefund.value = true;
  
  try {
    await ordersService.refundOrder(orderId.value, refundAmount.value, refundReason.value);
    
    // Update order status and payment status
    order.value.status = 'refunded';
    order.value.paymentStatus = 'refunded';
    
    // Add note about refund
    const response = await ordersService.addOrderNote(
      orderId.value, 
      `Refund processed: ${formatCurrency(refundAmount.value)}. Reason: ${refundReason.value}`
    );
    
    if (response.note) {
      orderNotes.value.unshift(response.note);
    }
    
    // Close modal
    closeRefundModal();
  } catch (err) {
    console.error('Error processing refund:', err);
  } finally {
    processingRefund.value = false;
  }
};

// Print order
const printOrder = () => {
  window.print();
};

// Lifecycle hooks
onMounted(() => {
  fetchOrder();
  fetchOrderNotes();
});
</script>

<style scoped>
.order-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.order-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.order-date {
  font-size: 0.9rem;
  color: #7a7a7a;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-name {
  font-weight: 500;
}

.product-id {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.total-cell {
  font-weight: 700;
  font-size: 1.1rem;
}

.address {
  font-style: normal;
  line-height: 1.5;
}

.notes-list {
  max-height: 300px;
  overflow-y: auto;
}

.note-item {
  padding: 1rem;
  border-bottom: 1px solid #f1f1f1;
}

.note-item:last-child {
  border-bottom: none;
}

.note-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.note-author {
  font-weight: 600;
}

.note-date {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.note-content {
  white-space: pre-line;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

@media print {
  .level-right,
  .add-note,
  .modal {
    display: none !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
</style>
