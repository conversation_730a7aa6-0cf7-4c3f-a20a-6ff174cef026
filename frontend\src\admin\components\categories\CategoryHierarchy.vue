<template>
  <div class="category-hierarchy">
    <div v-for="category in categories" :key="category.id" class="hierarchy-item root">
      <CategoryHierarchyItem
        :category="category"
        :level="0"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @add-child="$emit('add-child', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import CategoryHierarchyItem from './CategoryHierarchyItem.vue';

defineProps({
  categories: {
    type: Array,
    default: () => []
  }
});

defineEmits(['edit', 'delete', 'add-child']);
</script>

<style scoped>
.category-hierarchy {
  padding: 1rem 0;
}

.hierarchy-item.root {
  margin-bottom: 1rem;
}
</style>
</template>
