import{c as b}from"./companies-Cx9IcxUK.js";import{u as w,S as B}from"./useAdminSearch-DD4t1sR1.js";import{P as N}from"./Pagination-BX7rerWh.js";import{_ as D,g as L,i as E,c as i,a as e,b as m,D as t,d as R,t as r,s as V,k as f,n as z,F as T,p as j,w as y,r as I,o as n}from"./index-BtyG65bR.js";const U={class:"company-list"},$={key:0,class:"has-text-centered py-6"},M={key:1,class:"notification is-danger"},q={key:2,class:"has-text-centered py-6"},G={key:3,class:"card"},H={class:"card-content"},J={class:"table is-fullwidth is-hoverable"},K={class:"media"},Q={key:0,class:"media-left"},W={class:"image is-48x48"},X=["src","alt"],Y={class:"media-content"},Z={class:"title is-6"},ee={class:"subtitle is-7"},se={class:"has-text-grey is-size-7"},te={class:"has-text-grey is-size-7"},ae={key:0,class:"tag is-info"},le={key:1,class:"tag"},ne={class:"buttons"},ie=["onClick","disabled"],oe={key:0,class:"table-loading-overlay"},re={__name:"CompanyList",setup(de){const u=L(!1),C=[{key:"featured",label:"Featured",type:"select",columnClass:"is-3",allOption:"All Companies",options:[{value:"true",label:"Featured Only"},{value:"false",label:"Regular Only"}]},{key:"sortBy",label:"Sort By",type:"select",columnClass:"is-3",allOption:!1,options:[{value:"ApprovedAt",label:"Approved Date"},{value:"Name",label:"Name"}]},{key:"sortOrder",label:"Order",type:"select",columnClass:"is-3",options:[{value:"desc",label:"Descending"},{value:"asc",label:"Ascending"}]}],{items:g,loading:d,error:c,isFirstLoad:p,currentPage:k,totalPages:h,totalItems:F,filters:o,fetchData:_,handlePageChange:A}=w({fetchFunction:b.getCompanies,defaultFilters:{featured:"",sortBy:"ApprovedAt",sortOrder:"desc"},debounceTime:300,defaultPageSize:15,clientSideSearch:!1}),S=l=>{o.search=l},x=(l,s)=>{o[l]=s},O=()=>{Object.keys(o).forEach(l=>{l==="search"?o[l]="":l==="sortBy"?o[l]="ApprovedAt":l==="sortOrder"?o[l]="desc":o[l]=""})},P=async l=>{if(confirm("Are you sure you want to delete this company? This action cannot be undone.")){u.value=!0;try{await b.deleteCompany(l),await _()}catch(s){c.value=s.message||"Failed to delete company"}finally{u.value=!1}}};return E(()=>{_()}),(l,s)=>{const v=I("router-link");return n(),i("div",U,[s[8]||(s[8]=e("div",{class:"level"},[e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Companies")])])],-1)),m(B,{filters:t(o),"filter-fields":C,"search-label":"Search Companies","search-placeholder":"Search by name or description...","search-column-class":"is-4","total-items":t(F),"item-name":"companies",loading:t(d),onSearchChanged:S,onFilterChanged:x,onResetFilters:O},null,8,["filters","total-items","loading"]),t(d)&&t(p)?(n(),i("div",$,s[1]||(s[1]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading companies...",-1)]))):t(c)?(n(),i("div",M,[e("button",{class:"delete",onClick:s[0]||(s[0]=a=>c.value=null)}),R(" "+r(t(c)),1)])):!t(d)&&!t(g).length?(n(),i("div",q,s[2]||(s[2]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-building fa-2x"})],-1),e("p",{class:"mt-2"},"No companies found",-1),e("p",{class:"mt-2"},"Try adjusting your search criteria or filters",-1)]))):(n(),i("div",G,[e("div",H,[e("div",{class:z(["table-container",{"is-loading":t(d)&&!t(p)}])},[e("table",J,[s[6]||(s[6]=e("thead",null,[e("tr",null,[e("th",null,"Name"),e("th",null,"Contact"),e("th",null,"Location"),e("th",null,"Featured"),e("th",null,"Actions")])],-1)),e("tbody",null,[(n(!0),i(T,null,j(t(g),a=>(n(),i("tr",{key:a.id},[e("td",null,[e("div",K,[a.imageUrl?(n(),i("div",Q,[e("figure",W,[e("img",{src:a.imageUrl,alt:a.name,class:"is-rounded"},null,8,X)])])):f("",!0),e("div",Y,[e("p",Z,r(a.name),1),e("p",ee,r(a.slug),1)])])]),e("td",null,[e("p",null,r(a.contactEmail),1),e("p",se,r(a.contactPhone),1)]),e("td",null,[e("p",null,r(a.addressCity)+", "+r(a.addressRegion),1),e("p",te,r(a.addressStreet),1)]),e("td",null,[a.isFeatured?(n(),i("span",ae,"Featured")):(n(),i("span",le,"Regular"))]),e("td",null,[e("div",ne,[m(v,{to:{name:"AdminCompanyDetail",params:{id:a.id}},class:"button is-small is-info"},{default:y(()=>s[3]||(s[3]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),m(v,{to:{name:"AdminCompanyEdit",params:{id:a.id}},class:"button is-small is-warning"},{default:y(()=>s[4]||(s[4]=[e("span",{class:"icon"},[e("i",{class:"fas fa-edit"})],-1),e("span",null,"Edit",-1)])),_:2},1032,["to"]),e("button",{class:"button is-small is-danger",onClick:ce=>P(a.id),disabled:u.value},s[5]||(s[5]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Delete",-1)]),8,ie)])])]))),128))])]),t(d)&&!t(p)?(n(),i("div",oe,s[7]||(s[7]=[e("div",{class:"loading-spinner"},[e("i",{class:"fas fa-spinner fa-pulse"})],-1)]))):f("",!0)],2),t(h)>1?(n(),V(N,{key:0,"current-page":t(k),"total-pages":t(h),onPageChanged:t(A)},null,8,["current-page","total-pages","onPageChanged"])):f("",!0)])]))])}}},ge=D(re,[["__scopeId","data-v-9eb77bec"]]);export{ge as default};
