<template>
  <div v-if="isMounted" class="product-image-manager">
    <h2 class="title is-5 mb-4">🖼️ Product Images</h2>

    <!-- Upload Section -->
    <div class="upload-section mb-5">
      <div class="field">
        <label class="label">Upload Images</label>
        <div class="control">
          <!-- Drag and Drop Area -->
          <div 
            class="drop-zone"
            :class="{ 'is-dragover': isDragOver, 'is-disabled': uploading }"
            @drop="handleDrop"
            @dragover.prevent="handleDragOver"
            @dragleave="handleDragLeave"
            @click="triggerFileInput">
            
            <input 
              ref="fileInput"
              type="file" 
              accept="image/*,.jfif" 
              multiple
              @change="handleFileSelect"
              :disabled="uploading"
              style="display: none;">
            
            <div class="drop-zone-content">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-cloud-upload-alt fa-3x" v-if="!uploading"></i>
                <i class="fas fa-spinner fa-spin fa-3x" v-else></i>
              </span>
              <p class="has-text-grey mt-3">
                <strong v-if="!uploading">Drop images here or click to browse</strong>
                <strong v-else>Uploading images...</strong>
              </p>
              <p class="has-text-grey-light is-size-7">
                Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each)
              </p>
            </div>
          </div>
        </div>
        <p class="help" v-if="selectedFiles.length > 0">
          {{ selectedFiles.length }} file(s) selected
        </p>
      </div>

      <!-- Upload Progress -->
      <div v-if="uploading" class="notification is-info">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <span class="icon-text">
                <span class="icon">
                  <i class="fas fa-spinner fa-spin"></i>
                </span>
                <span>Uploading {{ uploadProgress.current }} of {{ uploadProgress.total }} images...</span>
              </span>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <progress 
                class="progress is-info" 
                :value="uploadProgress.current" 
                :max="uploadProgress.total">
                {{ Math.round((uploadProgress.current / uploadProgress.total) * 100) }}%
              </progress>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Current Images -->
    <div v-if="images.length > 0" class="current-images mb-5">
      <label class="label">Current Images ({{ images.length }})</label>
      <div class="columns is-multiline">
        <div v-for="image in images" :key="image.id" class="column is-6-tablet is-4-desktop">
          <div class="card image-card">
            <div class="card-image">
              <figure class="image is-4by3">
                <img 
                  :src="image.imageUrl || image.url" 
                  :alt="image.altText || 'Product image'"
                  @error="handleImageError"
                  @click="openImageModal(image)">
              </figure>
              
              <!-- Image Overlay -->
              <div class="image-overlay">
                <div class="image-actions">
                  <button 
                    v-if="!image.isMain" 
                    class="button is-small is-info"
                    @click="setAsMain(image.id)"
                    title="Set as main image">
                    <span class="icon is-small">
                      <i class="fas fa-star"></i>
                    </span>
                  </button>
                  
                  <button 
                    class="button is-small is-primary"
                    @click="openImageModal(image)"
                    title="View full size">
                    <span class="icon is-small">
                      <i class="fas fa-eye"></i>
                    </span>
                  </button>
                  
                  <button 
                    class="button is-small is-danger"
                    @click="confirmDelete(image)"
                    title="Delete image">
                    <span class="icon is-small">
                      <i class="fas fa-trash"></i>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="card-content p-3">
              <div class="level is-mobile">
                <div class="level-left">
                  <div class="level-item">
                    <span 
                      class="tag is-small" 
                      :class="image.isMain ? 'is-primary' : 'is-light'">
                      {{ image.isMain ? 'Main' : `#${image.order || 0}` }}
                    </span>
                  </div>
                </div>
                <div class="level-right">
                  <div class="level-item">
                    <span class="tag is-small is-info" v-if="image.createdAt">
                      {{ formatDate(image.createdAt) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Images -->
    <div v-if="pendingImages.length > 0" class="pending-images mb-5">
      <label class="label">Images to Upload ({{ pendingImages.length }})</label>
      <div class="notification is-warning is-light">
        <span class="icon-text">
          <span class="icon">
            <i class="fas fa-info-circle"></i>
          </span>
          <span>These images will be uploaded when you save the product.</span>
        </span>
      </div>
      
      <div class="columns is-multiline">
        <div v-for="image in pendingImages" :key="image.id" class="column is-6-tablet is-4-desktop">
          <div class="card image-card">
            <div class="card-image">
              <figure class="image is-4by3">
                <img :src="image.preview" :alt="image.name">
              </figure>
            </div>
            
            <div class="card-content p-3">
              <div class="level is-mobile">
                <div class="level-left">
                  <div class="level-item">
                    <span 
                      class="tag is-small" 
                      :class="image.isMain ? 'is-warning' : 'is-light'">
                      {{ image.isMain ? 'Will be Main' : 'Pending' }}
                    </span>
                  </div>
                </div>
                <div class="level-right">
                  <div class="level-item">
                    <div class="buttons are-small">
                      <button 
                        v-if="!image.isMain" 
                        class="button is-warning is-small"
                        @click="setPendingAsMain(image.id)"
                        title="Set as main">
                        <span class="icon">
                          <i class="fas fa-star"></i>
                        </span>
                      </button>
                      <button 
                        class="button is-danger is-small"
                        @click="removePending(image.id)"
                        title="Remove">
                        <span class="icon">
                          <i class="fas fa-times"></i>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="content is-small">
                <p><strong>{{ image.name }}</strong></p>
                <p>{{ (image.size / 1024 / 1024).toFixed(2) }} MB</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Images State -->
    <div v-if="images.length === 0 && pendingImages.length === 0" 
         class="no-images has-background-light has-text-centered p-6 is-rounded">
      <span class="icon is-large has-text-grey">
        <i class="fas fa-images fa-3x"></i>
      </span>
      <p class="has-text-grey mt-2">No images uploaded yet</p>
      <p class="has-text-grey-light is-size-7">Use the upload area above to add product images</p>
    </div>

    <!-- Image Modal -->
    <div class="modal" :class="{ 'is-active': showImageModal }">
      <div class="modal-background" @click="closeImageModal"></div>
      <div class="modal-content">
        <p class="image">
          <img :src="selectedImage?.imageUrl || selectedImage?.url" v-if="selectedImage">
        </p>
      </div>
      <button class="modal-close is-large" @click="closeImageModal"></button>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" :class="{ 'is-active': showDeleteModal }">
      <div class="modal-background" @click="closeDeleteModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Confirm Delete</p>
          <button class="delete" @click="closeDeleteModal"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to delete this image?</p>
          <p class="has-text-danger is-size-7 mt-2">This action cannot be undone.</p>
        </section>
        <footer class="modal-card-foot">
          <button class="button is-danger" @click="deleteImage">Delete</button>
          <button class="button" @click="closeDeleteModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import api from '@/services/api';

// Props
const props = defineProps({
  productId: {
    type: String,
    default: null
  },
  images: {
    type: Array,
    default: () => []
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'images-updated',
  'main-image-changed',
  'image-uploaded',
  'image-deleted',
  'pending-images-changed'
]);

// Reactive data
const isMounted = ref(false);
const fileInput = ref(null);
const isDragOver = ref(false);
const uploading = ref(false);
const selectedFiles = ref([]);
const pendingImages = ref([]);
const showImageModal = ref(false);
const showDeleteModal = ref(false);
const selectedImage = ref(null);
const imageToDelete = ref(null);

const uploadProgress = ref({
  current: 0,
  total: 0
});

// Constants
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/jfif'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Methods
const triggerFileInput = () => {
  if (!uploading.value && fileInput.value) {
    fileInput.value.click();
  }
};

const handleFileSelect = (event) => {
  if (!event.target || !event.target.files) return;

  const files = Array.from(event.target.files);
  processFiles(files);
  event.target.value = ''; // Clear input
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  if (uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files);
  processFiles(files);
};

const handleDragOver = (event) => {
  event.preventDefault();
  if (!uploading.value) {
    isDragOver.value = true;
  }
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const processFiles = (files) => {
  if (files.length === 0) return;

  // Validate files
  const validFiles = [];
  const errors = [];

  files.forEach(file => {
    // Check file type
    if (!ALLOWED_TYPES.includes(file.type.toLowerCase())) {
      errors.push(`${file.name}: Unsupported file type`);
      return;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      errors.push(`${file.name}: File too large (max 5MB)`);
      return;
    }

    validFiles.push(file);
  });

  if (errors.length > 0) {
    alert('Some files were rejected:\n' + errors.join('\n'));
  }

  if (validFiles.length === 0) return;

  selectedFiles.value = validFiles;

  // Create previews for valid files
  validFiles.forEach((file, index) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      const imagePreview = {
        id: `temp-${Date.now()}-${Math.random()}-${index}`,
        file: file,
        name: file.name,
        preview: e.target.result,
        size: file.size,
        isTemp: true,
        isMain: pendingImages.value.length === 0 && props.images.length === 0
      };

      pendingImages.value.push(imagePreview);

      // Використовуємо nextTick для забезпечення правильного рендерингу
      await nextTick();
      emit('pending-images-changed', pendingImages.value);
    };
    reader.readAsDataURL(file);
  });
};

const setAsMain = async (imageId) => {
  if (!props.productId) return;

  try {
    const response = await api.patch(`/api/admin/products/${props.productId}/images/${imageId}/main`);

    if (response.data && response.data.success) {
      emit('main-image-changed', imageId);
      emit('images-updated');
    }
  } catch (err) {
    console.error('Error setting main image:', err);
    alert('Failed to set main image. Please try again.');
  }
};

const setPendingAsMain = (imageId) => {
  pendingImages.value.forEach(img => {
    img.isMain = img.id === imageId;
  });
  emit('pending-images-changed', pendingImages.value);
};

const removePending = (imageId) => {
  const index = pendingImages.value.findIndex(img => img.id === imageId);
  if (index !== -1) {
    pendingImages.value.splice(index, 1);
    emit('pending-images-changed', pendingImages.value);
  }
};

const openImageModal = (image) => {
  selectedImage.value = image;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  selectedImage.value = null;
};

const confirmDelete = (image) => {
  imageToDelete.value = image;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  imageToDelete.value = null;
};

const deleteImage = async () => {
  if (!imageToDelete.value) return;

  try {
    await api.delete(`/api/admin/products/${props.productId}/images/${imageToDelete.value.id}`);
    emit('image-deleted', imageToDelete.value.id);
    emit('images-updated');
    closeDeleteModal();
  } catch (err) {
    console.error('Error deleting image:', err);
    alert('Failed to delete image. Please try again.');
  }
};

const handleImageError = (event) => {
  if (event.target.dataset.errorHandled) return;

  event.target.dataset.errorHandled = 'true';
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNMTAwIDExMEMxMTQuMTQyIDExMCAxMjYgOTguMTQyMSAxMjYgODRDMTI2IDY5Ljg1NzkgMTE0LjE0MiA1OCAxMDAgNThDODUuODU3OSA1OCA3NCA2OS44NTc5IDc0IDg0Qzc0IDk4LjE0MjEgODUuODU3OSAxMTAgMTAwIDExMFoiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik04NiA3NEg5MFY3OEg4NlY3NFoiIGZpbGw9IiNEMUQxRDEiLz4KPHA+CjwvcGF0aD4KPC9zdmc+Cg==';
  event.target.classList.add('image-error');
};

const formatDate = (dateString) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      month: 'short',
      day: 'numeric'
    }).format(date);
  } catch (e) {
    return '';
  }
};

// Upload pending images (called from parent)
const uploadPendingImages = async (productId) => {
  if (pendingImages.value.length === 0) return;

  try {
    uploading.value = true;
    uploadProgress.value = {
      current: 0,
      total: pendingImages.value.length
    };

    for (const pendingImage of pendingImages.value) {
      const formData = new FormData();
      formData.append('image', pendingImage.file);

      try {
        const response = await api.post(`/api/admin/products/${productId}/images/single`, formData);

        if (pendingImage.isMain && response.data && response.data.success && response.data.data) {
          const uploadedImageId = response.data.data;
          await setAsMain(uploadedImageId);
        }

        uploadProgress.value.current++;
        emit('image-uploaded', response.data);
      } catch (err) {
        console.error(`Failed to upload image: ${pendingImage.name}`, err);
        throw new Error(`Failed to upload ${pendingImage.name}: ${err.message}`);
      }
    }

    // Clear pending images
    pendingImages.value = [];
    emit('pending-images-changed', []);
    emit('images-updated');

  } catch (err) {
    console.error('Error uploading pending images:', err);
    throw err;
  } finally {
    uploading.value = false;
    uploadProgress.value = { current: 0, total: 0 };
  }
};

// Expose methods to parent
defineExpose({
  uploadPendingImages,
  getPendingImages: () => pendingImages.value,
  clearPendingImages: () => {
    pendingImages.value = [];
    emit('pending-images-changed', []);
  }
});

// Lifecycle
onMounted(() => {
  isMounted.value = true;
});
</script>

<style scoped>
.product-image-manager {
  width: 100%;
}

.drop-zone {
  border: 2px dashed #dbdbdb;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.drop-zone:hover:not(.is-disabled) {
  border-color: #3273dc;
  background-color: #f0f8ff;
}

.drop-zone.is-dragover {
  border-color: #3273dc;
  background-color: #e8f4fd;
  transform: scale(1.02);
}

.drop-zone.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.drop-zone-content {
  pointer-events: none;
}

.image-card {
  position: relative;
  transition: transform 0.2s ease;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-card .card-image {
  position: relative;
  overflow: hidden;
}

.image-card .card-image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.image-card .card-image img:hover {
  transform: scale(1.05);
}

.image-card .card-image img.image-error {
  opacity: 0.6;
  filter: grayscale(100%);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 0.5rem;
}

.no-images {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress {
  width: 200px;
}

/* Modal improvements */
.modal-content .image img {
  max-height: 80vh;
  width: auto;
  border-radius: 8px;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .drop-zone {
    padding: 1rem;
  }

  .image-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .image-actions .button {
    width: 100%;
  }
}
</style>
