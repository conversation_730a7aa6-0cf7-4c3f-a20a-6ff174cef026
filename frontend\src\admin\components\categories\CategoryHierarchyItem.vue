<template>
  <div class="hierarchy-item" :class="{ 'has-children': hasChildren }">
    <!-- Category Card -->
    <div class="card mb-3" :style="{ marginLeft: `${level * 2}rem` }">
      <div class="card-content py-3">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item">
              <div class="media">
                <div class="media-left" v-if="category.image">
                  <figure class="image is-48x48">
                    <img :src="category.image" :alt="category.name" class="is-rounded">
                  </figure>
                </div>
                <div class="media-left" v-else>
                  <span class="icon is-large has-text-grey-light">
                    <i class="fas fa-folder fa-2x"></i>
                  </span>
                </div>
                <div class="media-content">
                  <div class="content">
                    <div class="level is-mobile">
                      <div class="level-left">
                        <div class="level-item">
                          <div>
                            <strong class="title is-6">{{ category.name }}</strong>
                            <br>
                            <small class="has-text-grey">{{ category.slug }}</small>
                            <br>
                            <span class="tag is-small" :class="getProductCountClass(category.productCount)">
                              <span class="icon is-small">
                                <i class="fas fa-cube"></i>
                              </span>
                              <span>{{ category.productCount || 0 }} products</span>
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="level-right">
                        <div class="level-item">
                          <div class="tags">
                            <span v-if="level === 0" class="tag is-primary is-small">Root</span>
                            <span v-else class="tag is-info is-small">Level {{ level + 1 }}</span>
                            <span v-if="hasChildren" class="tag is-success is-small">
                              {{ category.children.length }} children
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p v-if="category.description" class="is-size-7 has-text-grey mt-2">
                      {{ truncateText(category.description, 100) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <div class="buttons are-small">
                <button 
                  class="button is-primary is-small" 
                  @click="$emit('edit', category)"
                  title="Edit category">
                  <span class="icon is-small">
                    <i class="fas fa-edit"></i>
                  </span>
                </button>
                <button 
                  class="button is-success is-small" 
                  @click="$emit('add-child', category)"
                  title="Add subcategory">
                  <span class="icon is-small">
                    <i class="fas fa-plus"></i>
                  </span>
                </button>
                <button 
                  class="button is-danger is-small" 
                  @click="$emit('delete', category)"
                  title="Delete category">
                  <span class="icon is-small">
                    <i class="fas fa-trash"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Children -->
    <div v-if="hasChildren && expanded" class="children">
      <CategoryHierarchyItem
        v-for="child in category.children"
        :key="child.id"
        :category="child"
        :level="level + 1"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
        @add-child="$emit('add-child', $event)"
      />
    </div>

    <!-- Expand/Collapse Button -->
    <div v-if="hasChildren" class="has-text-centered mb-3">
      <button 
        class="button is-small is-light" 
        @click="toggleExpanded"
        :style="{ marginLeft: `${level * 2}rem` }">
        <span class="icon is-small">
          <i class="fas" :class="expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
        </span>
        <span>{{ expanded ? 'Collapse' : 'Expand' }} ({{ category.children.length }})</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
});

defineEmits(['edit', 'delete', 'add-child']);

const expanded = ref(props.level < 2); // Auto-expand first 2 levels

const hasChildren = computed(() => {
  return props.category.children && props.category.children.length > 0;
});

const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

const getProductCountClass = (count) => {
  if (!count || count === 0) return 'is-light';
  if (count < 5) return 'is-warning';
  if (count < 20) return 'is-info';
  return 'is-success';
};

const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};
</script>

<style scoped>
.hierarchy-item {
  position: relative;
}

.hierarchy-item.has-children::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3273dc, #23d160);
  opacity: 0.3;
}

.children {
  position: relative;
}

.children::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 0;
  bottom: 2rem;
  width: 1px;
  background: #dbdbdb;
}

.card {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-left-color: #3273dc;
}

.media-left .icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f5f5f5;
  border-radius: 50%;
}

.buttons {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.card:hover .buttons {
  opacity: 1;
}
</style>
</template>
