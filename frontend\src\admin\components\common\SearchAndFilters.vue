<template>
  <div class="search-and-filters">
    <!-- Search Bar -->
    <div class="card mb-4">
      <div class="card-content">
        <div class="columns is-multiline">
          <!-- Search Input -->
          <div class="column" :class="searchColumnClass">
            <div class="field">
              <label class="label">{{ searchLabel }}</label>
              <div class="control has-icons-left">
                <input
                  class="input"
                  type="text"
                  :placeholder="searchPlaceholder"
                  v-model="localFilters.search"
                  @input="handleSearchInput"
                />
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>

          <!-- Dynamic Filter Fields -->
          <div 
            v-for="filter in filterFields" 
            :key="filter.key"
            class="column" 
            :class="filter.columnClass || 'is-3'"
          >
            <div class="field">
              <label class="label">{{ filter.label }}</label>
              <div class="control">
                <!-- Select Filter -->
                <div v-if="filter.type === 'select'" class="select is-fullwidth">
                  <select
                    v-model="localFilters[filter.key]"
                    @change="handleFilterChange(filter.key, localFilters[filter.key])"
                  >
                    <option v-if="filter.allOption !== false" value="">{{ filter.allOption || `All ${filter.label}` }}</option>
                    <option 
                      v-for="option in filter.options" 
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </option>
                  </select>
                </div>

                <!-- Text Input Filter -->
                <input
                  v-else-if="filter.type === 'text'"
                  class="input"
                  type="text"
                  :placeholder="filter.placeholder"
                  v-model="localFilters[filter.key]"
                  @input="handleFilterChange(filter.key, localFilters[filter.key])"
                />

                <!-- Date Input Filter -->
                <input
                  v-else-if="filter.type === 'date'"
                  class="input"
                  type="date"
                  v-model="localFilters[filter.key]"
                  @change="handleFilterChange(filter.key, localFilters[filter.key])"
                />

                <!-- Number Input Filter -->
                <input
                  v-else-if="filter.type === 'number'"
                  class="input"
                  type="number"
                  :placeholder="filter.placeholder"
                  v-model="localFilters[filter.key]"
                  @input="handleFilterChange(filter.key, localFilters[filter.key])"
                />
              </div>
            </div>
          </div>

          <!-- Reset Button -->
          <div class="column is-2" v-if="showResetButton">
            <div class="field" style="margin-top: 1.9rem;">
              <div class="buttons is-right">
                <button
                  class="button is-light"
                  @click="resetFilters"
                  :class="{ 'is-loading': loading }"
                >
                  {{ resetButtonText }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Status Bar -->
    <div class="level mb-4" v-if="showStatusBar && (totalItems > 0 || hasActiveFilters)">
      <div class="level-left">
        <div class="level-item">
          <p>
            <strong>{{ totalItems }}</strong> {{ itemName }} found
            <span v-if="hasActiveFilters">
              with filters:
              <span 
                v-for="(value, key) in activeFilters" 
                :key="key"
                class="tag is-info is-light mr-1"
              >
                {{ getFilterDisplayName(key) }}: {{ getFilterDisplayValue(key, value) }}
              </span>
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  filters: {
    type: Object,
    required: true
  },
  filterFields: {
    type: Array,
    default: () => []
  },
  searchLabel: {
    type: String,
    default: 'Search'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search...'
  },
  searchColumnClass: {
    type: String,
    default: 'is-5'
  },
  showResetButton: {
    type: Boolean,
    default: true
  },
  resetButtonText: {
    type: String,
    default: 'Reset Filters'
  },
  showStatusBar: {
    type: Boolean,
    default: true
  },
  totalItems: {
    type: Number,
    default: 0
  },
  itemName: {
    type: String,
    default: 'items'
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits([
  'search-changed',
  'filter-changed',
  'reset-filters'
]);

// Local reactive copy of filters
const localFilters = ref({ ...props.filters });

// Computed properties
const hasActiveFilters = computed(() => {
  return Object.values(localFilters.value).some(value => 
    value !== '' && value !== null && value !== undefined
  );
});

const activeFilters = computed(() => {
  return Object.entries(localFilters.value)
    .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});
});

// Methods
const handleSearchInput = () => {
  emit('search-changed', localFilters.value.search);
};

const handleFilterChange = (filterKey, filterValue) => {
  emit('filter-changed', filterKey, filterValue);
};

const resetFilters = () => {
  // Reset local filters to empty values
  Object.keys(localFilters.value).forEach(key => {
    localFilters.value[key] = '';
  });

  // Emit reset event to parent
  emit('reset-filters');
};

const getFilterDisplayName = (key) => {
  if (key === 'search') return 'Search';
  
  const field = props.filterFields.find(f => f.key === key);
  return field ? field.label : key;
};

const getFilterDisplayValue = (key, value) => {
  const field = props.filterFields.find(f => f.key === key);
  
  if (field && field.type === 'select' && field.options) {
    const option = field.options.find(opt => opt.value === value);
    return option ? option.label : value;
  }
  
  return value;
};

// Watch for external filter changes and sync immediately
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters };
}, { deep: true, immediate: true });

// Watch for local filter changes and emit them
watch(localFilters, (newLocalFilters) => {
  // Sync search changes
  if (newLocalFilters.search !== props.filters.search) {
    emit('search-changed', newLocalFilters.search);
  }

  // Sync other filter changes
  Object.keys(newLocalFilters).forEach(key => {
    if (key !== 'search' && newLocalFilters[key] !== props.filters[key]) {
      emit('filter-changed', key, newLocalFilters[key]);
    }
  });
}, { deep: true });
</script>

<style scoped>
.search-and-filters {
  margin-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}
</style>
