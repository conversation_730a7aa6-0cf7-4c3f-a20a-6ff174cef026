<template>
  <div class="categories-page">
    <!-- Header -->
    <div class="page-header">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <div>
              <h1 class="title is-4">Categories Management</h1>
              <p class="subtitle is-6">Manage category hierarchy and product organization</p>
            </div>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons">
              <button 
                class="button is-primary" 
                @click="openCreateModal"
                :disabled="loading">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add Category</span>
              </button>
              <button 
                class="button is-info" 
                @click="toggleView"
                :disabled="loading">
                <span class="icon">
                  <i :class="showHierarchy ? 'fas fa-table' : 'fas fa-sitemap'"></i>
                </span>
                <span>{{ showHierarchy ? 'Table View' : 'Hierarchy View' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="columns is-multiline mb-5">
      <div class="column is-3">
        <div class="card">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="heading">Total Categories</p>
                    <p class="title is-4">{{ stats.total }}</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-primary">
                    <i class="fas fa-folder fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="heading">Root Categories</p>
                    <p class="title is-4">{{ stats.rootCategories }}</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-info">
                    <i class="fas fa-sitemap fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="heading">With Products</p>
                    <p class="title is-4">{{ stats.withProducts }}</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-success">
                    <i class="fas fa-box fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="heading">Total Products</p>
                    <p class="title is-4">{{ stats.totalProducts }}</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-warning">
                    <i class="fas fa-shopping-bag fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-5">
      <div class="card-content">
        <div class="columns">
          <div class="column is-4">
            <div class="field">
              <label class="label">Search Categories</label>
              <div class="control has-icons-left">
                <input 
                  class="input" 
                  type="text" 
                  placeholder="Search by name or slug..."
                  v-model="searchQuery"
                  @input="debouncedSearch">
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Parent Category</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.parentId" @change="fetchCategories">
                    <option value="">All Categories</option>
                    <option value="root">Root Categories Only</option>
                    <option 
                      v-for="category in parentCategories" 
                      :key="category.id" 
                      :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-2">
            <div class="field">
              <label class="label">Sort By</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="sortBy" @change="fetchCategories">
                    <option value="name">Name</option>
                    <option value="productCount">Product Count</option>
                    <option value="createdAt">Created Date</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-2">
            <div class="field">
              <label class="label">Order</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="sortOrder" @change="fetchCategories">
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-1">
            <div class="field">
              <label class="label">&nbsp;</label>
              <div class="control">
                <button 
                  class="button is-light is-fullwidth" 
                  @click="resetFilters"
                  title="Reset Filters">
                  <span class="icon">
                    <i class="fas fa-undo"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered py-6">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p class="mt-3">Loading categories...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = ''"></button>
      {{ error }}
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Hierarchy View -->
      <div v-if="showHierarchy">
        <CategoryHierarchy
          :categories="hierarchicalCategories"
          @edit="openEditModal"
          @delete="confirmDelete"
          @add-child="openCreateChildModal"
          @view-products="viewCategoryProducts"
        />
      </div>

      <!-- Table View -->
      <div v-else>
        <CategoryTable
          :categories="categories"
          :loading="loading"
          @edit="openEditModal"
          @delete="confirmDelete"
          @view-products="viewCategoryProducts"
        />
        
        <!-- Pagination -->
        <nav class="pagination is-centered mt-5" v-if="pagination.totalPages > 1">
          <button 
            class="pagination-previous" 
            @click="changePage(pagination.currentPage - 1)"
            :disabled="pagination.currentPage <= 1">
            Previous
          </button>
          <button 
            class="pagination-next" 
            @click="changePage(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= pagination.totalPages">
            Next
          </button>
          <ul class="pagination-list">
            <li v-for="page in visiblePages" :key="page">
              <button 
                v-if="page !== '...'"
                class="pagination-link"
                :class="{ 'is-current': page === pagination.currentPage }"
                @click="changePage(page)">
                {{ page }}
              </button>
              <span v-else class="pagination-ellipsis">&hellip;</span>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- Modals -->
    <CategoryFormModal
      v-if="showFormModal"
      :category="selectedCategory"
      :is-edit="isEdit"
      :parent-categories="parentCategories"
      @close="closeFormModal"
      @saved="handleCategorySaved"
    />

    <ConfirmDialog
      v-if="showDeleteDialog"
      :title="'Delete Category'"
      :message="`Are you sure you want to delete '${categoryToDelete?.name}'? This action cannot be undone.`"
      :confirm-text="'Delete'"
      :confirm-class="'is-danger'"
      @confirm="handleDelete"
      @cancel="closeDeleteDialog"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { debounce } from 'lodash-es';
import CategoryTable from '@/admin/components/categories/CategoryTable.vue';
import CategoryHierarchy from '@/admin/components/categories/CategoryHierarchy.vue';
import CategoryFormModal from '@/admin/components/categories/CategoryFormModal.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { categoriesService } from '@/admin/services/categories';

// Reactive data
const loading = ref(false);
const error = ref('');
const categories = ref([]);
const hierarchicalCategories = ref([]);
const selectedCategory = ref(null);
const categoryToDelete = ref(null);

// UI state
const showFormModal = ref(false);
const showDeleteDialog = ref(false);
const isEdit = ref(false);
const showHierarchy = ref(false);

// Search and filters
const searchQuery = ref('');
const filters = ref({
  parentId: ''
});
const sortBy = ref('name');
const sortOrder = ref('asc');

// Pagination
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 20
});

// Stats
const stats = ref({
  total: 0,
  rootCategories: 0,
  withProducts: 0,
  totalProducts: 0
});

// Computed properties
const parentCategories = computed(() => {
  return categories.value.filter(cat => !cat.parentId);
});

const visiblePages = computed(() => {
  const current = pagination.value.currentPage;
  const total = pagination.value.totalPages;
  const pages = [];

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    if (current <= 4) {
      pages.push(1, 2, 3, 4, 5, '...', total);
    } else if (current >= total - 3) {
      pages.push(1, '...', total - 4, total - 3, total - 2, total - 1, total);
    } else {
      pages.push(1, '...', current - 1, current, current + 1, '...', total);
    }
  }

  return pages;
});

// Debounced search
const debouncedSearch = debounce(() => {
  pagination.value.currentPage = 1;
  fetchCategories();
}, 300);

// Methods
const fetchCategories = async () => {
  loading.value = true;
  error.value = '';

  try {
    const params = {
      filter: searchQuery.value || undefined,
      orderBy: sortBy.value,
      descending: sortOrder.value === 'desc',
      page: pagination.value.currentPage,
      pageSize: pagination.value.perPage
    };

    // Add parent filter
    if (filters.value.parentId === 'root') {
      params.parentId = null;
    } else if (filters.value.parentId) {
      params.parentId = filters.value.parentId;
    }

    const response = await categoriesService.getAll(params);

    categories.value = response.data || [];
    pagination.value = {
      currentPage: response.currentPage || 1,
      totalPages: response.totalPages || 1,
      totalItems: response.totalItems || 0,
      perPage: response.pageSize || 20
    };

    // Build hierarchy if needed
    if (showHierarchy.value) {
      buildHierarchy();
    }

    // Update stats
    updateStats();

  } catch (err) {
    console.error('Error fetching categories:', err);
    error.value = 'Failed to load categories. Please try again.';
  } finally {
    loading.value = false;
  }
};

const buildHierarchy = () => {
  const categoryMap = new Map();
  const rootCategories = [];

  // Create a map of all categories
  categories.value.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Build the hierarchy
  categories.value.forEach(category => {
    const categoryWithChildren = categoryMap.get(category.id);

    if (category.parentId) {
      const parent = categoryMap.get(category.parentId);
      if (parent) {
        parent.children.push(categoryWithChildren);
      }
    } else {
      rootCategories.push(categoryWithChildren);
    }
  });

  hierarchicalCategories.value = rootCategories;
};

const updateStats = () => {
  stats.value.total = pagination.value.totalItems;
  stats.value.rootCategories = categories.value.filter(cat => !cat.parentId).length;
  stats.value.withProducts = categories.value.filter(cat => cat.productCount > 0).length;
  stats.value.totalProducts = categories.value.reduce((sum, cat) => sum + (cat.productCount || 0), 0);
};

const resetFilters = () => {
  searchQuery.value = '';
  filters.value.parentId = '';
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  pagination.value.currentPage = 1;
  fetchCategories();
};

const toggleView = () => {
  showHierarchy.value = !showHierarchy.value;
  if (showHierarchy.value) {
    buildHierarchy();
  }
};

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.currentPage = page;
    fetchCategories();
  }
};

// Modal handlers
const openCreateModal = () => {
  selectedCategory.value = null;
  isEdit.value = false;
  showFormModal.value = true;
};

const openCreateChildModal = (parentCategory) => {
  selectedCategory.value = { parentId: parentCategory.id };
  isEdit.value = false;
  showFormModal.value = true;
};

const openEditModal = (category) => {
  selectedCategory.value = { ...category };
  isEdit.value = true;
  showFormModal.value = true;
};

const closeFormModal = () => {
  showFormModal.value = false;
  selectedCategory.value = null;
  isEdit.value = false;
};

const handleCategorySaved = () => {
  closeFormModal();
  fetchCategories();
};

const confirmDelete = (category) => {
  categoryToDelete.value = category;
  showDeleteDialog.value = true;
};

const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  categoryToDelete.value = null;
};

const handleDelete = async () => {
  if (!categoryToDelete.value) return;

  try {
    await categoriesService.delete(categoryToDelete.value.id);
    closeDeleteDialog();
    fetchCategories();
  } catch (err) {
    console.error('Error deleting category:', err);
    error.value = 'Failed to delete category. Please try again.';
  }
};

const viewCategoryProducts = (category) => {
  // Navigate to products page with category filter
  // This would be implemented based on your routing setup
  console.log('View products for category:', category.name);
};

// Watchers
watch([sortBy, sortOrder], () => {
  fetchCategories();
});

watch(showHierarchy, (newValue) => {
  if (newValue) {
    buildHierarchy();
  }
});

// Lifecycle
onMounted(() => {
  fetchCategories();
});
</script>
