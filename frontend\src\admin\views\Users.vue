<template>
  <div class="admin-users">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Users</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button class="button is-primary" @click="openAddUserModal">
            <span class="icon"><i class="fas fa-plus"></i></span>
            <span>Add User</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <user-filters @filter-changed="applyFilters" />

    <!-- Users Table -->
    <user-table
      :users="users"
      :loading="loading"
      @edit="editUser"
      @delete="confirmDeleteUser"
      @change-role="changeUserRole" />

    <!-- Pagination -->
    <div class="pagination-wrapper">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="changePage" />
    </div>

    <!-- Add/Edit User Modal -->
    <user-form-modal
      :is-open="isUserModalOpen"
      :user="selectedUser"
      @close="closeUserModal"
      @save="saveUser" />

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="isDeleteModalOpen"
      :title="'Delete User'"
      :message="'Are you sure you want to delete this user? This action cannot be undone.'"
      @confirm="deleteUser"
      @cancel="isDeleteModalOpen = false" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import UserFilters from '@/admin/components/users/UserFilters.vue';
import UserTable from '@/admin/components/users/UserTable.vue';
import UserFormModal from '@/admin/components/users/UserFormModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { usersService } from '@/admin/services/users';

// State
const users = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const filters = ref({});
const isUserModalOpen = ref(false);
const isDeleteModalOpen = ref(false);
const selectedUser = ref(null);

// Fetch users with pagination and filters
const fetchUsers = async () => {
  loading.value = true;
  users.value = [];

  try {
    console.log('Fetching users with filters:', filters.value);

    const response = await usersService.getUsers({
      page: currentPage.value,
      ...filters.value
    });

    // Handle the response format
    if (response.users) {
      users.value = response.users;
    } else if (response.items) {
      users.value = response.items;
    } else {
      users.value = [];
    }

    // Handle pagination
    if (response.pagination) {
      currentPage.value = response.pagination.page;
      totalPages.value = response.pagination.totalPages;
    } else if (response.totalPages) {
      totalPages.value = response.totalPages;
    }
  } catch (error) {
    console.error('Error fetching users:', error);

    // Show detailed error information in console for debugging
    if (error.response) {
      console.error('Server response error:', {
        status: error.response.status,
        data: error.response.data
      });
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }

    // Display user-friendly error notification
    alert('Failed to fetch users. Please check your connection and try again.');
  } finally {
    loading.value = false;
  }
};

// Change page
const changePage = (page) => {
  currentPage.value = page;
  fetchUsers();
};

// Apply filters
const applyFilters = (newFilters) => {
  filters.value = newFilters;
  currentPage.value = 1; // Reset to first page when filters change
  fetchUsers();
};

// Open add user modal
const openAddUserModal = () => {
  selectedUser.value = null; // Reset selected user for adding new
  isUserModalOpen.value = true;
};

// Edit user
const editUser = (user) => {
  selectedUser.value = { ...user };
  isUserModalOpen.value = true;
};

// Close user modal
const closeUserModal = () => {
  isUserModalOpen.value = false;
  selectedUser.value = null;
};

// Save user (create or update)
const saveUser = async (userData) => {
  loading.value = true;
  try {
    if (userData.id) {
      await usersService.updateUser(userData.id, userData);
    } else {
      await usersService.createUser(userData);
    }
    closeUserModal();
    fetchUsers();
  } catch (error) {
    console.error('Error saving user:', error);
  } finally {
    loading.value = false;
  }
};

// Change user role
const changeUserRole = async (userId, newRole) => {
  loading.value = true;
  try {
    await usersService.changeUserRole(userId, newRole);
    fetchUsers();
  } catch (error) {
    console.error('Error changing user role:', error);
  } finally {
    loading.value = false;
  }
};

// Confirm delete user
const confirmDeleteUser = (user) => {
  selectedUser.value = user;
  isDeleteModalOpen.value = true;
};

// Delete user
const deleteUser = async () => {
  if (!selectedUser.value) return;

  loading.value = true;
  try {
    await usersService.deleteUser(selectedUser.value.id);
    isDeleteModalOpen.value = false;
    fetchUsers();
  } catch (error) {
    console.error('Error deleting user:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.admin-users {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
