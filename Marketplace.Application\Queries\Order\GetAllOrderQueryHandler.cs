﻿using AutoMapper;
using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;
using Marketplace.Domain.Repositories;
using Marketplace.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using MediatR;
using Microsoft.Extensions.Configuration;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Order;

public class GetAllOrderQueryHandler :
    PaginatedQueryHandler<GetAllOrderQuery, Domain.Entities.Order, OrderResponse>,
    IRequestHandler<GetAllOrderQuery, PaginatedResponse<OrderResponse>>
{
    private readonly IOrderRepository _repository;
    private readonly IPaymentRepository _paymentRepository;
    private readonly MarketplaceDbContext _context;

    public GetAllOrderQueryHandler(
        IOrderRepository repository,
        IPaymentRepository paymentRepository,
        MarketplaceDbContext context,
        IConfiguration configuration,
        IMapper mapper) : base(configuration, mapper)
    {
        _repository = repository;
        _paymentRepository = paymentRepository;
        _context = context;
    }

    public async Task<PaginatedResponse<OrderResponse>> Handle(GetAllOrderQuery request, CancellationToken cancellationToken)
    {
        // Створюємо базовий фільтр
        Expression<Func<Domain.Entities.Order, bool>>? filter = null;

        // Фільтр пошуку
        if (!string.IsNullOrEmpty(request.Filter))
        {
            filter = o => o.Customer.Username.Contains(request.Filter) ||
                         o.Customer.Email.Value.Contains(request.Filter) ||
                         o.Id.ToString().Contains(request.Filter);
        }

        // Фільтр статусу замовлення
        if (request.Status.HasValue)
        {
            Expression<Func<Domain.Entities.Order, bool>> statusFilter = o => o.Status == request.Status.Value;
            filter = filter == null ?
                statusFilter :
                CombineFilters(filter, statusFilter);
        }

        // Якщо є фільтр по статусу платежу, потрібно додатково фільтрувати
        List<Guid>? orderIdsWithPaymentStatus = null;
        if (request.PaymentStatus.HasValue)
        {
            var paymentsWithStatus = await _paymentRepository.GetAllAsync(
                filter: p => p.Status == request.PaymentStatus.Value,
                cancellationToken: cancellationToken);
            orderIdsWithPaymentStatus = paymentsWithStatus.Select(p => p.OrderId).ToList();

            // Додаємо фільтр по ID замовлень з потрібним статусом платежу
            Expression<Func<Domain.Entities.Order, bool>> paymentFilter = o => orderIdsWithPaymentStatus.Contains(o.Id);
            filter = filter == null ?
                paymentFilter :
                CombineFilters(filter, paymentFilter);
        }

        // Отримуємо пагіновані дані з Customer, ShippingMethod, ShippingAddress
        var pagedResult = await _repository.GetPagedAsync(
            filter: filter,
            orderBy: request.OrderBy ?? "CreatedAt", // За замовчуванням сортуємо за датою
            descending: request.OrderBy == null ? true : request.Descending, // Найновіші спочатку
            page: request.Page ?? 1,
            pageSize: request.PageSize ?? 10, // Зменшуємо розмір сторінки для швидкості
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.Order, object>>[] {
                o => o.Customer, // Customer для імені та email (обов'язково для фільтрації)
                o => o.ShippingMethod, // ShippingMethod для назви
                o => o.ShippingAddress // ShippingAddress для адреси
            }
        );

        // Отримуємо Payment інформацію для кожного замовлення
        var orderIds = pagedResult.Items.Select(o => o.Id).ToList();
        var payments = await _context.Set<Domain.Entities.Payment>()
            .Where(p => orderIds.Contains(p.OrderId))
            .ToListAsync(cancellationToken);

        // Отримуємо кількість товарів для кожного замовлення
        var orderItems = await _context.Set<Domain.Entities.OrderItem>()
            .Where(oi => orderIds.Contains(oi.OrderId))
            .GroupBy(oi => oi.OrderId)
            .Select(g => new { OrderId = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        // Мапимо результати та додаємо Payment інформацію
        var orderResponses = new List<OrderResponse>();
        foreach (var order in pagedResult.Items)
        {
            var orderResponse = Mapper.Map<OrderResponse>(order);

            // Додаємо Payment інформацію
            var payment = payments.FirstOrDefault(p => p.OrderId == order.Id);
            if (payment != null)
            {
                orderResponse.PaymentStatus = payment.Status;
                orderResponse.PaymentMethod = payment.PaymentMethod;
                orderResponse.PaymentStatusText = payment.Status.ToString();
                orderResponse.PaymentMethodText = payment.PaymentMethod.ToString();
            }
            else
            {
                orderResponse.PaymentStatusText = "Unknown";
                orderResponse.PaymentMethodText = "Unknown";
            }

            // Додаємо кількість товарів
            var itemCount = orderItems.FirstOrDefault(oi => oi.OrderId == order.Id);
            orderResponse.ItemsCount = itemCount?.Count ?? 0;

            orderResponses.Add(orderResponse);
        }

        // Створюємо пагіновану відповідь з оновленими даними
        var response = CreatePaginatedResponse(request, pagedResult, "orders");
        response.Data = orderResponses;
        return response;
    }

    private static Expression<Func<Domain.Entities.Order, bool>> CombineFilters(
        Expression<Func<Domain.Entities.Order, bool>> filter1,
        Expression<Func<Domain.Entities.Order, bool>> filter2)
    {
        var parameter = Expression.Parameter(typeof(Domain.Entities.Order), "o");
        var body1 = Expression.Invoke(filter1, parameter);
        var body2 = Expression.Invoke(filter2, parameter);
        var combined = Expression.AndAlso(body1, body2);
        return Expression.Lambda<Func<Domain.Entities.Order, bool>>(combined, parameter);
    }
}
