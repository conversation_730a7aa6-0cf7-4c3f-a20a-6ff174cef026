import api from '@/services/api';

export const companiesService = {
  async getCompanies(params = {}) {
    try {
      console.log('Requesting companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;
      if (params.orderBy) apiParams.orderBy = params.orderBy;
      if (params.descending !== undefined) apiParams.descending = params.descending;

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      // Статус фільтр видалено - тепер показуємо тільки схвалені компанії

      // Featured фільтр
      if (params.featured && params.featured.trim() !== '') {
        apiParams.isFeatured = params.featured === 'true';
      }

      console.log('Final API params for companies:', apiParams);

      // Спробуємо спочатку admin endpoint, потім fallback до публічного
      let response;
      try {
        response = await api.get('/api/admin/companies', { params: apiParams });
        console.log('Admin companies API response:', response.data);

        // Admin endpoint повертає дані в response.data.data
        if (response.data && response.data.data) {
          return response.data.data;
        }
      } catch (adminError) {
        console.warn('Admin companies endpoint failed, falling back to public endpoint:', adminError.message);
        response = await api.get('/api/companies', { params: apiParams });
        console.log('Public companies API response:', response.data);
        return response.data;
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load companies');
    }
  },

  async getPendingCompanies(params = {}) {
    try {
      console.log('Requesting pending companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;
      if (params.orderBy) apiParams.orderBy = params.orderBy;
      if (params.descending !== undefined) apiParams.descending = params.descending;

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      console.log('Final API params for pending companies:', apiParams);

      const response = await api.get('/api/admin/companies/pending', { params: apiParams });

      console.log('Pending companies API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching pending companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load pending companies');
    }
  },

  async getCompanyById(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching company:', error);
      throw new Error(error.response?.data?.message || 'Failed to load company details');
    }
  },

  async approveCompany(id) {
    try {
      const response = await api.post(`/api/admin/companies/${id}/approve`);
      return response.data;
    } catch (error) {
      console.error('Error approving company:', error);
      throw new Error(error.response?.data?.message || 'Failed to approve company');
    }
  },

  async rejectCompany(id, reason = '') {
    try {
      const response = await api.post(`/api/admin/companies/${id}/reject`, { reason });
      return response.data;
    } catch (error) {
      console.error('Error rejecting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to reject company');
    }
  },

  async deleteCompany(id) {
    try {
      const response = await api.delete(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete company');
    }
  },

  async getCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get company');
    }
  },

  async getDetailedCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}/detailed`);
      return response.data;
    } catch (error) {
      console.error('Error getting detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get detailed company');
    }
  },

  async updateCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update company');
    }
  },

  async updateDetailedCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}/detailed`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update detailed company');
    }
  }
};
