using Marketplace.Application.Commands.Order;
using Marketplace.Application.Queries.Order;
using Marketplace.Application.Queries.OrderNote;
using Marketplace.Application.Queries.OrderItem;
using Marketplace.Application.Responses;
using Marketplace.Presentation.Models;
using Marketplace.Presentation.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Marketplace.Domain.Entities;

namespace Marketplace.Presentation.Controllers.Admin;

[ApiController]
[Authorize(Roles = "Admin,Moderator")]
[Route("api/admin/orders")]
public class AdminOrderController : BasicApiController
{
    private readonly IMediator _mediator;

    public AdminOrderController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllOrders(
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        [FromQuery] OrderStatus? status = null,
        [FromQuery] PaymentStatus? paymentStatus = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetAllOrderQuery(
            filter,
            orderBy,
            descending,
            page,
            pageSize,
            status,
            paymentStatus);

        var response = await _mediator.Send(query, cancellationToken);
        return Ok(ApiResponse<PaginatedResponse<OrderResponse>>.SuccessWithData(response));
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetOrderById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetOrderQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null
            ? Ok(ApiResponse<OrderResponse>.SuccessWithData(response))
            : NotFound(ApiResponse.Failure("Замовлення не знайдено."));
    }

    [HttpGet("{id}/items")]
    public async Task<IActionResult> GetOrderItems(
        [FromRoute] Guid id,
        [FromQuery] string? filter = null,
        [FromQuery] string? orderBy = null,
        [FromQuery] bool descending = false,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null,
        CancellationToken cancellationToken = default)
    {
        // Create a query to get order items for admin (no user restriction)
        var query = new GetAdminOrderItemsQuery(
            id,
            filter,
            orderBy,
            descending,
            page,
            pageSize);
        var response = await _mediator.Send(query, cancellationToken);

        return response != null
            ? Ok(ApiResponse<PaginatedResponse<OrderItemResponse>>.SuccessWithData(response))
            : NotFound(ApiResponse.Failure("Замовлення не знайдено."));
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateOrder(
        [FromRoute] Guid id,
        [FromBody] UpdateOrderRequest request,
        CancellationToken cancellationToken)
    {
        var command = new UpdateOrderCommand
        {
            Id = id,
            Status = !string.IsNullOrEmpty(request.Status) && Enum.TryParse<OrderStatus>(request.Status, out var status) ? status : null,
            PaymentStatus = !string.IsNullOrEmpty(request.PaymentStatus) && Enum.TryParse<PaymentStatus>(request.PaymentStatus, out var paymentStatus) ? paymentStatus : null,
            ShippingAddress = request.ShippingAddress,
            TrackingNumber = request.TrackingNumber,
            ShippingMethod = request.ShippingMethod,
            Notes = request.Notes
        };

        var response = await _mediator.Send(command, cancellationToken);

        return response.Success
            ? Ok(ApiResponse.SuccessResponse(response.Message))
            : BadRequest(ApiResponse.Failure(response.Message));
    }

    [HttpPatch("{id}/status")]
    public async Task<IActionResult> UpdateOrderStatus(
        [FromRoute] Guid id,
        [FromBody] UpdateOrderStatusRequest request,
        CancellationToken cancellationToken)
    {
        var command = new UpdateOrderCommand
        {
            Id = id,
            Status = !string.IsNullOrEmpty(request.Status) && Enum.TryParse<OrderStatus>(request.Status, out var status) ? status : null
        };

        var response = await _mediator.Send(command, cancellationToken);

        return response.Success
            ? Ok(ApiResponse.SuccessResponse(response.Message))
            : BadRequest(ApiResponse.Failure(response.Message));
    }

    [HttpPost("{id}/notes")]
    public async Task<IActionResult> AddOrderNote(
        [FromRoute] Guid id,
        [FromBody] AddOrderNoteRequest request,
        CancellationToken cancellationToken)
    {
        var currentUserId = GetCurrentUserId();
        if (!currentUserId.HasValue)
        {
            return Unauthorized(ApiResponse.Failure("Користувач не автентифікований."));
        }

        var command = new AddOrderNoteCommand(id, request.Note, currentUserId);
        var response = await _mediator.Send(command, cancellationToken);

        return response != null
            ? Ok(ApiResponse<OrderNoteResponse>.SuccessWithData(response, "Нотатка успішно додана."))
            : BadRequest(ApiResponse.Failure("Не вдалося додати нотатку."));
    }

    [HttpGet("{id}/notes")]
    public async Task<IActionResult> GetOrderNotes([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var query = new GetOrderNotesQuery(id);
        var response = await _mediator.Send(query, cancellationToken);

        return Ok(ApiResponse<List<OrderNoteResponse>>.SuccessWithData(response));
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteOrder([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var command = new DeleteOrderCommand(id);
        var result = await _mediator.Send(command, cancellationToken);

        return result 
            ? NoContent() 
            : NotFound(ApiResponse.Failure("Замовлення не знайдено."));
    }
}
