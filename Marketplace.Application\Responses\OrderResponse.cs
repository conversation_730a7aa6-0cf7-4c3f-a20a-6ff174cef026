﻿using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;

namespace Marketplace.Application.Responses;

public class OrderResponse
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public string? CustomerEmail { get; set; }
    public Money? TotalPrice { get; set; }
    public decimal TotalPriceAmount { get; set; }
    public string TotalPriceCurrency { get; set; }
    public OrderStatus Status { get; set; }
    public Guid ShippingAddressId { get; set; }
    public Guid ShippingMethodId { get; set; }
    public string? ShippingMethodName { get; set; }
    public string? ShippingAddressLine { get; set; }
    public string? ShippingCity { get; set; }
    public string? ShippingCountry { get; set; }
    public DateTime CreatedAt { get; set; }

    // Payment information
    public PaymentStatus? PaymentStatus { get; set; }
    public PaymentMethod? PaymentMethod { get; set; }
    public string? PaymentStatusText { get; set; }
    public string? PaymentMethodText { get; set; }

    // Order items count
    public int ItemsCount { get; set; }

    public OrderResponse() { }
}