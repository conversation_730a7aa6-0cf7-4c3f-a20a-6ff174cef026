﻿using Marketplace.Domain.Exceptions;
using Marketplace.Domain.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Marketplace.Infrastructure.Services;

/// <summary>
/// Реалізація сервісу файлів для локального зберігання
/// </summary>
public class LocalFileService : IFileService
{
    private readonly IConfiguration _configuration;
    private readonly FileServiceOptions _options;
    private readonly ILogger<LocalFileService> _logger;

    public LocalFileService(IOptions<FileServiceOptions> options, IConfiguration configuration, ILogger<LocalFileService> logger)
    {
        _configuration = configuration;
        _options = options.Value;
        _logger = logger;

        // Створюємо кореневу папку, якщо вона не існує
        if (!Directory.Exists(_options.StoragePath))
        {
            Directory.CreateDirectory(_options.StoragePath);
            _logger.LogInformation("Створено кореневу папку для зберігання файлів: {Path}", _options.StoragePath);
        }
        _options.BaseUrl = _configuration["Frontend:BaseUrl"]
            ?? throw new ArgumentNullException("Frontend:BaseUrl is not configured.");
    }

    /// <inheritdoc />
    public async Task<string> SaveFileAsync(Stream fileStream, string fileName, string contentType, string folder = null, CancellationToken cancellationToken = default)
    {
        ValidateFile(fileStream, contentType);

        // Генеруємо унікальне ім'я файлу
        var uniqueFileName = GetUniqueFileName(fileName);

        // Створюємо шлях до папки
        var folderPath = GetFolderPath(folder);

        // Створюємо папку, якщо вона не існує
        if (!Directory.Exists(folderPath))
        {
            Directory.CreateDirectory(folderPath);
        }

        // Повний шлях до файлу
        var filePath = Path.Combine(folderPath, uniqueFileName);

        try
        {
            // Зберігаємо файл
            using (var fileStream2 = new FileStream(filePath, FileMode.Create))
            {
                await fileStream.CopyToAsync(fileStream2, cancellationToken);
            }

            // Повертаємо URL для доступу до файлу
            return GetFileUrl(folder, uniqueFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при збереженні файлу: {FileName}", uniqueFileName);
            throw new DomainException("Помилка при збереженні файлу.");
        }
    }

    /// <inheritdoc />
    public Task<bool> DeleteFileAsync(string fileUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = GetFilePathFromUrl(fileUrl);

            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("Файл видалено: {FilePath}", filePath);
                return Task.FromResult(true);
            }

            _logger.LogWarning("Файл не знайдено для видалення: {FilePath}", filePath);
            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при видаленні файлу: {FileUrl}", fileUrl);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public Task<(Stream FileStream, string ContentType)> GetFileAsync(string fileUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = GetFilePathFromUrl(fileUrl);

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Файл не знайдено: {FilePath}", filePath);
                return Task.FromResult<(Stream, string)>((null, null));
            }

            var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var contentType = GetContentTypeFromFileName(filePath);

            return Task.FromResult<(Stream, string)>((fileStream, contentType));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при отриманні файлу: {FileUrl}", fileUrl);
            return Task.FromResult<(Stream, string)>((null, null));
        }
    }

    /// <inheritdoc />
    public Task<bool> FileExistsAsync(string fileUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = GetFilePathFromUrl(fileUrl);
            return Task.FromResult(File.Exists(filePath));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Помилка при перевірці існування файлу: {FileUrl}", fileUrl);
            return Task.FromResult(false);
        }
    }

    #region Private Methods

    private void ValidateFile(Stream fileStream, string contentType)
    {
        // Перевіряємо розмір файлу
        if (fileStream.Length > _options.MaxFileSize)
        {
            throw new DomainException($"Розмір файлу перевищує максимально допустимий ({_options.MaxFileSize / 1024 / 1024} MB).");
        }

        // Перевіряємо тип файлу
        if (!_options.AllowedFileTypes.Contains(contentType))
        {
            throw new DomainException($"Тип файлу {contentType} не підтримується.");
        }
    }

    private string GetUniqueFileName(string fileName)
    {
        // Отримуємо розширення файлу
        var extension = Path.GetExtension(fileName);

        // Генеруємо унікальне ім'я файлу
        var uniqueName = $"{Guid.NewGuid()}{extension}";

        return uniqueName;
    }

    private string GetFolderPath(string folder)
    {
        // Якщо папка не вказана, використовуємо кореневу папку
        if (string.IsNullOrEmpty(folder))
        {
            return _options.StoragePath;
        }

        // Інакше створюємо шлях до вказаної папки
        return Path.Combine(_options.StoragePath, folder);
    }

    private string GetFileUrl(string folder, string fileName)
    {
        // Формуємо відносний URL для доступу до файлу (без базового URL)
        if (string.IsNullOrEmpty(folder))
        {
            return fileName;
        }

        return $"{folder}/{fileName}";
    }

    private string GetFilePathFromUrl(string fileUrl)
    {
        // Отримуємо відносний шлях з URL
        var relativePath = fileUrl.Replace(_options.BaseUrl, "").TrimStart('/');

        // Формуємо повний шлях до файлу
        return Path.Combine(_options.StoragePath, relativePath);
    }

    private string GetContentTypeFromFileName(string filePath)
    {
        // Отримуємо розширення файлу
        var extension = Path.GetExtension(filePath).ToLowerInvariant();

        // Визначаємо MIME-тип за розширенням
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".jfif" => "image/jpeg", // JFIF - це варіант JPEG
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".webp" => "image/webp",
            ".pdf" => "application/pdf",
            _ => "application/octet-stream"
        };
    }

    #endregion
}
