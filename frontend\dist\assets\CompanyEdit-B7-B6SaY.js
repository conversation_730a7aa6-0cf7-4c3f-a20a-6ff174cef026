import{_ as W,g as m,z as S,i as G,c as d,a as s,k as E,b as h,w as C,r as H,t as p,A as o,B as i,d as w,J as D,F,p as N,n as V,m as K,f as Q,e as Y,o as r}from"./index-C1YYMYJd.js";import{c as T}from"./companies-p3GzLfMa.js";import{p as A}from"./products-DEnweLaU.js";const Z={class:"company-edit"},ss={class:"level"},es={class:"level-right"},ts={class:"level-item"},ls={key:0,class:"has-text-centered py-6"},as={key:1,class:"notification is-danger"},ns={key:2,class:"box"},os={class:"columns"},is={class:"column is-8"},ds={class:"field"},rs={class:"control"},us={class:"field"},cs={class:"control"},ms={class:"columns"},ps={class:"column"},vs={class:"field"},ys={class:"control"},fs={class:"column"},bs={class:"field"},_s={class:"control"},gs={class:"columns"},hs={class:"column"},Cs={class:"field"},ks={class:"control"},xs={class:"column"},Us={class:"field"},ws={class:"control"},Vs={class:"columns"},Ps={class:"column"},Ss={class:"field"},Es={class:"control"},Ds={class:"column is-4"},Fs={class:"field"},Ns={class:"control"},Ts={class:"column is-4"},As={class:"field"},Is={class:"control"},Rs={class:"file has-name is-fullwidth"},Xs={class:"file-label"},Bs={class:"file-name"},Ms={key:0,class:"mt-3"},js={class:"image is-128x128"},Ls=["src"],Os={class:"field"},$s={class:"control"},qs={class:"checkbox"},zs={class:"box mt-5"},Js={class:"columns"},Ws={class:"column"},Gs={class:"field"},Hs={class:"control"},Ks={class:"field"},Qs={class:"control"},Ys={class:"column"},Zs={class:"field"},se={class:"control"},ee={class:"field"},te={class:"control"},le={class:"field"},ae={class:"control"},ne={class:"box mt-5"},oe={class:"table-container"},ie={class:"table is-fullwidth"},de=["onUpdate:modelValue","disabled"],re=["onUpdate:modelValue","disabled"],ue={class:"checkbox"},ce=["onUpdate:modelValue"],me={class:"box mt-5"},pe={key:0,class:"has-text-centered"},ve={key:1,class:"has-text-centered"},ye={key:2,class:"table-container"},fe={class:"table is-fullwidth is-striped"},be={class:"image is-48x48"},_e=["src","alt"],ge={class:"has-text-grey"},he={class:"tag is-info"},Ce={class:"buttons are-small"},ke=["onClick","disabled"],xe={class:"icon"},Ue={key:0,class:"fas fa-trash"},we={key:1,class:"fas fa-spinner fa-spin"},Ve={class:"field is-grouped mt-5"},Pe={class:"control"},Se=["disabled"],Ee={class:"control"},De={__name:"CompanyEdit",setup(Fe){const k=Q(),I=Y(),x=m(!0),b=m(!1),v=m(""),n=m(null),P=m(""),y=m([]),U=m(!1),_=m(null),a=S({name:"",description:"",contactEmail:"",contactPhone:"",addressRegion:"",addressCity:"",addressStreet:"",addressPostalCode:"",imageUrl:"",isFeatured:!1}),u=S({bankName:"",bankAccount:"",bankCode:"",taxId:"",paymentDetails:""}),g=m([]),R=async()=>{try{x.value=!0,v.value="";const l=await T.getDetailedCompany(k.params.id);n.value=l.data,Object.assign(a,{name:n.value.name||"",description:n.value.description||"",contactEmail:n.value.contactEmail||"",contactPhone:n.value.contactPhone||"",addressRegion:n.value.addressRegion||"",addressCity:n.value.addressCity||"",addressStreet:n.value.addressStreet||"",addressPostalCode:n.value.addressPostalCode||"",imageUrl:n.value.imageUrl||"",isFeatured:n.value.isFeatured||!1}),n.value.finance&&Object.assign(u,{bankName:n.value.finance.bankName||"",bankAccount:n.value.finance.bankAccount||"",bankCode:n.value.finance.bankCode||"",taxId:n.value.finance.taxId||"",paymentDetails:n.value.finance.paymentDetails||""}),n.value.schedule&&n.value.schedule.length>0?g.value=n.value.schedule.map(e=>({day:e.day,openTime:e.openTime||"09:00",closeTime:e.closeTime||"18:00",isClosed:e.isClosed||!1})):g.value=Array.from({length:7},(e,c)=>({day:c,openTime:"09:00",closeTime:"18:00",isClosed:!1}))}catch(l){v.value=l.message||"Failed to load company"}finally{x.value=!1}},X=l=>{const e=l.target.files[0];if(e){P.value=e.name;const c=new FileReader;c.onload=t=>{a.imageUrl=t.target.result},c.readAsDataURL(e)}},B=async()=>{try{b.value=!0,v.value="";const l={company:a,finance:u,schedule:g.value};await T.updateDetailedCompany(k.params.id,l),I.push("/admin/companies")}catch(l){v.value=l.message||"Failed to update company"}finally{b.value=!1}},M=l=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][l]||"Unknown",j=async()=>{try{U.value=!0;const l=await A.getProducts({companyId:k.params.id,page:1,limit:100});y.value=l.data||[]}catch(l){console.error("Error fetching products:",l),y.value=[]}finally{U.value=!1}},L=async l=>{if(confirm("Are you sure you want to delete this product?"))try{_.value=l,await A.deleteProduct(l),y.value=y.value.filter(e=>e.id!==l)}catch(e){console.error("Error deleting product:",e),alert("Failed to delete product: "+(e.message||"Unknown error"))}finally{_.value=null}},O=(l,e)=>new Intl.NumberFormat("en-US",{style:"currency",currency:e||"USD"}).format(l||0),$=l=>l===0?"is-danger":l<10?"is-warning":"is-success",q=l=>{switch(l){case 0:return"is-warning";case 1:return"is-success";case 2:return"is-danger";default:return"is-light"}},z=l=>{switch(l){case 0:return"Pending";case 1:return"Approved";case 2:return"Rejected";default:return"Unknown"}};return G(()=>{R(),j()}),(l,e)=>{const c=H("router-link");return r(),d("div",Z,[s("div",ss,[e[15]||(e[15]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Edit Company")])],-1)),s("div",es,[s("div",ts,[h(c,{to:"/admin/companies",class:"button"},{default:C(()=>e[14]||(e[14]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Companies",-1)])),_:1})])])]),x.value?(r(),d("div",ls,e[16]||(e[16]=[s("div",{class:"is-loading"},null,-1),s("p",null,"Loading company...",-1)]))):v.value?(r(),d("div",as,[s("p",null,p(v.value),1)])):n.value?(r(),d("div",ns,[s("form",{onSubmit:K(B,["prevent"])},[s("div",os,[s("div",is,[s("div",ds,[e[17]||(e[17]=s("label",{class:"label"},"Company Name *",-1)),s("div",rs,[o(s("input",{"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),class:"input",type:"text",placeholder:"Enter company name",required:""},null,512),[[i,a.name]])])]),s("div",us,[e[18]||(e[18]=s("label",{class:"label"},"Description",-1)),s("div",cs,[o(s("textarea",{"onUpdate:modelValue":e[1]||(e[1]=t=>a.description=t),class:"textarea",placeholder:"Enter company description",rows:"4"},null,512),[[i,a.description]])])]),e[25]||(e[25]=s("h3",{class:"subtitle"},"Contact Information",-1)),s("div",ms,[s("div",ps,[s("div",vs,[e[19]||(e[19]=s("label",{class:"label"},"Contact Email *",-1)),s("div",ys,[o(s("input",{"onUpdate:modelValue":e[2]||(e[2]=t=>a.contactEmail=t),class:"input",type:"email",placeholder:"<EMAIL>",required:""},null,512),[[i,a.contactEmail]])])])]),s("div",fs,[s("div",bs,[e[20]||(e[20]=s("label",{class:"label"},"Contact Phone",-1)),s("div",_s,[o(s("input",{"onUpdate:modelValue":e[3]||(e[3]=t=>a.contactPhone=t),class:"input",type:"tel",placeholder:"+380XXXXXXXXX"},null,512),[[i,a.contactPhone]])])])])]),e[26]||(e[26]=s("h3",{class:"subtitle"},"Address",-1)),s("div",gs,[s("div",hs,[s("div",Cs,[e[21]||(e[21]=s("label",{class:"label"},"Region",-1)),s("div",ks,[o(s("input",{"onUpdate:modelValue":e[4]||(e[4]=t=>a.addressRegion=t),class:"input",type:"text",placeholder:"Region"},null,512),[[i,a.addressRegion]])])])]),s("div",xs,[s("div",Us,[e[22]||(e[22]=s("label",{class:"label"},"City",-1)),s("div",ws,[o(s("input",{"onUpdate:modelValue":e[5]||(e[5]=t=>a.addressCity=t),class:"input",type:"text",placeholder:"City"},null,512),[[i,a.addressCity]])])])])]),s("div",Vs,[s("div",Ps,[s("div",Ss,[e[23]||(e[23]=s("label",{class:"label"},"Street",-1)),s("div",Es,[o(s("input",{"onUpdate:modelValue":e[6]||(e[6]=t=>a.addressStreet=t),class:"input",type:"text",placeholder:"Street address"},null,512),[[i,a.addressStreet]])])])]),s("div",Ds,[s("div",Fs,[e[24]||(e[24]=s("label",{class:"label"},"Postal Code",-1)),s("div",Ns,[o(s("input",{"onUpdate:modelValue":e[7]||(e[7]=t=>a.addressPostalCode=t),class:"input",type:"text",placeholder:"12345"},null,512),[[i,a.addressPostalCode]])])])])])]),s("div",Ts,[s("div",As,[e[28]||(e[28]=s("label",{class:"label"},"Company Image",-1)),s("div",Is,[s("div",Rs,[s("label",Xs,[s("input",{class:"file-input",type:"file",onChange:X,accept:"image/*"},null,32),e[27]||(e[27]=s("span",{class:"file-cta"},[s("span",{class:"file-icon"},[s("i",{class:"fas fa-upload"})]),s("span",{class:"file-label"},"Choose image...")],-1)),s("span",Bs,p(P.value||"No file selected"),1)])])]),a.imageUrl?(r(),d("div",Ms,[s("figure",js,[s("img",{src:a.imageUrl,alt:"Company image",class:"is-rounded"},null,8,Ls)])])):E("",!0)]),s("div",Os,[s("div",$s,[s("label",qs,[o(s("input",{"onUpdate:modelValue":e[8]||(e[8]=t=>a.isFeatured=t),type:"checkbox"},null,512),[[D,a.isFeatured]]),e[29]||(e[29]=w(" Featured Company "))])]),e[30]||(e[30]=s("p",{class:"help"},"Featured companies appear prominently on the site",-1))])])]),s("div",zs,[e[36]||(e[36]=s("h3",{class:"subtitle"},"Finance Information",-1)),s("div",Js,[s("div",Ws,[s("div",Gs,[e[31]||(e[31]=s("label",{class:"label"},"Bank Name",-1)),s("div",Hs,[o(s("input",{"onUpdate:modelValue":e[9]||(e[9]=t=>u.bankName=t),class:"input",type:"text",placeholder:"Enter bank name"},null,512),[[i,u.bankName]])])]),s("div",Ks,[e[32]||(e[32]=s("label",{class:"label"},"Bank Account",-1)),s("div",Qs,[o(s("input",{"onUpdate:modelValue":e[10]||(e[10]=t=>u.bankAccount=t),class:"input",type:"text",placeholder:"Enter bank account number"},null,512),[[i,u.bankAccount]])])])]),s("div",Ys,[s("div",Zs,[e[33]||(e[33]=s("label",{class:"label"},"Bank Code",-1)),s("div",se,[o(s("input",{"onUpdate:modelValue":e[11]||(e[11]=t=>u.bankCode=t),class:"input",type:"text",placeholder:"Enter bank code"},null,512),[[i,u.bankCode]])])]),s("div",ee,[e[34]||(e[34]=s("label",{class:"label"},"Tax ID",-1)),s("div",te,[o(s("input",{"onUpdate:modelValue":e[12]||(e[12]=t=>u.taxId=t),class:"input",type:"text",placeholder:"Enter tax ID"},null,512),[[i,u.taxId]])])])])]),s("div",le,[e[35]||(e[35]=s("label",{class:"label"},"Payment Details",-1)),s("div",ae,[o(s("textarea",{"onUpdate:modelValue":e[13]||(e[13]=t=>u.paymentDetails=t),class:"textarea",placeholder:"Enter payment details",rows:"3"},null,512),[[i,u.paymentDetails]])])])]),s("div",ne,[e[39]||(e[39]=s("h3",{class:"subtitle"},"Schedule Information",-1)),s("div",oe,[s("table",ie,[e[38]||(e[38]=s("thead",null,[s("tr",null,[s("th",null,"Day"),s("th",null,"Open Time"),s("th",null,"Close Time"),s("th",null,"Status")])],-1)),s("tbody",null,[(r(!0),d(F,null,N(g.value,(t,J)=>(r(),d("tr",{key:t.day},[s("td",null,p(M(t.day)),1),s("td",null,[o(s("input",{"onUpdate:modelValue":f=>t.openTime=f,class:"input",type:"time",disabled:t.isClosed},null,8,de),[[i,t.openTime]])]),s("td",null,[o(s("input",{"onUpdate:modelValue":f=>t.closeTime=f,class:"input",type:"time",disabled:t.isClosed},null,8,re),[[i,t.closeTime]])]),s("td",null,[s("label",ue,[o(s("input",{"onUpdate:modelValue":f=>t.isClosed=f,type:"checkbox"},null,8,ce),[[D,t.isClosed]]),e[37]||(e[37]=w(" Closed "))])])]))),128))])])])]),s("div",me,[e[47]||(e[47]=s("h3",{class:"subtitle"},"Company Products",-1)),U.value?(r(),d("div",pe,e[40]||(e[40]=[s("div",{class:"loader"},null,-1),s("p",null,"Loading products...",-1)]))):y.value.length===0?(r(),d("div",ve,e[41]||(e[41]=[s("p",{class:"has-text-grey"},"No products found for this company.",-1)]))):(r(),d("div",ye,[s("table",fe,[e[46]||(e[46]=s("thead",null,[s("tr",null,[s("th",null,"Image"),s("th",null,"Name"),s("th",null,"Category"),s("th",null,"Price"),s("th",null,"Stock"),s("th",null,"Status"),s("th",null,"Actions")])],-1)),s("tbody",null,[(r(!0),d(F,null,N(y.value,t=>(r(),d("tr",{key:t.id},[s("td",null,[s("figure",be,[s("img",{src:t.imageUrl||"/placeholder-product.jpg",alt:t.name,class:"is-rounded"},null,8,_e)])]),s("td",null,[s("strong",null,p(t.name),1),e[42]||(e[42]=s("br",null,null,-1)),s("small",ge,p(t.slug),1)]),s("td",null,p(t.categoryName||"N/A"),1),s("td",null,[s("span",he,p(O(t.priceAmount,t.priceCurrency)),1)]),s("td",null,[s("span",{class:V(["tag",$(t.stock)])},p(t.stock),3)]),s("td",null,[s("span",{class:V(["tag",q(t.status)])},p(z(t.status)),3)]),s("td",null,[s("div",Ce,[h(c,{to:`/admin/products/${t.id}`,class:"button is-info is-small"},{default:C(()=>e[43]||(e[43]=[s("span",{class:"icon"},[s("i",{class:"fas fa-eye"})],-1),s("span",null,"View",-1)])),_:2},1032,["to"]),h(c,{to:`/admin/products/${t.id}/edit`,class:"button is-warning is-small"},{default:C(()=>e[44]||(e[44]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit",-1)])),_:2},1032,["to"]),s("button",{onClick:J=>L(t.id),class:"button is-danger is-small",disabled:_.value===t.id},[s("span",xe,[_.value!==t.id?(r(),d("i",Ue)):(r(),d("i",we))]),e[45]||(e[45]=s("span",null,"Delete",-1))],8,ke)])])]))),128))])])]))]),s("div",Ve,[s("div",Pe,[s("button",{type:"submit",class:V(["button is-primary",{"is-loading":b.value}]),disabled:b.value},e[48]||(e[48]=[s("span",{class:"icon"},[s("i",{class:"fas fa-save"})],-1),s("span",null,"Save Changes",-1)]),10,Se)]),s("div",Ee,[h(c,{to:"/admin/companies",class:"button"},{default:C(()=>e[49]||(e[49]=[w(" Cancel ")])),_:1})])])],32)])):E("",!0)])}}},Ie=W(De,[["__scopeId","data-v-9cbf2dba"]]);export{Ie as default};
