import{_ as N,g as v,i as x,c as t,a as s,k as d,b as I,w as S,d as b,r as T,t as l,n as p,F as j,p as B,A as R,B as F,f as M,o}from"./index-BtyG65bR.js";import{c as y}from"./companies-Cx9IcxUK.js";const P={class:"company-detail"},U={class:"level"},E={class:"level-left"},V={class:"level-item"},$={class:"breadcrumb"},L={class:"is-active"},O={class:"level-right"},z={class:"level-item"},W={key:0,class:"has-text-centered"},q={key:1,class:"notification is-danger"},G={key:2},H={class:"columns"},J={class:"column is-8"},K={class:"card"},Q={class:"card-content"},X={class:"columns"},Y={class:"column is-6"},Z={class:"field"},ss={class:"info-value"},es={class:"field"},as={class:"info-value"},ls={class:"field"},ts={class:"info-value"},os={class:"field"},is={class:"info-value"},ns={class:"column is-6"},ds={class:"field"},cs={class:"info-value"},rs={key:0,class:"tag is-info ml-1"},us={key:0,class:"field"},vs={class:"info-value"},ps={key:1,class:"field"},ms={class:"info-value"},_s={key:0,class:"field"},fs={class:"info-value"},ys={class:"field"},bs={class:"content"},gs={key:0,class:"card mt-4"},hs={class:"card-content"},Cs={class:"columns"},ks={class:"column is-6"},Ds={class:"field"},ws={class:"info-value"},As={class:"field"},Ns={class:"info-value"},xs={class:"column is-6"},Is={class:"field"},Ss={class:"info-value"},Ts={class:"field"},js={class:"info-value"},Bs={key:0,class:"field"},Rs={class:"info-value"},Fs={key:1,class:"card mt-4"},Ms={class:"card-content"},Ps={class:"table-container"},Us={class:"table is-fullwidth"},Es={class:"column is-4"},Vs={class:"card"},$s={class:"card-content"},Ls={class:"buttons is-fullwidth"},Os=["disabled"],zs={key:0,class:"card mt-4"},Ws={class:"card-content"},qs={class:"image"},Gs=["src","alt"],Hs={class:"card mt-4"},Js={class:"card-content"},Ks={class:"field"},Qs={class:"info-value"},Xs={class:"field"},Ys={class:"info-value"},Zs={key:0,class:"field"},se={class:"image is-128x128"},ee=["src","alt"],ae={class:"modal-card"},le={class:"modal-card-head"},te={class:"modal-card-body"},oe={class:"field"},ie={class:"control"},ne={class:"modal-card-foot"},de={__name:"CompanyDetail",setup(ce){const g=M(),a=v({}),_=v(!1),c=v(null),r=v(!1),u=v(!1),m=v(""),f=async()=>{_.value=!0,c.value=null;try{const n=await y.getDetailedCompany(g.params.id);a.value=n.data}catch(n){c.value=n.message||"Failed to load company details"}finally{_.value=!1}},h=async()=>{r.value=!0;try{await y.approveCompany(a.value.id),await f()}catch(n){c.value=n.message||"Failed to approve company"}finally{r.value=!1}},C=async()=>{if(!m.value.trim()){c.value="Please provide a rejection reason";return}r.value=!0;try{await y.rejectCompany(a.value.id,m.value),u.value=!1,m.value="",await f()}catch(n){c.value=n.message||"Failed to reject company"}finally{r.value=!1}},k=n=>new Date(n).toLocaleString(),D=n=>n?"is-success":"is-warning",w=n=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][n]||"Unknown";return x(()=>{f()}),(n,e)=>{const A=T("router-link");return o(),t("div",P,[s("div",U,[s("div",E,[s("div",V,[s("nav",$,[s("ul",null,[s("li",null,[I(A,{to:"/admin/companies"},{default:S(()=>e[6]||(e[6]=[b("Companies")])),_:1})]),s("li",L,[s("a",null,l(a.value.name||"Company Details"),1)])])])])]),s("div",O,[s("div",z,[s("button",{class:p(["button is-primary",{"is-loading":_.value}]),onClick:f},e[7]||(e[7]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Refresh",-1)]),2)])])]),_.value&&!a.value.id?(o(),t("div",W,e[8]||(e[8]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1),s("p",{class:"mt-3"},"Loading company details...",-1)]))):c.value?(o(),t("div",q,[s("button",{class:"delete",onClick:e[0]||(e[0]=i=>c.value=null)}),b(" "+l(c.value),1)])):a.value.id?(o(),t("div",G,[s("div",H,[s("div",J,[s("div",K,[e[18]||(e[18]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Company Information")],-1)),s("div",Q,[s("div",X,[s("div",Y,[s("div",Z,[e[9]||(e[9]=s("label",{class:"label"},"Name",-1)),s("p",ss,l(a.value.name),1)]),s("div",es,[e[10]||(e[10]=s("label",{class:"label"},"Slug",-1)),s("p",as,l(a.value.slug),1)]),s("div",ls,[e[11]||(e[11]=s("label",{class:"label"},"Contact Email",-1)),s("p",ts,l(a.value.contactEmail),1)]),s("div",os,[e[12]||(e[12]=s("label",{class:"label"},"Contact Phone",-1)),s("p",is,l(a.value.contactPhone),1)])]),s("div",ns,[s("div",ds,[e[13]||(e[13]=s("label",{class:"label"},"Status",-1)),s("p",cs,[s("span",{class:p(["tag",D(a.value.isApproved)])},l(a.value.isApproved?"Approved":"Pending"),3),a.value.isFeatured?(o(),t("span",rs,"Featured")):d("",!0)])]),a.value.approvedAt?(o(),t("div",us,[e[14]||(e[14]=s("label",{class:"label"},"Approved At",-1)),s("p",vs,l(k(a.value.approvedAt)),1)])):d("",!0),a.value.approvedByUserId?(o(),t("div",ps,[e[15]||(e[15]=s("label",{class:"label"},"Approved By",-1)),s("p",ms,l(a.value.approvedByUserId),1)])):d("",!0)])]),a.value.description?(o(),t("div",_s,[e[16]||(e[16]=s("label",{class:"label"},"Description",-1)),s("p",fs,l(a.value.description),1)])):d("",!0),s("div",ys,[e[17]||(e[17]=s("label",{class:"label"},"Address",-1)),s("div",bs,[s("p",null,l(a.value.addressStreet),1),s("p",null,l(a.value.addressCity)+", "+l(a.value.addressRegion)+" "+l(a.value.addressPostalCode),1)])])])]),a.value.finance?(o(),t("div",gs,[e[24]||(e[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Finance Information")],-1)),s("div",hs,[s("div",Cs,[s("div",ks,[s("div",Ds,[e[19]||(e[19]=s("label",{class:"label"},"Bank Name",-1)),s("p",ws,l(a.value.finance.bankName||"Not provided"),1)]),s("div",As,[e[20]||(e[20]=s("label",{class:"label"},"Bank Account",-1)),s("p",Ns,l(a.value.finance.bankAccount||"Not provided"),1)])]),s("div",xs,[s("div",Is,[e[21]||(e[21]=s("label",{class:"label"},"Bank Code",-1)),s("p",Ss,l(a.value.finance.bankCode||"Not provided"),1)]),s("div",Ts,[e[22]||(e[22]=s("label",{class:"label"},"Tax ID",-1)),s("p",js,l(a.value.finance.taxId||"Not provided"),1)])])]),a.value.finance.paymentDetails?(o(),t("div",Bs,[e[23]||(e[23]=s("label",{class:"label"},"Payment Details",-1)),s("p",Rs,l(a.value.finance.paymentDetails),1)])):d("",!0)])])):d("",!0),a.value.schedule&&a.value.schedule.length>0?(o(),t("div",Fs,[e[26]||(e[26]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Schedule Information")],-1)),s("div",Ms,[s("div",Ps,[s("table",Us,[e[25]||(e[25]=s("thead",null,[s("tr",null,[s("th",null,"Day"),s("th",null,"Open Time"),s("th",null,"Close Time"),s("th",null,"Status")])],-1)),s("tbody",null,[(o(!0),t(j,null,B(a.value.schedule,i=>(o(),t("tr",{key:i.day},[s("td",null,l(w(i.day)),1),s("td",null,l(i.isClosed?"-":i.openTime),1),s("td",null,l(i.isClosed?"-":i.closeTime),1),s("td",null,[s("span",{class:p(["tag",i.isClosed?"is-danger":"is-success"])},l(i.isClosed?"Closed":"Open"),3)])]))),128))])])])])])):d("",!0)]),s("div",Es,[s("div",Vs,[e[29]||(e[29]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Actions")],-1)),s("div",$s,[s("div",Ls,[a.value.isApproved?d("",!0):(o(),t("button",{key:0,class:p(["button is-success is-fullwidth",{"is-loading":r.value}]),onClick:h},e[27]||(e[27]=[s("span",{class:"icon"},[s("i",{class:"fas fa-check"})],-1),s("span",null,"Approve Company",-1)]),2)),a.value.isApproved?d("",!0):(o(),t("button",{key:1,class:"button is-danger is-fullwidth",onClick:e[1]||(e[1]=i=>u.value=!0),disabled:r.value},e[28]||(e[28]=[s("span",{class:"icon"},[s("i",{class:"fas fa-times"})],-1),s("span",null,"Reject Company",-1)]),8,Os))])])]),a.value.imageUrl?(o(),t("div",zs,[e[30]||(e[30]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Company Image")],-1)),s("div",Ws,[s("figure",qs,[s("img",{src:a.value.imageUrl,alt:a.value.name},null,8,Gs)])])])):d("",!0),s("div",Hs,[e[34]||(e[34]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"SEO Information")],-1)),s("div",Js,[s("div",Ks,[e[31]||(e[31]=s("label",{class:"label"},"Meta Title",-1)),s("p",Qs,l(a.value.metaTitle||"Not set"),1)]),s("div",Xs,[e[32]||(e[32]=s("label",{class:"label"},"Meta Description",-1)),s("p",Ys,l(a.value.metaDescription||"Not set"),1)]),a.value.metaImage?(o(),t("div",Zs,[e[33]||(e[33]=s("label",{class:"label"},"Meta Image",-1)),s("figure",se,[s("img",{src:a.value.metaImage,alt:a.value.name},null,8,ee)])])):d("",!0)])])])])])):d("",!0),s("div",{class:p(["modal",{"is-active":u.value}])},[s("div",{class:"modal-background",onClick:e[2]||(e[2]=i=>u.value=!1)}),s("div",ae,[s("header",le,[e[35]||(e[35]=s("p",{class:"modal-card-title"},"Reject Company",-1)),s("button",{class:"delete",onClick:e[3]||(e[3]=i=>u.value=!1)})]),s("section",te,[s("div",oe,[e[36]||(e[36]=s("label",{class:"label"},"Rejection Reason",-1)),s("div",ie,[R(s("textarea",{class:"textarea","onUpdate:modelValue":e[4]||(e[4]=i=>m.value=i),placeholder:"Enter reason for rejection..."},"              ",512),[[F,m.value]])])])]),s("footer",ne,[s("button",{class:p(["button is-danger",{"is-loading":r.value}]),onClick:C}," Reject Company ",2),s("button",{class:"button",onClick:e[5]||(e[5]=i=>u.value=!1)},"Cancel")])])],2)])}}},ve=N(de,[["__scopeId","data-v-0212c271"]]);export{ve as default};
