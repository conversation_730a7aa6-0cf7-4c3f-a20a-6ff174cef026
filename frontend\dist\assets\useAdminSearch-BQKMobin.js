import{_ as X,g as P,h as N,x,c as h,o as p,a as u,k as E,n as R,t as _,A as V,B as w,F as $,p as M,C as Z,d as q,z as K}from"./index-DMg5qKr1.js";const ee={class:"search-and-filters"},te={class:"card mb-4"},ae={class:"card-content"},se={class:"columns is-multiline"},le={class:"field"},oe={class:"label"},ne={class:"control has-icons-left"},re=["placeholder"],ie={class:"field"},ce={class:"label"},de={class:"control"},ue={key:0,class:"select is-fullwidth"},ge=["onUpdate:modelValue","onChange"],he={key:0,value:""},pe=["value"],fe=["placeholder","onUpdate:modelValue","onInput"],me=["onUpdate:modelValue","onChange"],ve=["placeholder","onUpdate:modelValue","onInput"],ye={key:0,class:"column is-2"},Pe={class:"field",style:{"margin-top":"1.9rem"}},be={class:"buttons is-right"},_e={key:0,class:"level mb-4"},Se={class:"level-left"},Ae={class:"level-item"},Ie={key:0},Ce={__name:"SearchAndFilters",props:{filters:{type:Object,required:!0},filterFields:{type:Array,default:()=>[]},searchLabel:{type:String,default:"Search"},searchPlaceholder:{type:String,default:"Search..."},searchColumnClass:{type:String,default:"is-5"},showResetButton:{type:Boolean,default:!0},resetButtonText:{type:String,default:"Reset Filters"},showStatusBar:{type:Boolean,default:!0},totalItems:{type:Number,default:0},itemName:{type:String,default:"items"},loading:{type:Boolean,default:!1}},emits:["search-changed","filter-changed","reset-filters"],setup(f,{emit:D}){const b=f,I=D,i=P({...b.filters}),F=N(()=>Object.values(i.value).some(a=>a!==""&&a!==null&&a!==void 0)),U=N(()=>Object.entries(i.value).filter(([a,s])=>s!==""&&s!==null&&s!==void 0).reduce((a,[s,t])=>(a[s]=t,a),{})),k=()=>{I("search-changed",i.value.search)},S=(a,s)=>{I("filter-changed",a,s)},B=()=>{Object.keys(i.value).forEach(a=>{i.value[a]=""}),I("reset-filters")},v=a=>{if(a==="search")return"Search";const s=b.filterFields.find(t=>t.key===a);return s?s.label:a},A=(a,s)=>{const t=b.filterFields.find(o=>o.key===a);if(t&&t.type==="select"&&t.options){const o=t.options.find(j=>j.value===s);return o?o.label:s}return s};return x(()=>b.filters,a=>{i.value={...a}},{deep:!0,immediate:!0}),x(i,a=>{a.search!==b.filters.search&&I("search-changed",a.search),Object.keys(a).forEach(s=>{s!=="search"&&a[s]!==b.filters[s]&&I("filter-changed",s,a[s])})},{deep:!0}),(a,s)=>(p(),h("div",ee,[u("div",te,[u("div",ae,[u("div",se,[u("div",{class:R(["column",f.searchColumnClass])},[u("div",le,[u("label",oe,_(f.searchLabel),1),u("div",ne,[V(u("input",{class:"input",type:"text",placeholder:f.searchPlaceholder,"onUpdate:modelValue":s[0]||(s[0]=t=>i.value.search=t),onInput:k},null,40,re),[[w,i.value.search]]),s[1]||(s[1]=u("span",{class:"icon is-small is-left"},[u("i",{class:"fas fa-search"})],-1))])])],2),(p(!0),h($,null,M(f.filterFields,t=>(p(),h("div",{key:t.key,class:R(["column",t.columnClass||"is-3"])},[u("div",ie,[u("label",ce,_(t.label),1),u("div",de,[t.type==="select"?(p(),h("div",ue,[V(u("select",{"onUpdate:modelValue":o=>i.value[t.key]=o,onChange:o=>S(t.key,i.value[t.key])},[t.allOption!==!1?(p(),h("option",he,_(t.allOption||`All ${t.label}`),1)):E("",!0),(p(!0),h($,null,M(t.options,o=>(p(),h("option",{key:o.value,value:o.value},_(o.label),9,pe))),128))],40,ge),[[Z,i.value[t.key]]])])):t.type==="text"?V((p(),h("input",{key:1,class:"input",type:"text",placeholder:t.placeholder,"onUpdate:modelValue":o=>i.value[t.key]=o,onInput:o=>S(t.key,i.value[t.key])},null,40,fe)),[[w,i.value[t.key]]]):t.type==="date"?V((p(),h("input",{key:2,class:"input",type:"date","onUpdate:modelValue":o=>i.value[t.key]=o,onChange:o=>S(t.key,i.value[t.key])},null,40,me)),[[w,i.value[t.key]]]):t.type==="number"?V((p(),h("input",{key:3,class:"input",type:"number",placeholder:t.placeholder,"onUpdate:modelValue":o=>i.value[t.key]=o,onInput:o=>S(t.key,i.value[t.key])},null,40,ve)),[[w,i.value[t.key]]]):E("",!0)])])],2))),128)),f.showResetButton?(p(),h("div",ye,[u("div",Pe,[u("div",be,[u("button",{class:R(["button is-light",{"is-loading":f.loading}]),onClick:B},_(f.resetButtonText),3)])])])):E("",!0)])])]),f.showStatusBar&&(f.totalItems>0||F.value)?(p(),h("div",_e,[u("div",Se,[u("div",Ae,[u("p",null,[u("strong",null,_(f.totalItems),1),q(" "+_(f.itemName)+" found ",1),F.value?(p(),h("span",Ie,[s[2]||(s[2]=q(" with filters: ")),(p(!0),h($,null,M(U.value,(t,o)=>(p(),h("span",{key:o,class:"tag is-info is-light mr-1"},_(v(o))+": "+_(A(o,t)),1))),128))])):E("",!0)])])])])):E("",!0)]))}},Ue=X(Ce,[["__scopeId","data-v-5865715b"]]);function Be(f={}){const{fetchFunction:D,defaultFilters:b={},debounceTime:I=300,defaultPageSize:i=15,clientSideSearch:F=!1}=f,U=P([]),k=P(!1),S=P(null),B=P(!0),v=P(1),A=P(1),a=P(0),s=P(i),t=K({search:"",...b}),o=P("createdAt"),j=P("desc"),Q=N(()=>Object.values(t).some(n=>n!==""&&n!==null&&n!==void 0)),T=N(()=>Object.entries(t).filter(([n,m])=>m!==""&&m!==null&&m!==void 0).reduce((n,[m,y])=>(n[m]=y,n),{})),O=async(n=1)=>{var m,y,C;(B.value||v.value!==n)&&(k.value=!0),v.value=n,S.value=null;try{const l={page:v.value,pageSize:s.value,orderBy:o.value,descending:j.value==="desc"};t.search&&t.search.trim()&&(l.filter=t.search.trim(),l.search=t.search.trim()),F?Object.entries(T.value).forEach(([d,r])=>{d!=="search"&&(d==="sortBy"?l.orderBy=r:d==="sortOrder"?l.descending=r==="desc":d==="status"?(console.log("🔄 Converting status filter (client-side):",r),l.status=r):d==="categoryId"?l.categoryId=r:d==="categoryIds"?Array.isArray(r)&&r.length>0&&(l.categoryIds=r.join(",")):d==="stock"?l.stock=r:l[d]=r)}):Object.entries(T.value).forEach(([d,r])=>{d!=="search"&&(d==="sortBy"?l.orderBy=r:d==="sortOrder"?l.descending=r==="desc":d==="status"?(console.log("🔄 Converting status filter:",r),l.status=r):d==="categoryId"?l.categoryId=r:d==="categoryIds"?Array.isArray(r)&&r.length>0&&(l.categoryIds=r.join(",")):d==="stock"?l.stock=r:l[d]=r)}),console.log("Fetching data with params:",l);const e=await D(l);let g=[],c={};if(console.log("Processing response:",e),e&&(e.data&&Array.isArray(e.data)&&e.pagination?(console.log("Using updated service format with pagination"),g=e.data,c={total:e.pagination.total||e.total,page:e.pagination.page||e.currentPage,totalPages:e.pagination.totalPages||e.totalPages,perPage:e.pagination.pageSize||e.pagination.limit}):e.data&&Array.isArray(e.data)&&(e.total!==void 0||e.totalPages!==void 0)?(console.log("Using data array with separate pagination"),g=e.data,c={total:e.total||e.totalItems,page:e.currentPage||e.page,totalPages:e.totalPages||e.lastPage,perPage:e.pageSize||e.perPage}):e.categories&&Array.isArray(e.categories)?(console.log("Using categories service format"),g=e.categories,c={total:e.totalCount||e.total,page:1,totalPages:1,perPage:g.length}):e.items&&Array.isArray(e.items)?(console.log("Using items array format"),g=e.items,c={total:e.total||e.totalItems||g.length,page:e.currentPage||e.page||1,totalPages:e.totalPages||e.lastPage||1,perPage:e.pageSize||e.perPage||g.length}):e.success&&e.data&&e.data.data?(console.log("Using ApiResponse<PaginatedResponse> format"),g=e.data.data,c={total:e.data.total,page:e.data.currentPage,totalPages:e.data.lastPage,perPage:e.data.perPage}):e.users?(console.log("Using Users service format"),g=e.users,c=e.pagination||{}):Array.isArray(e)?(console.log("Using direct array format"),g=e,c={total:e.length,page:1,totalPages:1,perPage:e.length}):e.data?(console.log("Using legacy data format"),Array.isArray(e.data)?(g=e.data,c=e.pagination||{total:e.data.length,page:1,totalPages:1,perPage:e.data.length}):e.data.data&&(g=e.data.data,c=e.data)):(console.warn("Unknown response format:",e),g=[],c={})),F&&t.search){const d=t.search.toLowerCase();g=g.filter(r=>Y(r,d))}if(U.value=g,F&&t.search)a.value=g.length,A.value=Math.ceil(a.value/s.value)||1;else{const d=c.total||c.totalItems||c.Total||g.length;a.value=d;const r=c.perPage||c.pageSize||s.value,W=Math.ceil(d/r);A.value=c.totalPages||c.lastPage||c.LastPage||W||1,v.value=c.page||c.currentPage||c.CurrentPage||n,console.log("Pagination updated:",{totalItems:a.value,totalPages:A.value,currentPage:v.value,itemsPerPage:r,paginationData:c})}console.log("Data fetched successfully:",{itemsCount:U.value.length,totalItems:a.value,totalPages:A.value,currentPage:v.value})}catch(l){console.error("Error fetching data:",l);let e="Failed to load data";l.code==="ECONNREFUSED"||l.message.includes("ECONNREFUSED")?e="Cannot connect to server. Please ensure the backend is running.":((m=l.response)==null?void 0:m.status)===401?e="Authentication required. Please log in.":((y=l.response)==null?void 0:y.status)===403?e="Access denied. You do not have permission to view this data.":((C=l.response)==null?void 0:C.status)===404?e="API endpoint not found.":l.message&&(e=l.message),S.value=e,U.value=[],A.value=1,a.value=0}finally{k.value=!1,B.value&&(B.value=!1)}},Y=(n,m)=>["name","username","email","title","description","contactEmail","contactPhone","slug"].some(C=>{const l=G(n,C);return l&&l.toString().toLowerCase().includes(m)}),G=(n,m)=>m.split(".").reduce((y,C)=>y&&y[C]!==void 0?y[C]:null,n),H=()=>{Object.keys(t).forEach(n=>{n==="search"?t[n]="":t[n]=b[n]||""}),v.value=1,O(1)},J=n=>{O(n)};let z=null;const L=()=>{z&&clearTimeout(z),z=setTimeout(()=>{v.value=1,O(1)},I)};return x(()=>t.search,()=>{console.log("Search changed:",t.search),L()}),Object.keys(b).forEach(n=>{n!=="search"&&x(()=>t[n],(m,y)=>{m!==y&&(console.log(`Filter ${n} changed:`,t[n]),v.value=1,O(1))})}),{items:U,loading:k,error:S,isFirstLoad:B,currentPage:v,totalPages:A,totalItems:a,pageSize:s,filters:t,hasFilters:Q,activeFilters:T,sortBy:o,sortOrder:j,fetchData:O,resetFilters:H,handlePageChange:J,debouncedSearch:L}}export{Ue as S,Be as u};
