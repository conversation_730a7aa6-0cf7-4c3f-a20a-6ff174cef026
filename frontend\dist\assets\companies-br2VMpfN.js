import{q as t}from"./index-DMg5qKr1.js";const c={async getCompanies(r={}){var n,o;try{console.log("Requesting companies with params:",r);const e={};r.page&&(e.page=r.page),r.pageSize&&(e.pageSize=r.pageSize),r.orderBy&&(e.orderBy=r.orderBy),r.descending!==void 0&&(e.descending=r.descending),r.search&&r.search.trim()!==""?e.filter=r.search.trim():r.filter&&r.filter.trim()!==""&&(e.filter=r.filter.trim()),r.featured&&r.featured.trim()!==""&&(e.isFeatured=r.featured==="true"),console.log("Final API params for companies:",e);let a;try{if(a=await t.get("/api/admin/companies",{params:e}),console.log("Admin companies API response:",a.data),a.data&&a.data.data)return a.data.data}catch(i){return console.warn("Admin companies endpoint failed, falling back to public endpoint:",i.message),a=await t.get("/api/companies",{params:e}),console.log("Public companies API response:",a.data),a.data}return a.data}catch(e){throw console.error("Error fetching companies:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to load companies")}},async getPendingCompanies(r={}){var n,o;try{console.log("Requesting pending companies with params:",r);const e={};r.page&&(e.page=r.page),r.pageSize&&(e.pageSize=r.pageSize),r.orderBy&&(e.orderBy=r.orderBy),r.descending!==void 0&&(e.descending=r.descending),r.search&&r.search.trim()!==""?e.filter=r.search.trim():r.filter&&r.filter.trim()!==""&&(e.filter=r.filter.trim()),console.log("Final API params for pending companies:",e);const a=await t.get("/api/admin/companies/pending",{params:e});return console.log("Pending companies API response:",a.data),a.data}catch(e){throw console.error("Error fetching pending companies:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to load pending companies")}},async getCompanyById(r){var n,o;try{return(await t.get(`/api/admin/companies/${r}`)).data}catch(e){throw console.error("Error fetching company:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to load company details")}},async approveCompany(r){var n,o;try{return(await t.post(`/api/admin/companies/${r}/approve`)).data}catch(e){throw console.error("Error approving company:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to approve company")}},async rejectCompany(r,n=""){var o,e;try{return(await t.post(`/api/admin/companies/${r}/reject`,{reason:n})).data}catch(a){throw console.error("Error rejecting company:",a),new Error(((e=(o=a.response)==null?void 0:o.data)==null?void 0:e.message)||"Failed to reject company")}},async deleteCompany(r){var n,o;try{return(await t.delete(`/api/admin/companies/${r}`)).data}catch(e){throw console.error("Error deleting company:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to delete company")}},async getCompany(r){var n,o;try{return(await t.get(`/api/admin/companies/${r}`)).data}catch(e){throw console.error("Error getting company:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to get company")}},async getDetailedCompany(r){var n,o;try{return(await t.get(`/api/admin/companies/${r}/detailed`)).data}catch(e){throw console.error("Error getting detailed company:",e),new Error(((o=(n=e.response)==null?void 0:n.data)==null?void 0:o.message)||"Failed to get detailed company")}},async updateCompany(r,n){var o,e;try{return(await t.put(`/api/admin/companies/${r}`,n)).data}catch(a){throw console.error("Error updating company:",a),new Error(((e=(o=a.response)==null?void 0:o.data)==null?void 0:e.message)||"Failed to update company")}},async updateDetailedCompany(r,n){var o,e;try{return(await t.put(`/api/admin/companies/${r}/detailed`,n)).data}catch(a){throw console.error("Error updating detailed company:",a),new Error(((e=(o=a.response)==null?void 0:o.data)==null?void 0:e.message)||"Failed to update detailed company")}}};export{c};
