﻿using Bogus;
using Marketplace.Domain.Entities;
using Marketplace.Domain.ValueObjects;
using Marketplace.Infrastructure.Persistence;
using Marketplace.Infrastructure.Services.Auth;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace Marketplace.Infrastructure.DatabaseSeeder;

public class DatabaseSeeder
{
    private readonly MarketplaceDbContext _context;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ILogger<DatabaseSeeder> _logger;
    private readonly Random _random = new Random();

    public DatabaseSeeder(MarketplaceDbContext context, IPasswordHasher passwordHasher, ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _passwordHasher = passwordHasher;
        _logger = logger;
    }

    public void Seed(int count = 10, int productCount = 40)
    {
        // Create admin user if it doesn't exist
        var adminUser = CreateAdminUserIfNotExists();

        // Create regular users if they don't exist
        var users = CreateUsersIfNotExist(count);

        // Додаємо адміністратора до списку користувачів
        var allUsers = new List<User>(users);
        if (adminUser != null)
        {
            allUsers.Add(adminUser);
        }

        // Створюємо адреси
        var addresses = CreateAddressesIfNotExist(allUsers, count);

        // Створюємо категорії
        var categories = CreateCategoriesIfNotExist(140);

        // Створюємо компанії
        var companies = CreateCompaniesIfNotExist(addresses, allUsers, count);

        // Створюємо продукти
        var products = CreateProductsIfNotExist(companies, categories, allUsers, count * 3);

        // Зображення продуктів створюються через API при завантаженні файлів

        // Створюємо рейтинги та відгуки
        var ratings = CreateRatingsIfNotExist(products, allUsers, count * 2);
        CreateReviewsIfNotExist(products, allUsers, ratings, count);

        // Створюємо методи доставки
        var shippingMethods = CreateShippingMethodsIfNotExist();

        // Створюємо купони
        var coupons = CreateCouponsIfNotExist(count);

        // Створюємо замовлення та пов'язані сутності
        var orders = CreateOrdersIfNotExist(allUsers, addresses, shippingMethods, count * 2);
        CreateOrderItemsIfNotExist(orders, products, count * 5);
        CreateOrderCouponsIfNotExist(orders, coupons);
        CreatePaymentsIfNotExist(orders);

        // Створюємо кошики та елементи кошиків
        var carts = CreateCartsIfNotExist(allUsers, count);
        CreateCartItemsIfNotExist(carts, products, count * 3);

        // Створюємо списки бажань
        var wishlists = CreateWishlistsIfNotExist(allUsers, count);
        CreateWishlistItemsIfNotExist(wishlists, products, count * 3);

        // Створюємо чати та повідомлення
        var chats = CreateChatsIfNotExist(allUsers, companies, count);
        CreateMessagesIfNotExist(chats, allUsers, count * 5);

        // Створюємо сповіщення
        CreateNotificationsIfNotExist(allUsers, count * 3);

        // Створюємо запити на продавця
        CreateSellerRequestsIfNotExist(allUsers, count);

        // Створюємо розклад компаній
        CreateCompanySchedulesIfNotExist(companies);

        // Створюємо користувачів компаній
        CreateCompanyUsersIfNotExist(companies, allUsers);

        // Створюємо фінансові налаштування компаній
        CreateCompanyFinancesIfNotExist(companies);

        // Створюємо улюблені
        CreateFavoritesIfNotExist(allUsers, products, count * 2);

        Console.WriteLine("Database seeding completed successfully.");
    }

    private User? CreateAdminUserIfNotExists()
    {
        // Check if admin user exists
        var adminUser = _context.Users.FirstOrDefault(u => u.Role == Role.Admin);

        if (adminUser == null)
        {
            // Hash the password
            string hashedPassword = _passwordHasher.HashPassword("Admin123!");

            // Create default admin user
            adminUser = new User(
                "admin",
                new Email("<EMAIL>"),
                new Password(hashedPassword),
                Role.Admin
            );

            // Встановлюємо додаткові властивості
            adminUser.Update(
                null, null, null, null, null,
                DateTime.UtcNow, null, null, DateTime.UtcNow
            );
            adminUser.Approve(adminUser.Id);

            _context.Users.Add(adminUser);
            _context.SaveChanges();

            Console.WriteLine("Default admin user created:");
            Console.WriteLine($"Username: {adminUser.Username}");
            Console.WriteLine($"Password: Admin123!");
            Console.WriteLine($"Email: {adminUser.Email}");

            return adminUser;
        }
        else
        {
            Console.WriteLine("Admin user already exists.");
            return adminUser;
        }
    }

    private List<User> CreateUsersIfNotExist(int count)
    {
        // Перевіряємо, чи є вже звичайні користувачі (не адміністратори)
        var existingUserCount = _context.Users.Count(u => u.Role == Role.Buyer);

        if (existingUserCount >= count)
        {
            Console.WriteLine($"Already have {existingUserCount} regular users.");
            return _context.Users.Where(u => u.Role == Role.Buyer).ToList();
        }

        // Створюємо додаткових користувачів, якщо потрібно
        var usersToCreate = count - existingUserCount;
        Console.WriteLine($"Creating {usersToCreate} additional regular users.");

        // Hash the password once for all users
        string hashedPassword = _passwordHasher.HashPassword("Password123!");

        var userFaker = new Faker<User>()
            .CustomInstantiator(f => new User(
                f.Internet.UserName(),
                new Email(f.Internet.Email()),
                new Password(hashedPassword), // Використовуємо хешований пароль для всіх тестових користувачів
                Role.Buyer
            ))
            .RuleFor(u => u.Id, f => Guid.NewGuid())
            .RuleFor(u => u.EmailConfirmed, f => true)
            .RuleFor(u => u.EmailConfirmedAt, f => DateTime.UtcNow)
            .RuleFor(u => u.LastSeenAt, f => DateTime.UtcNow);

        var newUsers = userFaker.Generate(usersToCreate);

        // Додаємо нових користувачів до бази даних
        _context.Users.AddRange(newUsers);
        _context.SaveChanges();

        Console.WriteLine($"Created {usersToCreate} regular users.");

        // Повертаємо всіх звичайних користувачів
        return _context.Users.Where(u => u.Role == Role.Buyer).ToList();
    }

    private List<Address> CreateAddressesIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже адреси
        var existingCount = _context.Addresses.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} addresses.");
            return _context.Addresses.ToList();
        }

        // Створюємо додаткові адреси, якщо потрібно
        var addressesToCreate = count - existingCount;
        Console.WriteLine($"Creating {addressesToCreate} additional addresses.");

        // Налаштування генератора фейкових даних для Address
        var addressFaker = new Faker<Address>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача для прив'язки адреси
                var randomUser = f.PickRandom(users);

                return new Address(
                    new AddressVO(
                        region: f.Address.State(),           // Регіон
                        city: f.Address.City(),              // Місто
                        street: f.Address.StreetName(),      // Вулиця
                        postalCode: f.Address.ZipCode("#####")      // Поштовий код
                    ),
                    userId: randomUser.Id
                );
            })
            .RuleFor(a => a.Id, f => Guid.NewGuid()); // Унікальний ID

        // Генерація даних
        var newAddresses = addressFaker.Generate(addressesToCreate);

        // Додавання до бази
        _context.Addresses.AddRange(newAddresses);
        _context.SaveChanges();

        Console.WriteLine($"Created {addressesToCreate} addresses.");

        // Повертаємо всі адреси
        return _context.Addresses.ToList();
    }

    private List<Category> CreateCategoriesIfNotExist(int count)
    {
        // Перевірка, чи є вже категорії
        var existingCount = _context.Categories.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} categories.");
            return _context.Categories.ToList();
        }

        // Створюємо додаткові категорії, якщо потрібно
        var categoriesToCreate = count - existingCount;
        Console.WriteLine($"Creating {categoriesToCreate} additional categories.");

        // Список категорій для створення
        var categoryNames = new Dictionary<string, Dictionary<string, List<string>>>
        {
            {
                "Ноутбуки та комп'ютери", new Dictionary<string, List<string>>
                {
                    { "Ноутбуки",
                        new List<string> {
                            "Геймерські ноутбуки",
                            "Бізнес-ноутбуки",
                            "Монітори 4K",
                            "Відеокарти",
                            "Клавіатури та миші" } },
                    { "Стаціонарні комп'ютери",
                        new List<string> {
                            "Системні блоки",
                            "Монітори",
                            "Клавіатури та миші" } },
                    { "Комплектуючі",
                        new List<string> {
                            "Материнські плати",
                            "Процесори",
                            "Оперативна пам'ять" } },
                    { "Аксесуари",
                        new List<string> {
                            "Мишки",
                            "Клавіатури",
                            "Навушники" }
                    }
                }
            },
            {
                "Смартфони, ТВ і електроніка", new Dictionary<string, List<string>>
                {
                    { "Смартфони",
                        new List<string> {
                            "Флагманські смартфони",
                            "Бюджетні смартфони",
                            "OLED телевізори",
                            "Bluetooth навушники",
                            "Зарядні пристрої" } },
                    { "Планшети",
                        new List<string> {
                            "Android планшети",
                            "iPad" } },
                    { "Телевізори",
                        new List<string> {
                            "Smart TV",
                            "4K телевізори" } },
                    { "Аудіо та відео",
                        new List<string> {
                            "Аудіосистеми",
                            "Проектори" } },
                    { "Аксесуари",
                        new List<string> {
                            "Кабелі",
                            "Зарядні пристрої" } }
                }
            },
            {
                "Побутова техніка", new Dictionary<string, List<string>>
                {
                    { "Холодильники",
                        new List<string> {
                            "Смарт-холодильники",
                            "Холодильники з морозильною камерою" } },
                    { "Пральні машини",
                        new List<string> {
                            "Пральні машини з сушкою",
                            "Автоматичні пральні машини" } },
                    { "Посудомийні машини",
                        new List<string> {
                            "Компактні посудомийні машини",
                            "Посудомийні машини з сушкою" } },
                    { "Кухонна техніка",
                        new List<string> {
                            "Кухонні комбайни",
                            "Мікрохвильові печі" } },
                    { "Аксесуари",
                        new List<string> {
                            "Кухонне приладдя",
                            "Посуд" } }
                }
            },
            {
                "Товари для дому", new Dictionary<string, List<string>>
                {
                    { "Меблі для вітальні",
                        new List<string> {
                            "Дивани",
                            "Крісла",
                            "Стільці" } },
                    { "Меблі для спальні",
                        new List<string> {
                            "Ліжка",
                            "Шафи",
                            "Комоди" } },
                    { "Кухонні меблі",
                        new List<string> {
                            "Кухонні столи",
                            "Кухонні шафи" } },
                    { "Офісні меблі",
                        new List<string> {
                            "Офісні столи",
                            "Офісні стільці" } },
                    { "Аксесуари",
                        new List<string> {
                            "Декор для дому",
                            "Освітлення" }
                    }
                }
            },
            {
                "Дача, сад і город", new Dictionary<string, List<string>>
                {
                    { "Іграшки",
                        new List<string> {
                            "Конструктори",
                            "Плюшеві іграшки" } },
                    { "Книги",
                        new List<string> {
                            "Художня література",
                            "Навчальна література" } },
                    { "Спортивний інвентар",
                        new List<string> {
                            "Футбольні м'ячі",
                            "Баскетбольні м'ячі" } },
                    { "Садовий інвентар",
                        new List<string> {
                            "Садові інструменти",
                            "Грядки та горщики" } },
                    { "Аксесуари",
                        new List<string> {
                            "Садові меблі",
                            "Освітлення для саду" }
                    }
                }
            },
            {
                "Спорт і хобі", new Dictionary<string, List<string>>
                {
                    { "Спортивний одяг",
                        new List<string> {
                            "Футболки",
                            "Шорти",
                            "Спортивні костюми" } },
                    { "Спортивне взуття",
                        new List<string> {
                            "Кросівки",
                            "Бутси" } },
                    { "Спортивні аксесуари",
                        new List<string> {
                            "Рюкзаки",
                            "Гамаші" } },
                    { "Хобі та творчість",
                        new List<string> {
                            "Набори для малювання",
                            "Книги з рукоділля" } },
                    { "Аксесуари",
                        new List<string> {
                            "Спортивні сумки",
                            "Годинники" }
                    }
                }
            },
            {
                "Краса та здоров'я", new Dictionary<string, List<string>>
                {
                    { "Косметика",
                        new List<string> {
                            "Макіяж",
                            "Догляд за шкірою" } },
                    { "Парфуми",
                        new List<string> {
                            "Чоловічі парфуми",
                            "Жіночі парфуми" } },
                    { "Засоби гігієни",
                        new List<string> {
                            "Зубні пасти",
                            "Шампуні" } },
                    { "Вітаміни та добавки",
                        new List<string> {
                            "Вітамінні комплекси",
                            "БАДи" } },
                    { "Аксесуари",
                        new List<string> {
                            "Косметички",
                            "Гребінці" }
                    }
                }
            },
            {
                "Одяг, взуття та прикраси", new Dictionary<string, List<string>>
                {
                    { "Жіночий одяг",
                        new List<string> {
                            "Сукні",
                            "Блузи",
                            "Спідниці" } },
                    { "Чоловічий одяг",
                        new List<string> {
                            "Сорочки",
                            "Штани",
                            "Куртки" } },
                    { "Дитячий одяг",
                        new List<string> {
                            "Дитячі сукні",
                            "Дитячі штани" } },
                    { "Взуття",
                        new List<string> {
                            "Черевики",
                            "Кросівки" } },
                    { "Прикраси",
                        new List<string> {
                            "Кільця",
                            "Браслети" }
                    }
                }
            }
        };



        // Обмежуємо кількість категорій до доступних назв
        categoriesToCreate = Math.Min(categoriesToCreate, categoryNames.Count);

        // Створюємо категорії
        var newCategories = new List<Category>();

        for (int i = 0; i < categoriesToCreate; i++)
        {
            var categoryName = categoryNames.Keys.ElementAt(i);
            var subCategories = categoryNames[categoryName];
            // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
            var slugValue = Regex.Replace(categoryName.ToLower(), @"[^a-z0-9-]", "-");
            // Замінюємо множинні дефіси на один
            slugValue = Regex.Replace(slugValue, @"-+", "-");
            // Видаляємо дефіси на початку та в кінці
            slugValue = slugValue.Trim('-');
            // Перевіряємо, чи не порожній слаг
            if (string.IsNullOrEmpty(slugValue))
            {
                slugValue = "category-" + i;
            }
            var slug = new Slug(slugValue);
            // Створюємо категорію без parentId
            var category = new Category(
                name: categoryName,
                slug: slug,
                description: $"Категорія для товарів типу {categoryName.ToLower()}",
                meta: new Meta(
                    title: $"{categoryName} - Найкращі товари",
                    description: $"Широкий вибір товарів у категорії {categoryName.ToLower()}"
                )
            );
            newCategories.Add(category);
            // Додаємо підкатегорії, якщо є
            foreach (var subCategory in subCategories)
            {
                // Створюємо слаг для підкатегорії
                var subSlugValue = Regex.Replace(subCategory.Key.ToLower(), @"[^a-z0-9-]", "-");
                subSlugValue = Regex.Replace(subSlugValue, @"-+", "-").Trim('-');
                if (string.IsNullOrEmpty(subSlugValue))
                {
                    subSlugValue = "subcategory-" + i;
                }
                var subSlug = new Slug(subSlugValue);

                // Створюємо підкатегорію
                var subCategoryEntity = new Category(
                    name: subCategory.Key,
                    slug: subSlug,
                    description: $"Підкатегорія для товарів типу {subCategory.Key.ToLower()}",
                    parentId: category.Id,
                    meta: new Meta(
                        title: $"{subCategory} - Найкращі товари",
                        description: $"Широкий вибір товар, у підкатегорії {subCategory.Key.ToLower()}",
                        image: new Url($"https://example.com/images/categories/{subSlug.Value}.jpg")
                    ));
                newCategories.Add(subCategoryEntity);
                // Додаємо підпідкатегорії, якщо є
                if (subCategory.Value != null && subCategory.Value.Count > 0)
                {
                    foreach (var subsubCategoryName in subCategory.Value)
                    {
                        // Створюємо слаг для підпідкатегорії
                        var subsubSlugValue = Regex.Replace(subsubCategoryName.ToLower(), @"[^a-z0-9-]", "-");
                        subsubSlugValue = Regex.Replace(subsubSlugValue, @"-+", "-").Trim('-');
                        if (string.IsNullOrEmpty(subsubSlugValue))
                        {
                            subsubSlugValue = "subsubcategory-" + i;
                        }
                        var subsubSlug = new Slug(subsubSlugValue);
                        // Створюємо підпідкатегорію
                        var subsubCategory = new Category(
                            name: subsubCategoryName,
                            slug: subsubSlug,
                            description: $"Підпідкатегорія для товарів типу {subsubCategoryName.ToLower()}",
                            parentId: subCategoryEntity.Id,
                            meta: new Meta(
                                title: $"{subsubCategoryName} - Найкращі товари",
                                description: $"Широкий вибір товарів у підпідкатегорії {subsubCategoryName.ToLower()}"
                            )
                        );
                        newCategories.Add(subsubCategory);
                    }
                }

            }
        }

        // Додавання до бази
        _context.Categories.AddRange(newCategories);
        _context.SaveChanges();

        Console.WriteLine($"Created {categoriesToCreate} categories.");

        // Повертаємо всі категорії
        return _context.Categories.ToList();
    }

    private List<Company> CreateCompaniesIfNotExist(List<Address> addresses, List<User> users, int count)
    {
        // Перевірка, чи є вже компанії
        var existingCount = _context.Companies.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} companies.");
            return _context.Companies.ToList();
        }

        // Створюємо додаткові компанії, якщо потрібно
        var companiesToCreate = count - existingCount;
        Console.WriteLine($"Creating {companiesToCreate} additional companies.");

        // Налаштування генератора фейкових даних для Company
        var companyFaker = new Faker<Company>()
            .CustomInstantiator(f =>
            {
                var name = f.Company.CompanyName();
                // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
                var slugValue = Regex.Replace(name.ToLower(), @"[^a-z0-9-]", "-");
                // Замінюємо множинні дефіси на один
                slugValue = Regex.Replace(slugValue, @"-+", "-");
                // Видаляємо дефіси на початку та в кінці
                slugValue = slugValue.Trim('-');
                // Перевіряємо, чи не порожній слаг
                if (string.IsNullOrEmpty(slugValue))
                {
                    slugValue = "company-" + f.Random.AlphaNumeric(5).ToLower();
                }
                var slug = new Slug(slugValue);

                // Створюємо нову адресу для компанії
                var addressVO = new AddressVO(
                    region: f.Address.State(),
                    city: f.Address.City(),
                    street: f.Address.StreetName(),
                    postalCode: f.Address.ZipCode("#####")
                );

                return new Company(
                    name: name,
                    slug: slug,
                    description: f.Lorem.Paragraph(),
                    image: new Url($"https://example.com/images/companies/{slug.Value}.jpg"),
                    contactEmail: new Email(f.Internet.Email(name)),
                    contactPhone: new Phone("+380" + f.Random.Number(10, 99).ToString() + f.Random.Number(1000000, 9999999).ToString()),
                    address: addressVO,
                    meta: new Meta(
                        title: $"{name} - Офіційний магазин",
                        description: $"Купуйте товари від {name} за найкращими цінами"
                    )
                );
            });

        // Генерація даних
        var newCompanies = companyFaker.Generate(companiesToCreate);

        // Знаходимо адміністратора для затвердження
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);

        // Встановлюємо всі компанії як затверджені (за сценарієм незатверджених компаній не може бути)
        foreach (var company in newCompanies)
        {
            // Всі компанії затверджені за замовчуванням
            if (admin != null)
            {
                company.ApprovedAt = DateTime.UtcNow;
                company.ApprovedByUserId = admin.Id;
            }

            if (new Random().Next(100) < 30) // 30% компаній рекомендовані
            {
                company.IsFeatured = true;
            }
        }

        // Додавання до бази
        _context.Companies.AddRange(newCompanies);
        _context.SaveChanges();

        Console.WriteLine($"Created {companiesToCreate} companies.");

        // Повертаємо всі компанії
        return _context.Companies.ToList();
    }

    private List<Product> CreateProductsIfNotExist(List<Company> companies, List<Category> categories, List<User> users, int count)
    {
        // Перевірка, чи є вже продукти
        var existingCount = _context.Products.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} products.");
            return _context.Products.ToList();
        }

        // Створюємо додаткові продукти, якщо потрібно
        var productsToCreate = count - existingCount;
        Console.WriteLine($"Creating {productsToCreate} additional products.");

        // Список можливих продуктів для різних категорій
        var productsByCategory = new Dictionary<string, List<string>>
        {
            { "Електроніка", new List<string> { "Смартфон", "Ноутбук", "Планшет", "Телевізор", "Навушники" } },
            { "Одяг", new List<string> { "Футболка", "Джинси", "Сукня", "Куртка", "Светр" } },
            { "Взуття", new List<string> { "Кросівки", "Туфлі", "Черевики", "Сандалі", "Мокасини" } },
            { "Меблі", new List<string> { "Диван", "Стіл", "Стілець", "Шафа", "Ліжко" } },
            { "Побутова техніка", new List<string> { "Холодильник", "Пральна машина", "Мікрохвильова піч", "Пилосос", "Кавоварка" } }
        };

        // Налаштування генератора фейкових даних для Product
        var productFaker = new Faker<Product>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкову компанію та категорію
                var company = f.PickRandom(companies);
                var category = f.PickRandom(categories);

                // Визначаємо назву продукту на основі категорії
                string productName;
                if (productsByCategory.ContainsKey(category.Name) && productsByCategory[category.Name].Any())
                {
                    var productType = f.PickRandom(productsByCategory[category.Name]);
                    productName = $"{productType} {f.Commerce.ProductAdjective()} {f.Random.AlphaNumeric(3)}";
                }
                else
                {
                    productName = f.Commerce.ProductName();
                }

                // Створюємо слаг, який відповідає регулярному виразу ^[a-z0-9-]+$
                var slugValue = Regex.Replace(productName.ToLower(), @"[^a-z0-9-]", "-");
                // Замінюємо множинні дефіси на один
                slugValue = Regex.Replace(slugValue, @"-+", "-");
                // Видаляємо дефіси на початку та в кінці
                slugValue = slugValue.Trim('-');
                // Перевіряємо, чи не порожній слаг
                if (string.IsNullOrEmpty(slugValue))
                {
                    slugValue = "product-" + f.Random.AlphaNumeric(5).ToLower();
                }
                var slug = new Slug(slugValue);

                // Створюємо атрибути продукту
                var attributes = new Dictionary<string, string>
                {
                    { "Колір", f.Commerce.Color() },
                    { "Матеріал", f.Commerce.ProductMaterial() },
                    { "Вага", $"{f.Random.Number(1, 10)} кг" },
                    { "Розміри", $"{f.Random.Number(10, 100)}x{f.Random.Number(10, 100)}x{f.Random.Number(10, 100)} см" }
                };

                return new Product(
                    companyId: company.Id,
                    name: productName,
                    slug: slug,
                    description: f.Commerce.ProductDescription(),
                    price: new Money(f.Random.Decimal(100, 10000), Currency.UAH),
                    stock: (uint)f.Random.Number(1, 100),
                    categoryId: category.Id,
                    attributes: attributes,
                    meta: new Meta(
                        title: $"{productName} - Купити в інтернет-магазині",
                        description: $"Купити {productName.ToLower()} за найкращою ціною. Доставка по всій Україні."
                    )
                );
            });

        // Генерація даних
        var newProducts = productFaker.Generate(productsToCreate);

        // Знаходимо адміністратора для затвердження
        var admin = users.FirstOrDefault(u => u.Role == Role.Admin);

        // Встановлюємо статус та затвердження для продуктів
        foreach (var product in newProducts)
        {
            // Встановлюємо статус продукту
            var statusRandom = new Random().Next(100);
            if (statusRandom < 80) // 80% продуктів затверджені
            {
                product.Status = ProductStatus.Approved;
            }
            else if (statusRandom < 90) // 10% продуктів очікують
            {
                product.Status = ProductStatus.Pending;
            }
            else // 10% продуктів відхилені
            {
                product.Status = ProductStatus.Rejected;
            }

            // Всі продукти повинні мати ApprovedByUserId, навіть якщо вони не затверджені
            if (admin != null)
            {
                product.ApprovedByUserId = admin.Id;

                // Встановлюємо затвердження продукту
                if (new Random().Next(100) < 70) // 70% продуктів затверджені
                {
                    product.IsApproved = true;
                    product.ApprovedAt = DateTime.UtcNow;
                }
            }

            // Встановлюємо дати створення та оновлення
            product.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 50) // 50% продуктів оновлені
            {
                product.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Products.AddRange(newProducts);
        _context.SaveChanges();

        Console.WriteLine($"Created {productsToCreate} products.");

        // Повертаємо всі продукти
        return _context.Products.ToList();
    }



    private List<Rating> CreateRatingsIfNotExist(List<Product> products, List<User> users, int count)
    {
        // Перевірка, чи є вже рейтинги
        var existingCount = _context.Ratings.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} ratings.");
            return _context.Ratings.ToList();
        }

        // Створюємо додаткові рейтинги, якщо потрібно
        var ratingsToCreate = count - existingCount;
        Console.WriteLine($"Creating {ratingsToCreate} additional ratings.");

        // Налаштування генератора фейкових даних для Rating
        var ratingFaker = new Faker<Rating>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий продукт та користувача
                var product = f.PickRandom(products);
                var user = f.PickRandom(users);

                // Генеруємо випадкові оцінки від 1 до 5
                var service = f.Random.Number(1, 5);
                var deliveryTime = f.Random.Number(1, 5);
                var accuracy = f.Random.Number(1, 5);

                // Генеруємо коментар (всі рейтинги мають коментар)
                string comment = f.Lorem.Sentence();

                return new Rating(
                    userId: user.Id,
                    productId: product.Id,
                    service: service,
                    deliveryTime: deliveryTime,
                    accuracy: accuracy,
                    comment: comment
                );
            });

        // Генерація даних
        var newRatings = ratingFaker.Generate(ratingsToCreate);

        // Встановлюємо дати створення та оновлення
        foreach (var rating in newRatings)
        {
            rating.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 20) // 20% рейтингів оновлені
            {
                rating.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Ratings.AddRange(newRatings);
        _context.SaveChanges();

        Console.WriteLine($"Created {ratingsToCreate} ratings.");

        // Повертаємо всі рейтинги
        return _context.Ratings.ToList();
    }

    private void CreateReviewsIfNotExist(List<Product> products, List<User> users, List<Rating> ratings, int count)
    {
        // Перевірка, чи є вже відгуки
        var existingCount = _context.Reviews.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} reviews.");
            return;
        }

        // Створюємо додаткові відгуки, якщо потрібно
        var reviewsToCreate = count - existingCount;
        Console.WriteLine($"Creating {reviewsToCreate} additional reviews.");

        // Налаштування генератора фейкових даних для Review
        var reviewFaker = new Faker<Review>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий рейтинг
                var rating = f.PickRandom(ratings);

                // Використовуємо той самий продукт та користувача, що і в рейтингу
                var productId = rating.ProductId;
                var userId = rating.UserId;

                // Генеруємо коментар (не більше 500 символів)
                var paragraph = f.Lorem.Paragraph();
                var comment = paragraph.Length <= 500 ? paragraph : paragraph.Substring(0, 500);

                // Створюємо відгук
                return new Review(
                    userId: userId,
                    productId: productId,
                    rating: rating,
                    parentId: Guid.NewGuid(), // Використовуємо новий GUID як parentId
                    comment: comment
                );
            });

        // Генерація даних
        var newReviews = reviewFaker.Generate(reviewsToCreate);

        // Встановлюємо дати створення та оновлення
        foreach (var review in newReviews)
        {
            review.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 20) // 20% відгуків оновлені
            {
                review.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Reviews.AddRange(newReviews);
        _context.SaveChanges();

        Console.WriteLine($"Created {reviewsToCreate} reviews.");
    }

    private List<ShippingMethod> CreateShippingMethodsIfNotExist()
    {
        // Перевірка, чи є вже методи доставки
        var existingCount = _context.ShippingMethods.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} shipping methods.");
            return _context.ShippingMethods.ToList();
        }

        Console.WriteLine("Creating shipping methods.");

        // Створюємо стандартні методи доставки
        var shippingMethods = new List<ShippingMethod>
        {
            new ShippingMethod(
                price: new Money(100, Currency.UAH),
                estimatedDays: 3,
                name: Name.NovaPoshta
            ),
            new ShippingMethod(
                price: new Money(150, Currency.UAH),
                estimatedDays: 1,
                name: Name.Courier
            ),
            new ShippingMethod(
                price: new Money(0.01m, Currency.UAH),
                estimatedDays: 1,
                name: Name.SelfPickup
            )
        };

        // Додавання до бази
        _context.ShippingMethods.AddRange(shippingMethods);
        _context.SaveChanges();

        Console.WriteLine($"Created {shippingMethods.Count} shipping methods.");

        // Повертаємо всі методи доставки
        return _context.ShippingMethods.ToList();
    }

    private List<Coupon> CreateCouponsIfNotExist(int count)
    {
        // Перевірка, чи є вже купони
        var existingCount = _context.Coupons.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} coupons.");
            return _context.Coupons.ToList();
        }

        // Створюємо додаткові купони, якщо потрібно
        var couponsToCreate = count - existingCount;
        Console.WriteLine($"Creating {couponsToCreate} additional coupons.");

        // Налаштування генератора фейкових даних для Coupon
        var couponFaker = new Faker<Coupon>()
            .CustomInstantiator(f =>
            {
                // Генеруємо код купона
                var code = f.Random.AlphaNumeric(8).ToUpper();

                // Визначаємо тип знижки
                var discountType = f.Random.Bool() ? DiscountType.Percentage : DiscountType.Fixed;

                // Генеруємо розмір знижки в залежності від типу
                double discount;
                if (discountType == DiscountType.Percentage)
                {
                    discount = f.Random.Double(5, 50); // Від 5% до 50%
                }
                else
                {
                    discount = f.Random.Double(50, 500); // Від 50 до 500 грн
                }

                // Генеруємо ліміт використань (не для всіх купонів)
                uint? usageLimit = null;
                if (f.Random.Bool(0.7f)) // 70% купонів мають ліміт
                {
                    usageLimit = (uint)f.Random.Number(1, 100);
                }

                // Генеруємо дату закінчення (не для всіх купонів)
                DateTime? expiresAt = null;
                if (f.Random.Bool(0.8f)) // 80% купонів мають дату закінчення
                {
                    expiresAt = DateTime.UtcNow.AddDays(f.Random.Number(7, 90));
                }

                return new Coupon(
                    code: code,
                    discount: discount,
                    discountType: discountType,
                    usageLimit: usageLimit,
                    expiresAt: expiresAt
                );
            });

        // Генерація даних
        var newCoupons = couponFaker.Generate(couponsToCreate);

        // Встановлюємо дати створення та оновлення
        foreach (var coupon in newCoupons)
        {
            coupon.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 20) // 20% купонів оновлені
            {
                coupon.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Coupons.AddRange(newCoupons);
        _context.SaveChanges();

        Console.WriteLine($"Created {couponsToCreate} coupons.");

        // Повертаємо всі купони
        return _context.Coupons.ToList();
    }

    private List<Order> CreateOrdersIfNotExist(List<User> users, List<Address> addresses, List<ShippingMethod> shippingMethods, int count)
    {
        // Перевірка, чи є вже замовлення
        var existingCount = _context.Orders.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} orders.");
            return _context.Orders.ToList();
        }

        // Створюємо додаткові замовлення, якщо потрібно
        var ordersToCreate = count - existingCount;
        Console.WriteLine($"Creating {ordersToCreate} additional orders.");

        // Налаштування генератора фейкових даних для Order
        var orderFaker = new Faker<Order>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача, адресу та метод доставки
                var user = f.PickRandom(users);

                // Вибираємо адресу, яка належить цьому користувачу, або будь-яку іншу
                var userAddresses = addresses.Where(a => a.UserId == user.Id).ToList();
                var address = userAddresses.Any() ? f.PickRandom(userAddresses) : f.PickRandom(addresses);

                var shippingMethod = f.PickRandom(shippingMethods);

                // Генеруємо випадкову суму замовлення
                var totalPrice = new Money(f.Random.Decimal(100, 10000), Currency.UAH);

                // Генеруємо випадковий статус замовлення
                var orderStatus = f.PickRandom<OrderStatus>();

                var order = new Order(
                    customerId: user.Id,
                    totalPrice: totalPrice,
                    shippingAddressId: address.Id,
                    shippingMethodId: shippingMethod.Id
                );

                // Встановлюємо статус замовлення
                order.Status = orderStatus;

                return order;
            });

        // Генерація даних
        var newOrders = orderFaker.Generate(ordersToCreate);

        // Додавання до бази
        _context.Orders.AddRange(newOrders);
        _context.SaveChanges();

        Console.WriteLine($"Created {ordersToCreate} orders.");

        // Повертаємо всі замовлення
        return _context.Orders.ToList();
    }

    private void CreateOrderItemsIfNotExist(List<Order> orders, List<Product> products, int count)
    {
        // Перевірка, чи є вже елементи замовлень
        var existingCount = _context.OrderItems.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} order items.");
            return;
        }

        // Створюємо додаткові елементи замовлень, якщо потрібно
        var orderItemsToCreate = count - existingCount;
        Console.WriteLine($"Creating {orderItemsToCreate} additional order items.");

        // Налаштування генератора фейкових даних для OrderItem
        var orderItemFaker = new Faker<OrderItem>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкове замовлення та продукт
                var order = f.PickRandom(orders);
                var product = f.PickRandom(products);

                // Генеруємо кількість
                var quantity = (uint)f.Random.Number(1, 10);

                // Створюємо нову ціну, оскільки ціна продукту може бути не ініціалізована правильно
                var price = new Money(
                    amount: f.Random.Decimal(100, 10000), // Від 100 до 10000 грн
                    currency: Currency.UAH
                );

                // Створюємо елемент замовлення
                return new OrderItem(
                    orderId: order.Id,
                    productId: product.Id,
                    quantity: quantity,
                    price: price
                );
            });

        // Генерація даних
        var newOrderItems = orderItemFaker.Generate(orderItemsToCreate);

        // Встановлюємо дати створення та оновлення
        foreach (var orderItem in newOrderItems)
        {
            orderItem.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 20) // 20% елементів замовлень оновлені
            {
                orderItem.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.OrderItems.AddRange(newOrderItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {orderItemsToCreate} order items.");
    }

    private void CreateOrderCouponsIfNotExist(List<Order> orders, List<Coupon> coupons)
    {
        // Перевірка, чи є вже зв'язки замовлень з купонами
        var existingCount = _context.OrderCoupons.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} order coupons.");
            return;
        }

        Console.WriteLine("Creating order coupons.");

        // Створюємо зв'язки для 30% замовлень
        var ordersWithCoupons = orders.OrderBy(o => Guid.NewGuid()).Take(orders.Count * 3 / 10).ToList();

        var orderCoupons = new List<OrderCoupon>();

        foreach (var order in ordersWithCoupons)
        {
            // Вибираємо випадковий купон
            var coupon = coupons.OrderBy(c => Guid.NewGuid()).First();

            orderCoupons.Add(new OrderCoupon(
                orderId: order.Id,
                couponId: coupon.Id
            ));
        }

        // Додавання до бази
        _context.OrderCoupons.AddRange(orderCoupons);
        _context.SaveChanges();

        Console.WriteLine($"Created {orderCoupons.Count} order coupons.");
    }

    private void CreatePaymentsIfNotExist(List<Order> orders)
    {
        // Перевірка, чи є вже платежі
        var existingCount = _context.Payments.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} payments.");
            return;
        }

        Console.WriteLine("Creating payments.");

        // Створюємо платежі для всіх замовлень
        var payments = new List<Payment>();

        foreach (var order in orders)
        {
            // Генеруємо випадковий метод оплати
            var paymentMethod = (PaymentMethod)new Random().Next(0, 3); // 0 - CreditCard, 1 - PayPal, 2 - BankTransfer

            // Генеруємо випадковий статус оплати
            var paymentStatus = (PaymentStatus)new Random().Next(0, 3); // 0 - Pending, 1 - Completed, 2 - Failed

            payments.Add(new Payment(
                orderId: order.Id,
                paymentMethod: paymentMethod,
                amount: order.TotalPrice,
                paymentStatus: paymentStatus
            ));
        }

        // Встановлюємо дати створення та оновлення
        foreach (var payment in payments)
        {
            payment.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 20) // 20% платежів оновлені
            {
                payment.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Payments.AddRange(payments);
        _context.SaveChanges();

        Console.WriteLine($"Created {payments.Count} payments.");
    }

    private List<Wishlist> CreateWishlistsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже списки бажань
        var existingCount = _context.Wishlists.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} wishlists.");
            return _context.Wishlists.ToList();
        }

        // Створюємо додаткові списки бажань, якщо потрібно
        var wishlistsToCreate = count - existingCount;
        Console.WriteLine($"Creating {wishlistsToCreate} additional wishlists.");

        // Налаштування генератора фейкових даних для Wishlist
        var wishlistFaker = new Faker<Wishlist>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача для прив'язки списку бажань
                var randomUser = f.PickRandom(users);

                return new Wishlist(
                    userId: randomUser.Id
                );
            });

        // Генерація даних
        var newWishlists = wishlistFaker.Generate(wishlistsToCreate);

        // Додавання до бази
        _context.Wishlists.AddRange(newWishlists);
        _context.SaveChanges();

        Console.WriteLine($"Created {wishlistsToCreate} wishlists.");

        // Повертаємо всі списки бажань
        return _context.Wishlists.ToList();
    }

    private void CreateWishlistItemsIfNotExist(List<Wishlist> wishlists, List<Product> products, int count)
    {
        // Перевірка, чи є вже елементи списків бажань
        var existingCount = _context.WishlistItems.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} wishlist items.");
            return;
        }

        // Створюємо додаткові елементи списків бажань, якщо потрібно
        var wishlistItemsToCreate = count - existingCount;
        Console.WriteLine($"Creating {wishlistItemsToCreate} additional wishlist items.");

        // Налаштування генератора фейкових даних для WishlistItem
        var wishlistItemFaker = new Faker<WishlistItem>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий список бажань та продукт
                var randomWishlist = f.PickRandom(wishlists);
                var randomProduct = f.PickRandom(products);

                return new WishlistItem(
                    wishlistId: randomWishlist.Id,
                    productId: randomProduct.Id
                );
            });

        // Генерація даних
        var newWishlistItems = new List<WishlistItem>();

        // Створюємо елементи списків бажань, уникаючи дублікатів
        for (int i = 0; i < wishlistItemsToCreate; i++)
        {
            var wishlistItem = wishlistItemFaker.Generate();

            // Перевіряємо, чи вже існує такий елемент
            var exists = _context.WishlistItems.Any(wi =>
                wi.WishlistId == wishlistItem.WishlistId &&
                wi.ProductId == wishlistItem.ProductId);

            if (!exists)
            {
                newWishlistItems.Add(wishlistItem);
            }
            else
            {
                i--; // Повторюємо спробу
            }
        }

        // Додавання до бази
        _context.WishlistItems.AddRange(newWishlistItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {newWishlistItems.Count} wishlist items.");
    }

    private List<Chat> CreateChatsIfNotExist(List<User> users, List<Company> companies, int count)
    {
        // Перевірка, чи є вже чати
        var existingCount = _context.Chats.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} chats.");
            return _context.Chats.ToList();
        }

        // Створюємо додаткові чати, якщо потрібно
        var chatsToCreate = count - existingCount;
        Console.WriteLine($"Creating {chatsToCreate} additional chats.");

        // Налаштування генератора фейкових даних для Chat
        var chatFaker = new Faker<Chat>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкових користувачів для покупця та продавця
                var buyer = f.PickRandom(users.Where(u => u.Role == Role.Buyer).ToList());
                var seller = f.PickRandom(users.Where(u => u.Role == Role.Seller || u.Role == Role.Admin).ToList());

                // Вибираємо випадкову компанію (для деяких чатів)
                Company company = null;
                Guid? companyId = null;

                if (f.Random.Bool(0.7f)) // 70% чатів пов'язані з компанією
                {
                    company = f.PickRandom(companies);
                    companyId = company.Id;
                }

                var chat = new Chat(
                    sellerId: seller.Id,
                    buyerId: buyer.Id
                );

                // Встановлюємо компанію, якщо вона вибрана
                if (companyId.HasValue)
                {
                    chat.CompanyId = companyId;
                }

                return chat;
            });

        // Генерація даних
        var newChats = chatFaker.Generate(chatsToCreate);

        // Встановлюємо дати створення
        foreach (var chat in newChats)
        {
            chat.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
        }

        // Додавання до бази
        _context.Chats.AddRange(newChats);
        _context.SaveChanges();

        Console.WriteLine($"Created {chatsToCreate} chats.");

        // Повертаємо всі чати
        return _context.Chats.ToList();
    }

    private void CreateMessagesIfNotExist(List<Chat> chats, List<User> users, int count)
    {
        // Перевірка, чи є вже повідомлення
        var existingCount = _context.Messages.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} messages.");
            return;
        }

        // Створюємо додаткові повідомлення, якщо потрібно
        var messagesToCreate = count - existingCount;
        Console.WriteLine($"Creating {messagesToCreate} additional messages.");

        // Налаштування генератора фейкових даних для Message
        var messageFaker = new Faker<Message>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий чат
                var chat = f.PickRandom(chats);

                // Визначаємо відправника (покупець або продавець)
                var sender = f.Random.Bool() ?
                    users.FirstOrDefault(u => u.Id == chat.BuyerId) :
                    users.FirstOrDefault(u => u.Id == chat.SellerId);

                if (sender == null)
                {
                    // Якщо не знайдено, вибираємо випадкового користувача
                    sender = f.PickRandom(users);
                }

                // Створюємо повідомлення, використовуючи конструктор
                var message = new Message(
                    chatId: chat.Id,
                    senderId: sender.Id,
                    messageText: f.Lorem.Sentence()
                );

                // Встановлюємо додаткові властивості
                message.IsRead = f.Random.Bool();
                message.CreatedAt = DateTime.UtcNow.AddDays(-f.Random.Int(1, 30));
                message.UpdatedAt = DateTime.UtcNow.AddDays(-f.Random.Int(0, 10));

                return message;
            });

        // Генерація даних
        var newMessages = messageFaker.Generate(messagesToCreate);

        // Додавання до бази
        _context.Messages.AddRange(newMessages);
        _context.SaveChanges();

        Console.WriteLine($"Created {messagesToCreate} messages.");
    }

    private void CreateNotificationsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже сповіщення
        var existingCount = _context.Notifications.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} notifications.");
            return;
        }

        // Створюємо додаткові сповіщення, якщо потрібно
        var notificationsToCreate = count - existingCount;
        Console.WriteLine($"Creating {notificationsToCreate} additional notifications.");

        // Список можливих заголовків сповіщень
        var notificationTitles = new List<string>
        {
            "Нове замовлення", "Знижка на товар", "Статус замовлення оновлено",
            "Новий відгук", "Товар знову в наявності", "Спеціальна пропозиція",
            "Підтвердження реєстрації", "Зміна пароля", "Нове повідомлення"
        };

        // Налаштування генератора фейкових даних для Notification
        var notificationFaker = new Faker<Notification>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача
                var user = f.PickRandom(users);

                // Вибираємо випадковий заголовок
                var title = f.PickRandom(notificationTitles);

                // Генеруємо текст сповіщення (не більше 100 символів)
                var sentence = f.Lorem.Sentence();
                var text = sentence.Length <= 100 ? sentence : sentence.Substring(0, 100);

                return new Notification(
                    userId: user.Id,
                    title: title,
                    text: text
                );
            });

        // Генерація даних
        var newNotifications = notificationFaker.Generate(notificationsToCreate);

        // Встановлюємо дати створення та статус прочитання
        foreach (var notification in newNotifications)
        {
            notification.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            notification.IsRead = new Random().Next(100) < 50; // 50% сповіщень прочитані
        }

        // Додавання до бази
        _context.Notifications.AddRange(newNotifications);
        _context.SaveChanges();

        Console.WriteLine($"Created {notificationsToCreate} notifications.");
    }

    private void CreateSellerRequestsIfNotExist(List<User> users, int count)
    {
        try
        {
            // Use the SellerRequestSeeder with a null logger for simplicity
            var seeder = new Persistence.Seeders.SellerRequestSeeder(_context, _logger);
            var task = seeder.SeedAsync(count);
            task.Wait(); // Wait for async operation to complete

            Console.WriteLine($"✅ SellerRequest seeding completed successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error seeding SellerRequests: {ex.Message}");
            _logger.LogError(ex, "Error seeding SellerRequests");
        }
    }

    private void CreateCompanySchedulesIfNotExist(List<Company> companies)
    {
        // Перевірка, чи є вже розклади компаній
        var existingCount = _context.CompanySchedules.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company schedules.");
            return;
        }

        Console.WriteLine("Creating company schedules.");

        // Створюємо розклади для всіх компаній
        var companySchedules = new List<CompanySchedule>();

        foreach (var company in companies)
        {
            // Створюємо розклад для кожного дня тижня
            foreach (Day day in Enum.GetValues<Day>())
            {
                // Визначаємо, чи компанія працює в цей день
                bool isClosed = day == Day.Saturday || day == Day.Sunday || new Random().Next(100) < 10; // Вихідні + 10% випадкових днів

                // Встановлюємо години роботи
                TimeSpan openTime = TimeSpan.FromHours(9); // За замовчуванням 9:00
                TimeSpan closeTime = TimeSpan.FromHours(18); // За замовчуванням 18:00

                // Для робочих днів генеруємо випадкові години
                if (!isClosed)
                {
                    int openHour = new Random().Next(7, 11); // Від 7:00 до 10:00
                    int closeHour = new Random().Next(17, 22); // Від 17:00 до 21:00

                    // Переконуємося, що час закриття більший за час відкриття
                    if (closeHour <= openHour)
                    {
                        closeHour = openHour + 8; // Гарантуємо, що компанія працює принаймні 8 годин
                    }

                    openTime = TimeSpan.FromHours(openHour);
                    closeTime = TimeSpan.FromHours(closeHour);
                }

                // Створюємо об'єкт CompanySchedule, використовуючи конструктор
                var schedule = new CompanySchedule(
                    companyId: company.Id,
                    openTime: openTime,
                    closeTime: closeTime,
                    isClosed: isClosed,
                    day: day
                );

                companySchedules.Add(schedule);
            }
        }

        // Додавання до бази
        _context.CompanySchedules.AddRange(companySchedules);
        _context.SaveChanges();

        Console.WriteLine($"Created {companySchedules.Count} company schedules.");
    }

    private void CreateCompanyUsersIfNotExist(List<Company> companies, List<User> users)
    {
        // Перевірка, чи є вже зв'язки компаній з користувачами
        var existingCount = _context.CompanyUsers.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company users.");
            return;
        }

        Console.WriteLine("Creating company users.");

        // Створюємо зв'язки для кожної компанії
        var companyUsers = new List<CompanyUser>();

        foreach (var company in companies)
        {
            // Вибираємо випадкову кількість користувачів для компанії (від 1 до 3)
            int userCount = new Random().Next(1, 4);

            // Вибираємо випадкових користувачів
            var companyUsersList = users
                .Where(u => u.Role == Role.Seller || u.Role == Role.Admin)
                .OrderBy(u => Guid.NewGuid())
                .Take(userCount)
                .ToList();

            foreach (var user in companyUsersList)
            {
                companyUsers.Add(new CompanyUser(
                    companyId: company.Id,
                    userId: user.Id,
                    isOwner: user == companyUsersList.First() // Перший користувач - власник
                ));
            }
        }

        // Додавання до бази
        _context.CompanyUsers.AddRange(companyUsers);
        _context.SaveChanges();

        Console.WriteLine($"Created {companyUsers.Count} company users.");
    }

    private List<Cart> CreateCartsIfNotExist(List<User> users, int count)
    {
        // Перевірка, чи є вже кошики
        var existingCount = _context.Carts.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} carts.");
            return _context.Carts.ToList();
        }

        // Створюємо додаткові кошики, якщо потрібно
        var cartsToCreate = count - existingCount;
        Console.WriteLine($"Creating {cartsToCreate} additional carts.");

        // Отримуємо користувачів, які ще не мають кошиків
        var existingUserIds = _context.Carts.Select(c => c.UserId).ToList();
        var usersWithoutCarts = users.Where(u => !existingUserIds.Contains(u.Id)).ToList();

        // Якщо немає користувачів без кошиків, повертаємо існуючі кошики
        if (usersWithoutCarts.Count == 0)
        {
            Console.WriteLine("All users already have carts.");
            return _context.Carts.ToList();
        }

        // Налаштування генератора фейкових даних для Cart
        var cartFaker = new Faker<Cart>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача без кошика
                var user = f.PickRandom(usersWithoutCarts);
                usersWithoutCarts.Remove(user); // Видаляємо користувача зі списку, щоб уникнути дублікатів

                return new Cart(
                    userId: user.Id
                );
            });

        // Генерація даних
        var newCarts = cartFaker.Generate(Math.Min(cartsToCreate, usersWithoutCarts.Count));

        // Встановлюємо дати створення та оновлення
        foreach (var cart in newCarts)
        {
            cart.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 50) // 50% кошиків оновлені
            {
                cart.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.Carts.AddRange(newCarts);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCarts.Count} carts.");

        // Повертаємо всі кошики
        return _context.Carts.ToList();
    }

    private void CreateCartItemsIfNotExist(List<Cart> carts, List<Product> products, int count)
    {
        // Перевірка, чи є вже елементи кошиків
        var existingCount = _context.CartItems.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} cart items.");
            return;
        }

        // Створюємо додаткові елементи кошиків, якщо потрібно
        var cartItemsToCreate = count - existingCount;
        Console.WriteLine($"Creating {cartItemsToCreate} additional cart items.");

        // Налаштування генератора фейкових даних для CartItem
        var cartItemFaker = new Faker<CartItem>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадковий кошик та продукт
                var cart = f.PickRandom(carts);
                var product = f.PickRandom(products);

                // Генеруємо кількість
                var quantity = (uint)f.Random.Number(1, 10);

                return new CartItem(
                    cartId: cart.Id,
                    productId: product.Id,
                    quantity: quantity
                );
            });

        // Генерація даних
        var newCartItems = new List<CartItem>();

        // Створюємо елементи кошиків, уникаючи дублікатів
        for (int i = 0; i < cartItemsToCreate; i++)
        {
            var cartItem = cartItemFaker.Generate();

            // Перевіряємо, чи вже існує такий елемент
            var exists = _context.CartItems.Any(ci =>
                ci.CartId == cartItem.CartId &&
                ci.ProductId == cartItem.ProductId);

            if (!exists)
            {
                newCartItems.Add(cartItem);
            }
            else
            {
                i--; // Повторюємо спробу
            }
        }

        // Встановлюємо дати створення та оновлення
        foreach (var cartItem in newCartItems)
        {
            cartItem.CreatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 30));
            if (new Random().Next(100) < 30) // 30% елементів кошиків оновлені
            {
                cartItem.UpdatedAt = DateTime.UtcNow.AddDays(-new Random().Next(1, 10));
            }
        }

        // Додавання до бази
        _context.CartItems.AddRange(newCartItems);
        _context.SaveChanges();

        Console.WriteLine($"Created {newCartItems.Count} cart items.");
    }

    private void CreateFavoritesIfNotExist(List<User> users, List<Product> products, int count)
    {
        // Перевірка, чи є вже улюблені
        var existingCount = _context.Favorites.Count();

        if (existingCount >= count)
        {
            Console.WriteLine($"Already have {existingCount} favorites.");
            return;
        }

        // Створюємо додаткові улюблені, якщо потрібно
        var favoritesToCreate = count - existingCount;
        Console.WriteLine($"Creating {favoritesToCreate} additional favorites.");

        // Налаштування генератора фейкових даних для Favorite
        var favoriteFaker = new Faker<Favorite>()
            .CustomInstantiator(f =>
            {
                // Вибираємо випадкового користувача та продукт
                var user = f.PickRandom(users);
                var product = f.PickRandom(products);

                return new Favorite(
                    userId: user.Id,
                    productId: product.Id
                );
            });

        // Генерація даних
        var newFavorites = favoriteFaker.Generate(favoritesToCreate);

        // Додавання до бази
        _context.Favorites.AddRange(newFavorites);
        _context.SaveChanges();

        Console.WriteLine($"Created {favoritesToCreate} favorites.");
    }

    private void CreateCompanyFinancesIfNotExist(List<Company> companies)
    {
        // Перевірка, чи є вже фінансові налаштування компаній
        var existingCount = _context.CompanyFinances.Count();

        if (existingCount > 0)
        {
            Console.WriteLine($"Already have {existingCount} company finances.");
            return;
        }

        Console.WriteLine("Creating company finances.");

        var companyFinances = new List<CompanyFinance>();
        var faker = new Faker("uk");

        foreach (var company in companies)
        {
            // Створюємо фінансові налаштування для кожної компанії
            var companyFinance = new CompanyFinance(
                companyId: company.Id,
                bankAccount: faker.Finance.Account(20),
                bankName: faker.Company.CompanyName() + " Банк",
                bankCode: faker.Random.Number(10000, 99999).ToString(),
                taxId: faker.Random.Number(*********, *********).ToString(),
                paymentDetails: faker.Lorem.Sentence()
            );

            // Встановлюємо дати створення та оновлення
            companyFinance.CreatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 30));
            if (_random.Next(100) < 30) // 30% фінансових налаштувань оновлені
            {
                companyFinance.UpdatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 10));
            }

            companyFinances.Add(companyFinance);
        }

        // Додавання до бази
        _context.CompanyFinances.AddRange(companyFinances);
        _context.SaveChanges();

        Console.WriteLine($"Created {companyFinances.Count} company finances.");
    }


}
