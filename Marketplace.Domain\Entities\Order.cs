using Marketplace.Domain.ValueObjects;

namespace Marketplace.Domain.Entities;

public enum OrderStatus
{
    Pending,
    Paid,
    Shipped,
    Delivered,
    Cancelled
}

public class Order : IEntity
{
    private Money _totalPrice;
    private OrderStatus _orderStatus;
    private Guid _shippingAddressId;
    private Guid _shippingMethodId;
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public User Customer { get; set; }
    public Money TotalPrice
    {
        get => _totalPrice;
        set => UpdateTotalPrice(value);
    }
    public OrderStatus Status
    {
        get => _orderStatus;
        set => UpdateOrderStatus(value);
    }
    public Guid ShippingAddressId
    {
        get => _shippingAddressId;
        set => UpdateShippingAddressId(value);
    }
    public Address ShippingAddress { get; set; }
    public Guid ShippingMethodId
    {
        get => _shippingMethodId;
        set => UpdateShippingMethodId(value);
    }
    public ShippingMethod ShippingMethod { get; set; }
    public DateTime CreatedAt { get; set; }

    private Order() { } // Для EF Core

    public Order(
        Guid customerId,
        Money totalPrice,
        Guid shippingAddressId,
        Guid shippingMethodId
    )
    {
        ValidateCreation(customerId, shippingAddressId, shippingMethodId);

        Id = Guid.NewGuid();
        CustomerId = customerId;
        TotalPrice = totalPrice;
        ShippingAddressId = shippingAddressId;
        ShippingMethodId = shippingMethodId;
        CreatedAt = DateTime.UtcNow;
    }

    public void Update(
        Currency? totalPriceCurrency = null,
        decimal? totalPriceAmount = null,
        OrderStatus? orderStatus = null,
        Guid? shippingAddressId = null,
        Guid? shippingMethodId = null)
    {
        if (totalPriceCurrency != null || totalPriceAmount != null)
        {
            UpdateTotalPrice(new Money(
                totalPriceAmount ?? TotalPrice.Amount,
                totalPriceCurrency ?? TotalPrice.Currency
                ));
        }

        if (orderStatus != null) UpdateOrderStatus((OrderStatus)orderStatus);

        if (shippingAddressId != null) UpdateShippingAddressId(shippingAddressId.Value);

        if (shippingMethodId != null) UpdateShippingMethodId(shippingMethodId.Value);
    }

    public void UpdateOrderStatus(OrderStatus orderStatus)
    {
        _orderStatus = orderStatus;
    }

    public void UpdateTotalPrice(Money totalPrice)
    {
        _totalPrice = totalPrice ?? throw new ArgumentNullException(nameof(totalPrice));
    }

    public void UpdateShippingAddressId(Guid shippingAddressId)
    {
        if (shippingAddressId == Guid.Empty) throw new ArgumentNullException(nameof(shippingAddressId));
        _shippingAddressId = shippingAddressId;
    }

    public void UpdateShippingMethodId(Guid shippingMethodId)
    {
        if (shippingMethodId == Guid.Empty) throw new ArgumentNullException(nameof(shippingMethodId));
        _shippingMethodId = shippingMethodId;
    }

    private void ValidateCreation(
        Guid customerId,
        Guid shippingAddressId,
        Guid shippingMethodId
    )
    {
        if (customerId == Guid.Empty) throw new ArgumentNullException(nameof(customerId));
        if (shippingAddressId == Guid.Empty) throw new ArgumentNullException(nameof(shippingAddressId));
        if (shippingMethodId == Guid.Empty) throw new ArgumentNullException(nameof(shippingMethodId));
    }
}
