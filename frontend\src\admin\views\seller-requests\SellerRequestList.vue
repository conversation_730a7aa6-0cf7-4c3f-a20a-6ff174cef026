<template>
  <div class="seller-request-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Seller Requests</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="field has-addons">
            <div class="control">
              <input
                class="input"
                type="text"
                placeholder="Search requests..."
                v-model="searchQuery"
                @input="debouncedSearch"
              />
            </div>
            <div class="control">
              <button class="button is-info" @click="search">
                <span class="icon">
                  <i class="fas fa-search"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="box">
      <div class="columns">
        <div class="column is-4">
          <div class="field">
            <label class="label">Status</label>
            <div class="control">
              <div class="select is-fullwidth">
                <select v-model="filters.status" @change="search">
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-content">
        <div v-if="loading && !requests.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading seller requests...</p>
        </div>
        <div v-else-if="!requests.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-store fa-2x"></i>
          </span>
          <p class="mt-2">No seller requests found</p>
          <p class="mt-2">There are currently no seller requests to review</p>
        </div>
        <div v-else>
          <div class="columns is-multiline">
            <div
              v-for="request in requests"
              :key="request.id"
              class="column is-12-tablet is-6-desktop">
              <div class="card seller-request-card">
                <div class="card-content">
                  <div class="media">
                    <div class="media-left">
                      <figure class="image is-64x64">
                        <img
                          :src="request.user?.avatar || 'https://via.placeholder.com/64'"
                          :alt="(request.user?.firstName || '') + ' ' + (request.user?.lastName || '')"
                          @error="handleImageError">
                      </figure>
                    </div>
                    <div class="media-content">
                      <p class="title is-4">{{ request.user?.username || 'Unknown User' }}</p>
                      <p class="subtitle is-6">
                        <a :href="`mailto:${request.user?.email?.value || request.user?.email}`">{{ request.user?.email?.value || request.user?.email }}</a>
                      </p>
                      <p class="subtitle is-6">
                        <status-badge
                          :status="getStatusString(request.status)"
                          type="default" />
                        <span class="ml-2">{{ formatDate(request.createdAt) }}</span>
                      </p>
                    </div>
                  </div>

                  <div class="content">
                    <div class="field">
                      <label class="label">Company Name</label>
                      <p>{{ request.companyRequestData?.name || 'N/A' }}</p>
                    </div>

                    <div class="field">
                      <label class="label">Company Description</label>
                      <p>{{ request.companyRequestData?.description || 'N/A' }}</p>
                    </div>

                    <div class="field">
                      <label class="label">Contact Email</label>
                      <p>{{ request.companyRequestData?.contactEmail || 'N/A' }}</p>
                    </div>

                    <div class="field">
                      <label class="label">Contact Phone</label>
                      <p>{{ request.companyRequestData?.contactPhone || 'N/A' }}</p>
                    </div>

                    <div v-if="request.additionalInformation" class="field">
                      <label class="label">Additional Information</label>
                      <p>{{ request.additionalInformation }}</p>
                    </div>

                    <!-- View Details Button -->
                    <div class="field mt-4">
                      <router-link
                        :to="{ name: 'AdminSellerRequestDetail', params: { id: request.id } }"
                        class="button is-info is-small">
                        <span class="icon">
                          <i class="fas fa-eye"></i>
                        </span>
                        <span>View Details</span>
                      </router-link>
                    </div>

                    <div v-if="getStatusString(request.status) === 'pending'" class="field is-grouped mt-4">
                      <div class="control">
                        <button
                          class="button is-success"
                          @click="confirmApprove(request)"
                          :class="{ 'is-loading': request.processing }">
                          <span class="icon">
                            <i class="fas fa-check"></i>
                          </span>
                          <span>Approve</span>
                        </button>
                      </div>
                      <div class="control">
                        <button
                          class="button is-danger"
                          @click="confirmReject(request)"
                          :class="{ 'is-loading': request.processing }">
                          <span class="icon">
                            <i class="fas fa-times"></i>
                          </span>
                          <span>Reject</span>
                        </button>
                      </div>
                    </div>

                    <div v-else-if="getStatusString(request.status) === 'approved'" class="field mt-4">
                      <router-link
                        :to="`/admin/users/${request.userId}`"
                        class="button is-info">
                        <span class="icon">
                          <i class="fas fa-user"></i>
                        </span>
                        <span>View Seller Profile</span>
                      </router-link>
                    </div>

                    <div v-else-if="getStatusString(request.status) === 'rejected'" class="field mt-4">
                      <p class="has-text-danger">
                        <span class="icon">
                          <i class="fas fa-info-circle"></i>
                        </span>
                        <span>Rejected on {{ formatDate(request.updatedAt) }}</span>
                      </p>
                      <p v-if="request.rejectionReason" class="mt-2">
                        <strong>Reason:</strong> {{ request.rejectionReason }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <pagination
            :current-page="currentPage"
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Approve Confirmation Modal -->
    <confirm-dialog
      :is-open="showApproveModal"
      title="Approve Seller Request"
      :message="`Are you sure you want to approve ${requestToProcess?.user?.username}'s seller request?`"
      confirm-text="Approve"
      cancel-text="Cancel"
      confirm-button-class="is-success"
      @confirm="approveRequest"
      @cancel="cancelProcess" />

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="cancelProcess"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Seller Request</p>
          <button class="delete" aria-label="close" @click="cancelProcess"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to reject {{ requestToProcess?.user?.username }}'s seller request?</p>

          <div class="field mt-4">
            <label class="label">Reason for Rejection (Optional)</label>
            <div class="control">
              <textarea
                class="textarea"
                v-model="rejectionReason"
                placeholder="Provide a reason for rejection">
              </textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-danger"
            @click="rejectRequest"
            :class="{ 'is-loading': processing }">
            Reject
          </button>
          <button class="button is-light" @click="cancelProcess">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { sellerRequestsService } from '@/admin/services/seller-requests';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
// Utility function for debouncing
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// State
const requests = ref([]);
const loading = ref(false);
const searchQuery = ref('');

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);

// Process state
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const requestToProcess = ref(null);
const rejectionReason = ref('');
const processing = ref(false);

// Filters
const filters = reactive({
  status: ''
});

// Fetch seller requests
const fetchRequests = async (page = 1) => {
  loading.value = true;
  currentPage.value = page;

  try {
    const params = {
      page: currentPage.value,
      pageSize: itemsPerPage.value,
      filter: searchQuery.value,
      status: filters.status
    };

    console.log('🔍 Fetching seller requests with params:', params);
    const response = await sellerRequestsService.getSellerRequests(params);
    console.log('📥 Raw API response:', response);

    // Обробляємо відповідь від сервісу
    let requestsData = [];
    let paginationData = {};

    if (response && response.success && response.data) {
      const data = response.data;
      console.log('📊 Data structure:', data);

      // Дані знаходяться в data.data (PaginatedResponse)
      if (data.data && Array.isArray(data.data)) {
        requestsData = data.data;
        paginationData = data;
      }
      // Альтернативний варіант - дані безпосередньо в data
      else if (Array.isArray(data)) {
        requestsData = data;
      }
    }

    console.log('📋 Final requests data:', requestsData);
    console.log('📊 Final pagination data:', paginationData);

    // Встановлюємо дані
    requests.value = requestsData.map(request => ({
      ...request,
      processing: false
    }));

    // Встановлюємо пагінацію
    totalPages.value = paginationData.totalPages || Math.ceil((paginationData.totalItems || requestsData.length) / (paginationData.pageSize || 10));
    totalItems.value = paginationData.totalItems || requestsData.length;
    currentPage.value = paginationData.currentPage || 1;

    console.log('✅ Final state:', {
      requestsCount: requests.value.length,
      totalPages: totalPages.value,
      totalItems: totalItems.value,
      currentPage: currentPage.value
    });
  } catch (error) {
    console.error('❌ Error fetching seller requests:', error);
    console.error('❌ Error details:', error.response?.data || error.message);
  } finally {
    loading.value = false;
  }
};

const search = () => {
  currentPage.value = 1;
  fetchRequests();
};

const debouncedSearch = debounce(search, 300);

// Handle page change
const handlePageChange = (page) => {
  fetchRequests(page);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Convert status enum to string
const getStatusString = (status) => {
  if (typeof status === 'string') {
    return status.toLowerCase();
  }

  // Handle enum values (0 = Pending, 1 = Approved, 2 = Rejected)
  switch (status) {
    case 0:
      return 'pending';
    case 1:
      return 'approved';
    case 2:
      return 'rejected';
    default:
      return 'pending';
  }
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/64?text=No+Image';
};

// Confirm approve
const confirmApprove = (request) => {
  requestToProcess.value = request;
  showApproveModal.value = true;
};

// Confirm reject
const confirmReject = (request) => {
  requestToProcess.value = request;
  rejectionReason.value = '';
  showRejectModal.value = true;
};

// Cancel process
const cancelProcess = () => {
  showApproveModal.value = false;
  showRejectModal.value = false;
  requestToProcess.value = null;
  rejectionReason.value = '';
};

// Approve request
const approveRequest = async () => {
  if (!requestToProcess.value) return;

  // Set processing flag
  const index = requests.value.findIndex(r => r.id === requestToProcess.value.id);
  if (index !== -1) {
    requests.value[index].processing = true;
  }

  try {
    await sellerRequestsService.approveSellerRequest(requestToProcess.value.id);

    // Update request in list
    if (index !== -1) {
      requests.value[index].status = 1; // Approved
      requests.value[index].updatedAt = new Date();
      requests.value[index].processing = false;
    }

    // Close modal
    showApproveModal.value = false;
    requestToProcess.value = null;
  } catch (error) {
    console.error('Error approving seller request:', error);

    // Reset processing flag
    if (index !== -1) {
      requests.value[index].processing = false;
    }
  }
};

// Reject request
const rejectRequest = async () => {
  if (!requestToProcess.value) return;

  processing.value = true;

  // Set processing flag
  const index = requests.value.findIndex(r => r.id === requestToProcess.value.id);
  if (index !== -1) {
    requests.value[index].processing = true;
  }

  try {
    await sellerRequestsService.rejectSellerRequest(
      requestToProcess.value.id,
      rejectionReason.value
    );

    // Update request in list
    if (index !== -1) {
      requests.value[index].status = 2; // Rejected
      requests.value[index].updatedAt = new Date();
      requests.value[index].rejectionReason = rejectionReason.value;
      requests.value[index].processing = false;
    }

    // Close modal
    showRejectModal.value = false;
    requestToProcess.value = null;
    rejectionReason.value = '';
  } catch (error) {
    console.error('Error rejecting seller request:', error);

    // Reset processing flag
    if (index !== -1) {
      requests.value[index].processing = false;
    }
  } finally {
    processing.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchRequests();
});
</script>

<style scoped>
.seller-request-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.seller-request-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.seller-request-card .card-content {
  flex-grow: 1;
}

.seller-request-card .media {
  align-items: center;
}

.seller-request-card .media-left .image {
  border-radius: 50%;
  overflow: hidden;
}

.seller-request-card .media-left .image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.seller-request-card .field {
  margin-bottom: 1rem;
}

.seller-request-card .label {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.tag.is-primary {
  background-color: #ff7700;
}

.tag.is-primary.is-light {
  background-color: #fff2e5;
  color: #ff7700;
}
</style>
