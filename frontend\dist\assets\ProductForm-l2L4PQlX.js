import{_ as O,g as f,z as $,h as V,f as z,x as j,i as K,c as o,a as s,t as _,b as x,w as E,r as W,d as h,A as n,B as r,k as P,F as w,p as U,n as M,C as F,J,m as G,I as H,e as Q,o as d}from"./index-C1YYMYJd.js";import{p as u}from"./products-DEnweLaU.js";const X={class:"product-form"},Y={class:"level"},Z={class:"level-left"},ss={class:"level-item"},ts={class:"title"},es={class:"level-right"},ls={class:"level-item"},is={key:0,class:"has-text-centered py-6"},as={key:1,class:"notification is-danger"},os={key:2},ns={class:"columns"},ds={class:"column is-8"},rs={class:"card"},cs={class:"card-content"},us={class:"field"},ps={class:"control"},ms={class:"field"},vs={class:"control"},gs={class:"columns"},fs={class:"column is-6"},_s={class:"field"},hs={class:"control"},bs={class:"column is-6"},ys={class:"field"},ks={class:"control"},Ps={class:"card mt-4"},ws={class:"card-content"},Us={class:"columns"},Cs={class:"column is-4"},Is={class:"field"},Ss={class:"control has-icons-left"},Vs={class:"column is-4"},xs={class:"field"},Es={class:"control has-icons-left"},Ms={class:"column is-4"},Fs={class:"field"},Ds={class:"control has-icons-left"},Ts={class:"columns"},As={class:"column is-4"},Bs={class:"field"},Ls={class:"control"},Ns={class:"column is-4"},qs={class:"field"},Rs={class:"control"},Os={class:"column is-4"},$s={class:"field"},zs={class:"control"},js={class:"card mt-4"},Ks={class:"card-content"},Ws={class:"file is-boxed is-centered mb-4"},Js={class:"file-label"},Gs={key:0,class:"has-text-centered mb-4"},Hs={key:1,class:"product-images"},Qs={class:"image-container"},Xs=["src"],Ys={class:"image-actions"},Zs=["onClick"],st=["onClick"],tt={class:"image-info"},et={key:0,class:"tag is-primary"},lt={key:2,class:"has-text-centered"},it={class:"column is-4"},at={class:"card"},ot={class:"card-content"},nt={class:"field"},dt={class:"control"},rt={class:"select is-fullwidth"},ct={class:"field"},ut={class:"control"},pt={class:"checkbox"},mt={class:"field"},vt={class:"control"},gt={class:"card mt-4"},ft={class:"card-content"},_t={class:"field"},ht={class:"control"},bt={class:"select is-fullwidth"},yt=["value"],kt={class:"field"},Pt={class:"control"},wt={key:0,class:"tags-container"},Ut=["onClick"],Ct={class:"card mt-4"},It={class:"card-content"},St={class:"field is-grouped"},Vt={class:"control is-expanded"},xt={class:"control"},Et={__name:"ProductForm",setup(Mt){const C=z(),D=Q(),b=f(!1),y=f(!1),p=f(null),I=f([]),k=f(!1),m=f(""),e=$({name:"",description:"",sku:"",slug:"",price:0,compareAtPrice:null,costPrice:null,stock:0,lowStockThreshold:5,weight:null,status:"active",isFeatured:!1,publishDate:new Date().toISOString().split("T")[0],categoryId:"",tags:[],images:[]}),c=V(()=>!!C.params.id),v=V(()=>C.params.id);j(m,i=>{if(i.includes(",")){const t=i.split(",").map(a=>a.trim()).filter(a=>a);e.tags=[...new Set([...e.tags,...t])],m.value=""}});const T=async()=>{if(c.value){b.value=!0;try{const i=await u.getProductById(v.value);Object.keys(e).forEach(t=>{i[t]!==void 0&&(e[t]=i[t])}),typeof i.tags=="string"&&(e.tags=i.tags.split(",").map(t=>t.trim()).filter(t=>t)),Array.isArray(e.images)||(e.images=[])}catch(i){console.error("Error fetching product:",i),p.value="Failed to load product data. Please try again."}finally{b.value=!1}}},A=async()=>{try{const i=await H.getCategories();i.categories&&(I.value=i.categories)}catch(i){console.error("Error fetching categories:",i)}},B=async i=>{const t=i.target.files;if(t.length){k.value=!0;try{for(const a of t)if(a.type.startsWith("image/"))if(c.value){const l=await u.uploadProductImage(v.value,a);l.image&&e.images.push(l.image)}else{const l=URL.createObjectURL(a);e.images.push({url:l,file:a,isMain:e.images.length===0})}i.target.value=""}catch(a){console.error("Error uploading images:",a),p.value="Failed to upload images. Please try again."}finally{k.value=!1}}},L=i=>{e.images.forEach((t,a)=>{t.isMain=a===i}),c.value&&e.images[i].id&&u.setMainProductImage(v.value,e.images[i].id).catch(t=>{console.error("Error setting main image:",t)})},N=async i=>{const t=e.images[i];if(c.value&&t.id)try{await u.deleteProductImage(v.value,t.id)}catch(a){console.error("Error deleting image:",a);return}e.images.splice(i,1),t.isMain&&e.images.length>0&&(e.images[0].isMain=!0,c.value&&e.images[0].id&&u.setMainProductImage(v.value,e.images[0].id).catch(a=>{console.error("Error setting main image:",a)}))},q=i=>{e.tags.splice(i,1)},R=async()=>{y.value=!0,p.value=null;try{if(m.value.trim()){const a=m.value.split(",").map(l=>l.trim()).filter(l=>l);e.tags=[...new Set([...e.tags,...a])],m.value=""}const i={...e};c.value||delete i.images;let t;if(c.value)t=await u.updateProduct(v.value,i);else if(t=await u.createProduct(i),e.images.length>0&&t.product&&t.product.id)for(const a of e.images)a.file&&await u.uploadProductImage(t.product.id,a.file);D.push("/admin/products")}catch(i){console.error("Error saving product:",i),p.value="Failed to save product. Please check your input and try again."}finally{y.value=!1}};return K(()=>{A(),T()}),(i,t)=>{const a=W("router-link");return d(),o("div",X,[s("div",Y,[s("div",Z,[s("div",ss,[s("h1",ts,_(c.value?"Edit Product":"Add Product"),1)])]),s("div",es,[s("div",ls,[x(a,{to:"/admin/products",class:"button is-light"},{default:E(()=>t[16]||(t[16]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Products",-1)])),_:1})])])]),b.value?(d(),o("div",is,t[17]||(t[17]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading product data...",-1)]))):p.value?(d(),o("div",as,[s("button",{class:"delete",onClick:t[0]||(t[0]=l=>p.value=null)}),h(" "+_(p.value),1)])):(d(),o("div",os,[s("form",{onSubmit:G(R,["prevent"])},[s("div",ns,[s("div",ds,[s("div",rs,[t[23]||(t[23]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Product Information")],-1)),s("div",cs,[s("div",us,[t[18]||(t[18]=s("label",{class:"label"},"Product Name*",-1)),s("div",ps,[n(s("input",{class:"input",type:"text","onUpdate:modelValue":t[1]||(t[1]=l=>e.name=l),required:"",placeholder:"Enter product name"},null,512),[[r,e.name]])])]),s("div",ms,[t[19]||(t[19]=s("label",{class:"label"},"Description*",-1)),s("div",vs,[n(s("textarea",{class:"textarea","onUpdate:modelValue":t[2]||(t[2]=l=>e.description=l),required:"",placeholder:"Enter product description",rows:"5"},null,512),[[r,e.description]])])]),s("div",gs,[s("div",fs,[s("div",_s,[t[20]||(t[20]=s("label",{class:"label"},"SKU",-1)),s("div",hs,[n(s("input",{class:"input",type:"text","onUpdate:modelValue":t[3]||(t[3]=l=>e.sku=l),placeholder:"Enter product SKU"},null,512),[[r,e.sku]])])])]),s("div",bs,[s("div",ys,[t[21]||(t[21]=s("label",{class:"label"},"Slug",-1)),s("div",ks,[n(s("input",{class:"input",type:"text","onUpdate:modelValue":t[4]||(t[4]=l=>e.slug=l),placeholder:"Enter product slug"},null,512),[[r,e.slug]])]),t[22]||(t[22]=s("p",{class:"help"},"Leave empty to auto-generate from name",-1))])])])])]),s("div",Ps,[t[33]||(t[33]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Pricing & Inventory")],-1)),s("div",ws,[s("div",Us,[s("div",Cs,[s("div",Is,[t[25]||(t[25]=s("label",{class:"label"},"Price*",-1)),s("div",Ss,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[5]||(t[5]=l=>e.price=l),required:"",min:"0",step:"0.01",placeholder:"0.00"},null,512),[[r,e.price]]),t[24]||(t[24]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-hryvnia"})],-1))])])]),s("div",Vs,[s("div",xs,[t[27]||(t[27]=s("label",{class:"label"},"Compare at Price",-1)),s("div",Es,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[6]||(t[6]=l=>e.compareAtPrice=l),min:"0",step:"0.01",placeholder:"0.00"},null,512),[[r,e.compareAtPrice]]),t[26]||(t[26]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-hryvnia"})],-1))])])]),s("div",Ms,[s("div",Fs,[t[29]||(t[29]=s("label",{class:"label"},"Cost Price",-1)),s("div",Ds,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[7]||(t[7]=l=>e.costPrice=l),min:"0",step:"0.01",placeholder:"0.00"},null,512),[[r,e.costPrice]]),t[28]||(t[28]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-hryvnia"})],-1))])])])]),s("div",Ts,[s("div",As,[s("div",Bs,[t[30]||(t[30]=s("label",{class:"label"},"Stock*",-1)),s("div",Ls,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[8]||(t[8]=l=>e.stock=l),required:"",min:"0",step:"1",placeholder:"0"},null,512),[[r,e.stock]])])])]),s("div",Ns,[s("div",qs,[t[31]||(t[31]=s("label",{class:"label"},"Low Stock Threshold",-1)),s("div",Rs,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[9]||(t[9]=l=>e.lowStockThreshold=l),min:"0",step:"1",placeholder:"5"},null,512),[[r,e.lowStockThreshold]])])])]),s("div",Os,[s("div",$s,[t[32]||(t[32]=s("label",{class:"label"},"Weight (kg)",-1)),s("div",zs,[n(s("input",{class:"input",type:"number","onUpdate:modelValue":t[10]||(t[10]=l=>e.weight=l),min:"0",step:"0.01",placeholder:"0.00"},null,512),[[r,e.weight]])])])])])])]),s("div",js,[t[39]||(t[39]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Product Images")],-1)),s("div",Ks,[s("div",Ws,[s("label",Js,[s("input",{class:"file-input",type:"file",accept:"image/*",multiple:"",onChange:B},null,32),t[34]||(t[34]=s("span",{class:"file-cta"},[s("span",{class:"file-icon"},[s("i",{class:"fas fa-upload"})]),s("span",{class:"file-label"}," Choose images to upload ")],-1))])]),k.value?(d(),o("div",Gs,t[35]||(t[35]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Uploading images...",-1)]))):P("",!0),e.images&&e.images.length>0?(d(),o("div",Hs,[(d(!0),o(w,null,U(e.images,(l,g)=>(d(),o("div",{key:g,class:"product-image-item"},[s("div",Qs,[s("img",{src:l.url,alt:"Product image"},null,8,Xs),s("div",Ys,[s("button",{type:"button",class:M(["button is-small is-primary",{"is-outlined":!l.isMain}]),onClick:S=>L(g),title:"Set as main image"},t[36]||(t[36]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-star"})],-1)]),10,Zs),s("button",{type:"button",class:"button is-small is-danger",onClick:S=>N(g),title:"Remove image"},t[37]||(t[37]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-trash"})],-1)]),8,st)])]),s("div",tt,[l.isMain?(d(),o("span",et,"Main")):P("",!0)])]))),128))])):(d(),o("div",lt,t[38]||(t[38]=[s("p",null,"No images uploaded yet",-1)])))])])]),s("div",it,[s("div",at,[t[45]||(t[45]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Status & Visibility")],-1)),s("div",ot,[s("div",nt,[t[41]||(t[41]=s("label",{class:"label"},"Status",-1)),s("div",dt,[s("div",rt,[n(s("select",{"onUpdate:modelValue":t[11]||(t[11]=l=>e.status=l)},t[40]||(t[40]=[s("option",{value:"active"},"Active",-1),s("option",{value:"inactive"},"Inactive",-1),s("option",{value:"draft"},"Draft",-1)]),512),[[F,e.status]])])])]),s("div",ct,[t[43]||(t[43]=s("label",{class:"label"},"Visibility",-1)),s("div",ut,[s("label",pt,[n(s("input",{type:"checkbox","onUpdate:modelValue":t[12]||(t[12]=l=>e.isFeatured=l)},null,512),[[J,e.isFeatured]]),t[42]||(t[42]=h(" Featured product "))])])]),s("div",mt,[t[44]||(t[44]=s("label",{class:"label"},"Publish Date",-1)),s("div",vt,[n(s("input",{class:"input",type:"date","onUpdate:modelValue":t[13]||(t[13]=l=>e.publishDate=l)},null,512),[[r,e.publishDate]])])])])]),s("div",gt,[t[49]||(t[49]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Organization")],-1)),s("div",ft,[s("div",_t,[t[47]||(t[47]=s("label",{class:"label"},"Category*",-1)),s("div",ht,[s("div",bt,[n(s("select",{"onUpdate:modelValue":t[14]||(t[14]=l=>e.categoryId=l),required:""},[t[46]||(t[46]=s("option",{value:"",disabled:""},"Select a category",-1)),(d(!0),o(w,null,U(I.value,l=>(d(),o("option",{key:l.id,value:l.id},_(l.name),9,yt))),128))],512),[[F,e.categoryId]])])])]),s("div",kt,[t[48]||(t[48]=s("label",{class:"label"},"Tags",-1)),s("div",Pt,[n(s("input",{class:"input",type:"text","onUpdate:modelValue":t[15]||(t[15]=l=>m.value=l),placeholder:"Enter tags separated by commas"},null,512),[[r,m.value]])])]),e.tags&&e.tags.length>0?(d(),o("div",wt,[(d(!0),o(w,null,U(e.tags,(l,g)=>(d(),o("span",{key:g,class:"tag is-primary is-medium"},[h(_(l)+" ",1),s("button",{class:"delete is-small",type:"button",onClick:S=>q(g)},null,8,Ut)]))),128))])):P("",!0)])]),s("div",Ct,[s("div",It,[s("div",St,[s("div",Vt,[s("button",{type:"submit",class:M(["button is-primary is-fullwidth",{"is-loading":y.value}])},_(c.value?"Update Product":"Create Product"),3)]),s("div",xt,[x(a,{to:"/admin/products",class:"button is-light"},{default:E(()=>t[50]||(t[50]=[h(" Cancel ")])),_:1})])])])])])])],32)]))])}}},Tt=O(Et,[["__scopeId","data-v-20c029cd"]]);export{Tt as default};
