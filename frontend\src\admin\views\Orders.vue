<template>
  <div class="admin-orders">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Orders</h1>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Orders"
      search-placeholder="Search by order ID, customer name, or email..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="orders"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading orders...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchData">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Orders Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <order-table
            :orders="items"
            :loading="loading"
            @view="viewOrderDetails"
            @update-status="updateOrderStatus" />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-wrapper" v-if="totalPages > 1">
      <pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-changed="handlePageChange" />
    </div>

    <!-- Order Details Modal -->
    <order-details-modal
      :is-open="isOrderDetailsModalOpen"
      :order="selectedOrder"
      @close="closeOrderDetailsModal"
      @update-status="updateOrderStatus" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import OrderTable from '@/admin/components/orders/OrderTable.vue';
import OrderDetailsModal from '@/admin/components/orders/OrderDetailsModal.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { ordersService } from '@/admin/services/orders';
import { useAdminSearch } from '@/composables/useAdminSearch';

// Filter configuration
const filterFields = [
  {
    key: 'status',
    label: 'Order Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Statuses',
    options: [
      { value: 'Pending', label: 'Pending' },
      { value: 'Paid', label: 'Paid' },
      { value: 'Shipped', label: 'Shipped' },
      { value: 'Delivered', label: 'Delivered' },
      { value: 'Cancelled', label: 'Cancelled' }
    ]
  },
  {
    key: 'paymentStatus',
    label: 'Payment Status',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Payment Statuses',
    options: [
      { value: 'Pending', label: 'Pending' },
      { value: 'Completed', label: 'Completed' },
      { value: 'Failed', label: 'Failed' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false,
    options: [
      { value: 'CreatedAt', label: 'Order Date' },
      { value: 'TotalPrice', label: 'Total Amount' },
      { value: 'CustomerName', label: 'Customer Name' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: ordersService.getOrders,
  defaultFilters: {
    status: '',
    paymentStatus: '',
    sortBy: 'CreatedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Modal state
const isOrderDetailsModalOpen = ref(false);
const selectedOrder = ref(null);

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else if (key === 'sortBy') {
      filters[key] = 'CreatedAt';
    } else if (key === 'sortOrder') {
      filters[key] = 'desc';
    } else {
      filters[key] = '';
    }
  });
};

// View order details
const viewOrderDetails = async (orderId) => {
  loading.value = true;
  try {
    const orderDetails = await ordersService.getOrderById(orderId);
    selectedOrder.value = orderDetails;
    isOrderDetailsModalOpen.value = true;
  } catch (error) {
    console.error('Error fetching order details:', error);
  } finally {
    loading.value = false;
  }
};

// Close order details modal
const closeOrderDetailsModal = () => {
  isOrderDetailsModalOpen.value = false;
  selectedOrder.value = null;
};

// Update order status
const updateOrderStatus = async (orderId, newStatus) => {
  try {
    await ordersService.updateOrderStatus(orderId, newStatus);

    // If the order details modal is open, update the selected order status
    if (selectedOrder.value && selectedOrder.value.id === orderId) {
      selectedOrder.value.status = newStatus;
    }

    // Refresh the orders list
    fetchData(currentPage.value);
  } catch (error) {
    console.error('Error updating order status:', error);
  }
};
</script>

<style scoped>
.admin-orders {
  padding: 1rem;
}

.pagination-wrapper {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}
</style>
