﻿﻿using AutoMapper;
using Marketplace.Application.Responses;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using System.Globalization;
using System.Linq.Expressions;

namespace Marketplace.Application.Queries.Analytics;

public class GetSellerOrderAnalyticsQueryHandler : IRequestHandler<GetSellerOrderAnalyticsQuery, OrderAnalyticsResponse>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IOrderItemRepository _orderItemRepository;
    private readonly IProductRepository _productRepository;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IMapper _mapper;

    public GetSellerOrderAnalyticsQueryHandler(
        IOrderRepository orderRepository,
        IOrderItemRepository orderItemRepository,
        IProductRepository productRepository,
        ICompanyUserRepository companyUserRepository,
        IMapper mapper)
    {
        _orderRepository = orderRepository;
        _orderItemRepository = orderItemRepository;
        _productRepository = productRepository;
        _companyUserRepository = companyUserRepository;
        _mapper = mapper;
    }

    public async Task<OrderAnalyticsResponse> Handle(GetSellerOrderAnalyticsQuery request, CancellationToken cancellationToken)
    {
        // Отримуємо компанії, до яких належить продавець
        var companyUsers = await _companyUserRepository.GetAllAsync(
            filter: cu => cu.UserId == request.SellerId,
            cancellationToken: cancellationToken
        );

        if (!companyUsers.Any())
            return null;

        // Отримуємо ID компаній продавця
        var companyIds = companyUsers.Select(cu => cu.CompanyId).ToList();

        // Отримуємо продукти компаній продавця
        var products = await _productRepository.GetAllAsync(
            filter: p => companyIds.Contains(p.CompanyId),
            cancellationToken: cancellationToken
        );

        if (!products.Any())
            return new OrderAnalyticsResponse
            {
                TotalOrders = 0,
                ProcessingOrders = 0,
                PendingOrders = 0,
                ShippedOrders = 0,
                DeliveredOrders = 0,
                CancelledOrders = 0,
                TotalRevenue = new Money(0, Currency.UAH),
                AverageOrderValue = new Money(0, Currency.UAH),
                OrdersByStatus = new Dictionary<string, int>(),
                RevenueByStatus = new Dictionary<string, Money>(),
                OrdersByMonth = new Dictionary<string, int>(),
                RevenueByMonth = new Dictionary<string, Money>(),
                TopCustomers = new List<TopCustomerResponse>()
            };

        // Отримуємо ID продуктів компаній продавця
        var productIds = products.Select(p => p.Id).ToList();

        // Отримуємо елементи замовлень, які містять продукти продавця
        var orderItems = await _orderItemRepository.GetAllAsync(
            filter: oi => productIds.Contains(oi.ProductId),
            cancellationToken: cancellationToken,
            includes: new Expression<Func<Domain.Entities.OrderItem, object>>[] {
                oi => oi.Order,
                oi => oi.Order.Customer
            }
        );

        // Фільтруємо за датою, якщо вказано
        if (request.StartDate.HasValue)
        {
            orderItems = orderItems.Where(oi => oi.CreatedAt >= request.StartDate.Value).ToList();
        }

        if (request.EndDate.HasValue)
        {
            orderItems = orderItems.Where(oi => oi.CreatedAt <= request.EndDate.Value).ToList();
        }

        // Отримуємо унікальні замовлення
        var orders = orderItems
            .Select(oi => oi.Order)
            .DistinctBy(o => o.Id)
            .ToList();

        // Створюємо аналітику замовлень
        var response = new OrderAnalyticsResponse
        {
            TotalOrders = orders.Count(),
            ProcessingOrders = orders.Count(o => o.Status == OrderStatus.Processing),
            PendingOrders = orders.Count(o => o.Status == OrderStatus.Pending),
            ShippedOrders = orders.Count(o => o.Status == OrderStatus.Shipped),
            DeliveredOrders = orders.Count(o => o.Status == OrderStatus.Delivered),
            CancelledOrders = orders.Count(o => o.Status == OrderStatus.Cancelled),
            TotalRevenue = CalculateTotalRevenue(orderItems),
            AverageOrderValue = CalculateAverageOrderValue(orderItems),
            OrdersByStatus = GetOrdersByStatus(orders),
            RevenueByStatus = GetRevenueByStatus(orderItems),
            OrdersByMonth = GetOrdersByMonth(orders),
            RevenueByMonth = GetRevenueByMonth(orderItems),
            TopCustomers = GetTopCustomers(orderItems)
        };

        return response;
    }

    private Money CalculateTotalRevenue(IEnumerable<Domain.Entities.OrderItem> orderItems)
    {
        if (!orderItems.Any())
            return new Money(0, Currency.UAH);

        // Групуємо елементи замовлень за валютою
        var revenueByOrderAndCurrency = orderItems
            .GroupBy(oi => new { oi.Order.Id, oi.Price.Currency })
            .Select(g => new
            {
                Currency = g.Key.Currency,
                Revenue = g.Sum(oi => oi.Price.Amount * oi.Quantity)
            })
            .GroupBy(x => x.Currency)
            .Select(g => new
            {
                Currency = g.Key,
                Revenue = g.Sum(x => x.Revenue)
            })
            .OrderByDescending(x => x.Revenue)
            .FirstOrDefault();

        if (revenueByOrderAndCurrency == null)
            return new Money(0, Currency.UAH);

        return new Money(revenueByOrderAndCurrency.Revenue, revenueByOrderAndCurrency.Currency);
    }

    private Money CalculateAverageOrderValue(IEnumerable<Domain.Entities.OrderItem> orderItems)
    {
        if (!orderItems.Any())
            return new Money(0, Currency.UAH);

        // Групуємо елементи замовлень за замовленням та валютою
        var revenueByOrderAndCurrency = orderItems
            .GroupBy(oi => new { oi.Order.Id, oi.Price.Currency })
            .Select(g => new
            {
                Currency = g.Key.Currency,
                Revenue = g.Sum(oi => oi.Price.Amount * oi.Quantity)
            })
            .GroupBy(x => x.Currency)
            .Select(g => new
            {
                Currency = g.Key,
                Revenue = g.Average(x => x.Revenue)
            })
            .OrderByDescending(x => x.Revenue)
            .FirstOrDefault();

        if (revenueByOrderAndCurrency == null)
            return new Money(0, Currency.UAH);

        return new Money(revenueByOrderAndCurrency.Revenue, revenueByOrderAndCurrency.Currency);
    }

    private Dictionary<string, int> GetOrdersByStatus(IEnumerable<Domain.Entities.Order> orders)
    {
        return orders
            .GroupBy(o => o.Status)
            .ToDictionary(g => g.Key.ToString(), g => g.Count());
    }

    private Dictionary<string, Money> GetRevenueByStatus(IEnumerable<Domain.Entities.OrderItem> orderItems)
    {
        // Групуємо елементи замовлень за статусом замовлення та валютою
        var revenueByStatusAndCurrency = orderItems
            .GroupBy(oi => new { Status = oi.Order.Status, oi.Price.Currency })
            .Select(g => new
            {
                Status = g.Key.Status.ToString(),
                Currency = g.Key.Currency,
                Revenue = g.Sum(oi => oi.Price.Amount * oi.Quantity)
            })
            .ToList();

        // Вибираємо найпоширенішу валюту
        var mostCommonCurrency = orderItems
            .GroupBy(oi => oi.Price.Currency)
            .OrderByDescending(g => g.Count())
            .Select(g => g.Key)
            .FirstOrDefault();

        if (mostCommonCurrency == default)
            mostCommonCurrency = Currency.UAH;

        // Створюємо словник з виручкою за статусом
        var result = new Dictionary<string, Money>();
        foreach (var status in Enum.GetValues<OrderStatus>())
        {
            var statusStr = status.ToString();
            var revenue = revenueByStatusAndCurrency
                .Where(x => x.Status == statusStr && x.Currency == mostCommonCurrency)
                .Sum(x => x.Revenue);
            result[statusStr] = new Money(revenue, mostCommonCurrency);
        }

        return result;
    }

    private Dictionary<string, int> GetOrdersByMonth(IEnumerable<Domain.Entities.Order> orders)
    {
        return orders
            .GroupBy(o => new { o.CreatedAt.Year, o.CreatedAt.Month })
            .OrderBy(g => g.Key.Year)
            .ThenBy(g => g.Key.Month)
            .ToDictionary(
                g => $"{g.Key.Year}-{g.Key.Month:D2}",
                g => g.Count()
            );
    }

    private Dictionary<string, Money> GetRevenueByMonth(IEnumerable<Domain.Entities.OrderItem> orderItems)
    {
        // Групуємо елементи замовлень за місяцем та валютою
        var revenueByMonthAndCurrency = orderItems
            .GroupBy(oi => new { oi.CreatedAt.Year, oi.CreatedAt.Month, oi.Price.Currency })
            .Select(g => new
            {
                Month = $"{g.Key.Year}-{g.Key.Month:D2}",
                Currency = g.Key.Currency,
                Revenue = g.Sum(oi => oi.Price.Amount * oi.Quantity)
            })
            .ToList();

        // Вибираємо найпоширенішу валюту
        var mostCommonCurrency = orderItems
            .GroupBy(oi => oi.Price.Currency)
            .OrderByDescending(g => g.Count())
            .Select(g => g.Key)
            .FirstOrDefault();

        if (mostCommonCurrency == default)
            mostCommonCurrency = Currency.UAH;

        // Отримуємо всі місяці в діапазоні
        var allMonths = orderItems
            .GroupBy(oi => new { oi.CreatedAt.Year, oi.CreatedAt.Month })
            .OrderBy(g => g.Key.Year)
            .ThenBy(g => g.Key.Month)
            .Select(g => $"{g.Key.Year}-{g.Key.Month:D2}")
            .ToList();

        // Створюємо словник з виручкою за місяцями
        var result = new Dictionary<string, Money>();
        foreach (var month in allMonths)
        {
            var revenue = revenueByMonthAndCurrency
                .Where(x => x.Month == month && x.Currency == mostCommonCurrency)
                .Sum(x => x.Revenue);
            result[month] = new Money(revenue, mostCommonCurrency);
        }

        return result;
    }

    private List<TopCustomerResponse> GetTopCustomers(IEnumerable<Domain.Entities.OrderItem> orderItems, int count = 10)
    {
        // Групуємо елементи замовлень за користувачем
        var customerOrders = orderItems
            .GroupBy(oi => oi.Order.CustomerId)
            .Select(g => new
            {
                CustomerId = g.Key,
                Customer = g.First().Order.Customer,
                OrderCount = g.Select(oi => oi.OrderId).Distinct().Count(),
                TotalSpent = new Money(
                    g.Sum(oi => oi.Price.Amount * oi.Quantity),
                    g.First().Price.Currency
                ),
                LastOrderDate = g.Max(oi => oi.CreatedAt)
            })
            .OrderByDescending(x => x.OrderCount)
            .Take(count)
            .ToList();

        // Створюємо список топових клієнтів
        return customerOrders
            .Select(co => new TopCustomerResponse
            {
                Id = co.CustomerId,
                Username = co.Customer.Username,
                Email = co.Customer.Email.Value,
                OrderCount = co.OrderCount,
                TotalSpent = co.TotalSpent,
                LastOrderDate = co.LastOrderDate
            })
            .ToList();
    }
}
