/* empty css                                                                    */import{_ as i,h as r,c,o as u,t as d,n as p}from"./index-BtyG65bR.js";const o={__name:"StatusBadge",props:{status:{type:[String,Number],required:!0},label:{type:String,default:""},type:{type:String,default:"default",validator:s=>["default","order","payment","user","product"].includes(s)}},setup(s){const e=s,a=r(()=>{if(e.status===null||e.status===void 0)return"Unknown";if(e.label)return e.label;if(typeof e.status=="number")switch(e.status){case 0:return e.type==="product","Pending";case 1:return e.type==="product","Approved";case 2:return e.type==="product","Rejected";default:return e.status.toString()}if(e.type==="product")switch(e.status.toString().toLowerCase()){case"pending":return"Pending";case"approved":return"Approved";case"rejected":return"Rejected";default:return e.status.toString()}if(e.type==="order")switch(e.status.toString().toLowerCase()){case"pending":return"Pending";case"paid":return"Paid";case"shipped":return"Shipped";case"delivered":return"Delivered";case"cancelled":return"Cancelled";default:return e.status.toString()}if(e.type==="payment")switch(e.status.toString().toLowerCase()){case"pending":return"Pending";case"completed":return"Completed";case"failed":return"Failed";case"unknown":return"Unknown";default:return e.status.toString()}return e.status.toString()}),n=r(()=>{if(e.status===null||e.status===void 0)return"is-light";const t=e.status.toString().toLowerCase();if(e.type==="default")switch(t){case"active":case"approved":case"completed":case"1":return"is-success";case"inactive":case"pending":case"0":return"is-warning";case"rejected":case"cancelled":case"2":return"is-danger";default:return"is-light"}if(e.type==="order")switch(t){case"pending":return"is-warning";case"processing":return"is-info";case"shipped":return"is-primary";case"delivered":return"is-success";case"cancelled":return"is-danger";case"refunded":return"is-danger is-light";case"on hold":return"is-warning is-light";default:return"is-light"}if(e.type==="payment")switch(t){case"paid":return"is-success";case"pending":return"is-warning";case"failed":return"is-danger";case"refunded":return"is-danger is-light";case"partially refunded":return"is-warning is-light";default:return"is-light"}if(e.type==="product")switch(t){case"approved":case"1":return"is-success";case"pending":case"0":return"is-warning";case"rejected":case"2":return"is-danger";case"out of stock":return"is-danger";case"low stock":return"is-warning is-light";default:return"is-light"}if(e.type==="order")switch(t){case"pending":case"0":return"is-warning";case"processing":case"paid":case"1":return"is-info";case"shipped":case"2":return"is-primary";case"delivered":case"completed":case"3":return"is-success";case"cancelled":case"refunded":case"4":return"is-danger";default:return"is-light"}if(e.type==="payment")switch(t){case"pending":case"0":return"is-warning";case"completed":case"paid":case"1":return"is-success";case"failed":case"declined":case"2":return"is-danger";case"unknown":return"is-light";default:return"is-light"}return"is-light"});return(t,l)=>(u(),c("span",{class:p(["tag",n.value])},d(a.value),3))}},h=i(o,[["__scopeId","data-v-95318e6d"]]);export{h as S};
