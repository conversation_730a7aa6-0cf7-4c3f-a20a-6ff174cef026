import{q as o}from"./index-BtyG65bR.js";const d=new Map,c=3e4,u={async getOrders(e={}){const t=JSON.stringify(e),a=d.get(t);if(a&&Date.now()-a.timestamp<c)return console.log("Orders loaded from cache"),a.data;try{const s={...e,pageSize:Math.min(e.pageSize||10,10)},r=await o.get("/api/admin/orders",{params:s});if(console.log("Orders API response:",r),r.data&&r.data.success&&r.data.data){const n=r.data.data;return{orders:n.items||n.data||[],pagination:{total:n.totalItems||n.total||0,page:n.currentPage||n.page||e.page||1,limit:n.pageSize||n.perPage||e.pageSize||10,totalPages:n.totalPages||n.lastPage||1}}}if(r.data&&r.data.items)return{orders:r.data.items||[],pagination:{total:r.data.totalItems||r.data.total||0,page:r.data.currentPage||r.data.page||e.page||1,limit:r.data.pageSize||r.data.perPage||e.pageSize||10,totalPages:r.data.totalPages||r.data.lastPage||1}};const i=r.data;return d.set(t,{data:i,timestamp:Date.now()}),i}catch(s){console.error("Error fetching orders:",s),s.response&&(s.response.status===401||s.response.status===403)&&console.warn("Authentication required for orders. Using mock data.");const r={orders:[{id:"ORD-1001",userId:"1",userName:"John Doe",total:156.78,status:"Processing",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"ORD-1002",userId:"2",userName:"Jane Smith",total:89.99,status:"Pending",paymentStatus:"Pending",createdAt:new Date(Date.now()-1e3*60*60*5)},{id:"ORD-1003",userId:"3",userName:"Robert Johnson",total:245.5,status:"Shipped",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24)},{id:"ORD-1004",userId:"4",userName:"Emily Davis",total:78.25,status:"Delivered",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24*2)},{id:"ORD-1005",userId:"5",userName:"Michael Wilson",total:189.99,status:"Cancelled",paymentStatus:"Refunded",createdAt:new Date(Date.now()-1e3*60*60*24*3)}],pagination:{total:5,page:1,limit:10,totalPages:1}};return d.set(t,{data:r,timestamp:Date.now()}),r}},async getOrderById(e){try{return(await o.get(`/api/admin/orders/${e}`)).data}catch(t){return console.error(`Error fetching order ${e}:`,t),{id:e,userId:"1",userName:"John Doe",email:"<EMAIL>",total:156.78,subtotal:149.99,tax:6.79,shipping:0,discount:0,status:"Processing",paymentStatus:"Paid",paymentMethod:"Credit Card",shippingMethod:"Standard",shippingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},billingAddress:{firstName:"John",lastName:"Doe",address1:"123 Main St",address2:"Apt 4B",city:"New York",state:"NY",postalCode:"10001",country:"USA",phone:"************"},items:[{id:"1",productId:"1",productName:"Smartphone X",quantity:1,price:99.99,total:99.99},{id:"2",productId:"2",productName:"Wireless Headphones",quantity:1,price:49.99,total:49.99}],notes:[],createdAt:new Date(Date.now()-1e3*60*60*2),updatedAt:new Date(Date.now()-1e3*60*30)}}},async updateOrderStatus(e,t){try{return(await o.patch(`/api/admin/orders/${e}/status`,{status:t})).data}catch(a){return console.error(`Error updating status for order ${e}:`,a),{success:!0,order:{id:e,status:t,updatedAt:new Date}}}},async updatePaymentStatus(e,t){try{return(await o.patch(`/api/admin/orders/${e}/payment-status`,{paymentStatus:t})).data}catch(a){return console.error(`Error updating payment status for order ${e}:`,a),{success:!0,order:{id:e,paymentStatus:t,updatedAt:new Date}}}},async addOrderNote(e,t){try{return(await o.post(`/api/admin/orders/${e}/notes`,{note:t})).data}catch(a){return console.error(`Error adding note to order ${e}:`,a),{success:!0,note:{id:Math.floor(Math.random()*1e3).toString(),content:t,createdAt:new Date,createdBy:"Admin"}}}},async getOrderNotes(e){try{return(await o.get(`/api/admin/orders/${e}/notes`)).data}catch(t){return console.error(`Error fetching notes for order ${e}:`,t),[{id:"1",content:"Order received and processing",createdAt:new Date(Date.now()-1e3*60*60*2),createdBy:"System"},{id:"2",content:"Payment confirmed",createdAt:new Date(Date.now()-1e3*60*60),createdBy:"System"},{id:"3",content:"Customer requested expedited shipping",createdAt:new Date(Date.now()-1e3*60*30),createdBy:"Admin"}]}},async refundOrder(e,t,a){try{return(await o.post(`/api/admin/orders/${e}/refund`,{amount:t,reason:a})).data}catch(s){return console.error(`Error processing refund for order ${e}:`,s),{success:!0,refund:{id:Math.floor(Math.random()*1e3).toString(),amount:t,reason:a,createdAt:new Date}}}},async exportOrders(e={}){try{return(await o.get("/api/admin/orders/export",{params:e,responseType:"blob"})).data}catch(t){console.error("Error exporting orders:",t);const a=`Order ID,Customer,Email,Date,Total,Status,Payment Status
`,s=["ORD-1001,John Doe,<EMAIL>,2023-01-01,156.78,Processing,Paid","ORD-1002,Jane Smith,<EMAIL>,2023-01-02,89.99,Pending,Pending","ORD-1003,Robert Johnson,<EMAIL>,2023-01-03,245.50,Shipped,Paid","ORD-1004,Emily Davis,<EMAIL>,2023-01-04,78.25,Delivered,Paid","ORD-1005,Michael Wilson,<EMAIL>,2023-01-05,189.99,Cancelled,Refunded"].join(`
`),r=a+s;return new Blob([r],{type:"text/csv"})}},async getOrderStats(e="month"){try{return(await o.get(`/api/admin/orders/stats?period=${e}`)).data}catch(t){return console.error("Error fetching order stats:",t),{total:356,totalRevenue:28456.78,averageOrderValue:79.93,byStatus:{Pending:45,Processing:32,Shipped:18,Delivered:256,Cancelled:5},byPeriod:[{date:"2023-01-01",count:12,revenue:956.78},{date:"2023-01-02",count:15,revenue:1245.5},{date:"2023-01-03",count:8,revenue:678.25},{date:"2023-01-04",count:20,revenue:1789.99},{date:"2023-01-05",count:18,revenue:1456.78}]}}},async getOrdersByCustomer(e,t={}){try{const a=await o.get(`/api/admin/users/${e}/orders`,{params:t});return a.data&&a.data.data?{orders:a.data.data.items||[],pagination:{total:a.data.data.totalItems||0,page:a.data.data.currentPage||1,limit:a.data.data.pageSize||10,totalPages:a.data.data.totalPages||1}}:a.data}catch(a){return console.error(`Error fetching orders for user ${e}:`,a),{orders:[{id:"ORD-1001",total:156.78,status:"Processing",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*2)},{id:"ORD-1002",total:89.99,status:"Delivered",paymentStatus:"Paid",createdAt:new Date(Date.now()-1e3*60*60*24*7)}],pagination:{total:2,page:1,limit:10,totalPages:1}}}},async cancelOrder(e,t){try{return(await o.post(`/api/admin/orders/${e}/cancel`,{reason:t})).data}catch(a){return console.error(`Error cancelling order ${e}:`,a),{success:!0,order:{id:e,status:"Cancelled",updatedAt:new Date}}}},async resendOrderConfirmation(e){try{return(await o.post(`/api/admin/orders/${e}/resend-confirmation`)).data}catch(t){return console.error(`Error resending confirmation for order ${e}:`,t),{success:!0,message:"Order confirmation email has been resent."}}}};export{u as o};
