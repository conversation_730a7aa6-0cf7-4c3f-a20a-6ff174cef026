import{_ as s,c as n,a as e,b as a,w as r,d,r as l,o as p}from"./index-DMg5qKr1.js";const i={name:"NotFound"},m={class:"not-found text-center py-5"};function u(c,o,f,_,x,b){const t=l("router-link");return p(),n("div",m,[o[1]||(o[1]=e("h1",{class:"display-1"},"404",-1)),o[2]||(o[2]=e("h2",{class:"mb-4"},"Page Not Found",-1)),o[3]||(o[3]=e("p",{class:"lead mb-4"},"The page you are looking for does not exist or has been moved.",-1)),a(t,{to:"/",class:"btn btn-primary"},{default:r(()=>o[0]||(o[0]=[d("Go to Home")])),_:1})])}const k=s(i,[["render",u],["__scopeId","data-v-ec3f4981"]]);export{k as default};
