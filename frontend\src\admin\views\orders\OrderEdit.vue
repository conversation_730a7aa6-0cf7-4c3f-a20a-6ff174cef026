<template>
  <div class="order-edit">
    <!-- Header -->
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <div>
            <h1 class="title">Edit Order</h1>
            <p class="subtitle" v-if="order.id">Order ID: {{ order.id }}</p>
          </div>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button 
              class="button is-light" 
              @click="$router.push('/admin/orders')"
            >
              <span class="icon">
                <i class="fas fa-arrow-left"></i>
              </span>
              <span>Back to Orders</span>
            </button>
            <button 
              class="button is-info" 
              @click="$router.push(`/admin/orders/${orderId}`)"
            >
              <span class="icon">
                <i class="fas fa-eye"></i>
              </span>
              <span>View Order</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="has-text-centered">
      <div class="loader-wrapper">
        <div class="loader is-loading"></div>
        <p>Loading order details...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="notification is-danger">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- Edit Form -->
    <div v-else-if="order.id" class="columns">
      <div class="column is-8">
        <!-- Order Status -->
        <div class="card mb-5">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-edit"></i>
              </span>
              Order Status
            </p>
          </header>
          <div class="card-content">
            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label">Order Status</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="editForm.status">
                        <option value="Pending">Pending</option>
                        <option value="Processing">Processing</option>
                        <option value="Shipped">Shipped</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Cancelled">Cancelled</option>
                        <option value="Refunded">Refunded</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">Payment Status</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="editForm.paymentStatus">
                        <option value="Pending">Pending</option>
                        <option value="Paid">Paid</option>
                        <option value="Failed">Failed</option>
                        <option value="Refunded">Refunded</option>
                        <option value="PartiallyRefunded">Partially Refunded</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Shipping Information -->
        <div class="card mb-5">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-truck"></i>
              </span>
              Shipping Information
            </p>
          </header>
          <div class="card-content">
            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label">Shipping Address</label>
                  <div class="control">
                    <textarea 
                      class="textarea" 
                      v-model="editForm.shippingAddress"
                      rows="3"
                      placeholder="Enter shipping address"
                    ></textarea>
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">Tracking Number</label>
                  <div class="control">
                    <input 
                      class="input" 
                      type="text" 
                      v-model="editForm.trackingNumber"
                      placeholder="Enter tracking number"
                    >
                  </div>
                </div>
                <div class="field">
                  <label class="label">Shipping Method</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select v-model="editForm.shippingMethod">
                        <option value="Standard">Standard Shipping</option>
                        <option value="Express">Express Shipping</option>
                        <option value="Overnight">Overnight Shipping</option>
                        <option value="Pickup">Store Pickup</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Notes -->
        <div class="card mb-5">
          <header class="card-header">
            <p class="card-header-title">
              <span class="icon">
                <i class="fas fa-sticky-note"></i>
              </span>
              Order Notes
            </p>
          </header>
          <div class="card-content">
            <div class="field">
              <label class="label">Internal Notes</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  v-model="editForm.notes"
                  rows="4"
                  placeholder="Add internal notes about this order..."
                ></textarea>
              </div>
              <p class="help">These notes are only visible to administrators</p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="field is-grouped">
          <div class="control">
            <button 
              class="button is-primary is-medium"
              @click="saveOrder"
              :class="{ 'is-loading': saving }"
              :disabled="saving"
            >
              <span class="icon">
                <i class="fas fa-save"></i>
              </span>
              <span>Save Changes</span>
            </button>
          </div>
          <div class="control">
            <button 
              class="button is-light is-medium"
              @click="resetForm"
            >
              <span class="icon">
                <i class="fas fa-undo"></i>
              </span>
              <span>Reset</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Order Summary Sidebar -->
      <div class="column is-4">
        <div class="card">
          <header class="card-header">
            <p class="card-header-title">Order Summary</p>
          </header>
          <div class="card-content">
            <div class="content">
              <p><strong>Customer:</strong> {{ order.customerName }}</p>
              <p><strong>Email:</strong> {{ order.customerEmail }}</p>
              <p><strong>Order Date:</strong> {{ formatDate(order.createdAt) }}</p>
              <p><strong>Total Amount:</strong> {{ formatCurrency(order.totalPriceAmount) }}</p>
              <p><strong>Items Count:</strong> {{ order.itemsCount }}</p>
              
              <hr>
              
              <div class="field">
                <label class="label">Current Status</label>
                <StatusBadge :status="order.status" />
              </div>
              
              <div class="field">
                <label class="label">Payment Status</label>
                <StatusBadge :status="order.paymentStatus" type="payment" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ordersService } from '@/admin/services/orders';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const saving = ref(false);
const error = ref(null);
const order = ref({});

// Form data
const editForm = reactive({
  status: '',
  paymentStatus: '',
  shippingAddress: '',
  trackingNumber: '',
  shippingMethod: '',
  notes: ''
});

// Computed
const orderId = computed(() => route.params.id);

// Methods
const fetchOrder = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    const response = await ordersService.getById(orderId.value);
    order.value = response;
    
    // Populate form with current data
    editForm.status = response.status || '';
    editForm.paymentStatus = response.paymentStatus || '';
    editForm.shippingAddress = response.shippingAddress || '';
    editForm.trackingNumber = response.trackingNumber || '';
    editForm.shippingMethod = response.shippingMethod || '';
    editForm.notes = response.notes || '';
    
  } catch (err) {
    console.error('Error fetching order:', err);
    error.value = 'Failed to load order details';
  } finally {
    loading.value = false;
  }
};

const saveOrder = async () => {
  try {
    saving.value = true;
    error.value = null;
    
    await ordersService.update(orderId.value, editForm);
    
    // Show success message
    // You can add a toast notification here
    
    // Redirect to order detail page
    router.push(`/admin/orders/${orderId.value}`);
    
  } catch (err) {
    console.error('Error saving order:', err);
    error.value = 'Failed to save order changes';
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  // Reset form to original values
  editForm.status = order.value.status || '';
  editForm.paymentStatus = order.value.paymentStatus || '';
  editForm.shippingAddress = order.value.shippingAddress || '';
  editForm.trackingNumber = order.value.trackingNumber || '';
  editForm.shippingMethod = order.value.shippingMethod || '';
  editForm.notes = order.value.notes || '';
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('uk-UA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount) => {
  if (!amount) return '₴0.00';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(amount);
};

// Lifecycle
onMounted(() => {
  fetchOrder();
});
</script>

<style scoped>
.order-edit {
  padding: 1.5rem;
}

.loader-wrapper {
  padding: 3rem;
}

.loader {
  width: 3rem;
  height: 3rem;
  margin: 0 auto 1rem;
}
</style>
