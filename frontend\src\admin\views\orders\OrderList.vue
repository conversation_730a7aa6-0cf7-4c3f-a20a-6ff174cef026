<template>
  <div class="order-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Orders</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <button 
            class="button is-primary" 
            @click="exportOrders"
            :class="{ 'is-loading': exporting }">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
            <span>Export Orders</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Orders"
      search-placeholder="Order ID, customer name, email..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="orders"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Orders Table -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading && !items.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading orders...</p>
        </div>
        <div v-else-if="!items.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-shopping-cart fa-2x"></i>
          </span>
          <p class="mt-2">No orders found</p>
          <p class="mt-2">Try adjusting your search criteria or filters</p>
        </div>
        <div v-else>
          <div class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Order ID</th>
                  <th>Customer</th>
                  <th>Date</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Payment</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="order in items" :key="order.id">
                  <td>
                    <code class="has-text-info">{{ order.id }}</code>
                  </td>
                  <td>
                    <div>
                      <strong>{{ order.customerName || order.userName || 'N/A' }}</strong>
                      <br>
                      <small class="has-text-grey">{{ order.customerEmail || 'No email' }}</small>
                    </div>
                  </td>
                  <td>{{ formatDate(order.createdAt) }}</td>
                  <td>
                    <strong class="has-text-success">{{ formatCurrency(order.total || order.totalPriceAmount) }}</strong>
                  </td>
                  <td>
                    <span class="tag" :class="getOrderStatusClass(order.status)">
                      {{ getOrderStatusText(order.status) }}
                    </span>
                  </td>
                  <td>
                    <span class="tag" :class="getPaymentStatusClass(order.paymentStatusText || order.paymentStatus)">
                      {{ getPaymentStatusText(order.paymentStatusText || order.paymentStatus) }}
                    </span>
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <router-link
                        :to="`/admin/orders/${order.id}`"
                        class="button is-info"
                        title="View Order">
                        <span class="icon is-small">
                          <i class="fas fa-eye"></i>
                        </span>
                      </router-link>
                      <router-link
                        :to="`/admin/orders/${order.id}/edit`"
                        class="button is-primary"
                        title="Edit Order">
                        <span class="icon is-small">
                          <i class="fas fa-edit"></i>
                        </span>
                      </router-link>
                      <button
                        class="button is-warning"
                        @click="openStatusModal(order)"
                        title="Update Status">
                        <span class="icon is-small">
                          <i class="fas fa-cog"></i>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <pagination
            :current-page="currentPage"
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal" :class="{ 'is-active': showStatusModal }">
      <div class="modal-background" @click="closeStatusModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Update Order Status</p>
          <button class="delete" aria-label="close" @click="closeStatusModal"></button>
        </header>
        <section class="modal-card-body">
          <div v-if="selectedOrder" class="content">
            <p><strong>Order ID:</strong> {{ selectedOrder.id }}</p>
            <p><strong>Customer:</strong> {{ selectedOrder.userName }}</p>
            <p><strong>Current Status:</strong> 
              <status-badge 
                :status="selectedOrder.status" 
                type="order" />
            </p>
            
            <div class="field">
              <label class="label">New Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newStatus">
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Payment Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="newPaymentStatus">
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="failed">Failed</option>
                    <option value="refunded">Refunded</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="field">
              <label class="label">Note (Optional)</label>
              <div class="control">
                <textarea 
                  class="textarea" 
                  v-model="statusNote" 
                  placeholder="Add a note about this status change">
                </textarea>
              </div>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button 
            class="button is-primary" 
            @click="updateOrderStatus"
            :class="{ 'is-loading': updatingStatus }">
            Update Status
          </button>
          <button class="button" @click="closeStatusModal">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ordersService } from '@/admin/services/orders';
import { exportService } from '@/admin/services/exportService';
import { useAdminSearch } from '@/composables/useAdminSearch';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';

// Action loading state
const exporting = ref(false);
const showStatusModal = ref(false);
const selectedOrder = ref(null);
const newStatus = ref('');
const newPaymentStatus = ref('');
const statusNote = ref('');
const updatingStatus = ref(false);

// Filter fields configuration (matching backend enums exactly)
const filterFields = [
  {
    key: 'status',
    label: 'Order Status',
    type: 'select',
    options: [
      { value: '', label: 'All Order Status' },
      { value: 0, label: 'Processing' },  // OrderStatus.Processing = 0
      { value: 1, label: 'Pending' },     // OrderStatus.Pending = 1
      { value: 2, label: 'Paid' },        // OrderStatus.Paid = 2
      { value: 3, label: 'Shipped' },     // OrderStatus.Shipped = 3
      { value: 4, label: 'Delivered' },   // OrderStatus.Delivered = 4
      { value: 5, label: 'Cancelled' }    // OrderStatus.Cancelled = 5
    ]
  },
  {
    key: 'paymentStatus',
    label: 'Payment Status',
    type: 'select',
    options: [
      { value: '', label: 'All Payment Status' },
      { value: 0, label: 'Pending' },     // PaymentStatus.Pending = 0
      { value: 1, label: 'Completed' },   // PaymentStatus.Completed = 1
      { value: 2, label: 'Failed' }       // PaymentStatus.Failed = 2
    ]
  },
  {
    key: 'dateRange',
    label: 'Date Range',
    type: 'select',
    options: [
      { value: '', label: 'All Time' },
      { value: 'today', label: 'Today' },
      { value: 'yesterday', label: 'Yesterday' },
      { value: 'last7days', label: 'Last 7 Days' },
      { value: 'last30days', label: 'Last 30 Days' },
      { value: 'thisMonth', label: 'This Month' },
      { value: 'lastMonth', label: 'Last Month' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: ordersService.getAllAdmin,
  defaultFilters: {
    status: '',
    paymentStatus: '',
    dateRange: ''
  },
  debounceTime: 300,
  defaultPageSize: 10,
  clientSideSearch: false
});



// Event handlers
const handleSearchChange = (searchValue) => {
  console.log('Search changed to:', searchValue);
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  console.log(`Filter ${filterKey} changed to:`, filterValue);
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  console.log('Resetting filters');
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = '';
    }
  });
  // Trigger data fetch after reset
  fetchData(1);
};

// Export orders
const exportOrders = async () => {
  try {
    exporting.value = true;

    // Prepare export filters using current filters
    const exportFilters = { ...filters };

    // Remove empty filters
    Object.keys(exportFilters).forEach(key => {
      if (!exportFilters[key]) {
        delete exportFilters[key];
      }
    });

    const result = await exportService.exportOrdersToExcel(exportFilters, 'orders_export');

    // Show success notification
    console.log('Export successful:', result);
    alert(`Successfully exported orders to ${result.filename}`);

  } catch (error) {
    console.error('Export failed:', error);
    alert(`Export failed: ${error.message}`);
  } finally {
    exporting.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency
const formatCurrency = (value) => {
  if (!value) return '₴0.00';

  // Округлюємо до 2 знаків після коми
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};

// Order status functions (matching backend enums)
const getOrderStatusText = (status) => {
  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Processing',  // OrderStatus.Processing = 0
    1: 'Pending',     // OrderStatus.Pending = 1
    2: 'Paid',        // OrderStatus.Paid = 2
    3: 'Shipped',     // OrderStatus.Shipped = 3
    4: 'Delivered',   // OrderStatus.Delivered = 4
    5: 'Cancelled',   // OrderStatus.Cancelled = 5
    // String values (for backward compatibility)
    'Processing': 'Processing',
    'Pending': 'Pending',
    'Paid': 'Paid',
    'Shipped': 'Shipped',
    'Delivered': 'Delivered',
    'Cancelled': 'Cancelled'
  };
  return statusMap[status] || status || 'Unknown';
};

const getOrderStatusClass = (status) => {
  const classMap = {
    // Numeric values (backend enum values)
    0: 'is-info',     // Processing
    1: 'is-warning',  // Pending
    2: 'is-success',  // Paid
    3: 'is-primary',  // Shipped
    4: 'is-success',  // Delivered
    5: 'is-danger',   // Cancelled
    // String values (for backward compatibility)
    'Processing': 'is-info',
    'Pending': 'is-warning',
    'Paid': 'is-success',
    'Shipped': 'is-primary',
    'Delivered': 'is-success',
    'Cancelled': 'is-danger'
  };
  return classMap[status] || 'is-light';
};

// Payment status functions (matching backend enums)
const getPaymentStatusText = (status) => {
  // Handle null/undefined values
  if (status === null || status === undefined) {
    return 'Unknown';
  }

  const statusMap = {
    // Numeric values (backend enum values)
    0: 'Pending',     // PaymentStatus.Pending = 0
    1: 'Completed',   // PaymentStatus.Completed = 1
    2: 'Failed',      // PaymentStatus.Failed = 2
    // String values (for backward compatibility)
    'Pending': 'Pending',
    'Completed': 'Completed',
    'Failed': 'Failed'
  };
  return statusMap[status] || status || 'Unknown';
};

const getPaymentStatusClass = (status) => {
  // Handle null/undefined values
  if (status === null || status === undefined) {
    return 'is-light';
  }

  const classMap = {
    // Numeric values (backend enum values)
    0: 'is-warning',  // Pending
    1: 'is-success',  // Completed
    2: 'is-danger',   // Failed
    // String values (for backward compatibility)
    'Pending': 'is-warning',
    'Completed': 'is-success',
    'Failed': 'is-danger'
  };
  return classMap[status] || 'is-light';
};





// Open status modal
const openStatusModal = (order) => {
  selectedOrder.value = order;
  newStatus.value = order.status;
  newPaymentStatus.value = order.paymentStatusText || order.paymentStatus;
  statusNote.value = '';
  showStatusModal.value = true;
};

// Close status modal
const closeStatusModal = () => {
  showStatusModal.value = false;
  selectedOrder.value = null;
};

// Update order status
const updateOrderStatus = async () => {
  if (!selectedOrder.value) return;
  
  updatingStatus.value = true;
  
  try {
    // Update order status
    if (newStatus.value !== selectedOrder.value.status) {
      await ordersService.updateOrderStatus(selectedOrder.value.id, newStatus.value);
    }

    // Update payment status
    if (newPaymentStatus.value !== selectedOrder.value.paymentStatus) {
      await ordersService.updatePaymentStatus(selectedOrder.value.id, newPaymentStatus.value);
    }

    // Add note if provided
    if (statusNote.value.trim()) {
      await ordersService.addOrderNote(selectedOrder.value.id, statusNote.value);
    }
    
    // Update the order in the list
    const index = items.value.findIndex(o => o.id === selectedOrder.value.id);
    if (index !== -1) {
      items.value[index].status = newStatus.value;
      items.value[index].paymentStatusText = newPaymentStatus.value;
    }
    
    // Close modal
    closeStatusModal();
  } catch (error) {
    console.error('Error updating order status:', error);
  } finally {
    updatingStatus.value = false;
  }
};





// Lifecycle hooks
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.order-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f5;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}
</style>
