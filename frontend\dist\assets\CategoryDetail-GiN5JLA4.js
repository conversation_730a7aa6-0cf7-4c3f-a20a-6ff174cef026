import{_ as U,g as u,h as N,f as K,i as O,c as o,a as s,b as d,w as c,r as R,d as p,t as n,F as w,p as x,k as H,n as m,I as y,e as z,o as l}from"./index-BtyG65bR.js";import{C as j}from"./ConfirmDialog-Cm3a8HQn.js";const q={class:"category-detail"},G={class:"level"},J={class:"level-right"},Q={class:"level-item"},W={key:0,class:"has-text-centered py-6"},X={key:1,class:"notification is-danger"},Y={key:2,class:"notification is-warning"},Z={key:3},ss={class:"card mb-4"},ts={class:"card-content"},es={class:"columns"},as={class:"column is-8"},os={class:"category-title"},ls={class:"category-path"},is={key:0},ns={key:0,class:"path-separator"},rs={class:"current-category"},ds={key:1,class:"root-category"},cs={class:"column is-4 has-text-right"},us={class:"buttons is-right"},vs={class:"columns"},gs={class:"column is-4"},fs={class:"card"},ps={class:"card-content"},ms={class:"category-image"},ys=["src","alt"],_s={class:"card mt-4"},hs={class:"card-content"},bs={class:"info-group"},Cs={class:"info-value"},ks={class:"info-group"},ws={class:"info-value"},xs={class:"info-group"},Is={class:"info-value"},Ds={class:"info-group"},As={class:"info-value"},Ns={class:"info-group"},Es={class:"info-value"},Fs={class:"info-group"},Ps={class:"info-value"},Vs={class:"info-group"},Ss={class:"info-value"},$s={class:"column is-8"},Bs={class:"card"},Ls={class:"card-content"},Ms={class:"content"},Ts={key:0},Us={key:1,class:"has-text-grey"},Ks={class:"card mt-4"},Os={class:"card-content"},Rs={class:"info-group"},Hs={class:"info-value"},zs={class:"info-group"},js={class:"info-value"},qs={class:"info-group"},Gs={key:0,class:"tags"},Js={key:1,class:"has-text-grey"},Qs={class:"card mt-4"},Ws={class:"card-header"},Xs={class:"card-header-icon"},Ys={class:"card-content"},Zs={key:0,class:"has-text-centered py-4"},st={key:1,class:"has-text-centered py-4"},tt={key:2},et={class:"table-container"},at={class:"table is-fullwidth is-hoverable"},ot={class:"image-cell"},lt={class:"image is-48x48"},it=["src","alt"],nt={class:"buttons are-small"},rt={__name:"CategoryDetail",setup(dt){const E=K(),F=z(),_=u(!0),h=u(!1),v=u(null),e=u({}),I=u([]),g=u([]),f=u(!1),b=N(()=>E.params.id),C=N(()=>{if(!e.value.parentId)return[];const a=[];let t=e.value.parentId;for(;t;){const r=I.value.find(i=>i.id===t);if(r)a.unshift(r),t=r.parentId;else break}return a}),P=async()=>{_.value=!0,v.value=null;try{const a=await y.getCategoryById(b.value);e.value=a}catch(a){console.error("Error fetching category:",a),v.value="Failed to load category data. Please try again."}finally{_.value=!1}},V=async()=>{try{const a=await y.getCategories();a.categories&&(I.value=a.categories)}catch(a){console.error("Error fetching categories:",a)}},S=async()=>{h.value=!0;try{const a=await y.getCategoryProducts(b.value,{limit:5});a.products?g.value=a.products:g.value=[]}catch(a){console.error("Error fetching category products:",a)}finally{h.value=!1}},D=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(a)):"N/A",$=a=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(a),B=a=>{if(!a)return"is-light";switch(a.toLowerCase()){case"active":return"is-success";case"inactive":return"is-warning";case"draft":return"is-info";default:return"is-light"}},A=a=>{a.target.src="https://via.placeholder.com/400?text=No+Image"},L=()=>{f.value=!0},M=async()=>{try{await y.deleteCategory(b.value),F.push("/admin/categories")}catch(a){console.error("Error deleting category:",a),v.value="Failed to delete category. Please try again."}finally{f.value=!1}},T=()=>{f.value=!1};return O(()=>{P(),V(),S()}),(a,t)=>{const r=R("router-link");return l(),o("div",q,[s("div",G,[t[2]||(t[2]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Category Details")])],-1)),s("div",J,[s("div",Q,[d(r,{to:"/admin/categories",class:"button is-light"},{default:c(()=>t[1]||(t[1]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Categories",-1)])),_:1})])])]),_.value?(l(),o("div",W,t[3]||(t[3]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading category details...",-1)]))):v.value?(l(),o("div",X,[s("button",{class:"delete",onClick:t[0]||(t[0]=i=>v.value=null)}),p(" "+n(v.value),1)])):e.value.id?(l(),o("div",Z,[s("div",ss,[s("div",ts,[s("div",es,[s("div",as,[s("h2",os,n(e.value.name),1),s("p",ls,[C.value.length>0?(l(),o("span",is,[(l(!0),o(w,null,x(C.value,(i,k)=>(l(),o("span",{key:i.id},[d(r,{to:`/admin/categories/${i.id}`},{default:c(()=>[p(n(i.name),1)]),_:2},1032,["to"]),k<C.value.length-1?(l(),o("span",ns,t[6]||(t[6]=[s("i",{class:"fas fa-chevron-right"},null,-1)]))):H("",!0)]))),128)),t[7]||(t[7]=s("span",{class:"path-separator"},[s("i",{class:"fas fa-chevron-right"})],-1)),s("span",rs,n(e.value.name),1)])):(l(),o("span",ds,t[8]||(t[8]=[s("i",{class:"fas fa-folder"},null,-1),p(" Root Category ")])))])]),s("div",cs,[s("div",us,[d(r,{to:`/admin/categories/${e.value.id}/edit`,class:"button is-primary"},{default:c(()=>t[9]||(t[9]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Edit",-1)])),_:1},8,["to"]),s("button",{class:"button is-danger",onClick:L},t[10]||(t[10]=[s("span",{class:"icon"},[s("i",{class:"fas fa-trash"})],-1),s("span",null,"Delete",-1)]))])])])])]),s("div",vs,[s("div",gs,[s("div",fs,[t[11]||(t[11]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Category Image")],-1)),s("div",ps,[s("div",ms,[s("img",{src:e.value.image||"https://via.placeholder.com/400?text=No+Image",alt:e.value.name,onError:A},null,40,ys)])])]),s("div",_s,[t[19]||(t[19]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Category Information")],-1)),s("div",hs,[s("div",bs,[t[12]||(t[12]=s("h3",{class:"info-label"},"Status",-1)),s("p",Cs,[s("span",{class:m(["tag",e.value.isActive?"is-success":"is-danger"])},n(e.value.isActive?"Active":"Inactive"),3)])]),s("div",ks,[t[13]||(t[13]=s("h3",{class:"info-label"},"Visibility",-1)),s("p",ws,[s("span",{class:m(["tag",e.value.isVisible?"is-success":"is-warning"])},n(e.value.isVisible?"Visible":"Hidden"),3)])]),s("div",xs,[t[14]||(t[14]=s("h3",{class:"info-label"},"Featured",-1)),s("p",Is,[s("span",{class:m(["tag",e.value.isFeatured?"is-primary":"is-light"])},n(e.value.isFeatured?"Featured":"Not Featured"),3)])]),s("div",Ds,[t[15]||(t[15]=s("h3",{class:"info-label"},"Display Order",-1)),s("p",As,n(e.value.displayOrder||0),1)]),s("div",Ns,[t[16]||(t[16]=s("h3",{class:"info-label"},"Slug",-1)),s("p",Es,n(e.value.slug||"N/A"),1)]),s("div",Fs,[t[17]||(t[17]=s("h3",{class:"info-label"},"Created",-1)),s("p",Ps,n(D(e.value.createdAt)),1)]),s("div",Vs,[t[18]||(t[18]=s("h3",{class:"info-label"},"Last Updated",-1)),s("p",Ss,n(D(e.value.updatedAt)),1)])])])]),s("div",$s,[s("div",Bs,[t[20]||(t[20]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Description")],-1)),s("div",Ls,[s("div",Ms,[e.value.description?(l(),o("p",Ts,n(e.value.description),1)):(l(),o("p",Us,"No description provided"))])])]),s("div",Ks,[t[24]||(t[24]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"SEO Information")],-1)),s("div",Os,[s("div",Rs,[t[21]||(t[21]=s("h3",{class:"info-label"},"Meta Title",-1)),s("p",Hs,n(e.value.metaTitle||e.value.name||"N/A"),1)]),s("div",zs,[t[22]||(t[22]=s("h3",{class:"info-label"},"Meta Description",-1)),s("p",js,n(e.value.metaDescription||"N/A"),1)]),s("div",qs,[t[23]||(t[23]=s("h3",{class:"info-label"},"Meta Keywords",-1)),e.value.metaKeywords?(l(),o("div",Gs,[(l(!0),o(w,null,x(e.value.metaKeywords.split(","),(i,k)=>(l(),o("span",{key:k,class:"tag is-info"},n(i.trim()),1))),128))])):(l(),o("p",Js,"No keywords provided"))])])]),s("div",Qs,[s("div",Ws,[t[26]||(t[26]=s("p",{class:"card-header-title"},"Products in Category",-1)),s("div",Xs,[d(r,{to:`/admin/products?categoryId=${e.value.id}`,class:"button is-small is-primary is-outlined"},{default:c(()=>t[25]||(t[25]=[s("span",null,"View All",-1),s("span",{class:"icon is-small"},[s("i",{class:"fas fa-arrow-right"})],-1)])),_:1},8,["to"])])]),s("div",Ys,[h.value?(l(),o("div",Zs,t[27]||(t[27]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading products...",-1)]))):g.value.length?(l(),o("div",tt,[s("div",et,[s("table",at,[t[33]||(t[33]=s("thead",null,[s("tr",null,[s("th",null,"Image"),s("th",null,"Name"),s("th",null,"Price"),s("th",null,"Stock"),s("th",null,"Status"),s("th",null,"Actions")])],-1)),s("tbody",null,[(l(!0),o(w,null,x(g.value,i=>(l(),o("tr",{key:i.id},[s("td",ot,[s("figure",lt,[s("img",{src:i.image||"https://via.placeholder.com/48",alt:i.name,onError:A},null,40,it)])]),s("td",null,n(i.name),1),s("td",null,n($(i.price)),1),s("td",null,n(i.stock),1),s("td",null,[s("span",{class:m(["tag",B(i.status)])},n(i.status),3)]),s("td",null,[s("div",nt,[d(r,{to:`/admin/products/${i.id}`,class:"button is-info",title:"View"},{default:c(()=>t[31]||(t[31]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),d(r,{to:`/admin/products/${i.id}/edit`,class:"button is-primary",title:"Edit"},{default:c(()=>t[32]||(t[32]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})],-1)])),_:2},1032,["to"])])])]))),128))])])])])):(l(),o("div",st,[t[29]||(t[29]=s("span",{class:"icon is-large"},[s("i",{class:"fas fa-box fa-2x"})],-1)),t[30]||(t[30]=s("p",{class:"mt-2"},"No products in this category",-1)),d(r,{to:`/admin/products/create?categoryId=${e.value.id}`,class:"button is-primary mt-4"},{default:c(()=>t[28]||(t[28]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Product to Category",-1)])),_:1},8,["to"])]))])])])])])):(l(),o("div",Y,[t[5]||(t[5]=s("p",null,"Category not found.",-1)),d(r,{to:"/admin/categories",class:"button is-primary mt-4"},{default:c(()=>t[4]||(t[4]=[p(" Back to Categories ")])),_:1})])),d(j,{"is-open":f.value,title:"Delete Category",message:`Are you sure you want to delete '${e.value.name}'? This may affect products in this category.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:M,onCancel:T},null,8,["is-open","message"])])}}},vt=U(rt,[["__scopeId","data-v-b898dff7"]]);export{vt as default};
