import{_ as I,u as Y,e as ee,f as te,g as W,h as R,i as B,j as J,c as S,o as C,k as T,a as e,l as ae,n as h,m as D,d as k,F as E,r as ne,t as O,p as se,b as P,w as L,q as Q,G as ie,T as oe,s as re,v as le,E as de}from"./index-DMg5qKr1.js";const ue={class:"sidebar-container"},ce={class:"panel sidebar"},me={class:"menu-list"},fe={class:"menu-list"},ve={class:"menu-list"},he={class:"menu-list"},ge={class:"menu-list"},be={class:"menu-list"},pe={class:"menu-list"},ye={class:"panel-block logout-container"},we=["disabled"],X=300,ke={__name:"Sidebar",props:{isVisible:{type:Boolean,default:!0}},emits:["toggle-sidebar"],setup(n,{emit:s}){const t=Y(),o=ee(),i=te(),u=W(!1),l=W(!1),r=W(0),p=s,v=R(()=>t.getters["auth/isAdmin"]);R(()=>t.getters["auth/isModerator"]),R(()=>t.getters["auth/isAdminOrModerator"]);const w=()=>{p("toggle-sidebar")},m=c=>i.path===c||i.path.startsWith(`${c}/`),d=c=>{const a=Date.now();if(l.value||a-r.value<X||m(c))return;l.value=!0,r.value=a;const y=t.getters["loading/apiService"];y&&y.cancelRequestsForRoute(i.path);let g="",q={};c==="/admin/dashboard"?g="AdminDashboard":c==="/admin/users"?g="AdminUsers":c==="/admin/products"?g="AdminProducts":c==="/admin/categories"?g="AdminCategories":c==="/admin/orders"?g="AdminOrders":c==="/admin/seller-requests"?g="AdminSellerRequests":c==="/admin/companies"?g="AdminCompanies":c==="/admin/reviews"?g="AdminReviews":c==="/admin/ratings"?g="AdminRatings":c==="/admin/chats"?g="AdminChats":c==="/admin/addresses"?g="AdminAddresses":c==="/admin/settings"?g="AdminSettings":c==="/admin/reports"?g="AdminReports":c==="/admin/security"?g="AdminSecurity":g="",g?o.push({name:g,params:q}).finally(()=>{setTimeout(()=>{l.value=!1},X)}):o.push(c).finally(()=>{setTimeout(()=>{l.value=!1},X)}),u.value&&w()},A=async()=>{if(!l.value){l.value=!0;try{await t.dispatch("auth/logout"),o.push("/login")}finally{setTimeout(()=>{l.value=!1},X)}}},M=()=>{u.value=window.innerWidth<1024};return B(()=>{M(),window.addEventListener("resize",M)}),J(()=>{window.removeEventListener("resize",M)}),(c,a)=>(C(),S("div",ue,[u.value?(C(),S("button",{key:0,class:"sidebar-close is-hidden-desktop",onClick:w},a[14]||(a[14]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1)]))):T("",!0),a[37]||(a[37]=e("div",{class:"sidebar-header"},[e("div",{class:"logo-container"},[e("img",{src:ae,alt:"Klondike",class:"logo"}),e("h2",{class:"title is-5 has-text-white"},"Klondike Admin")])],-1)),e("nav",ce,[a[34]||(a[34]=e("p",{class:"menu-label"},"General",-1)),e("div",me,[e("a",{onClick:a[0]||(a[0]=D(y=>d("/admin/dashboard"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/dashboard")}])},a[15]||(a[15]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-tachometer-alt"})],-1),k(" Dashboard ")]),2)]),a[35]||(a[35]=e("p",{class:"menu-label"},"Users",-1)),e("div",fe,[v.value?(C(),S("a",{key:0,onClick:a[1]||(a[1]=D(y=>d("/admin/users"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/users")}])},a[16]||(a[16]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-users"})],-1),k(" Users ")]),2)):T("",!0),e("a",{onClick:a[2]||(a[2]=D(y=>d("/admin/seller-requests"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/seller-requests")}])},a[17]||(a[17]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-user-plus"})],-1),k(" Seller Applications ")]),2),e("a",{onClick:a[3]||(a[3]=D(y=>d("/admin/companies"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/companies")}])},a[18]||(a[18]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-building"})],-1),k(" Companies ")]),2)]),v.value?(C(),S(E,{key:0},[a[23]||(a[23]=e("p",{class:"menu-label"},"Catalog",-1)),e("div",ve,[e("a",{onClick:a[4]||(a[4]=D(y=>d("/admin/products"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/products")}])},a[19]||(a[19]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-box"})],-1),k(" Products ")]),2),e("a",{onClick:a[5]||(a[5]=D(y=>d("/admin/categories"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/categories")}])},a[20]||(a[20]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-tags"})],-1),k(" Categories ")]),2)]),a[24]||(a[24]=e("p",{class:"menu-label"},"Sales",-1)),e("div",he,[e("a",{onClick:a[6]||(a[6]=D(y=>d("/admin/orders"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/orders")}])},a[21]||(a[21]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-shopping-cart"})],-1),k(" Orders ")]),2),e("a",{onClick:a[7]||(a[7]=D(y=>d("/admin/reports"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/reports")}])},a[22]||(a[22]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-chart-bar"})],-1),k(" Reports ")]),2)])],64)):T("",!0),a[36]||(a[36]=e("p",{class:"menu-label"},"Communication",-1)),e("div",ge,[e("a",{onClick:a[8]||(a[8]=D(y=>d("/admin/reviews"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/reviews")}])},a[25]||(a[25]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-star"})],-1),k(" Reviews ")]),2),e("a",{onClick:a[9]||(a[9]=D(y=>d("/admin/ratings"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/ratings")}])},a[26]||(a[26]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-star-half-alt"})],-1),k(" Ratings ")]),2),e("a",{onClick:a[10]||(a[10]=D(y=>d("/admin/chats"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/chats")}])},a[27]||(a[27]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-comments"})],-1),k(" Chats ")]),2)]),v.value?(C(),S(E,{key:1},[a[29]||(a[29]=e("p",{class:"menu-label"},"System",-1)),e("div",be,[e("a",{onClick:a[11]||(a[11]=D(y=>d("/admin/addresses"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/addresses")}])},a[28]||(a[28]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-map-marker-alt"})],-1),k(" Addresses ")]),2)])],64)):T("",!0),v.value?(C(),S(E,{key:2},[a[32]||(a[32]=e("p",{class:"menu-label"},"Configuration",-1)),e("div",pe,[e("a",{onClick:a[12]||(a[12]=D(y=>d("/admin/settings"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/settings")}])},a[30]||(a[30]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-cog"})],-1),k(" Settings ")]),2),e("a",{onClick:a[13]||(a[13]=D(y=>d("/admin/security"),["prevent"])),class:h(["panel-block",{"is-active":m("/admin/security")}])},a[31]||(a[31]=[e("span",{class:"panel-icon"},[e("i",{class:"fas fa-shield-alt"})],-1),k(" Security & Logs ")]),2)])],64)):T("",!0),e("div",ye,[e("button",{class:"button is-danger is-fullwidth",onClick:A,disabled:l.value},a[33]||(a[33]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sign-out-alt"})],-1),e("span",null,"Logout",-1)]),8,we)])])]))}},Me=I(ke,[["__scopeId","data-v-9c5dc82f"]]),z=43200,K=1440,G=Symbol.for("constructDateFrom");function U(n,s){return typeof n=="function"?n(s):n&&typeof n=="object"&&G in n?n[G](s):n instanceof Date?new n.constructor(s):new Date(s)}function $(n,s){return U(n,n)}let De={};function Ce(){return De}function Z(n){const s=$(n),t=new Date(Date.UTC(s.getFullYear(),s.getMonth(),s.getDate(),s.getHours(),s.getMinutes(),s.getSeconds(),s.getMilliseconds()));return t.setUTCFullYear(s.getFullYear()),+n-+t}function H(n,...s){const t=U.bind(null,n||s.find(o=>typeof o=="object"));return s.map(t)}function V(n,s){const t=+$(n)-+$(s);return t<0?-1:t>0?1:t}function Se(n){return U(n,Date.now())}function Ae(n,s,t){const[o,i]=H(t==null?void 0:t.in,n,s),u=o.getFullYear()-i.getFullYear(),l=o.getMonth()-i.getMonth();return u*12+l}function Pe(n){return s=>{const o=(n?Math[n]:Math.trunc)(s);return o===0?0:o}}function We(n,s){return+$(n)-+$(s)}function $e(n,s){const t=$(n);return t.setHours(23,59,59,999),t}function Re(n,s){const t=$(n),o=t.getMonth();return t.setFullYear(t.getFullYear(),o+1,0),t.setHours(23,59,59,999),t}function xe(n,s){const t=$(n);return+$e(t)==+Re(t)}function Te(n,s,t){const[o,i,u]=H(t==null?void 0:t.in,n,n,s),l=V(i,u),r=Math.abs(Ae(i,u));if(r<1)return 0;i.getMonth()===1&&i.getDate()>27&&i.setDate(30),i.setMonth(i.getMonth()-l*r);let p=V(i,u)===-l;xe(o)&&r===1&&V(o,u)===1&&(p=!1);const v=l*(r-+p);return v===0?0:v}function Fe(n,s,t){const o=We(n,s)/1e3;return Pe(t==null?void 0:t.roundingMethod)(o)}const Ne={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},_e=(n,s,t)=>{let o;const i=Ne[n];return typeof i=="string"?o=i:s===1?o=i.one:o=i.other.replace("{{count}}",s.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+o:o+" ago":o};function j(n){return(s={})=>{const t=s.width?String(s.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const Le={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},qe={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Oe={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Xe={date:j({formats:Le,defaultWidth:"full"}),time:j({formats:qe,defaultWidth:"full"}),dateTime:j({formats:Oe,defaultWidth:"full"})},ze={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ee=(n,s,t,o)=>ze[n];function N(n){return(s,t)=>{const o=t!=null&&t.context?String(t.context):"standalone";let i;if(o==="formatting"&&n.formattingValues){const l=n.defaultFormattingWidth||n.defaultWidth,r=t!=null&&t.width?String(t.width):l;i=n.formattingValues[r]||n.formattingValues[l]}else{const l=n.defaultWidth,r=t!=null&&t.width?String(t.width):n.defaultWidth;i=n.values[r]||n.values[l]}const u=n.argumentCallback?n.argumentCallback(s):s;return i[u]}}const Ve={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},je={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Ie={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Ye={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Be={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Je={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Ue=(n,s)=>{const t=Number(n),o=t%100;if(o>20||o<10)switch(o%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},He={ordinalNumber:Ue,era:N({values:Ve,defaultWidth:"wide"}),quarter:N({values:je,defaultWidth:"wide",argumentCallback:n=>n-1}),month:N({values:Ie,defaultWidth:"wide"}),day:N({values:Ye,defaultWidth:"wide"}),dayPeriod:N({values:Be,defaultWidth:"wide",formattingValues:Je,defaultFormattingWidth:"wide"})};function _(n){return(s,t={})=>{const o=t.width,i=o&&n.matchPatterns[o]||n.matchPatterns[n.defaultMatchWidth],u=s.match(i);if(!u)return null;const l=u[0],r=o&&n.parsePatterns[o]||n.parsePatterns[n.defaultParseWidth],p=Array.isArray(r)?Ke(r,m=>m.test(l)):Qe(r,m=>m.test(l));let v;v=n.valueCallback?n.valueCallback(p):p,v=t.valueCallback?t.valueCallback(v):v;const w=s.slice(l.length);return{value:v,rest:w}}}function Qe(n,s){for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&s(n[t]))return t}function Ke(n,s){for(let t=0;t<n.length;t++)if(s(n[t]))return t}function Ge(n){return(s,t={})=>{const o=s.match(n.matchPattern);if(!o)return null;const i=o[0],u=s.match(n.parsePattern);if(!u)return null;let l=n.valueCallback?n.valueCallback(u[0]):u[0];l=t.valueCallback?t.valueCallback(l):l;const r=s.slice(i.length);return{value:l,rest:r}}}const Ze=/^(\d+)(th|st|nd|rd)?/i,et=/\d+/i,tt={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},nt={any:[/^b/i,/^(a|c)/i]},at={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},st={any:[/1/i,/2/i,/3/i,/4/i]},it={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},ot={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},rt={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},lt={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},dt={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},ut={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},ct={ordinalNumber:Ge({matchPattern:Ze,parsePattern:et,valueCallback:n=>parseInt(n,10)}),era:_({matchPatterns:tt,defaultMatchWidth:"wide",parsePatterns:nt,defaultParseWidth:"any"}),quarter:_({matchPatterns:at,defaultMatchWidth:"wide",parsePatterns:st,defaultParseWidth:"any",valueCallback:n=>n+1}),month:_({matchPatterns:it,defaultMatchWidth:"wide",parsePatterns:ot,defaultParseWidth:"any"}),day:_({matchPatterns:rt,defaultMatchWidth:"wide",parsePatterns:lt,defaultParseWidth:"any"}),dayPeriod:_({matchPatterns:dt,defaultMatchWidth:"any",parsePatterns:ut,defaultParseWidth:"any"})},mt={code:"en-US",formatDistance:_e,formatLong:Xe,formatRelative:Ee,localize:He,match:ct,options:{weekStartsOn:0,firstWeekContainsDate:1}};function ft(n,s,t){const o=Ce(),i=(t==null?void 0:t.locale)??o.locale??mt,u=2520,l=V(n,s);if(isNaN(l))throw new RangeError("Invalid time value");const r=Object.assign({},t,{addSuffix:t==null?void 0:t.addSuffix,comparison:l}),[p,v]=H(t==null?void 0:t.in,...l>0?[s,n]:[n,s]),w=Fe(v,p),m=(Z(v)-Z(p))/1e3,d=Math.round((w-m)/60);let A;if(d<2)return t!=null&&t.includeSeconds?w<5?i.formatDistance("lessThanXSeconds",5,r):w<10?i.formatDistance("lessThanXSeconds",10,r):w<20?i.formatDistance("lessThanXSeconds",20,r):w<40?i.formatDistance("halfAMinute",0,r):w<60?i.formatDistance("lessThanXMinutes",1,r):i.formatDistance("xMinutes",1,r):d===0?i.formatDistance("lessThanXMinutes",1,r):i.formatDistance("xMinutes",d,r);if(d<45)return i.formatDistance("xMinutes",d,r);if(d<90)return i.formatDistance("aboutXHours",1,r);if(d<K){const M=Math.round(d/60);return i.formatDistance("aboutXHours",M,r)}else{if(d<u)return i.formatDistance("xDays",1,r);if(d<z){const M=Math.round(d/K);return i.formatDistance("xDays",M,r)}else if(d<z*2)return A=Math.round(d/z),i.formatDistance("aboutXMonths",A,r)}if(A=Te(v,p),A<12){const M=Math.round(d/z);return i.formatDistance("xMonths",M,r)}else{const M=A%12,c=Math.trunc(A/12);return M<3?i.formatDistance("aboutXYears",c,r):M<9?i.formatDistance("overXYears",c,r):i.formatDistance("almostXYears",c+1,r)}}function vt(n,s){return ft(n,Se(n),s)}const ht={class:"admin-header"},gt={class:"navbar",role:"navigation","aria-label":"main navigation"},bt={class:"navbar-end"},pt={class:"icon"},yt={key:0,class:"notification-badge"},wt={class:"navbar-dropdown is-right notifications-dropdown"},kt={key:0,class:"notifications-list"},Mt=["onClick"],Dt={class:"notification-icon"},Ct={class:"icon"},St={class:"notification-content"},At={class:"notification-text"},Pt={class:"notification-time"},Wt={key:1,class:"notifications-empty"},$t={class:"notifications-footer"},Rt={class:"navbar-dropdown is-right"},xt={__name:"Header",emits:["toggle-sidebar"],setup(n,{emit:s}){const t=Y(),o=ee(),i=R(()=>{var b;return((b=t.getters["auth/user"])==null?void 0:b.username)||"Admin"}),u=W(!1),l=W(!1),r=W(!1),p=W([{id:1,type:"order",message:"New order #12345 has been placed",isRead:!1,createdAt:new Date(Date.now()-1e3*60*30)},{id:2,type:"seller",message:"New seller application from John Doe",isRead:!1,createdAt:new Date(Date.now()-1e3*60*60*2)},{id:3,type:"product",message:'Product "iPhone 13" is low in stock (2 remaining)',isRead:!0,createdAt:new Date(Date.now()-1e3*60*60*5)}]),v=R(()=>p.value.filter(b=>!b.isRead).length),w=s,m=()=>{w("toggle-sidebar")},d=()=>{l.value=!l.value,l.value&&(r.value=!1)},A=()=>{r.value=!r.value,r.value&&(l.value=!1)},M=async()=>{await t.dispatch("auth/logout"),o.push("/login")},c=()=>{p.value.forEach(b=>{b.isRead=!0})},a=b=>{b.isRead=!0,b.type==="order"?o.push("/admin/orders"):b.type==="seller"?o.push("/admin/seller-requests"):b.type==="product"&&o.push("/admin/products"),r.value=!1},y=b=>{switch(b){case"order":return"fas fa-shopping-cart";case"seller":return"fas fa-user-plus";case"product":return"fas fa-box";default:return"fas fa-bell"}},g=b=>vt(new Date(b),{addSuffix:!0}),q=b=>{const f=document.querySelector(".navbar-item.has-dropdown:last-child"),F=document.querySelector(".navbar-item.has-dropdown:first-child");f&&!f.contains(b.target)&&(l.value=!1),F&&!F.contains(b.target)&&(r.value=!1)};return B(()=>{document.addEventListener("click",q)}),J(()=>{document.removeEventListener("click",q)}),(b,f)=>{const F=ne("router-link");return C(),S("header",ht,[e("nav",gt,[e("button",{class:"sidebar-toggle is-hidden-desktop",onClick:m},f[0]||(f[0]=[e("span",{class:"icon"},[e("i",{class:"fas fa-bars"})],-1)])),f[9]||(f[9]=e("div",{class:"navbar-brand"},[e("h1",{class:"navbar-item title is-4"},"Klondike Admin")],-1)),e("div",{class:h(["navbar-menu",{"is-active":u.value}])},[e("div",bt,[e("div",{class:h(["navbar-item has-dropdown",{"is-active":r.value}])},[e("a",{class:"navbar-link notification-link",onClick:A},[e("span",pt,[f[1]||(f[1]=e("i",{class:"fas fa-bell"},null,-1)),v.value>0?(C(),S("span",yt,O(v.value),1)):T("",!0)])]),e("div",wt,[e("div",{class:"notifications-header"},[f[2]||(f[2]=e("h3",{class:"title is-6"},"Notifications",-1)),e("a",{onClick:c,class:"is-size-7"},"Mark all as read")]),p.value.length>0?(C(),S("div",kt,[(C(!0),S(E,null,se(p.value,x=>(C(),S("a",{key:x.id,class:h(["navbar-item notification-item",{"is-unread":!x.isRead}]),onClick:Xt=>a(x)},[e("div",Dt,[e("span",Ct,[e("i",{class:h(y(x.type))},null,2)])]),e("div",St,[e("p",At,O(x.message),1),e("p",Pt,O(g(x.createdAt)),1)])],10,Mt))),128))])):(C(),S("div",Wt,f[3]||(f[3]=[e("p",null,"No notifications",-1)]))),e("div",$t,[P(F,{to:"/admin/notifications",class:"navbar-item"},{default:L(()=>f[4]||(f[4]=[k(" View all notifications ")])),_:1})])])],2),e("div",{class:h(["navbar-item has-dropdown",{"is-active":l.value}])},[e("a",{class:"navbar-link",onClick:d},[f[5]||(f[5]=e("span",{class:"icon"},[e("i",{class:"fas fa-user"})],-1)),e("span",null,O(i.value),1)]),e("div",Rt,[P(F,{to:"/admin/profile",class:"navbar-item"},{default:L(()=>f[6]||(f[6]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user-circle"})],-1),e("span",null,"Profile",-1)])),_:1}),f[8]||(f[8]=e("hr",{class:"navbar-divider"},null,-1)),e("a",{class:"navbar-item",onClick:M},f[7]||(f[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sign-out-alt"})],-1),e("span",null,"Logout",-1)]))])],2)])],2)])])}}},Tt=I(xt,[["__scopeId","data-v-5aeee9d2"]]),Ft={class:"admin-theme"},Nt={class:"columns is-gapless"},_t={class:"column is-three-quarters main-content"},Lt={class:"section"},qt={class:"container is-fluid"},Ot={__name:"AdminLayout",setup(n){const s=Y(),t=te(),o=R(()=>s.getters["loading/isLoading"]),i=R(()=>s.getters["loading/loadingMessage"]),u=W(!0),l=()=>{u.value=!u.value},r=()=>{window.innerWidth>=1024?u.value=!0:u.value=!1},p=()=>{Q.cancelRequestsForRoute(t.path),s.dispatch("loading/startRouteChange")},v=()=>{s.dispatch("loading/finishRouteChange")};return B(()=>{window.addEventListener("resize",r),r()}),J(()=>{window.removeEventListener("resize",r),Q.cancelAllRequests()}),(w,m)=>{const d=ne("router-view");return C(),S("div",Ft,[P(ie,{"is-loading":o.value,message:i.value},null,8,["is-loading","message"]),e("div",Nt,[e("div",{class:h(["column is-one-quarter sidebar-column",{"is-hidden-mobile":!u.value}])},[P(Me,{"is-visible":u.value,onToggleSidebar:l},null,8,["is-visible"])],2),e("div",_t,[P(Tt,{onToggleSidebar:l}),e("section",Lt,[e("div",qt,[P(de,null,{default:L(()=>[P(d,null,{default:L(({Component:A})=>[P(oe,{name:"fade",mode:"out-in",onBeforeLeave:p,onAfterEnter:v},{default:L(()=>[(C(),re(le(A),{key:w.$route.fullPath}))]),_:2},1024)]),_:1})]),_:1})])])])])])}}},Et=I(Ot,[["__scopeId","data-v-f925bf12"]]);export{Et as default};
