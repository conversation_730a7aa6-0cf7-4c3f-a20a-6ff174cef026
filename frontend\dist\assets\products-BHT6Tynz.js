import{q as s}from"./index-DMg5qKr1.js";const c={async getProducts(t={}){try{console.log("Fetching products with params:",t);const e={};t.page&&(e.page=t.page),t.pageSize&&(e.pageSize=t.pageSize),t.limit&&(e.pageSize=t.limit),t.orderBy&&(e.orderBy=t.orderBy),t.sortBy&&(e.orderBy=t.sortBy),t.descending!==void 0&&(e.descending=t.descending),t.sortOrder&&(e.descending=t.sortOrder==="desc"),t.search&&t.search.trim()!==""?e.filter=t.search.trim():t.filter&&t.filter.trim()!==""&&(e.filter=t.filter.trim()),t.categoryId&&t.categoryId!==""&&(e.categoryId=t.categoryId),t.status&&t.status!==""&&(e.status=t.status),t.stock&&t.stock!==""&&(e.stock=t.stock);try{throw console.log("Sending standardized params to admin API:",e),new Error("Admin endpoint not implemented yet");let a=[],r={};response.data&&(response.data.data&&Array.isArray(response.data.data)?(a=response.data.data,r={total:response.data.total||response.data.totalItems||0,page:response.data.page||response.data.currentPage||e.page||1,pageSize:response.data.pageSize||response.data.perPage||e.pageSize||15,totalPages:response.data.totalPages||response.data.lastPage||Math.ceil((response.data.total||0)/(e.pageSize||15))}):Array.isArray(response.data)?(a=response.data,r={total:response.data.length,page:e.page||1,pageSize:e.pageSize||15,totalPages:Math.ceil(response.data.length/(e.pageSize||15))}):response.data.items&&Array.isArray(response.data.items)&&(a=response.data.items,r={total:response.data.total||response.data.totalItems||response.data.items.length,page:response.data.page||response.data.currentPage||e.page||1,pageSize:response.data.pageSize||response.data.perPage||e.pageSize||15,totalPages:response.data.totalPages||response.data.lastPage||Math.ceil((response.data.total||response.data.items.length)/(e.pageSize||15))}));const n={data:a,pagination:r,items:a,total:r.total,currentPage:r.page,totalPages:r.totalPages};return console.log("Products service returning:",{itemsCount:a.length,pagination:r,totalItems:r.total,totalPages:r.totalPages}),n}catch(a){console.warn("Admin products endpoint failed, falling back to public endpoint:",a.message);try{console.log("Using public products endpoint with params:",e);const r=await s.get("/api/products",{params:e});console.log("Public products API response:",r.data);let n=[],o={};return r.data&&(r.data.data&&Array.isArray(r.data.data)?(n=r.data.data,o={total:r.data.total||r.data.totalItems||0,page:r.data.currentPage||e.page||1,pageSize:r.data.perPage||e.pageSize||15,totalPages:r.data.lastPage||Math.ceil((r.data.total||0)/(e.pageSize||15))}):Array.isArray(r.data)&&(n=r.data,o={total:r.data.length,page:e.page||1,pageSize:e.pageSize||15,totalPages:Math.ceil(r.data.length/(e.pageSize||15))})),{data:n,pagination:o,items:n,total:o.total,currentPage:o.page,totalPages:o.totalPages}}catch(r){return console.error("Both admin and public products endpoints failed:",r.message),{data:[],pagination:{total:0,page:e.page||1,pageSize:e.pageSize||20,totalPages:1},items:[],total:0,currentPage:e.page||1,totalPages:1}}}}catch(e){throw console.error("Error fetching products:",e),e}},async getProductById(t){try{return(await s.get(`/api/admin/products/${t}`)).data}catch(e){throw console.error(`Error fetching product ${t}:`,e),e}},async createProduct(t){try{console.log("🚀 Creating product with data:",t);const e=await s.post("/api/admin/products",t);return console.log("✅ Product creation response:",e.data),e.data}catch(e){throw console.error("❌ Error creating product:",e),e.response&&(console.error("📋 Response status:",e.response.status),console.error("📋 Response data:",e.response.data),console.error("📋 Response headers:",e.response.headers)),e}},async updateProduct(t,e){try{return(await s.put(`/api/admin/products/${t}`,e)).data}catch(a){throw console.error(`Error updating product ${t}:`,a),a}},async deleteProduct(t){try{return(await s.delete(`/api/admin/products/${t}`)).data}catch(e){throw console.error(`Error deleting product ${t}:`,e),e}},async uploadProductImage(t,e){try{const a=new FormData;return a.append("image",e),(await s.post(`/api/admin/products/${t}/images`,a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(a){throw console.error(`Error uploading image for product ${t}:`,a),a}},async deleteProductImage(t,e){try{return(await s.delete(`/api/admin/products/${t}/images/${e}`)).data}catch(a){throw console.error(`Error deleting image ${e} for product ${t}:`,a),a}},async setMainProductImage(t,e){try{return(await s.patch(`/api/admin/products/${t}/images/${e}/main`)).data}catch(a){throw console.error(`Error setting main image ${e} for product ${t}:`,a),a}},async toggleProductStatus(t,e){try{return(await s.patch(`/api/admin/products/${t}/status`,{status:e})).data}catch(a){throw console.error(`Error updating status for product ${t}:`,a),a}},async getProductStats(){try{return(await s.get("/api/admin/products/stats")).data}catch(t){throw console.error("Error fetching product stats:",t),t}},async getCategories(t={}){try{console.log("Fetching categories with params:",t);const e={pageSize:t.pageSize||1e3,page:t.page||1};t.search&&t.search.trim()!==""&&(e.filter=t.search.trim());const a=await s.get("/api/categories/all",{params:e});console.log("Categories API response:",a.data);let r=[];a.data&&a.data.data&&Array.isArray(a.data.data)?r=a.data.data:Array.isArray(a.data)&&(r=a.data);const n=r.map(o=>({id:o.id,name:o.name,parentId:o.parentId,slug:o.slug}));return n.sort((o,i)=>o.name.localeCompare(i.name)),console.log("Processed categories:",n.length),n}catch(e){throw console.error("Error fetching categories:",e),e}}};export{c as p};
