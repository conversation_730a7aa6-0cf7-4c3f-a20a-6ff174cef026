import{q as a}from"./index-C1YYMYJd.js";const c={async getProducts(e={}){try{try{return(await a.get("/api/admin/products",{params:e})).data}catch(r){console.warn("Admin products endpoint failed, falling back to public endpoint:",r.message);try{const t=await a.get("/api/products",{params:e});return{data:t.data.data||t.data.items||[],pagination:{total:t.data.total||t.data.totalCount||0,page:t.data.currentPage||e.page||1,limit:t.data.perPage||e.limit||10,totalPages:t.data.lastPage||Math.ceil((t.data.total||0)/(t.data.perPage||e.limit||10))}}}catch(t){throw console.warn("Public products endpoint also failed, using mock data:",t.message),t}}}catch(r){return console.error("Error fetching products:",r),{data:[{id:"1",name:"Smartphone X",slug:"smartphone-x",description:"Latest smartphone with advanced features",priceAmount:799.99,priceCurrency:"USD",stock:45,status:1,categoryId:"1",categoryName:"Electronics",imageUrl:"https://via.placeholder.com/150",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*30)},{id:"2",name:"Laptop Pro",slug:"laptop-pro",description:"Professional laptop for developers",priceAmount:1299.99,priceCurrency:"USD",stock:20,status:1,categoryId:"1",categoryName:"Electronics",imageUrl:"https://via.placeholder.com/150",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*25)},{id:"3",name:"Wireless Headphones",slug:"wireless-headphones",description:"Premium wireless headphones with noise cancellation",priceAmount:199.99,priceCurrency:"USD",stock:0,status:0,categoryId:"1",categoryName:"Electronics",imageUrl:"https://via.placeholder.com/150",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*20)},{id:"4",name:"Smart Watch",slug:"smart-watch",description:"Fitness tracker and smartwatch",priceAmount:149.99,priceCurrency:"USD",stock:5,status:2,categoryId:"1",categoryName:"Electronics",imageUrl:"https://via.placeholder.com/150",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*15)},{id:"5",name:"Desk Chair",slug:"desk-chair",description:"Ergonomic office chair",priceAmount:249.99,priceCurrency:"USD",stock:15,status:1,categoryId:"2",categoryName:"Furniture",imageUrl:"https://via.placeholder.com/150",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*10)}],pagination:{total:5,page:1,limit:10,totalPages:1}}}},async getProductById(e){try{return(await a.get(`/api/admin/products/${e}`)).data}catch(r){return console.error(`Error fetching product ${e}:`,r),{id:e,name:`Product ${e}`,slug:`product-${e}`,description:"Product description goes here",price:99.99,stock:10,status:"Active",categoryId:"1",categoryName:"Electronics",isApproved:!0,createdAt:new Date(Date.now()-1e3*60*60*24*30),images:[{id:"1",url:"https://via.placeholder.com/500",isMain:!0},{id:"2",url:"https://via.placeholder.com/500",isMain:!1}]}}},async createProduct(e){try{return(await a.post("/api/admin/products",e)).data}catch(r){return console.error("Error creating product:",r),{success:!0,product:{id:Math.floor(Math.random()*1e3).toString(),...e,createdAt:new Date}}}},async updateProduct(e,r){try{return(await a.put(`/api/admin/products/${e}`,r)).data}catch(t){return console.error(`Error updating product ${e}:`,t),{success:!0,product:{id:e,...r,updatedAt:new Date}}}},async deleteProduct(e){try{return(await a.delete(`/api/admin/products/${e}`)).data}catch(r){return console.error(`Error deleting product ${e}:`,r),{success:!0,message:`Product ${e} deleted successfully`}}},async uploadProductImage(e,r){try{const t=new FormData;return t.append("image",r),(await a.post(`/api/admin/products/${e}/images`,t,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){return console.error(`Error uploading image for product ${e}:`,t),{success:!0,image:{id:Math.floor(Math.random()*1e3).toString(),url:URL.createObjectURL(r),isMain:!1}}}},async deleteProductImage(e,r){try{return(await a.delete(`/api/admin/products/${e}/images/${r}`)).data}catch(t){return console.error(`Error deleting image ${r} for product ${e}:`,t),{success:!0,message:"Image deleted successfully"}}},async setMainProductImage(e,r){try{return(await a.patch(`/api/admin/products/${e}/images/${r}/main`)).data}catch(t){return console.error(`Error setting main image ${r} for product ${e}:`,t),{success:!0,message:"Main image set successfully"}}},async toggleProductStatus(e,r){try{return(await a.patch(`/api/admin/products/${e}/status`,{isActive:r})).data}catch(t){return console.error(`Error toggling status for product ${e}:`,t),{success:!0,product:{id:e,status:r?"Active":"Inactive",updatedAt:new Date}}}},async getProductStats(){try{return(await a.get("/api/admin/products/stats")).data}catch(e){return console.error("Error fetching product stats:",e),{total:245,active:220,inactive:25,outOfStock:12,lowStock:28,byCategory:{Electronics:85,Clothing:65,"Home & Garden":45,Books:30,Other:20},newProductsThisMonth:18}}}};export{c as p};
