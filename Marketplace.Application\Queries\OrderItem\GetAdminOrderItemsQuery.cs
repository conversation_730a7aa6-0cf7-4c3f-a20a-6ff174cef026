using Marketplace.Application.Queries.Common;
using Marketplace.Application.Responses;

namespace Marketplace.Application.Queries.OrderItem;

public record GetAdminOrderItemsQuery(
    Guid OrderId,
    string? Filter = null,
    string? OrderBy = null,
    bool Descending = false,
    int? Page = null,
    int? PageSize = null
    ) : PaginatedQuery<OrderItemResponse>(Filter, OrderBy, Descending, Page, PageSize);
