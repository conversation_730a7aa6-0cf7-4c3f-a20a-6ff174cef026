<template>
  <div class="admin-theme">
    <!-- Global Loading Overlay -->
    <global-loading :is-loading="isLoading" :message="loadingMessage" />

    <div class="columns is-gapless">
      <!-- Sidebar -->
      <div class="column is-one-quarter sidebar-column" :class="{ 'is-hidden-mobile': !isSidebarVisible }">
        <admin-sidebar :is-visible="isSidebarVisible" @toggle-sidebar="toggleSidebar" />
      </div>

      <!-- Main Content -->
      <div class="column is-three-quarters main-content">
        <admin-header @toggle-sidebar="toggleSidebar" />
        <section class="section">
          <div class="container is-fluid">
            <error-boundary>
              <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in" @before-leave="beforeRouteChange" @after-enter="afterRouteChange">
                  <component :is="Component" :key="$route.fullPath" />
                </transition>
              </router-view>
            </error-boundary>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import AdminSidebar from '@/admin/components/Sidebar.vue';
import AdminHeader from '@/admin/components/Header.vue';
import GlobalLoading from '@/components/GlobalLoading.vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import apiService from '@/services/api';

// Store and route
const store = useStore();
const route = useRoute();

// Computed properties for loading state
const isLoading = computed(() => store.getters['loading/isLoading']);
const loadingMessage = computed(() => store.getters['loading/loadingMessage']);

// Sidebar visibility state
const isSidebarVisible = ref(true);

// Toggle sidebar visibility
const toggleSidebar = () => {
  isSidebarVisible.value = !isSidebarVisible.value;
};

// Handle responsive behavior
const handleResponsiveLayout = () => {
  if (window.innerWidth >= 1024) { // Desktop
    isSidebarVisible.value = true;
  } else { // Mobile
    isSidebarVisible.value = false;
  }
};

// Route transition handlers
const beforeRouteChange = () => {
  // Cancel any pending requests related to the current route
  apiService.cancelRequestsForRoute(route.path);

  // Start the loading state for route change
  store.dispatch('loading/startRouteChange');
};

const afterRouteChange = () => {
  // End the loading state for route change
  store.dispatch('loading/finishRouteChange');
};

onMounted(() => {
  window.addEventListener('resize', handleResponsiveLayout);
  handleResponsiveLayout();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResponsiveLayout);

  // Cancel any pending requests when the layout is unmounted
  apiService.cancelAllRequests();
});
</script>

<style scoped>
.columns {
  margin: 0;
  min-height: 100vh;
}

.sidebar-column {
  position: relative;
  z-index: 30;
  background-color: #2a2a2a;
}

.main-content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* Route transition animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Responsive Adjustments */
@media screen and (max-width: 1023px) {
  .sidebar-column {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100vh;
    z-index: 30;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }
}
</style>
