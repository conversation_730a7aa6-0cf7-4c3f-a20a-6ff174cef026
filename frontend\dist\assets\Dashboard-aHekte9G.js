import{_ as A,c as u,o as f,a as e,t as w,n as S,g as _,h as T,x as H,i as F,F as M,p as E,y as V,r as O,b as y,w as x,q,j,k as P,d as B}from"./index-C1YYMYJd.js";import{C as z}from"./auto-Cz6uSJnr.js";import{S as L}from"./StatusBadge-7HpbPXqn.js";/* empty css                                                                    */const W={class:"card admin-stat-card"},J={class:"level is-mobile"},Q={class:"level-left"},Y={class:"level-item"},Z={class:"heading"},G={class:"title"},K={class:"level-right"},X={class:"level-item"},ee={class:"icon is-large"},se={__name:"AdminStatCard",props:{title:{type:String,required:!0},value:{type:[String,Number],required:!0},icon:{type:String,required:!0},color:{type:String,default:"is-primary"}},setup(o){return(n,s)=>(f(),u("div",W,[e("div",{class:S(["card-content",o.color])},[e("div",J,[e("div",Q,[e("div",Y,[e("div",null,[e("p",Z,w(o.title),1),e("p",G,w(o.value),1)])])]),e("div",K,[e("div",X,[e("span",ee,[e("i",{class:S(["fas","fa-"+o.icon,"fa-2x"])},null,2)])])])])],2)]))}},N=A(se,[["__scopeId","data-v-086603d9"]]),ae={class:"card"},te={class:"card-header"},re={class:"card-header-icon"},ne={class:"tabs is-toggle is-small"},oe={class:"card-content"},le={key:0,class:"has-text-centered py-6"},ie={key:1,class:"has-text-centered py-6"},de={key:2,class:"chart-container"},ce={__name:"SalesChart",props:{data:{type:Array,required:!0},loading:{type:Boolean,default:!1}},emits:["period-changed"],setup(o,{emit:n}){const s=o,a=n,r=_(null),b=_(null),t=_("month"),d=_(!1),m=c=>{t.value!==c&&(t.value=c,d.value=!0,a("period-changed",c))},v=T(()=>({labels:s.data.map(c=>c.label),datasets:[{label:"Sales",data:s.data.map(c=>c.value),backgroundColor:"rgba(255, 119, 0, 0.2)",borderColor:"#ff7700",borderWidth:2,pointBackgroundColor:"#ff7700",pointBorderColor:"#ffffff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6,tension:.3,fill:!0}]})),g=()=>{b.value&&b.value.destroy();const c=r.value.getContext("2d");b.value=new z(c,{type:"line",data:v.value,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.7)",titleFont:{size:14,weight:"bold"},bodyFont:{size:13},padding:12,callbacks:{label:function(p){let h=p.dataset.label||"";return h&&(h+=": "),p.parsed.y!==null&&(h+=new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(p.parsed.y)),h}}}},scales:{y:{beginAtZero:!0,ticks:{color:"#e0e0e0",font:{weight:"bold",size:12},callback:function(p){return new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",notation:"compact",compactDisplay:"short"}).format(p)}},grid:{color:"rgba(255, 255, 255, 0.1)"}},x:{ticks:{color:"#e0e0e0",font:{weight:"bold",size:12}},grid:{display:!1}}}}})};return H(()=>s.data,()=>{d.value=!1,setTimeout(()=>{s.data&&s.data.length>0&&g()},0)},{deep:!0}),F(()=>{s.data&&s.data.length>0&&g()}),(c,p)=>(f(),u("div",ae,[e("div",te,[p[3]||(p[3]=e("p",{class:"card-header-title sales-title"},"Sales Overview",-1)),e("div",re,[e("div",ne,[e("ul",null,[e("li",{class:S({"is-active":t.value==="week"})},[e("a",{onClick:p[0]||(p[0]=h=>m("week"))},"Week")],2),e("li",{class:S({"is-active":t.value==="month"})},[e("a",{onClick:p[1]||(p[1]=h=>m("month"))},"Month")],2),e("li",{class:S({"is-active":t.value==="year"})},[e("a",{onClick:p[2]||(p[2]=h=>m("year"))},"Year")],2)])])])]),e("div",oe,[d.value?(f(),u("div",le,p[4]||(p[4]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading chart data...",-1)]))):!o.data||o.data.length===0?(f(),u("div",ie,p[5]||(p[5]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-chart-line fa-2x"})],-1),e("p",{class:"mt-2"},"No sales data available for this period",-1)]))):(f(),u("div",de,[e("canvas",{ref_key:"chartCanvas",ref:r,height:"300"},null,512)]))])]))}},ue=A(ce,[["__scopeId","data-v-dd806dfe"]]),fe={class:"card"},ve={class:"card-content"},pe={key:0,class:"has-text-centered py-6"},me={key:1,class:"has-text-centered py-6"},he={key:2},ge={class:"chart-container"},ye={class:"status-legend"},_e={class:"legend-label"},be={class:"legend-value"},$e={__name:"OrdersByStatusChart",props:{data:{type:Array,required:!0},loading:{type:Boolean,default:!1}},setup(o){const n=o,s=_(null),a=_(null),r=_(!1),b=["#ff7700","#3298dc","#48c774","#ffdd57","#ff3860","#9c27b0","#00d1b2","#f39c12","#8e44ad","#3498db"],t=()=>{if(!s.value)return;const m=s.value.getContext("2d");a.value&&a.value.destroy();const v=n.data.map(c=>c.status),g=n.data.map(c=>c.count);a.value=new z(m,{type:"doughnut",data:{labels:v,datasets:[{data:g,backgroundColor:b.slice(0,n.data.length),borderColor:"#ffffff",borderWidth:2,hoverOffset:10}]},options:{responsive:!0,maintainAspectRatio:!1,cutout:"70%",plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleFont:{size:14,weight:"bold"},bodyFont:{size:13,weight:"bold"},titleColor:"#ffffff",bodyColor:"#ffffff",padding:12,cornerRadius:6,boxPadding:6,callbacks:{label:function(c){const p=c.label||"",h=c.raw||0,D=c.dataset.data.reduce((C,I)=>C+I,0),R=Math.round(h/D*100);return`${p}: ${h} (${R}%)`}}}}}})},d=()=>{a.value&&(a.value.data.labels=n.data.map(m=>m.status),a.value.data.datasets[0].data=n.data.map(m=>m.count),a.value.update())};return H(()=>n.data,()=>{r.value=!1,setTimeout(()=>{n.data&&n.data.length>0&&(a.value?d():t())},0)},{deep:!0}),F(()=>{n.data&&n.data.length>0&&t()}),(m,v)=>(f(),u("div",fe,[v[2]||(v[2]=e("div",{class:"card-header"},[e("p",{class:"card-header-title status-title"},"Orders by Status")],-1)),e("div",ve,[r.value?(f(),u("div",pe,v[0]||(v[0]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading chart data...",-1)]))):!o.data||o.data.length===0?(f(),u("div",me,v[1]||(v[1]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-chart-pie fa-2x"})],-1),e("p",{class:"mt-2"},"No order data available",-1)]))):(f(),u("div",he,[e("div",ge,[e("canvas",{ref_key:"chartCanvas",ref:s},null,512)]),e("div",ye,[(f(!0),u(M,null,E(o.data,(g,c)=>(f(),u("div",{key:g.status,class:"legend-item"},[e("span",{class:"legend-color",style:V({backgroundColor:b[c%b.length]})},null,4),e("span",_e,w(g.status),1),e("span",be,w(g.count),1)]))),128))])]))])]))}},we=A($e,[["__scopeId","data-v-a8f7f040"]]),ke={class:"card"},Ce={class:"card-header"},xe={class:"card-header-icon"},De={class:"card-content"},Se={key:0,class:"has-text-centered py-4"},Ae={key:1,class:"has-text-centered py-4"},qe={key:2,class:"table-container"},Ne={class:"table is-fullwidth is-hoverable"},Te={class:"has-text-right"},Re={__name:"RecentOrders",props:{orders:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},setup(o){const n=a=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(a),s=a=>{if(!a)return"";const r=new Date(a),t=new Date-r,d=Math.round(t/1e3),m=Math.round(d/60),v=Math.round(m/60),g=Math.round(v/24);return d<60?"Just now":m<60?`${m} minute${m!==1?"s":""} ago`:v<24?`${v} hour${v!==1?"s":""} ago`:g<7?`${g} day${g!==1?"s":""} ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)};return(a,r)=>{const b=O("router-link");return f(),u("div",ke,[e("div",Ce,[r[1]||(r[1]=e("p",{class:"card-header-title orders-title"},"Recent Orders",-1)),e("div",xe,[y(b,{to:"/admin/orders",class:"button is-small is-primary"},{default:x(()=>r[0]||(r[0]=[e("span",null,"View All",-1),e("span",{class:"icon is-small"},[e("i",{class:"fas fa-arrow-right"})],-1)])),_:1})])]),e("div",De,[o.loading?(f(),u("div",Se,r[2]||(r[2]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading orders...",-1)]))):!o.orders||o.orders.length===0?(f(),u("div",Ae,r[3]||(r[3]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-shopping-cart fa-2x"})],-1),e("p",{class:"mt-2"},"No recent orders found",-1)]))):(f(),u("div",qe,[e("table",Ne,[r[5]||(r[5]=e("thead",null,[e("tr",null,[e("th",null,"Order ID"),e("th",null,"Customer"),e("th",null,"Total"),e("th",null,"Status"),e("th",null,"Date"),e("th")])],-1)),e("tbody",null,[(f(!0),u(M,null,E(o.orders,t=>(f(),u("tr",{key:t.id},[e("td",null,w(t.id),1),e("td",null,w(t.customerName),1),e("td",null,w(n(t.total)),1),e("td",null,[y(L,{status:t.status,type:"order"},null,8,["status"])]),e("td",null,w(s(t.createdAt)),1),e("td",Te,[y(b,{to:`/admin/orders/${t.id}`,class:"button is-small"},{default:x(()=>r[4]||(r[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"])])]))),128))])])]))])])}}},Ie=A(Re,[["__scopeId","data-v-47ee18bc"]]),Fe={class:"card"},Me={class:"card-header"},Ee={class:"card-header-icon"},Oe={class:"card-content"},Pe={key:0,class:"has-text-centered py-4"},Be={key:1,class:"has-text-centered py-4"},Ue={key:2},He={class:"seller-request-header"},ze={class:"seller-info"},Ve={class:"seller-name"},je={class:"store-name"},Le={class:"request-date"},We={class:"seller-request-actions"},Je=["onClick"],Qe=["onClick"],Ye={__name:"PendingSellerRequests",props:{requests:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["approve","reject"],setup(o,{emit:n}){const s=n,a=t=>{if(!t)return"";const d=new Date(t),v=new Date-d,g=Math.round(v/1e3),c=Math.round(g/60),p=Math.round(c/60),h=Math.round(p/24);return g<60?"Just now":c<60?`${c} minute${c!==1?"s":""} ago`:p<24?`${p} hour${p!==1?"s":""} ago`:h<7?`${h} day${h!==1?"s":""} ago`:new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(d)},r=t=>{s("approve",t)},b=t=>{s("reject",t)};return(t,d)=>{const m=O("router-link");return f(),u("div",Fe,[e("div",Me,[d[1]||(d[1]=e("p",{class:"card-header-title requests-title"},"Pending Seller Requests",-1)),e("div",Ee,[y(m,{to:"/admin/seller-requests",class:"button is-small is-primary"},{default:x(()=>d[0]||(d[0]=[e("span",null,"View All",-1),e("span",{class:"icon is-small"},[e("i",{class:"fas fa-arrow-right"})],-1)])),_:1})])]),e("div",Oe,[o.loading?(f(),u("div",Pe,d[2]||(d[2]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading requests...",-1)]))):!o.requests||o.requests.length===0?(f(),u("div",Be,d[3]||(d[3]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-store fa-2x"})],-1),e("p",{class:"mt-2"},"No pending seller requests",-1)]))):(f(),u("div",Ue,[(f(!0),u(M,null,E(o.requests,v=>(f(),u("div",{class:"seller-request",key:v.id},[e("div",He,[e("div",ze,[e("h4",Ve,w(v.userName),1),e("p",je,w(v.companyName),1)]),e("div",Le,w(a(v.createdAt)),1)]),e("div",We,[y(m,{to:`/admin/seller-requests/${v.id}`,class:"button is-small"},{default:x(()=>d[4]||(d[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View",-1)])),_:2},1032,["to"]),e("button",{class:"button is-small is-success",onClick:g=>r(v.id)},d[5]||(d[5]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-check"})],-1),e("span",null,"Approve",-1)]),8,Je),e("button",{class:"button is-small is-danger",onClick:g=>b(v.id)},d[6]||(d[6]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Reject",-1)]),8,Qe)])]))),128))]))])])}}},Ze=A(Ye,[["__scopeId","data-v-42ba6d1e"]]),U={async getDashboardData(){var o,n;try{const s=await q.get("/api/admin/dashboard",{timeout:15e3});if(console.log("Dashboard data response:",s),console.log("Full API response:",{status:s.status,statusText:s.statusText,headers:s.headers,data:s.data}),!s.data)throw new Error("Empty response from server");return s.data.data&&s.data.data.stats&&(s.data.data.stats.revenue=Number(s.data.data.stats.revenue)),s.data.data?s.data.data:(s.data.success!==void 0,s.data)}catch(s){console.error("Error fetching dashboard data:",s);const a=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Failed to load dashboard data from the server";throw new Error(a)}},async getSalesData(o="month"){var n,s;try{const a=await q.get(`/api/admin/dashboard/sales?period=${o}`,{timeout:15e3});if(console.log("Sales data response:",a),!a.data||!a.data.data)throw new Error("Invalid response format from server");return a.data.data}catch(a){console.error("Error fetching sales data:",a);const r=((s=(n=a.response)==null?void 0:n.data)==null?void 0:s.message)||`Failed to load sales data for period: ${o}`;throw new Error(r)}},async getOrdersByStatus(){var o,n;try{const s=await q.get("/api/admin/dashboard/orders-by-status",{timeout:15e3});if(console.log("Orders by status response:",s),!s.data||!s.data.data)throw new Error("Invalid response format from server");return s.data.data}catch(s){console.error("Error fetching orders by status:",s);const a=((n=(o=s.response)==null?void 0:o.data)==null?void 0:n.message)||"Failed to load orders by status data";throw new Error(a)}},async getRecentOrders(o=5){var n,s;try{const a=await q.get(`/api/admin/dashboard/recent-orders?limit=${o}`,{timeout:15e3});if(console.log("Recent orders response:",a),!a.data||!a.data.data)throw new Error("Invalid response format from server");return a.data.data}catch(a){console.error("Error fetching recent orders:",a);const r=((s=(n=a.response)==null?void 0:n.data)==null?void 0:s.message)||"Failed to load recent orders";throw new Error(r)}},async getPendingSellerRequests(o=5){var n,s;try{const a=await q.get(`/api/admin/dashboard/pending-seller-requests?limit=${o}`,{timeout:15e3});if(console.log("Pending seller requests response:",a),!a.data||!a.data.data)throw new Error("Invalid response format from server");return a.data.data}catch(a){console.error("Error fetching pending seller requests:",a);const r=((s=(n=a.response)==null?void 0:n.data)==null?void 0:s.message)||"Failed to load pending seller requests";throw new Error(r)}}},Ge={class:"admin-dashboard"},Ke={class:"level"},Xe={class:"level-right"},es={class:"level-item"},ss={key:0,class:"notification is-danger"},as={key:1,class:"has-text-centered"},ts={key:0,class:"mt-4"},rs={key:2},ns={key:3},os={class:"columns is-multiline"},ls={class:"column is-3"},is={class:"column is-3"},ds={class:"column is-3"},cs={class:"column is-3"},us={class:"columns"},fs={class:"column is-8"},vs={class:"column is-4"},ps={class:"columns"},ms={class:"column is-12"},hs={class:"card quick-actions-card"},gs={class:"card-content"},ys={class:"buttons is-centered"},_s={class:"columns"},bs={class:"column is-6"},$s={class:"column is-6"},ws={__name:"Dashboard",setup(o){const n=_(!0),s=_(null),a=_(0),r=_(null),b=T(()=>!n.value&&t.value.products===0&&t.value.users===0&&t.value.orders===0&&t.value.revenue===0&&d.value.length===0&&m.value.length===0),t=_({products:0,users:0,orders:0,revenue:0}),d=_([]),m=_([]),v=_("month"),g=_([]),c=_([]),p=$=>{const l=typeof $=="string"?Number($):$;return isNaN(l)?(console.error("Invalid currency value:",$),"UAH 0.00"):new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH",currencyDisplay:"code"}).format(l).replace("UAH","UAH")};T(()=>c.value.length>0),T(()=>c.value.length);let h=null;const D=async()=>{h&&clearTimeout(h),r.value&&(clearInterval(r.value),r.value=null),n.value=!0,s.value=null,a.value=0,r.value=setInterval(()=>{a.value++},1e3),h=setTimeout(async()=>{var $,l;try{console.log("Fetching dashboard data...");const i=await U.getDashboardData();if(console.log("Dashboard data received:",i),console.log("Data structure check:",{hasData:!!i,dataType:typeof i,keys:i?Object.keys(i):"none"}),!i)throw new Error("No data received from server");if(i.stats){if(t.value={products:i.stats.products||0,users:i.stats.users||0,orders:i.stats.orders||0,revenue:Number(i.stats.revenue)||0},console.log("Revenue value:",t.value.revenue,typeof t.value.revenue),(typeof t.value.revenue!="number"||isNaN(t.value.revenue))&&(console.warn("Revenue is not a valid number, converting:",i.stats.revenue),t.value.revenue=0,typeof i.stats.revenue=="string")){const k=parseFloat(i.stats.revenue.replace(/[^\d.-]/g,""));isNaN(k)||(t.value.revenue=k,console.log("Parsed revenue:",k))}d.value=i.salesData||[],m.value=i.ordersByStatus||[],g.value=i.recentOrders||[],c.value=i.sellerRequests||[]}else console.log("Trying alternative data structure"),t.value={products:i.products||0,users:i.users||0,orders:i.orders||0,revenue:Number(i.revenue)||0},console.log("Revenue value (alt):",t.value.revenue,typeof t.value.revenue),d.value=[],m.value=[],g.value=[],c.value=[];b.value&&console.warn("Dashboard data is empty")}catch(i){!(($=i.message)!=null&&$.includes("canceled"))&&!((l=i.message)!=null&&l.includes("aborted"))&&(console.error("Error fetching dashboard data:",i),s.value=`Failed to load dashboard data: ${i.message}. Please try again later.`)}finally{r.value&&(clearInterval(r.value),r.value=null),n.value=!1,h=null}},100)},R=()=>{h&&(clearTimeout(h),h=null),r.value&&(clearInterval(r.value),r.value=null),n.value=!1,a.value=0,setTimeout(()=>{D()},500)};let C=null;const I=async $=>{C&&clearTimeout(C),v.value=$,C=setTimeout(async()=>{var l,i;try{const k=await U.getSalesData($);d.value=k}catch(k){!((l=k.message)!=null&&l.includes("canceled"))&&!((i=k.message)!=null&&i.includes("aborted"))&&(console.error("Error fetching sales data:",k),s.value=k.message||`Failed to load sales data for period: ${$}`)}finally{C=null}},100)};return F(()=>{D()}),j(()=>{h&&clearTimeout(h),C&&clearTimeout(C),r.value&&(clearInterval(r.value),r.value=null)}),($,l)=>{const i=O("router-link");return f(),u("div",Ge,[e("div",Ke,[l[2]||(l[2]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Dashboard")])],-1)),e("div",Xe,[e("div",es,[e("button",{class:S(["button is-primary",{"is-loading":n.value}]),onClick:D},l[1]||(l[1]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sync-alt"})],-1),e("span",null,"Refresh",-1)]),2)])])]),s.value?(f(),u("div",ss,[e("button",{class:"delete",onClick:l[0]||(l[0]=k=>s.value=null)}),B(" "+w(s.value),1)])):P("",!0),n.value&&!t.value.products?(f(),u("div",as,[l[4]||(l[4]=e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-3x"})],-1)),l[5]||(l[5]=e("p",{class:"mt-3"},"Loading dashboard data...",-1)),l[6]||(l[6]=e("p",{class:"mt-2 has-text-grey"},"This may take a moment to fetch data from the database",-1)),a.value>10?(f(),u("div",ts,[e("button",{class:"button is-warning",onClick:R},l[3]||(l[3]=[e("span",{class:"icon"},[e("i",{class:"fas fa-exclamation-triangle"})],-1),e("span",null,"Taking too long? Click to retry",-1)]))])):P("",!0)])):b.value?(f(),u("div",rs,[e("div",{class:"notification is-warning"},[l[8]||(l[8]=e("p",null,[e("strong",null,"No data available."),B(" The database may be empty or there was an error retrieving the data.")],-1)),e("button",{class:"button is-small is-warning mt-2",onClick:D},l[7]||(l[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-sync-alt"})],-1),e("span",null,"Try Again",-1)]))])])):(f(),u("div",ns,[e("div",os,[e("div",ls,[y(N,{title:"Products",value:t.value.products,icon:"box",color:"is-info"},null,8,["value"])]),e("div",is,[y(N,{title:"Users",value:t.value.users,icon:"users",color:"is-success"},null,8,["value"])]),e("div",ds,[y(N,{title:"Orders",value:t.value.orders,icon:"shopping-cart",color:"is-warning"},null,8,["value"])]),e("div",cs,[y(N,{title:"Revenue",value:p(t.value.revenue),icon:"hryvnia",color:"is-primary"},null,8,["value"])])]),e("div",us,[e("div",fs,[y(ue,{data:d.value,onPeriodChanged:I},null,8,["data"])]),e("div",vs,[y(we,{data:m.value},null,8,["data"])])]),e("div",ps,[e("div",ms,[e("div",hs,[l[13]||(l[13]=e("div",{class:"card-header"},[e("p",{class:"card-header-title"},"Quick Actions")],-1)),e("div",gs,[e("div",ys,[y(i,{to:"/admin/products/create",class:"button is-info"},{default:x(()=>l[9]||(l[9]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add Product",-1)])),_:1}),y(i,{to:"/admin/categories/create",class:"button is-success"},{default:x(()=>l[10]||(l[10]=[e("span",{class:"icon"},[e("i",{class:"fas fa-folder-plus"})],-1),e("span",null,"Add Category",-1)])),_:1}),y(i,{to:"/admin/users",class:"button is-warning"},{default:x(()=>l[11]||(l[11]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user-cog"})],-1),e("span",null,"Manage Users",-1)])),_:1}),y(i,{to:"/admin/orders",class:"button is-primary"},{default:x(()=>l[12]||(l[12]=[e("span",{class:"icon"},[e("i",{class:"fas fa-shipping-fast"})],-1),e("span",null,"Process Orders",-1)])),_:1})])])])])]),e("div",_s,[e("div",bs,[y(Ie,{orders:g.value},null,8,["orders"])]),e("div",$s,[y(Ze,{requests:c.value},null,8,["requests"])])])]))])}}},Ss=A(ws,[["__scopeId","data-v-2fe1879e"]]);export{Ss as default};
