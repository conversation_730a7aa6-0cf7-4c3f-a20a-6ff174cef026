import{q as t}from"./index-C1YYMYJd.js";const n={async getReviews(s={}){var a,e;try{return(await t.get("/api/admin/reviews",{params:s})).data.data}catch(r){throw console.error("Error fetching reviews:",r),new Error(((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||"Failed to load reviews")}},async getReviewById(s){var a,e;try{return(await t.get(`/api/admin/reviews/${s}`)).data.data}catch(r){throw console.error("Error fetching review:",r),new Error(((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||"Failed to load review details")}},async updateReview(s,a){var e,r;try{return(await t.put(`/api/admin/reviews/${s}`,a)).data}catch(o){throw console.error("Error updating review:",o),new Error(((r=(e=o.response)==null?void 0:e.data)==null?void 0:r.message)||"Failed to update review")}},async deleteReview(s){var a,e;try{return(await t.delete(`/api/admin/reviews/${s}`)).data}catch(r){throw console.error("Error deleting review:",r),new Error(((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||"Failed to delete review")}},async bulkDeleteReviews(s){var a,e;try{return(await t.post("/api/admin/reviews/bulk-delete",{ids:s})).data}catch(r){throw console.error("Error bulk deleting reviews:",r),new Error(((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||"Failed to delete reviews")}},async getReviewStats(){var s,a;try{return(await t.get("/api/admin/reviews/stats")).data.data}catch(e){throw console.error("Error fetching review stats:",e),new Error(((a=(s=e.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to load review statistics")}}};export{n as r};
