import{_ as bs,g as i,f as ks,i as As,c as n,a as s,b as A,w as V,r as xs,d as g,t as l,s as Ss,F as is,p as rs,k as x,A as y,B as w,n as S,C as us,H as Ns,m as Cs,e as ws,o as d}from"./index-BtyG65bR.js";import{o as c}from"./orders-D4s5ZKaF.js";import{S as B}from"./StatusBadge-Tzu5Tszv.js";/* empty css                                                                    */const Os={class:"order-detail"},Ps={class:"level"},Rs={class:"level-right"},Is={class:"level-item"},Ds={key:0,class:"has-text-centered py-6"},Ms={key:1,class:"notification is-danger"},Us={key:2,class:"notification is-warning"},Vs={key:3},Bs={class:"card mb-4"},Es={class:"card-content"},Ts={class:"columns"},Fs={class:"column is-8"},$s={class:"order-title"},Ls={class:"order-date"},Hs={class:"columns"},qs={class:"column is-4"},zs={class:"card"},Gs={class:"card-content"},Qs={class:"info-group"},js={class:"info-value"},Js={class:"info-group"},Ks={class:"info-value"},Ws={class:"info-group"},Xs={class:"info-value"},Ys={class:"info-group"},Zs={class:"info-value"},st={class:"card mt-4"},tt={class:"card-content"},et={class:"info-group"},lt={class:"info-value"},at={class:"info-group"},ot={class:"info-value"},dt=["href"],nt={class:"info-group"},it={class:"info-value"},rt={class:"info-group"},ut={class:"info-value"},ct={key:1},vt={class:"column is-8"},pt={class:"card"},ft={class:"card-content"},ht={class:"table-container"},_t={class:"table is-fullwidth"},mt={class:"product-info"},gt={class:"image is-48x48 mr-2"},yt=["src","alt"],bt={class:"product-name"},kt={class:"product-id"},At={class:"has-text-right"},xt={class:"has-text-right"},St={key:0},Nt={class:"has-text-right"},Ct={class:"has-text-right"},wt={class:"has-text-right"},Ot={class:"has-text-right total-cell"},Pt={class:"columns mt-4"},Rt={class:"column is-6"},It={class:"card"},Dt={class:"card-content"},Mt={class:"address"},Ut={key:0},Vt={key:1},Bt={class:"column is-6"},Et={class:"card"},Tt={class:"card-content"},Ft={class:"address"},$t={key:0},Lt={key:1},Ht={class:"card mt-4"},qt={class:"card-content"},zt={key:0,class:"has-text-centered py-4"},Gt={key:1,class:"has-text-centered py-4"},Qt={key:2,class:"notes-list"},jt={class:"note-header"},Jt={class:"note-author"},Kt={class:"note-date"},Wt={class:"note-content"},Xt={class:"add-note mt-4"},Yt={class:"field"},Zt={class:"control"},se={class:"field"},te={class:"control"},ee=["disabled"],le={class:"modal-card"},ae={class:"modal-card-body"},oe={class:"content"},de={class:"field"},ne={class:"control"},ie={class:"select is-fullwidth"},re={class:"field"},ue={class:"control"},ce={class:"select is-fullwidth"},ve={class:"field"},pe={class:"control"},fe={class:"modal-card-foot"},he={class:"modal-card"},_e={class:"modal-card-body"},me={class:"content"},ge={class:"field"},ye={class:"control has-icons-left"},be=["max"],ke={class:"help"},Ae={class:"field"},xe={class:"control"},Se={class:"modal-card-foot"},Ne=["disabled"],Ce={__name:"OrderDetail",setup(we){const cs=ks();ws();const O=i(!0),P=i(!1),b=i(null),e=i({}),h=i([]),_=i(""),R=i(!1),I=i(!1),v=i(""),p=i(""),k=i(""),D=i(!1),E=i(!1),f=i(0),m=i(""),M=i(!1),u=computed(()=>cs.params.id),vs=async()=>{O.value=!0,b.value=null;try{const a=await c.getOrderById(u.value);e.value=a,v.value=a.status,p.value=a.paymentStatus}catch(a){console.error("Error fetching order:",a),b.value="Failed to load order data. Please try again."}finally{O.value=!1}},ps=async()=>{P.value=!0;try{const a=await c.getOrderNotes(u.value);h.value=a}catch(a){console.error("Error fetching order notes:",a)}finally{P.value=!1}},T=a=>a?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(new Date(a)):"",r=a=>new Intl.NumberFormat("uk-UA",{style:"currency",currency:"UAH"}).format(a),fs=a=>{a.target.src="https://via.placeholder.com/48?text=No+Image"},hs=async()=>{if(_.trim()){R.value=!0;try{const a=await c.addOrderNote(u.value,_);a.note&&(h.value.unshift(a.note),_.value="")}catch(a){console.error("Error adding note:",a)}finally{R.value=!1}}},_s=()=>{v.value=e.value.status,p.value=e.value.paymentStatus,k.value="",I.value=!0},N=()=>{I.value=!1},ms=async()=>{D.value=!0;try{if(v.value!==e.value.status&&(await c.updateOrderStatus(u.value,v.value),e.value.status=v.value),p.value!==e.value.paymentStatus&&(await c.updatePaymentStatus(u.value,p.value),e.value.paymentStatus=p.value),k.value.trim()){const a=await c.addOrderNote(u.value,k.value);a.note&&h.value.unshift(a.note)}N()}catch(a){console.error("Error updating order status:",a)}finally{D.value=!1}},C=()=>{E.value=!1},gs=async()=>{if(!(!f.value||!m.value.trim())){M.value=!0;try{await c.refundOrder(u.value,f.value,m.value),e.value.status="refunded",e.value.paymentStatus="refunded";const a=await c.addOrderNote(u.value,`Refund processed: ${r(f.value)}. Reason: ${m.value}`);a.note&&h.value.unshift(a.note),C()}catch(a){console.error("Error processing refund:",a)}finally{M.value=!1}}},ys=()=>{window.print()};return As(()=>{vs(),ps()}),(a,t)=>{var F,$,L,H,q,z,G,Q,j,J,K,W,X,Y,Z,ss,ts,es,ls,as,os,ds,ns;const U=xs("router-link");return d(),n("div",Os,[s("div",Ps,[t[9]||(t[9]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Order Details")])],-1)),s("div",Rs,[s("div",Is,[A(U,{to:"/admin/orders",class:"button is-light"},{default:V(()=>t[8]||(t[8]=[s("span",{class:"icon"},[s("i",{class:"fas fa-arrow-left"})],-1),s("span",null,"Back to Orders",-1)])),_:1})])])]),O.value?(d(),n("div",Ds,t[10]||(t[10]=[s("span",{class:"icon is-large"},[s("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),s("p",{class:"mt-2"},"Loading order details...",-1)]))):b.value?(d(),n("div",Ms,[s("button",{class:"delete",onClick:t[0]||(t[0]=o=>b.value=null)}),g(" "+l(b.value),1)])):e.value.id?(d(),n("div",Vs,[s("div",Bs,[s("div",Es,[s("div",Ts,[s("div",Fs,[s("h2",$s,"Order #"+l(e.value.id),1),s("p",Ls,l(T(e.value.createdAt)),1)]),s("div",{class:"column is-4 has-text-right"},[s("div",{class:"buttons is-right"},[s("button",{class:"button is-primary",onClick:_s},t[13]||(t[13]=[s("span",{class:"icon"},[s("i",{class:"fas fa-edit"})],-1),s("span",null,"Update Status",-1)])),s("button",{class:"button is-info",onClick:ys},t[14]||(t[14]=[s("span",{class:"icon"},[s("i",{class:"fas fa-print"})],-1),s("span",null,"Print",-1)]))])])])])]),s("div",Hs,[s("div",qs,[s("div",zs,[t[19]||(t[19]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Status")],-1)),s("div",Gs,[s("div",Qs,[t[15]||(t[15]=s("h3",{class:"info-label"},"Status",-1)),s("p",js,[A(B,{status:e.value.status,type:"order"},null,8,["status"])])]),s("div",Js,[t[16]||(t[16]=s("h3",{class:"info-label"},"Payment Status",-1)),s("p",Ks,[A(B,{status:e.value.paymentStatus,type:"payment"},null,8,["status"])])]),s("div",Ws,[t[17]||(t[17]=s("h3",{class:"info-label"},"Payment Method",-1)),s("p",Xs,l(e.value.paymentMethod||"N/A"),1)]),s("div",Ys,[t[18]||(t[18]=s("h3",{class:"info-label"},"Shipping Method",-1)),s("p",Zs,l(e.value.shippingMethod||"N/A"),1)])])]),s("div",st,[t[25]||(t[25]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Customer Information")],-1)),s("div",tt,[s("div",et,[t[20]||(t[20]=s("h3",{class:"info-label"},"Customer",-1)),s("p",lt,l(e.value.userName),1)]),s("div",at,[t[21]||(t[21]=s("h3",{class:"info-label"},"Email",-1)),s("p",ot,[s("a",{href:`mailto:${e.value.email}`},l(e.value.email),9,dt)])]),s("div",nt,[t[22]||(t[22]=s("h3",{class:"info-label"},"Phone",-1)),s("p",it,l(((F=e.value.shippingAddress)==null?void 0:F.phone)||"N/A"),1)]),s("div",rt,[t[24]||(t[24]=s("h3",{class:"info-label"},"Customer Account",-1)),s("p",ut,[e.value.userId?(d(),Ss(U,{key:0,to:`/admin/users/${e.value.userId}`},{default:V(()=>t[23]||(t[23]=[g(" View Customer Profile ")])),_:1},8,["to"])):(d(),n("span",ct,"Guest Checkout"))])])])])]),s("div",vt,[s("div",pt,[t[32]||(t[32]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Items")],-1)),s("div",ft,[s("div",ht,[s("table",_t,[t[31]||(t[31]=s("thead",null,[s("tr",null,[s("th",null,"Product"),s("th",null,"Price"),s("th",null,"Quantity"),s("th",{class:"has-text-right"},"Total")])],-1)),s("tbody",null,[(d(!0),n(is,null,rs(e.value.items,o=>(d(),n("tr",{key:o.id},[s("td",null,[s("div",mt,[s("figure",gt,[s("img",{src:o.image||"https://via.placeholder.com/48",alt:o.productName,onError:fs},null,40,yt)]),s("div",null,[s("p",bt,l(o.productName),1),s("p",kt,"ID: "+l(o.productId),1)])])]),s("td",null,l(r(o.price)),1),s("td",null,l(o.quantity),1),s("td",At,l(r(o.total)),1)]))),128))]),s("tfoot",null,[s("tr",null,[t[26]||(t[26]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Subtotal:")],-1)),s("td",xt,l(r(e.value.subtotal)),1)]),e.value.discount?(d(),n("tr",St,[t[27]||(t[27]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Discount:")],-1)),s("td",Nt,"-"+l(r(e.value.discount)),1)])):x("",!0),s("tr",null,[t[28]||(t[28]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Shipping:")],-1)),s("td",Ct,l(r(e.value.shipping)),1)]),s("tr",null,[t[29]||(t[29]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Tax:")],-1)),s("td",wt,l(r(e.value.tax)),1)]),s("tr",null,[t[30]||(t[30]=s("td",{colspan:"3",class:"has-text-right"},[s("strong",null,"Total:")],-1)),s("td",Ot,l(r(e.value.total)),1)])])])])])]),s("div",Pt,[s("div",Rt,[s("div",It,[t[33]||(t[33]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Shipping Address")],-1)),s("div",Dt,[s("address",Mt,[s("p",null,l(($=e.value.shippingAddress)==null?void 0:$.firstName)+" "+l((L=e.value.shippingAddress)==null?void 0:L.lastName),1),s("p",null,l((H=e.value.shippingAddress)==null?void 0:H.address1),1),(q=e.value.shippingAddress)!=null&&q.address2?(d(),n("p",Ut,l((z=e.value.shippingAddress)==null?void 0:z.address2),1)):x("",!0),s("p",null,l((G=e.value.shippingAddress)==null?void 0:G.city)+", "+l((Q=e.value.shippingAddress)==null?void 0:Q.state)+" "+l((j=e.value.shippingAddress)==null?void 0:j.postalCode),1),s("p",null,l((J=e.value.shippingAddress)==null?void 0:J.country),1),(K=e.value.shippingAddress)!=null&&K.phone?(d(),n("p",Vt,"Phone: "+l((W=e.value.shippingAddress)==null?void 0:W.phone),1)):x("",!0)])])])]),s("div",Bt,[s("div",Et,[t[34]||(t[34]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Billing Address")],-1)),s("div",Tt,[s("address",Ft,[s("p",null,l((X=e.value.billingAddress)==null?void 0:X.firstName)+" "+l((Y=e.value.billingAddress)==null?void 0:Y.lastName),1),s("p",null,l((Z=e.value.billingAddress)==null?void 0:Z.address1),1),(ss=e.value.billingAddress)!=null&&ss.address2?(d(),n("p",$t,l((ts=e.value.billingAddress)==null?void 0:ts.address2),1)):x("",!0),s("p",null,l((es=e.value.billingAddress)==null?void 0:es.city)+", "+l((ls=e.value.billingAddress)==null?void 0:ls.state)+" "+l((as=e.value.billingAddress)==null?void 0:as.postalCode),1),s("p",null,l((os=e.value.billingAddress)==null?void 0:os.country),1),(ds=e.value.billingAddress)!=null&&ds.phone?(d(),n("p",Lt,"Phone: "+l((ns=e.value.billingAddress)==null?void 0:ns.phone),1)):x("",!0)])])])])]),s("div",Ht,[t[38]||(t[38]=s("div",{class:"card-header"},[s("p",{class:"card-header-title"},"Order Notes")],-1)),s("div",qt,[P.value?(d(),n("div",zt,t[35]||(t[35]=[s("span",{class:"icon"},[s("i",{class:"fas fa-spinner fa-pulse"})],-1),s("p",{class:"mt-2"},"Loading notes...",-1)]))):h.value.length?(d(),n("div",Qt,[(d(!0),n(is,null,rs(h.value,o=>(d(),n("div",{key:o.id,class:"note-item"},[s("div",jt,[s("span",Jt,l(o.createdBy),1),s("span",Kt,l(T(o.createdAt)),1)]),s("div",Wt,l(o.content),1)]))),128))])):(d(),n("div",Gt,t[36]||(t[36]=[s("p",null,"No notes for this order",-1)]))),s("div",Xt,[s("div",Yt,[t[37]||(t[37]=s("label",{class:"label"},"Add Note",-1)),s("div",Zt,[y(s("textarea",{class:"textarea","onUpdate:modelValue":t[1]||(t[1]=o=>_.value=o),placeholder:"Add a note about this order"},"                    ",512),[[w,_.value]])])]),s("div",se,[s("div",te,[s("button",{class:S(["button is-primary",{"is-loading":R.value}]),onClick:hs,disabled:!_.value.trim()}," Add Note ",10,ee)])])])])])])])])):(d(),n("div",Us,[t[12]||(t[12]=s("p",null,"Order not found.",-1)),A(U,{to:"/admin/orders",class:"button is-primary mt-4"},{default:V(()=>t[11]||(t[11]=[g(" Back to Orders ")])),_:1})])),s("div",{class:S(["modal",{"is-active":I.value}])},[s("div",{class:"modal-background",onClick:N}),s("div",le,[s("header",{class:"modal-card-head"},[t[39]||(t[39]=s("p",{class:"modal-card-title"},"Update Order Status",-1)),s("button",{class:"delete","aria-label":"close",onClick:N})]),s("section",ae,[s("div",oe,[s("p",null,[t[40]||(t[40]=s("strong",null,"Order ID:",-1)),g(" "+l(e.value.id),1)]),s("p",null,[t[41]||(t[41]=s("strong",null,"Current Status:",-1)),A(B,{status:e.value.status,type:"order"},null,8,["status"])]),s("div",de,[t[43]||(t[43]=s("label",{class:"label"},"New Status",-1)),s("div",ne,[s("div",ie,[y(s("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>v.value=o)},t[42]||(t[42]=[Ns('<option value="pending" data-v-1ed1ec48>Pending</option><option value="processing" data-v-1ed1ec48>Processing</option><option value="shipped" data-v-1ed1ec48>Shipped</option><option value="delivered" data-v-1ed1ec48>Delivered</option><option value="cancelled" data-v-1ed1ec48>Cancelled</option><option value="refunded" data-v-1ed1ec48>Refunded</option>',6)]),512),[[us,v.value]])])])]),s("div",re,[t[45]||(t[45]=s("label",{class:"label"},"Payment Status",-1)),s("div",ue,[s("div",ce,[y(s("select",{"onUpdate:modelValue":t[3]||(t[3]=o=>p.value=o)},t[44]||(t[44]=[s("option",{value:"pending"},"Pending",-1),s("option",{value:"paid"},"Paid",-1),s("option",{value:"failed"},"Failed",-1),s("option",{value:"refunded"},"Refunded",-1)]),512),[[us,p.value]])])])]),s("div",ve,[t[46]||(t[46]=s("label",{class:"label"},"Note (Optional)",-1)),s("div",pe,[y(s("textarea",{class:"textarea","onUpdate:modelValue":t[4]||(t[4]=o=>k.value=o),placeholder:"Add a note about this status change"},"                ",512),[[w,k.value]])])])])]),s("footer",fe,[s("button",{class:S(["button is-primary",{"is-loading":D.value}]),onClick:ms}," Update Status ",2),s("button",{class:"button",onClick:N},"Cancel")])])],2),s("div",{class:S(["modal",{"is-active":E.value}])},[s("div",{class:"modal-background",onClick:C}),s("div",he,[s("header",{class:"modal-card-head"},[t[47]||(t[47]=s("p",{class:"modal-card-title"},"Process Refund",-1)),s("button",{class:"delete","aria-label":"close",onClick:C})]),s("section",_e,[s("div",me,[s("p",null,[t[48]||(t[48]=s("strong",null,"Order ID:",-1)),g(" "+l(e.value.id),1)]),s("p",null,[t[49]||(t[49]=s("strong",null,"Order Total:",-1)),g(" "+l(r(e.value.total)),1)]),s("div",ge,[t[51]||(t[51]=s("label",{class:"label"},"Refund Amount",-1)),s("div",ye,[y(s("input",{class:"input",type:"number","onUpdate:modelValue":t[5]||(t[5]=o=>f.value=o),min:"0",max:e.value.total,step:"0.01"},null,8,be),[[w,f.value]]),t[50]||(t[50]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-hryvnia"})],-1))]),s("p",ke,[s("a",{href:"#",onClick:t[6]||(t[6]=Cs(o=>f.value=e.value.total,["prevent"]))},"Refund full amount")])]),s("div",Ae,[t[52]||(t[52]=s("label",{class:"label"},"Reason for Refund",-1)),s("div",xe,[y(s("textarea",{class:"textarea","onUpdate:modelValue":t[7]||(t[7]=o=>m.value=o),placeholder:"Enter reason for refund"},"                ",512),[[w,m.value]])])])])]),s("footer",Se,[s("button",{class:S(["button is-danger",{"is-loading":M.value}]),onClick:gs,disabled:!f.value||!m.value.trim()}," Process Refund ",10,Ne),s("button",{class:"button",onClick:C},"Cancel")])])],2)])}}},De=bs(Ce,[["__scopeId","data-v-1ed1ec48"]]);export{De as default};
