<template>
  <div class="categories-page">
    <!-- Hero <PERSON>er -->
    <div class="hero is-light is-bold mb-6">
      <div class="hero-body py-4">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <div>
                <h1 class="title is-2 has-text-dark">
                  <span class="icon-text">
                    <span class="icon has-text-primary">
                      <i class="fas fa-sitemap"></i>
                    </span>
                    <span>Categories Management</span>
                  </span>
                </h1>
                <p class="subtitle is-5 has-text-grey">
                  Manage product categories and their hierarchy
                </p>
              </div>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button class="button is-primary is-medium" @click="openCreateModal">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add Category</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="columns mb-5">
      <div class="column is-3">
        <div class="card has-background-primary-light">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="title is-4 has-text-primary">{{ stats.total }}</p>
                    <p class="subtitle is-6 has-text-grey">Total Categories</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-primary">
                    <i class="fas fa-sitemap fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card has-background-success-light">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="title is-4 has-text-success">{{ stats.rootCategories }}</p>
                    <p class="subtitle is-6 has-text-grey">Root Categories</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-success">
                    <i class="fas fa-layer-group fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card has-background-info-light">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="title is-4 has-text-info">{{ stats.withProducts }}</p>
                    <p class="subtitle is-6 has-text-grey">With Products</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-info">
                    <i class="fas fa-boxes fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column is-3">
        <div class="card has-background-warning-light">
          <div class="card-content">
            <div class="level">
              <div class="level-left">
                <div class="level-item">
                  <div>
                    <p class="title is-4 has-text-warning">{{ stats.totalProducts }}</p>
                    <p class="subtitle is-6 has-text-grey">Total Products</p>
                  </div>
                </div>
              </div>
              <div class="level-right">
                <div class="level-item">
                  <span class="icon is-large has-text-warning">
                    <i class="fas fa-cube fa-2x"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-5">
      <div class="card-content">
        <div class="columns">
          <div class="column is-6">
            <div class="field">
              <label class="label">Search Categories</label>
              <div class="control has-icons-left">
                <input 
                  v-model="searchQuery" 
                  class="input" 
                  type="text" 
                  placeholder="Search by name, slug, or description..."
                  @input="handleSearch">
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Filter by Type</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.type" @change="handleFilter">
                    <option value="">All Categories</option>
                    <option value="root">Root Categories</option>
                    <option value="child">Child Categories</option>
                    <option value="with-products">With Products</option>
                    <option value="empty">Empty Categories</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Sort by</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="sortBy" @change="handleSort">
                    <option value="name">Name</option>
                    <option value="productCount">Product Count</option>
                    <option value="createdAt">Created Date</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <div class="field is-grouped">
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" v-model="showHierarchy" @change="toggleHierarchy">
                    Show Hierarchy View
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button class="button is-light" @click="handleReset">
                <span class="icon">
                  <i class="fas fa-undo"></i>
                </span>
                <span>Reset Filters</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Display -->
    <div class="card">
      <div class="card-content">
        <!-- Loading State -->
        <div v-if="loading" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading categories...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="notification is-danger">
          <p>{{ error }}</p>
          <button class="button is-light mt-2" @click="fetchCategories">
            <span class="icon"><i class="fas fa-redo"></i></span>
            <span>Retry</span>
          </button>
        </div>

        <!-- Categories Content -->
        <div v-else>
          <!-- Hierarchy View -->
          <div v-if="showHierarchy" class="hierarchy-view">
            <CategoryHierarchy
              :categories="hierarchicalCategories"
              @edit="openEditModal"
              @delete="confirmDelete"
              @add-child="openCreateChildModal"
            />
          </div>

          <!-- Table View -->
          <div v-else class="table-view">
            <CategoryTable
              :categories="categories"
              :loading="loading"
              @edit="openEditModal"
              @delete="confirmDelete"
              @view-products="viewCategoryProducts"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <nav v-if="pagination.totalPages > 1" class="pagination is-centered mt-5" role="navigation">
      <button 
        class="pagination-previous" 
        :disabled="pagination.currentPage === 1"
        @click="changePage(pagination.currentPage - 1)">
        Previous
      </button>
      <button 
        class="pagination-next" 
        :disabled="pagination.currentPage === pagination.totalPages"
        @click="changePage(pagination.currentPage + 1)">
        Next page
      </button>
      <ul class="pagination-list">
        <li v-for="page in visiblePages" :key="page">
          <button 
            v-if="page !== '...'"
            class="pagination-link" 
            :class="{ 'is-current': page === pagination.currentPage }"
            @click="changePage(page)">
            {{ page }}
          </button>
          <span v-else class="pagination-ellipsis">&hellip;</span>
        </li>
      </ul>
    </nav>

    <!-- Modals -->
    <CategoryFormModal
      :is-open="showFormModal"
      :category="selectedCategory"
      :is-edit="isEdit"
      :parent-categories="parentCategories"
      @save="handleSave"
      @close="closeFormModal"
    />

    <ConfirmDialog
      :is-open="showDeleteDialog"
      title="Delete Category"
      :message="deleteMessage"
      confirm-text="Delete"
      confirm-class="is-danger"
      @confirm="handleDelete"
      @cancel="closeDeleteDialog"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import CategoryTable from '@/admin/components/categories/CategoryTable.vue';
import CategoryHierarchy from '@/admin/components/categories/CategoryHierarchy.vue';
import CategoryFormModal from '@/admin/components/categories/CategoryFormModal.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import { categoriesService } from '@/admin/services/categories';

// Reactive data
const loading = ref(false);
const error = ref('');
const categories = ref([]);
const hierarchicalCategories = ref([]);
const selectedCategory = ref(null);
const categoryToDelete = ref(null);

// UI state
const showFormModal = ref(false);
const showDeleteDialog = ref(false);
const isEdit = ref(false);
const showHierarchy = ref(false);

// Search and filters
const searchQuery = ref('');
const filters = ref({
  type: '',
  parentId: null
});
const sortBy = ref('name');
const sortOrder = ref('asc');

// Pagination
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  perPage: 20
});

// Stats
const stats = ref({
  total: 0,
  rootCategories: 0,
  withProducts: 0,
  totalProducts: 0
});

// Computed properties
const parentCategories = computed(() => {
  return categories.value.filter(cat => !cat.parentId);
});

const visiblePages = computed(() => {
  const current = pagination.value.currentPage;
  const total = pagination.value.totalPages;
  const pages = [];

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i);
      pages.push('...');
      pages.push(total);
    } else if (current >= total - 3) {
      pages.push(1);
      pages.push('...');
      for (let i = total - 4; i <= total; i++) pages.push(i);
    } else {
      pages.push(1);
      pages.push('...');
      for (let i = current - 1; i <= current + 1; i++) pages.push(i);
      pages.push('...');
      pages.push(total);
    }
  }

  return pages;
});

const deleteMessage = computed(() => {
  if (!categoryToDelete.value) return '';
  const hasChildren = categories.value.some(cat => cat.parentId === categoryToDelete.value.id);
  const hasProducts = categoryToDelete.value.productCount > 0;

  let message = `Are you sure you want to delete '${categoryToDelete.value.name}'?`;

  if (hasChildren) {
    message += '\n\nThis category has subcategories that will also be affected.';
  }

  if (hasProducts) {
    message += `\n\nThis category contains ${categoryToDelete.value.productCount} products.`;
  }

  return message;
});

// Methods
const fetchCategories = async () => {
  try {
    loading.value = true;
    error.value = '';

    const params = {
      filter: searchQuery.value || undefined,
      orderBy: sortBy.value,
      descending: sortOrder.value === 'desc',
      page: pagination.value.currentPage,
      pageSize: pagination.value.perPage
    };

    // Add type filter
    if (filters.value.type) {
      switch (filters.value.type) {
        case 'root':
          params.parentId = null;
          break;
        case 'child':
          params.hasParent = true;
          break;
        case 'with-products':
          params.hasProducts = true;
          break;
        case 'empty':
          params.hasProducts = false;
          break;
      }
    }

    const response = await categoriesService.getAll(params);

    categories.value = response.data || [];
    pagination.value = {
      currentPage: response.currentPage || 1,
      totalPages: response.totalPages || 1,
      totalItems: response.totalItems || 0,
      perPage: response.pageSize || 20
    };

    // Build hierarchy if needed
    if (showHierarchy.value) {
      buildHierarchy();
    }

    // Update stats
    updateStats();

  } catch (err) {
    console.error('Error fetching categories:', err);
    error.value = 'Failed to load categories. Please try again.';
  } finally {
    loading.value = false;
  }
};

const buildHierarchy = () => {
  const categoryMap = new Map();
  const rootCategories = [];

  // Create map of all categories
  categories.value.forEach(cat => {
    categoryMap.set(cat.id, { ...cat, children: [] });
  });

  // Build hierarchy
  categories.value.forEach(cat => {
    if (cat.parentId) {
      const parent = categoryMap.get(cat.parentId);
      if (parent) {
        parent.children.push(categoryMap.get(cat.id));
      }
    } else {
      rootCategories.push(categoryMap.get(cat.id));
    }
  });

  hierarchicalCategories.value = rootCategories;
};

const updateStats = () => {
  stats.value = {
    total: categories.value.length,
    rootCategories: categories.value.filter(cat => !cat.parentId).length,
    withProducts: categories.value.filter(cat => cat.productCount > 0).length,
    totalProducts: categories.value.reduce((sum, cat) => sum + (cat.productCount || 0), 0)
  };
};

// Event handlers
const handleSearch = () => {
  pagination.value.currentPage = 1;
  fetchCategories();
};

const handleFilter = () => {
  pagination.value.currentPage = 1;
  fetchCategories();
};

const handleSort = () => {
  pagination.value.currentPage = 1;
  fetchCategories();
};

const handleReset = () => {
  searchQuery.value = '';
  filters.value = { type: '', parentId: null };
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  pagination.value.currentPage = 1;
  fetchCategories();
};

const toggleHierarchy = () => {
  if (showHierarchy.value) {
    buildHierarchy();
  }
};

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.currentPage = page;
    fetchCategories();
  }
};

// Modal handlers
const openCreateModal = () => {
  selectedCategory.value = null;
  isEdit.value = false;
  showFormModal.value = true;
};

const openCreateChildModal = (parentCategory) => {
  selectedCategory.value = { parentId: parentCategory.id };
  isEdit.value = false;
  showFormModal.value = true;
};

const openEditModal = (category) => {
  selectedCategory.value = { ...category };
  isEdit.value = true;
  showFormModal.value = true;
};

const closeFormModal = () => {
  showFormModal.value = false;
  selectedCategory.value = null;
};

const confirmDelete = (category) => {
  categoryToDelete.value = category;
  showDeleteDialog.value = true;
};

const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  categoryToDelete.value = null;
};

const handleSave = async (categoryData) => {
  try {
    if (isEdit.value) {
      await categoriesService.update(selectedCategory.value.id, categoryData);
    } else {
      await categoriesService.create(categoryData);
    }

    closeFormModal();
    fetchCategories();
  } catch (err) {
    console.error('Error saving category:', err);
    error.value = 'Failed to save category. Please try again.';
  }
};

const handleDelete = async () => {
  try {
    await categoriesService.delete(categoryToDelete.value.id);
    closeDeleteDialog();
    fetchCategories();
  } catch (err) {
    console.error('Error deleting category:', err);
    error.value = 'Failed to delete category. Please try again.';
  }
};

const viewCategoryProducts = (category) => {
  // Navigate to products page with category filter
  window.location.href = `/admin/products?categoryId=${category.id}`;
};

// Watchers
watch([searchQuery, filters, sortBy], () => {
  // Debounce search
  clearTimeout(window.searchTimeout);
  window.searchTimeout = setTimeout(() => {
    handleSearch();
  }, 300);
}, { deep: true });

// Lifecycle
onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.categories-page {
  padding: 1rem;
}

.hierarchy-view {
  max-height: 600px;
  overflow-y: auto;
}

.table-view {
  overflow-x: auto;
}

.pagination-wrapper {
  margin-top: 2rem;
}

.stats-card {
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.search-filters {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

.hierarchy-item {
  border-left: 2px solid #e0e0e0;
  margin-left: 1rem;
  padding-left: 1rem;
}

.hierarchy-item.root {
  border-left: none;
  margin-left: 0;
  padding-left: 0;
}
</style>
