﻿using AutoMapper;
using Marketplace.Application.Commands.Notification;
using Marketplace.Domain.Repositories;
using Marketplace.Domain.ValueObjects;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Application.Commands.Order;

public class UpdateOrderCommandHandler : IRequestHandler<UpdateOrderCommand, UpdateOrderResponse>
{
    private readonly INotificationRepository? _notificationRepository;
    private readonly IOrderRepository _repository;
    private readonly IMapper _mapper;

    public UpdateOrderCommandHandler(IOrderRepository repository, IMapper mapper, INotificationRepository? notificationRepository = null)
    {
        _repository = repository;
        _mapper = mapper;
        _notificationRepository = notificationRepository;
    }

    public async Task<UpdateOrderResponse> Handle(UpdateOrderCommand command, CancellationToken cancellationToken)
    {
        try
        {
            var item = await _repository.GetByIdAsync(command.Id, cancellationToken);
            if (item == null)
            {
                return new UpdateOrderResponse
                {
                    Success = false,
                    Message = "Order not found",
                    OrderId = command.Id
                };
            }

            // Send notification if status changed
            if (command.Status.HasValue && item.Status != command.Status && _notificationRepository != null)
            {
                string oldOrderStatus = item.Status.ToString();
                string newOrderStatus = command.Status.ToString()!;
                var notification = new StoreNotificationCommand(
                    item.CustomerId,
                    "Змінено статус вашого замовлення!",
                    "Вітання! Один з наших адміністраторів змінив статус вашого замолення з " + oldOrderStatus + " на " + newOrderStatus + ".");

                var notificationItem = _mapper.Map<Domain.Entities.Notification>(notification);
                await _notificationRepository.AddAsync(notificationItem, cancellationToken);
            }

            // Update order properties
            if (command.Status.HasValue)
            {
                item.Status = command.Status.Value;
            }

            // Note: PaymentStatus is handled through Payment entity, not Order
            // Note: UpdatedAt is not available in Order entity

            await _repository.UpdateAsync(item, cancellationToken);

            return new UpdateOrderResponse
            {
                Success = true,
                Message = "Order updated successfully",
                OrderId = item.Id
            };
        }
        catch (Exception ex)
        {
            return new UpdateOrderResponse
            {
                Success = false,
                Message = $"Error updating order: {ex.Message}",
                OrderId = command.Id
            };
        }
    }
}

