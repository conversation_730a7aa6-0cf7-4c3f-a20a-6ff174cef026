{"file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/start-dev.bat": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/README-GOOGLE-AUTH.md": {"language": "<PERSON><PERSON>", "code": 76, "comment": 0, "blank": 27}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Properties/launchSettings.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Exceptions/DomainException.cs": {"language": "C#", "code": 15, "comment": 1, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IProductImageRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IRatingRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IPaymentRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IProductRepository.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IOrderRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IOrderItemRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IOrderCouponRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/INotificationRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IMessageRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IFavoriteRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/ICouponRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/ICompanyRepository.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/ICompanyScheduleRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/ICompanyUserRepository.cs": {"language": "C#", "code": 7, "comment": 4, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IChatRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IRepository.cs": {"language": "C#", "code": 15, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Url.cs": {"language": "C#", "code": 16, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Slug.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/ICategoryRepository.cs": {"language": "C#", "code": 10, "comment": 1, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IAddressRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Password.cs": {"language": "C#", "code": 28, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IReviewRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Phone.cs": {"language": "C#", "code": 21, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Money.cs": {"language": "C#", "code": 29, "comment": 1, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Meta.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IShippingMethodRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/Email.cs": {"language": "C#", "code": 27, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/ValueObjects/AddressVO.cs": {"language": "C#", "code": 39, "comment": 1, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Marketplace.Domain.csproj": {"language": "XML", "code": 22, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IUserRepository.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IWishlistRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Repositories/IWishlistItemRepository.cs": {"language": "C#", "code": 5, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/CompanyUser.cs": {"language": "C#", "code": 39, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Coupon.cs": {"language": "C#", "code": 102, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/CompanySchedule.cs": {"language": "C#", "code": 92, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Favorite.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Company.cs": {"language": "C#", "code": 187, "comment": 0, "blank": 18}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Chat.cs": {"language": "C#", "code": 48, "comment": 17, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Category.cs": {"language": "C#", "code": 124, "comment": 2, "blank": 18}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/WishlistItem.cs": {"language": "C#", "code": 27, "comment": 19, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Wishlist.cs": {"language": "C#", "code": 50, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/User.cs": {"language": "C#", "code": 104, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/ShippingMethod.cs": {"language": "C#", "code": 75, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Review.cs": {"language": "C#", "code": 63, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Rating.cs": {"language": "C#", "code": 104, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/ProductImage.cs": {"language": "C#", "code": 41, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Product.cs": {"language": "C#", "code": 186, "comment": 1, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Payment.cs": {"language": "C#", "code": 83, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/OrderCoupon.cs": {"language": "C#", "code": 27, "comment": 18, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/OrderItem.cs": {"language": "C#", "code": 74, "comment": 0, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Order.cs": {"language": "C#", "code": 105, "comment": 0, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Notification.cs": {"language": "C#", "code": 65, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Message.cs": {"language": "C#", "code": 58, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/IEntity.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Domain/Entities/Address.cs": {"language": "C#", "code": 36, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Privacy.cshtml": {"language": "ASP.NET Razor", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/_ViewStart.cshtml": {"language": "ASP.NET Razor", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Program.cs": {"language": "C#", "code": 112, "comment": 3, "blank": 21}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/_ViewImports.cshtml": {"language": "ASP.NET Razor", "code": 5, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Privacy.cshtml.cs": {"language": "C#", "code": 14, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Error.cshtml.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Error.cshtml": {"language": "ASP.NET Razor", "code": 23, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/MockAuthenticationHandler.cs": {"language": "C#", "code": 27, "comment": 2, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Shared/_ValidationScriptsPartial.cshtml": {"language": "ASP.NET Razor", "code": 2, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Shared/_Layout.cshtml.css": {"language": "CSS", "code": 38, "comment": 2, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Marketplace.Presentation.csproj": {"language": "XML", "code": 24, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Pages/Shared/_Layout.cshtml": {"language": "ASP.NET Razor", "code": 49, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/ChatController.cs": {"language": "C#", "code": 69, "comment": 0, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/CategoryController.cs": {"language": "C#", "code": 61, "comment": 0, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/BasicApiController.cs": {"language": "C#", "code": 15, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/AuthController.cs": {"language": "C#", "code": 97, "comment": 14, "blank": 21}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/AdminCategoryController.cs": {"language": "C#", "code": 158, "comment": 10, "blank": 33}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/NotificationController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/OrderController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/MessageController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/FavoriteController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/CouponController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/OrderCouponController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/CompanyUserController.cs": {"language": "C#", "code": 55, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/CompanyScheduleController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/CompanyController.cs": {"language": "C#", "code": 61, "comment": 0, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/AddressController.cs": {"language": "C#", "code": 53, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/ShippingMethodController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/UserController.cs": {"language": "C#", "code": 68, "comment": 0, "blank": 19}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/ReviewController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Routes.md": {"language": "<PERSON><PERSON>", "code": 170, "comment": 0, "blank": 21}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/ProductImageController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/RatingController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/ProductController.cs": {"language": "C#", "code": 75, "comment": 0, "blank": 19}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/WishlistItemController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/WishlistController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/PaymentController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/Controllers/OrderItemController.cs": {"language": "C#", "code": 54, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/appsettings.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Presentation/appsettings.Development.json": {"language": "JSON", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/MarketplaceDbContext.cs": {"language": "C#", "code": 37, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/OrderItemRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/OrderCouponRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/NotificationRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/MessageRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/OrderItemConfiguration.cs": {"language": "C#", "code": 43, "comment": 2, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/PaymentConfiguration.cs": {"language": "C#", "code": 41, "comment": 2, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/OrderCouponConfiguration.cs": {"language": "C#", "code": 29, "comment": 2, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/OrderConfiguration.cs": {"language": "C#", "code": 48, "comment": 2, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/ProductConfiguration.cs": {"language": "C#", "code": 78, "comment": 2, "blank": 22}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/NotificationConfiguration.cs": {"language": "C#", "code": 28, "comment": 2, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/WishlistRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/MessageConfiguration.cs": {"language": "C#", "code": 33, "comment": 8, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/WishlistItemRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/FavoriteConfiguration.cs": {"language": "C#", "code": 46, "comment": 2, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/CouponConfiguration.cs": {"language": "C#", "code": 36, "comment": 0, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/ShippingMethodRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/CompanyUserConfiguration.cs": {"language": "C#", "code": 26, "comment": 4, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/UserRepository.cs": {"language": "C#", "code": 49, "comment": 4, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/ReviewRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/CompanyScheduleConfiguration.cs": {"language": "C#", "code": 29, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/Repository.cs": {"language": "C#", "code": 76, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/CompanyConfiguration.cs": {"language": "C#", "code": 78, "comment": 1, "blank": 19}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/RatingRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/ChatConfiguration.cs": {"language": "C#", "code": 31, "comment": 7, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/ProductImageConfiguration.cs": {"language": "C#", "code": 27, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/ProductRepository.cs": {"language": "C#", "code": 33, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/CategoryConfiguration.cs": {"language": "C#", "code": 60, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/ProductImageRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/AddressConfiguration.cs": {"language": "C#", "code": 36, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/PaymentRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/OrderRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/FavoriteRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/CouponRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/CompanyUserRepository.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/CompanyScheduleRepository.cs": {"language": "C#", "code": 15, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/CompanyRepository.cs": {"language": "C#", "code": 24, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/ChatRepository.cs": {"language": "C#", "code": 15, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/CategoryRepository.cs": {"language": "C#", "code": 58, "comment": 7, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Implementation/AddressRepository.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/WishlistItemConfiguration.cs": {"language": "C#", "code": 29, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/RatingConfiguration.cs": {"language": "C#", "code": 41, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/UserConfiguration.cs": {"language": "C#", "code": 36, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/WishlistConfiguration.cs": {"language": "C#", "code": 30, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/ShippingMethodConfiguration.cs": {"language": "C#", "code": 32, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Persistence/Configurations/ReviewConfiguration.cs": {"language": "C#", "code": 41, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Marketplace.Infrastructure.csproj": {"language": "XML", "code": 27, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250506200445_RemovePhoneColumn.cs": {"language": "C#", "code": 14, "comment": 3, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250506200445_RemovePhoneColumn.Designer.cs": {"language": "C#", "code": 1244, "comment": 2, "blank": 448}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250420180614_UserUpdate.cs": {"language": "C#", "code": 47, "comment": 3, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250420180614_UserUpdate.Designer.cs": {"language": "C#", "code": 1244, "comment": 2, "blank": 448}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/MarketplaceDbContextModelSnapshot.cs": {"language": "C#", "code": 1242, "comment": 1, "blank": 448}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/InfrastructureServiceExtensions.cs": {"language": "C#", "code": 46, "comment": 8, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250412102541_InitialCreate.Designer.cs": {"language": "C#", "code": 1236, "comment": 2, "blank": 444}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/Migrations/20250412102541_InitialCreate.cs": {"language": "C#", "code": 912, "comment": 3, "blank": 98}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Infrastructure/DatabaseSeeder/DatabaseSeeder.cs": {"language": "C#", "code": 64, "comment": 7, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/CompanyScheduleValidator.cs": {"language": "C#", "code": 38, "comment": 0, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/ChatValidator.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/CategoryValidator.cs": {"language": "C#", "code": 65, "comment": 0, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/AddressValidator.cs": {"language": "C#", "code": 42, "comment": 1, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/WishlistValidator.cs": {"language": "C#", "code": 23, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/WishlistItemValidator.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Services/Auth/TokenService.cs": {"language": "C#", "code": 15, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/UserValidator.cs": {"language": "C#", "code": 52, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/ShippingMethodValidator.cs": {"language": "C#", "code": 28, "comment": 5, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/ReviewValidator.cs": {"language": "C#", "code": 32, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Services/Auth/JwtTokenService.cs": {"language": "C#", "code": 55, "comment": 3, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/RatingValidator.cs": {"language": "C#", "code": 40, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Services/Auth/EmailService.cs": {"language": "C#", "code": 48, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/ProductValidator.cs": {"language": "C#", "code": 81, "comment": 5, "blank": 28}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/ProductImageValidator.cs": {"language": "C#", "code": 27, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/PaymentValidator.cs": {"language": "C#", "code": 37, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/OrderValidator.cs": {"language": "C#", "code": 43, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/OrderItemValidator.cs": {"language": "C#", "code": 36, "comment": 5, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/OrderCouponValidator.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/NotificationValidator.cs": {"language": "C#", "code": 29, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/MessageValidator.cs": {"language": "C#", "code": 34, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/CouponValidator.cs": {"language": "C#", "code": 40, "comment": 6, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/CompanyValidator.cs": {"language": "C#", "code": 89, "comment": 0, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/CompanyUserValidator.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Validators/FavoriteValidator.cs": {"language": "C#", "code": 45, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/NotificationResponse.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/WishlistItemResponse.cs": {"language": "C#", "code": 14, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/UserResponse.cs": {"language": "C#", "code": 35, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/ShippingMethodResponse.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/ReviewResponse.cs": {"language": "C#", "code": 14, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderItem/GetOrderItemQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderItem/GetOrderItemQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderItem/GetAllOrderItemQueryHandler.cs": {"language": "C#", "code": 89, "comment": 1, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/WishlistResponse.cs": {"language": "C#", "code": 12, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/RatingResponse.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Wishlist/GetWishlistQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Wishlist/GetAllWishlistQueryHandler.cs": {"language": "C#", "code": 76, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Wishlist/GetAllWishlistQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQueryHandler.cs": {"language": "C#", "code": 90, "comment": 1, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/WishlistItem/GetWishlistItemQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/WishlistItem/GetAllWishlistItemQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/ProductResponse.cs": {"language": "C#", "code": 51, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Wishlist/GetWishlistQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/ProductImageResponse.cs": {"language": "C#", "code": 12, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Review/GetReviewQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Review/GetReviewQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Review/GetAllReviewQueryHandler.cs": {"language": "C#", "code": 91, "comment": 1, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Review/GetAllReviewQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ShippingMethod/GetShippingMethodQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserByUsernameQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQueryHandler.cs": {"language": "C#", "code": 75, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ShippingMethod/GetAllShippingMethodQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserByUsernameQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserByEmailQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetUserByEmailQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetAllUserQueryHandler.cs": {"language": "C#", "code": 74, "comment": 0, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/User/GetAllUserQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/PaymentResponse.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/PaginatedResponse.cs": {"language": "C#", "code": 16, "comment": 0, "blank": 0}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/OrderResponse.cs": {"language": "C#", "code": 23, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductBySlugQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetAllProductQueryHandler.cs": {"language": "C#", "code": 87, "comment": 1, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ProductImage/GetProductImageQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductBySlugQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetAllProductQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ProductImage/GetProductImageQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Rating/GetRatingQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ProductImage/GetAllProductImageQueryHandler.cs": {"language": "C#", "code": 87, "comment": 1, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Rating/GetRatingQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/ProductImage/GetAllProductImageQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Rating/GetAllRatingQueryHandler.cs": {"language": "C#", "code": 91, "comment": 1, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/OrderItemResponse.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Rating/GetAllRatingQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/OrderCouponResponse.cs": {"language": "C#", "code": 14, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductsByCategoryQueryHandler.cs": {"language": "C#", "code": 88, "comment": 8, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Product/GetProductsByCategoryQuery.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/MessageResponse.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQueryHandler.cs": {"language": "C#", "code": 76, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderCoupon/GetAllOrderCouponQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Payment/GetPaymentQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Payment/GetPaymentQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Payment/GetAllPaymentQueryHandler.cs": {"language": "C#", "code": 77, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Payment/GetAllPaymentQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Order/GetOrderQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Order/GetOrderQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Order/GetAllOrderQueryHandler.cs": {"language": "C#", "code": 74, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Order/GetAllOrderQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/OrderCoupon/GetOrderCouponQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/FavoriteResponse.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/CouponResponse.cs": {"language": "C#", "code": 21, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Favorite/GetAllFavoriteQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetAllMessageQueryHandler.cs": {"language": "C#", "code": 73, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetAllMessageQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Notification/GetNotificationQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Notification/GetNotificationQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Notification/GetAllNotificationQueryHandler.cs": {"language": "C#", "code": 73, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetMessageQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Notification/GetAllNotificationQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Favorite/GetAllFavoriteQueryHandler.cs": {"language": "C#", "code": 73, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/CompanyUserResponse.cs": {"language": "C#", "code": 16, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetMessagesByChatQueryHandler.cs": {"language": "C#", "code": 82, "comment": 7, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetMessageQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Favorite/GetFavoriteQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Message/GetMessagesByChatQuery.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Favorite/GetFavoriteQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/CompanyScheduleResponse.cs": {"language": "C#", "code": 21, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQueryHandler.cs": {"language": "C#", "code": 26, "comment": 47, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanyUser/GetAllCompanyUserQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Coupon/GetCouponQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Coupon/GetCouponQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Coupon/GetAllCouponQueryHandler.cs": {"language": "C#", "code": 74, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Coupon/GetAllCouponQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/CompanyResponse.cs": {"language": "C#", "code": 51, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanySchedule/GetCompanyScheduleQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQueryHandler.cs": {"language": "C#", "code": 74, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQueryHandler.cs": {"language": "C#", "code": 19, "comment": 4, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanySchedule/GetAllCompanyScheduleQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/CompanyUser/GetCompanyUserQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/ChatResponse.cs": {"language": "C#", "code": 16, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/CategoryResponse.cs": {"language": "C#", "code": 26, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Chat/GetChatQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Chat/GetChatQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Chat/GetAllChatQueryHandler.cs": {"language": "C#", "code": 73, "comment": 5, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Chat/GetAllChatQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetCompanyQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetCompanyQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetCategoryBySlugQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetAllCompanyQueryHandler.cs": {"language": "C#", "code": 81, "comment": 5, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetAllCompanyQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Company/GetCategoryBySlugQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetAllCategoryQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/AuthResponse.cs": {"language": "C#", "code": 12, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetCategoryBySlugQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetAllCategoryQueryHandler.cs": {"language": "C#", "code": 74, "comment": 5, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetCategoryQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetCategoryQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Category/GetCategoryBySlugQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Responses/AddressResponse.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Marketplace.Application.csproj": {"language": "XML", "code": 30, "comment": 0, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Address/GetAddressQueryHandler.cs": {"language": "C#", "code": 22, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Address/GetAddressQuery.cs": {"language": "C#", "code": 4, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Address/GetAllAddressQueryHandler.cs": {"language": "C#", "code": 76, "comment": 5, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Queries/Address/GetAllAddressQuery.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/OrderItemMappingProfile.cs": {"language": "C#", "code": 25, "comment": 6, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/OrderCouponMappingProfile.cs": {"language": "C#", "code": 16, "comment": 4, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/NotificationMappingProfile.cs": {"language": "C#", "code": 21, "comment": 4, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/MessageMappingProfile.cs": {"language": "C#", "code": 21, "comment": 4, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/FavoriteMappingProfile.cs": {"language": "C#", "code": 22, "comment": 8, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/OrderMappingProfile.cs": {"language": "C#", "code": 24, "comment": 6, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Extensions/ValidationExtensions.cs": {"language": "C#", "code": 46, "comment": 1, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/CouponMappingProfile.cs": {"language": "C#", "code": 24, "comment": 8, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/CompanyUserMappingProfile.cs": {"language": "C#", "code": 18, "comment": 3, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/CompanyMappingProfile.cs": {"language": "C#", "code": 39, "comment": 19, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/CompanyScheduleMappingProfile.cs": {"language": "C#", "code": 24, "comment": 6, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/ChatMappingProfile.cs": {"language": "C#", "code": 20, "comment": 6, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/CategoryMappingProfile.cs": {"language": "C#", "code": 29, "comment": 1, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/AddressMappingProfile.cs": {"language": "C#", "code": 19, "comment": 8, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/RegisterCommandHandler.cs": {"language": "C#", "code": 49, "comment": 3, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/RequestPasswordResetCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/ResetPasswordCommand.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/RequestPasswordResetCommandHandler.cs": {"language": "C#", "code": 37, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/RegisterCommand.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/UpdateProfileCommandHandler.cs": {"language": "C#", "code": 46, "comment": 1, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/UpdateProfileCommand.cs": {"language": "C#", "code": 16, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/UpdatePasswordCommandHandler.cs": {"language": "C#", "code": 31, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/UpdatePasswordCommand.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/ResetPasswordCommandHandler.cs": {"language": "C#", "code": 24, "comment": 1, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/LoginCommandHandler.cs": {"language": "C#", "code": 53, "comment": 6, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommandHandler.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/UpdateWishlistCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommandHandler.cs": {"language": "C#", "code": 22, "comment": 11, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/DeleteUserCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/StoreWishlistCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/UpdateWishlistItemCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/StoreWishlistCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/StoreWishlistItemCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Wishlist/DeleteWishlistCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/WishlistItem/DeleteWishlistItemCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/DeleteUserCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/UpdateAddressCommandHandler.cs": {"language": "C#", "code": 28, "comment": 11, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/LoginCommand.cs": {"language": "C#", "code": 8, "comment": 5, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/GoogleLoginCommandHandler.cs": {"language": "C#", "code": 50, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/UpdateUserCommandHandler.cs": {"language": "C#", "code": 34, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/UpdateUserCommand.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/StoreUserCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/User/StoreUserCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/UpdateAddressCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/DeleteRatingCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/DeleteRatingCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/UpdateReviewCommandHandler.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/StoreRatingCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommandHandler.cs": {"language": "C#", "code": 29, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/UpdateShippingMethodCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/UpdateReviewCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/StoreReviewCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/StoreReviewCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/StoreShippingMethodCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/DeleteReviewCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Review/DeleteReviewCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ShippingMethod/DeleteShippingMethodCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/StoreAddressCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/GoogleLoginCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/StoreRatingCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/ConfirmEmailCommandHandler.cs": {"language": "C#", "code": 24, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/StoreAddressCommand.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/UpdateRatingCommandHandler.cs": {"language": "C#", "code": 28, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Rating/UpdateRatingCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Auth/ConfirmEmailCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/DeleteAddressCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/StorePaymentCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/StorePaymentCommand.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/DeletePaymentCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/UpdatePaymentCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/UpdateProductCommandHandler.cs": {"language": "C#", "code": 36, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/DeletePaymentCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommandHandler.cs": {"language": "C#", "code": 26, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/UpdateProductCommand.cs": {"language": "C#", "code": 21, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/UpdateProductImageCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/StoreProductCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/StoreProductImageCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/StoreProductCommand.cs": {"language": "C#", "code": 17, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/StoreProductImageCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/DeleteProductCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/ProductImage/DeleteProductImageCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Address/DeleteAddressCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Product/DeleteProductCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Payment/UpdatePaymentCommandHandler.cs": {"language": "C#", "code": 29, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/UpdateOrderCommandHandler.cs": {"language": "C#", "code": 43, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/UpdateOrderCommand.cs": {"language": "C#", "code": 12, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/StoreOrderCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommandHandler.cs": {"language": "C#", "code": 22, "comment": 11, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/StoreOrderCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommandHandler.cs": {"language": "C#", "code": 28, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/DeleteOrderCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/UpdateOrderItemCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/UpdateOrderCouponCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Order/DeleteOrderCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/StoreOrderCouponCommand.cs": {"language": "C#", "code": 6, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/StoreOrderItemCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderItem/DeleteOrderItemCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/OrderCoupon/DeleteOrderCouponCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/UpdateNotificationCommandHandler.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/UpdateNotificationCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommandHandler.cs": {"language": "C#", "code": 28, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/UpdateMessageCommandHandler.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/UpdateMessageCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/UpdateFavoriteCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/StoreNotificationCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/StoreFavoriteCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/StoreMessageCommandHandler.cs": {"language": "C#", "code": 36, "comment": 8, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/StoreNotificationCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/StoreFavoriteCommand.cs": {"language": "C#", "code": 9, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/StoreMessageCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/DeleteNotificationCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/DeleteMessageCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Favorite/DeleteFavoriteCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Message/DeleteMessageCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Notification/DeleteNotificationCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommandHandler.cs": {"language": "C#", "code": 28, "comment": 11, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommandHandler.cs": {"language": "C#", "code": 18, "comment": 18, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/UpdateCompanyScheduleCommand.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/UpdateCompanyUserCommand.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/UpdateCouponCommandHandler.cs": {"language": "C#", "code": 29, "comment": 11, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/StoreCompanyScheduleCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/UpdateCouponCommand.cs": {"language": "C#", "code": 11, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/StoreCompanyUserCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/StoreCouponCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/StoreCouponCommand.cs": {"language": "C#", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanySchedule/DeleteCompanyScheduleCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommandHandler.cs": {"language": "C#", "code": 15, "comment": 5, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/DeleteCouponCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/CompanyUser/DeleteCompanyUserCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Coupon/DeleteCouponCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/UpdateCategoryCommandHandler.cs": {"language": "C#", "code": 33, "comment": 1, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/UpdateCategoryCommand.cs": {"language": "C#", "code": 13, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/UpdateChatCommandHandler.cs": {"language": "C#", "code": 22, "comment": 11, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/StoreCategoryCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/UpdateChatCommand.cs": {"language": "C#", "code": 8, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/UpdateCompanyCommandHandler.cs": {"language": "C#", "code": 35, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/StoreCategoryCommand.cs": {"language": "C#", "code": 12, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/UpdateCompanyCommand.cs": {"language": "C#", "code": 18, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/StoreChatCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/StoreCompanyCommandHandler.cs": {"language": "C#", "code": 20, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/DeleteCategoryCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/StoreChatCommand.cs": {"language": "C#", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/StoreCompanyCommand.cs": {"language": "C#", "code": 17, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Category/DeleteCategoryCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/DeleteCompanyCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/DeleteChatCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Chat/DeleteChatCommandHandler.cs": {"language": "C#", "code": 19, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Commands/Company/DeleteCompanyCommand.cs": {"language": "C#", "code": 3, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/WishlistMappingProfile.cs": {"language": "C#", "code": 18, "comment": 5, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/WishlistItemMappingProfile.cs": {"language": "C#", "code": 18, "comment": 6, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/UserMappingProfile.cs": {"language": "C#", "code": 52, "comment": 1, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/ReviewMappingProfile.cs": {"language": "C#", "code": 23, "comment": 5, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/RatingMappingProfile.cs": {"language": "C#", "code": 25, "comment": 7, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/ShippingMethodMappingProfile.cs": {"language": "C#", "code": 21, "comment": 1, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/ProductMappingProfile.cs": {"language": "C#", "code": 40, "comment": 12, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/ProductImageMappingProfile.cs": {"language": "C#", "code": 19, "comment": 4, "blank": 5}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Mappings/PaymentMappingProfile.cs": {"language": "C#", "code": 24, "comment": 6, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/Behaviors/ValidationBehavior.cs": {"language": "C#", "code": 25, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/Marketplace.Application/ApplicationServiceExtensions.cs": {"language": "C#", "code": 24, "comment": 3, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/README.md": {"language": "<PERSON><PERSON>", "code": 36, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/.env": {"language": "Properties", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/package.json": {"language": "JSON", "code": 29, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/vite.config.js": {"language": "JavaScript", "code": 20, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/package-lock.json": {"language": "JSON", "code": 914, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Profile.vue": {"language": "vue", "code": 139, "comment": 0, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/NotFound.vue": {"language": "vue", "code": 19, "comment": 0, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Register.vue": {"language": "vue", "code": 707, "comment": 2, "blank": 119}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Login.vue": {"language": "vue", "code": 647, "comment": 2, "blank": 113}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Dashboard.vue": {"language": "vue", "code": 71, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Home.vue": {"language": "vue", "code": 55, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/AdminDashboard.vue": {"language": "vue", "code": 150, "comment": 0, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/Cart.vue": {"language": "vue", "code": 706, "comment": 0, "blank": 74}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/Users.vue": {"language": "vue", "code": 412, "comment": 5, "blank": 45}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/SellerRequests.vue": {"language": "vue", "code": 469, "comment": 5, "blank": 49}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/Products.vue": {"language": "vue", "code": 434, "comment": 5, "blank": 53}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/Orders.vue": {"language": "vue", "code": 567, "comment": 5, "blank": 50}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/Dashboard.vue": {"language": "vue", "code": 839, "comment": 8, "blank": 101}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/views/admin/Categories.vue": {"language": "vue", "code": 421, "comment": 5, "blank": 62}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/utils/slugify.js": {"language": "JavaScript", "code": 18, "comment": 12, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/store/index.js": {"language": "JavaScript", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/store/modules/loading.js": {"language": "JavaScript", "code": 70, "comment": 1, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/TestComponent.vue": {"language": "vue", "code": 17, "comment": 0, "blank": 4}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/store/modules/auth.js": {"language": "JavaScript", "code": 124, "comment": 20, "blank": 29}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/store/modules/categories.js": {"language": "JavaScript", "code": 181, "comment": 7, "blank": 39}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/seller-request.service.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/user.service.js": {"language": "JavaScript", "code": 25, "comment": 7, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/request-manager.js": {"language": "JavaScript", "code": 47, "comment": 28, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/product.service.js": {"language": "JavaScript", "code": 32, "comment": 8, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/order.service.js": {"language": "JavaScript", "code": 25, "comment": 7, "blank": 9}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/dashboard.service.js": {"language": "JavaScript", "code": 85, "comment": 10, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/router/index.js": {"language": "JavaScript", "code": 227, "comment": 22, "blank": 22}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/category.service.js": {"language": "JavaScript", "code": 76, "comment": 12, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/auth.service.js": {"language": "JavaScript", "code": 103, "comment": 21, "blank": 32}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/api.service.js": {"language": "JavaScript", "code": 48, "comment": 10, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/services/api.js": {"language": "JavaScript", "code": 193, "comment": 43, "blank": 48}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/layouts/AdminLayout.vue": {"language": "vue", "code": 494, "comment": 3, "blank": 69}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/config/google-auth.js": {"language": "JavaScript", "code": 110, "comment": 23, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/main.js": {"language": "JavaScript", "code": 14, "comment": 1, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/assets/images/klondike-logo.svg": {"language": "XML", "code": 8, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/assets/css/style.css": {"language": "CSS", "code": 46, "comment": 1, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/assets/images/google-icon.svg": {"language": "XML", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/assets/css/admin.css": {"language": "CSS", "code": 184, "comment": 12, "blank": 38}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/assets/images/apple-icon.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/components/GlobalLoading.vue": {"language": "vue", "code": 72, "comment": 0, "blank": 10}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/components/ErrorBoundary.vue": {"language": "vue", "code": 173, "comment": 0, "blank": 33}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/App.vue": {"language": "vue", "code": 329, "comment": 1, "blank": 58}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/api.js": {"language": "JavaScript", "code": 2, "comment": 2, "blank": 3}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Orders.vue": {"language": "vue", "code": 119, "comment": 4, "blank": 18}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Users.vue": {"language": "vue", "code": 178, "comment": 5, "blank": 24}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/products/ProductList.vue": {"language": "vue", "code": 397, "comment": 4, "blank": 37}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/products/ProductForm.vue": {"language": "vue", "code": 661, "comment": 7, "blank": 70}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/products/ProductDetail.vue": {"language": "vue", "code": 453, "comment": 5, "blank": 52}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Settings.vue": {"language": "vue", "code": 331, "comment": 6, "blank": 42}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/SellerRequests.vue": {"language": "vue", "code": 136, "comment": 4, "blank": 19}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/users/UserList.vue": {"language": "vue", "code": 617, "comment": 7, "blank": 72}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/users/UserDetail.vue": {"language": "vue", "code": 803, "comment": 8, "blank": 97}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Reports.vue": {"language": "vue", "code": 332, "comment": 6, "blank": 34}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Products.vue": {"language": "vue", "code": 151, "comment": 5, "blank": 21}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/seller-requests/SellerRequestList.vue": {"language": "vue", "code": 420, "comment": 3, "blank": 60}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/seller-requests/SellerRequestDetail.vue": {"language": "vue", "code": 551, "comment": 5, "blank": 76}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Dashboard.vue": {"language": "vue", "code": 250, "comment": 6, "blank": 33}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/Categories.vue": {"language": "vue", "code": 119, "comment": 3, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/orders/OrderList.vue": {"language": "vue", "code": 528, "comment": 4, "blank": 44}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/orders/OrderDetail.vue": {"language": "vue", "code": 701, "comment": 6, "blank": 88}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/categories/CategoryList.vue": {"language": "vue", "code": 555, "comment": 10, "blank": 73}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/categories/CategoryForm.vue": {"language": "vue", "code": 454, "comment": 6, "blank": 64}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/views/categories/CategoryDetail.vue": {"language": "vue", "code": 535, "comment": 6, "blank": 65}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/Sidebar.vue": {"language": "vue", "code": 257, "comment": 1, "blank": 41}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/users/UserTable.vue": {"language": "vue", "code": 143, "comment": 0, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/users/UserFormModal.vue": {"language": "vue", "code": 223, "comment": 4, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/users/UserFilters.vue": {"language": "vue", "code": 109, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/seller-requests/SellerRequestTable.vue": {"language": "vue", "code": 102, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/seller-requests/SellerRequestFilters.vue": {"language": "vue", "code": 113, "comment": 0, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/seller-requests/SellerRequestDetailsModal.vue": {"language": "vue", "code": 194, "comment": 5, "blank": 23}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/products/ProductTable.vue": {"language": "vue", "code": 107, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/products/ProductFormModal.vue": {"language": "vue", "code": 263, "comment": 4, "blank": 23}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/products/ProductFilters.vue": {"language": "vue", "code": 147, "comment": 0, "blank": 18}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/Header.vue": {"language": "vue", "code": 306, "comment": 3, "blank": 54}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/orders/OrderTable.vue": {"language": "vue", "code": 110, "comment": 0, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/orders/OrderFilters.vue": {"language": "vue", "code": 140, "comment": 0, "blank": 15}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/orders/OrderDetailsModal.vue": {"language": "vue", "code": 244, "comment": 4, "blank": 22}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/dashboard/SalesChart.vue": {"language": "vue", "code": 205, "comment": 0, "blank": 23}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/dashboard/RecentOrders.vue": {"language": "vue", "code": 160, "comment": 0, "blank": 23}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/dashboard/PendingSellerRequests.vue": {"language": "vue", "code": 191, "comment": 0, "blank": 30}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/dashboard/OrdersByStatusChart.vue": {"language": "vue", "code": 200, "comment": 0, "blank": 26}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/categories/CategoryTable.vue": {"language": "vue", "code": 86, "comment": 0, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/categories/CategoryTreeNode.vue": {"language": "vue", "code": 141, "comment": 0, "blank": 19}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/categories/CategorySkeleton.vue": {"language": "vue", "code": 162, "comment": 2, "blank": 20}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/categories/CategoryFormModal.vue": {"language": "vue", "code": 209, "comment": 4, "blank": 24}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/StatusBadge.vue": {"language": "vue", "code": 171, "comment": 0, "blank": 25}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/LoadingIndicator.vue": {"language": "vue", "code": 58, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/FilterPanel.vue": {"language": "vue", "code": 98, "comment": 0, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/Pagination.vue": {"language": "vue", "code": 134, "comment": 5, "blank": 22}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/EmptyState.vue": {"language": "vue", "code": 62, "comment": 0, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/DataTable.vue": {"language": "vue", "code": 403, "comment": 5, "blank": 56}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/ConfirmDialog.vue": {"language": "vue", "code": 102, "comment": 0, "blank": 16}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/components/common/AdminStatCard.vue": {"language": "vue", "code": 89, "comment": 0, "blank": 13}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/README.md": {"language": "<PERSON><PERSON>", "code": 74, "comment": 0, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/layouts/AdminLayout.vue": {"language": "vue", "code": 112, "comment": 3, "blank": 21}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/users.js": {"language": "JavaScript", "code": 366, "comment": 15, "blank": 17}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/settings.js": {"language": "JavaScript", "code": 259, "comment": 10, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/sellerRequests.js": {"language": "JavaScript", "code": 221, "comment": 7, "blank": 8}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/seller-requests.js": {"language": "JavaScript", "code": 189, "comment": 5, "blank": 6}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/reports.js": {"language": "JavaScript", "code": 210, "comment": 6, "blank": 7}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/products.js": {"language": "JavaScript", "code": 240, "comment": 10, "blank": 12}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/orders.js": {"language": "JavaScript", "code": 334, "comment": 12, "blank": 14}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/dashboard.js": {"language": "JavaScript", "code": 53, "comment": 5, "blank": 11}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/admin/services/categories.js": {"language": "JavaScript", "code": 228, "comment": 36, "blank": 51}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/index.html": {"language": "HTML", "code": 152, "comment": 2, "blank": 26}, "file:///d%3A/Diplomkla_VS_Code/Nash%20Git/Marketplace/frontend/src/components/admin/common/StatusBadge.vue": {"language": "vue", "code": 81, "comment": 0, "blank": 9}}