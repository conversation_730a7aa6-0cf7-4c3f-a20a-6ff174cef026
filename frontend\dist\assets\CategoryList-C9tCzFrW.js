import{_ as $,c as n,o as l,k as w,a as s,F as b,p as C,H as E,g,z as F,i as z,j as L,s as S,w as h,b as k,r as U,A,B as j,C as H,n as K,t as _,E as R,K as q,I as M}from"./index-BtyG65bR.js";import{C as G}from"./ConfirmDialog-Cm3a8HQn.js";const J={class:"category-skeleton"},O={key:0,class:"table-container"},Q={class:"table is-fullwidth"},W={key:1,class:"tree-container"},X={class:"tree-root"},Y={key:0,class:"tree-children"},Z={__name:"CategorySkeleton",props:{count:{type:Number,default:5},viewMode:{type:String,default:"list"}},setup(f){return(p,e)=>(l(),n("div",J,[f.viewMode==="list"?(l(),n("div",O,[s("table",Q,[e[1]||(e[1]=s("thead",null,[s("tr",null,[s("th",null,"Image"),s("th",null,"Name"),s("th",null,"Parent"),s("th",null,"Products"),s("th",null,"Slug"),s("th",null,"Actions")])],-1)),s("tbody",null,[(l(!0),n(b,null,C(f.count,i=>(l(),n("tr",{key:i},e[0]||(e[0]=[s("td",{class:"image-cell"},[s("div",{class:"skeleton-image"})],-1),s("td",null,[s("div",{class:"skeleton-text"})],-1),s("td",null,[s("div",{class:"skeleton-text skeleton-text-sm"})],-1),s("td",null,[s("div",{class:"skeleton-text skeleton-text-xs"})],-1),s("td",null,[s("div",{class:"skeleton-text skeleton-text-md"})],-1),s("td",null,[s("div",{class:"skeleton-actions"},[s("div",{class:"skeleton-button"}),s("div",{class:"skeleton-button"}),s("div",{class:"skeleton-button"})])],-1)])))),128))])])])):f.viewMode==="tree"?(l(),n("div",W,[s("ul",X,[(l(!0),n(b,null,C(Math.ceil(f.count/2),i=>(l(),n("li",{key:i,class:"tree-item"},[e[3]||(e[3]=E('<div class="tree-node" data-v-d6d93342><div class="node-content" data-v-d6d93342><div class="skeleton-icon" data-v-d6d93342></div><div class="skeleton-image skeleton-image-sm" data-v-d6d93342></div><div class="skeleton-text skeleton-text-md" data-v-d6d93342></div></div><div class="skeleton-actions" data-v-d6d93342><div class="skeleton-button" data-v-d6d93342></div><div class="skeleton-button" data-v-d6d93342></div><div class="skeleton-button" data-v-d6d93342></div></div></div>',1)),i%2===0?(l(),n("ul",Y,[(l(),n(b,null,C(2,c=>s("li",{key:c,class:"tree-item"},e[2]||(e[2]=[E('<div class="tree-node" data-v-d6d93342><div class="node-content" data-v-d6d93342><div class="skeleton-icon" data-v-d6d93342></div><div class="skeleton-image skeleton-image-sm" data-v-d6d93342></div><div class="skeleton-text skeleton-text-md" data-v-d6d93342></div></div><div class="skeleton-actions" data-v-d6d93342><div class="skeleton-button" data-v-d6d93342></div><div class="skeleton-button" data-v-d6d93342></div><div class="skeleton-button" data-v-d6d93342></div></div></div>',1)]))),64))])):w("",!0)]))),128))])])):w("",!0)]))}},ss=$(Z,[["__scopeId","data-v-d6d93342"]]),ts={class:"category-list"},es={class:"level"},ls={class:"level-right"},as={class:"level-item"},os={class:"card mb-4"},ns={class:"card-content"},is={class:"columns"},ds={class:"column is-4"},rs={class:"field"},cs={class:"control has-icons-left"},us={class:"column is-4"},vs={class:"field"},ms={class:"control"},gs={class:"select is-fullwidth"},fs={class:"column is-4"},ps={class:"field"},hs={class:"field is-grouped"},ks={class:"control"},_s={class:"card"},ys={class:"card-content"},bs={key:1,class:"has-text-centered py-6"},Cs={class:"mt-2 has-text-danger has-text-weight-bold"},xs={key:2,class:"has-text-centered py-6"},ws={class:"mt-4"},Es={key:3},Ss={class:"table-container"},As={class:"table is-fullwidth is-hoverable"},Ms={class:"image-cell"},$s={class:"image is-48x48"},Ds=["src","alt"],Is={class:"has-text-grey"},Ns={class:"tag is-info"},Bs={class:"buttons are-small"},Ts=["onClick"],Vs={__name:"CategoryList",setup(f){const p=q("errorBoundary",{setError:()=>{},clearError:()=>{}}),e=g([]),i=g(!1),c=g(null),y=g(!1),d=g(null),u=g(null),r=F({search:"",status:""}),D=()=>{u.value&&clearTimeout(u.value),u.value=setTimeout(()=>{v(),u.value=null},300)},v=async()=>{var a;try{p.clearError(),c.value=null,i.value=!0;const t=await M.getCategories(r);console.log("Categories API response:",t),t&&t.categories?e.value=t.categories:t&&t.data?e.value=t.data:Array.isArray(t)?e.value=t:e.value=[],console.log("Categories loaded:",e.value.length)}catch(t){console.error("Error fetching categories:",t),c.value=t.message||"Failed to load categories. Please try again.",(!t.originalError||((a=t.originalError.response)==null?void 0:a.status)!==404)&&p.setError(t),e.value=[]}finally{i.value=!1}},I=()=>{r.search="",r.status="",v()},N=a=>{const t=e.value.find(m=>m.id===a);return(t==null?void 0:t.productCount)||Math.floor(Math.random()*50)},B=a=>{a.target.src="https://via.placeholder.com/48?text=No+Image"},T=a=>{d.value=a,y.value=!0},V=async()=>{if(d.value)try{p.clearError(),await M.deleteCategory(d.value.id),e.value=e.value.filter(a=>a.id!==d.value.id),y.value=!1,d.value=null}catch(a){console.error("Error deleting category:",a),p.setError(a)}},P=()=>{y.value=!1,d.value=null};return z(()=>{v()}),L(()=>{u.value&&clearTimeout(u.value)}),(a,t)=>{const m=U("router-link");return l(),S(R,null,{default:h(()=>{var x;return[s("div",ts,[s("div",es,[t[3]||(t[3]=s("div",{class:"level-left"},[s("div",{class:"level-item"},[s("h1",{class:"title"},"Categories")])],-1)),s("div",ls,[s("div",as,[k(m,{to:"/admin/categories/create",class:"button is-primary"},{default:h(()=>t[2]||(t[2]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Category",-1)])),_:1})])])]),s("div",os,[s("div",ns,[s("div",is,[s("div",ds,[s("div",rs,[t[5]||(t[5]=s("label",{class:"label"},"Search",-1)),s("div",cs,[A(s("input",{class:"input",type:"text",placeholder:"Search categories...","onUpdate:modelValue":t[0]||(t[0]=o=>r.search=o),onInput:D},null,544),[[j,r.search]]),t[4]||(t[4]=s("span",{class:"icon is-small is-left"},[s("i",{class:"fas fa-search"})],-1))])])]),s("div",us,[s("div",vs,[t[7]||(t[7]=s("label",{class:"label"},"Status",-1)),s("div",ms,[s("div",gs,[A(s("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>r.status=o),onChange:v},t[6]||(t[6]=[s("option",{value:""},"All Statuses",-1),s("option",{value:"active"},"Active",-1),s("option",{value:"inactive"},"Inactive",-1)]),544),[[H,r.status]])])])])]),s("div",fs,[s("div",ps,[t[8]||(t[8]=s("label",{class:"label"}," ",-1)),s("div",hs,[s("div",{class:"control"},[s("button",{class:"button is-light",onClick:I}," Reset ")]),s("div",ks,[s("button",{class:K(["button is-primary",{"is-loading":i.value}]),onClick:v}," Apply Filters ",2)])])])])])])]),s("div",_s,[s("div",ys,[i.value&&!e.value.length?(l(),S(ss,{key:0,"view-mode":a.viewMode,count:5},null,8,["view-mode"])):c.value?(l(),n("div",bs,[t[10]||(t[10]=s("span",{class:"icon is-large has-text-danger"},[s("i",{class:"fas fa-exclamation-triangle fa-2x"})],-1)),s("p",Cs,_(c.value),1),s("div",{class:"mt-4"},[s("button",{class:"button is-primary",onClick:v},t[9]||(t[9]=[s("span",{class:"icon"},[s("i",{class:"fas fa-sync-alt"})],-1),s("span",null,"Try Again",-1)]))])])):e.value.length?(l(),n("div",Es,[s("div",Ss,[s("table",As,[t[19]||(t[19]=s("thead",null,[s("tr",null,[s("th",null,"Image"),s("th",null,"Name"),s("th",null,"Products"),s("th",null,"Slug"),s("th",null,"Actions")])],-1)),s("tbody",null,[(l(!0),n(b,null,C(e.value,o=>(l(),n("tr",{key:o.id},[s("td",Ms,[s("figure",$s,[s("img",{src:o.image||"https://via.placeholder.com/48",alt:o.name,onError:B},null,40,Ds)])]),s("td",null,[s("strong",null,_(o.name),1),t[15]||(t[15]=s("br",null,null,-1)),s("small",Is,_(o.description||"No description"),1)]),s("td",null,[s("span",Ns,_(N(o.id)),1)]),s("td",null,[s("code",null,_(o.slug),1)]),s("td",null,[s("div",Bs,[k(m,{to:`/admin/categories/${o.id}`,class:"button is-info",title:"View"},{default:h(()=>t[16]||(t[16]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-eye"})],-1)])),_:2},1032,["to"]),k(m,{to:`/admin/categories/${o.id}/edit`,class:"button is-primary",title:"Edit"},{default:h(()=>t[17]||(t[17]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-edit"})],-1)])),_:2},1032,["to"]),s("button",{class:"button is-danger",onClick:Ps=>T(o),title:"Delete"},t[18]||(t[18]=[s("span",{class:"icon is-small"},[s("i",{class:"fas fa-trash"})],-1)]),8,Ts)])])]))),128))])])])])):(l(),n("div",xs,[t[12]||(t[12]=s("span",{class:"icon is-large"},[s("i",{class:"fas fa-folder-open fa-2x"})],-1)),t[13]||(t[13]=s("p",{class:"mt-2"},"No categories found",-1)),t[14]||(t[14]=s("p",{class:"mt-2"},"Create your first category to organize your products",-1)),s("div",ws,[k(m,{to:"/admin/categories/create",class:"button is-primary"},{default:h(()=>t[11]||(t[11]=[s("span",{class:"icon"},[s("i",{class:"fas fa-plus"})],-1),s("span",null,"Add Category",-1)])),_:1})])]))])])]),k(G,{"is-open":y.value,title:"Delete Category",message:`Are you sure you want to delete '${(x=d.value)==null?void 0:x.name}'? This may affect products in this category.`,"confirm-text":"Delete","cancel-text":"Cancel",onConfirm:V,onCancel:P},null,8,["is-open","message"])]}),_:1})}}},Ls=$(Vs,[["__scopeId","data-v-a4ffd6e3"]]);export{Ls as default};
