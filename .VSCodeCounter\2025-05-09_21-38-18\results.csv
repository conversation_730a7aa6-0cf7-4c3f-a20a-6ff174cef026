"filename", "language", "Batch", "Markdown", "JSON", "C#", "XML", "ASP.NET Razor", "CSS", "Properties", "JavaScript", "vue", "HTML", "comment", "blank", "total"
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\ApplicationServiceExtensions.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 3, 8, 35
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Behaviors\ValidationBehavior.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\DeleteAddressCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\DeleteAddressCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\StoreAddressCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\StoreAddressCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\UpdateAddressCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 3, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Address\UpdateAddressCommandHandler.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 11, 8, 47
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\ConfirmEmailCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\ConfirmEmailCommandHandler.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 6, 30
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\GoogleLoginCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\GoogleLoginCommandHandler.cs", "C#", 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 7, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\LoginCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 5, 2, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\LoginCommandHandler.cs", "C#", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 6, 12, 71
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\RegisterCommand.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\RegisterCommandHandler.cs", "C#", 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 3, 11, 63
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\RequestPasswordResetCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\RequestPasswordResetCommandHandler.cs", "C#", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 6, 43
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\ResetPasswordCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\ResetPasswordCommandHandler.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 1, 5, 30
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\UpdatePasswordCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\UpdatePasswordCommandHandler.cs", "C#", 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 6, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\UpdateProfileCommand.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Auth\UpdateProfileCommandHandler.cs", "C#", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 1, 8, 55
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\DeleteCategoryCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\DeleteCategoryCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\StoreCategoryCommand.cs", "C#", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 4, 16
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\StoreCategoryCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\UpdateCategoryCommand.cs", "C#", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 4, 17
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Category\UpdateCategoryCommandHandler.cs", "C#", 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 1, 8, 42
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\DeleteChatCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\DeleteChatCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\StoreChatCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\StoreChatCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\UpdateChatCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Chat\UpdateChatCommandHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 11, 8, 41
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\DeleteCompanyScheduleCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\DeleteCompanyScheduleCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\StoreCompanyScheduleCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\StoreCompanyScheduleCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\UpdateCompanyScheduleCommand.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 3, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanySchedule\UpdateCompanyScheduleCommandHandler.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 11, 9, 48
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\DeleteCompanyUserCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\DeleteCompanyUserCommandHandler.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 5, 8, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\StoreCompanyUserCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\StoreCompanyUserCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\UpdateCompanyUserCommand.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\CompanyUser\UpdateCompanyUserCommandHandler.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 18, 10, 46
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\DeleteCompanyCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\DeleteCompanyCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\StoreCompanyCommand.cs", "C#", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 4, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\StoreCompanyCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\UpdateCompanyCommand.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 4, 22
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Company\UpdateCompanyCommandHandler.cs", "C#", 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 8, 43
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\DeleteCouponCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\DeleteCouponCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\StoreCouponCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\StoreCouponCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\UpdateCouponCommand.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 3, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Coupon\UpdateCouponCommandHandler.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 11, 9, 49
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\DeleteFavoriteCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\DeleteFavoriteCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\StoreFavoriteCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 3, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\StoreFavoriteCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\UpdateFavoriteCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Favorite\UpdateFavoriteCommandHandler.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 9, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\DeleteMessageCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\DeleteMessageCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\StoreMessageCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\StoreMessageCommandHandler.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 8, 10, 54
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\UpdateMessageCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Message\UpdateMessageCommandHandler.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 9, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\DeleteNotificationCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\DeleteNotificationCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\StoreNotificationCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\StoreNotificationCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\UpdateNotificationCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Notification\UpdateNotificationCommandHandler.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 9, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\DeleteOrderCouponCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\DeleteOrderCouponCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\StoreOrderCouponCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\StoreOrderCouponCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\UpdateOrderCouponCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderCoupon\UpdateOrderCouponCommandHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 11, 8, 41
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\DeleteOrderItemCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\DeleteOrderItemCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\StoreOrderItemCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 4, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\StoreOrderItemCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\UpdateOrderItemCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\OrderItem\UpdateOrderItemCommandHandler.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 9, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\DeleteOrderCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\DeleteOrderCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\StoreOrderCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\StoreOrderCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\UpdateOrderCommand.cs", "C#", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Order\UpdateOrderCommandHandler.cs", "C#", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 10, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\DeletePaymentCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\DeletePaymentCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\StorePaymentCommand.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 4, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\StorePaymentCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\UpdatePaymentCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Payment\UpdatePaymentCommandHandler.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 9, 38
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\DeleteProductImageCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\DeleteProductImageCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\StoreProductImageCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\StoreProductImageCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\UpdateProductImageCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ProductImage\UpdateProductImageCommandHandler.cs", "C#", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 9, 35
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\DeleteProductCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\DeleteProductCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\StoreProductCommand.cs", "C#", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 4, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\StoreProductCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\UpdateProductCommand.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 2, 23
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Product\UpdateProductCommandHandler.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 9, 45
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\DeleteRatingCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\DeleteRatingCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\StoreRatingCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\StoreRatingCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\UpdateRatingCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Rating\UpdateRatingCommandHandler.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 9, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\DeleteReviewCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\DeleteReviewCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\StoreReviewCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 4, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\StoreReviewCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\UpdateReviewCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Review\UpdateReviewCommandHandler.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 9, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\DeleteShippingMethodCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\DeleteShippingMethodCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\StoreShippingMethodCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 4, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\StoreShippingMethodCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\UpdateShippingMethodCommand.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\ShippingMethod\UpdateShippingMethodCommandHandler.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 9, 38
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\DeleteUserCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\DeleteUserCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\StoreUserCommand.cs", "C#", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 4, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\StoreUserCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\UpdateUserCommand.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 2, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\User\UpdateUserCommandHandler.cs", "C#", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 9, 43
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\DeleteWishlistItemCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\DeleteWishlistItemCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\StoreWishlistItemCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\StoreWishlistItemCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\UpdateWishlistItemCommand.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\WishlistItem\UpdateWishlistItemCommandHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 11, 8, 41
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\DeleteWishlistCommand.cs", "C#", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\DeleteWishlistCommandHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\StoreWishlistCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\StoreWishlistCommandHandler.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\UpdateWishlistCommand.cs", "C#", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Commands\Wishlist\UpdateWishlistCommandHandler.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 9, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Extensions\ValidationExtensions.cs", "C#", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 1, 5, 52
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\AddressMappingProfile.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 8, 6, 33
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\CategoryMappingProfile.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 1, 6, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\ChatMappingProfile.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 6, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\CompanyMappingProfile.cs", "C#", 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 19, 4, 62
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\CompanyScheduleMappingProfile.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 6, 5, 35
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\CompanyUserMappingProfile.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 3, 6, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\CouponMappingProfile.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 8, 4, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\FavoriteMappingProfile.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 8, 4, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\MessageMappingProfile.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 5, 30
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\NotificationMappingProfile.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 4, 29
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\OrderCouponMappingProfile.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 4, 4, 24
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\OrderItemMappingProfile.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 6, 6, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\OrderMappingProfile.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 6, 5, 35
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\PaymentMappingProfile.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 6, 6, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\ProductImageMappingProfile.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 5, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\ProductMappingProfile.cs", "C#", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 12, 5, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\RatingMappingProfile.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 7, 5, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\ReviewMappingProfile.cs", "C#", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 5, 5, 33
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\ShippingMethodMappingProfile.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 1, 5, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\UserMappingProfile.cs", "C#", 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 1, 6, 59
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\WishlistItemMappingProfile.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 6, 5, 29
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Mappings\WishlistMappingProfile.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 5, 5, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Marketplace.Application.csproj", "XML", 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 5, 35
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Address\GetAddressQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Address\GetAddressQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Address\GetAllAddressQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Address\GetAllAddressQueryHandler.cs", "C#", 0, 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 5, 12, 93
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetAllCategoryQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetAllCategoryQueryHandler.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 5, 12, 91
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetCategoryBySlugQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetCategoryBySlugQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetCategoryQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Category\GetCategoryQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Chat\GetAllChatQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Chat\GetAllChatQueryHandler.cs", "C#", 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 5, 12, 90
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Chat\GetChatQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Chat\GetChatQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanySchedule\GetAllCompanyScheduleQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanySchedule\GetAllCompanyScheduleQueryHandler.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 12, 86
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanySchedule\GetCompanyScheduleQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanySchedule\GetCompanyScheduleQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanyUser\GetAllCompanyUserQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanyUser\GetAllCompanyUserQueryHandler.cs", "C#", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 47, 11, 84
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanyUser\GetCompanyUserQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\CompanyUser\GetCompanyUserQueryHandler.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 6, 29
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetAllCompanyQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetAllCompanyQueryHandler.cs", "C#", 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 5, 12, 98
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetCategoryBySlugQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetCategoryBySlugQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetCompanyQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Company\GetCompanyQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Coupon\GetAllCouponQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Coupon\GetAllCouponQueryHandler.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 12, 86
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Coupon\GetCouponQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Coupon\GetCouponQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Favorite\GetAllFavoriteQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Favorite\GetAllFavoriteQueryHandler.cs", "C#", 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 12, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Favorite\GetFavoriteQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Favorite\GetFavoriteQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetAllMessageQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetAllMessageQueryHandler.cs", "C#", 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 12, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetMessageQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetMessageQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetMessagesByChatQuery.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Message\GetMessagesByChatQueryHandler.cs", "C#", 0, 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 7, 13, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Notification\GetAllNotificationQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Notification\GetAllNotificationQueryHandler.cs", "C#", 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 12, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Notification\GetNotificationQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Notification\GetNotificationQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderCoupon\GetAllOrderCouponQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderCoupon\GetAllOrderCouponQueryHandler.cs", "C#", 0, 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 12, 88
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderCoupon\GetOrderCouponQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderCoupon\GetOrderCouponQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderItem\GetAllOrderItemQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderItem\GetAllOrderItemQueryHandler.cs", "C#", 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 1, 12, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderItem\GetOrderItemQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\OrderItem\GetOrderItemQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Order\GetAllOrderQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Order\GetAllOrderQueryHandler.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 12, 86
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Order\GetOrderQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Order\GetOrderQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Payment\GetAllPaymentQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Payment\GetAllPaymentQueryHandler.cs", "C#", 0, 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 12, 89
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Payment\GetPaymentQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Payment\GetPaymentQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ProductImage\GetAllProductImageQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ProductImage\GetAllProductImageQueryHandler.cs", "C#", 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 1, 12, 100
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ProductImage\GetProductImageQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ProductImage\GetProductImageQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetAllProductQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetAllProductQueryHandler.cs", "C#", 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 1, 12, 100
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductBySlugQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductBySlugQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductsByCategoryQuery.cs", "C#", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Product\GetProductsByCategoryQueryHandler.cs", "C#", 0, 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 8, 14, 110
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Rating\GetAllRatingQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Rating\GetAllRatingQueryHandler.cs", "C#", 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 1, 12, 104
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Rating\GetRatingQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Rating\GetRatingQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Review\GetAllReviewQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Review\GetAllReviewQueryHandler.cs", "C#", 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 1, 11, 103
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Review\GetReviewQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Review\GetReviewQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ShippingMethod\GetAllShippingMethodQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ShippingMethod\GetAllShippingMethodQueryHandler.cs", "C#", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 12, 87
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ShippingMethod\GetShippingMethodQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\ShippingMethod\GetShippingMethodQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetAllUserQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetAllUserQueryHandler.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 11, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserByEmailQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserByEmailQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserByUsernameQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserByUsernameQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\User\GetUserQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\WishlistItem\GetAllWishlistItemQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\WishlistItem\GetAllWishlistItemQueryHandler.cs", "C#", 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 1, 11, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\WishlistItem\GetWishlistItemQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\WishlistItem\GetWishlistItemQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Wishlist\GetAllWishlistQuery.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Wishlist\GetAllWishlistQueryHandler.cs", "C#", 0, 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 12, 88
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Wishlist\GetWishlistQuery.cs", "C#", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Queries\Wishlist\GetWishlistQueryHandler.cs", "C#", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\AddressResponse.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 3, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\AuthResponse.cs", "C#", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\CategoryResponse.cs", "C#", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 5, 31
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\ChatResponse.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\CompanyResponse.cs", "C#", 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 4, 55
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\CompanyScheduleResponse.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 3, 24
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\CompanyUserResponse.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\CouponResponse.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 3, 24
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\FavoriteResponse.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\MessageResponse.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\NotificationResponse.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 2, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\OrderCouponResponse.cs", "C#", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\OrderItemResponse.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\OrderResponse.cs", "C#", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\PaginatedResponse.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\PaymentResponse.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 1, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\ProductImageResponse.cs", "C#", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\ProductResponse.cs", "C#", 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 1, 52
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\RatingResponse.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\ReviewResponse.cs", "C#", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\ShippingMethodResponse.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 1, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\UserResponse.cs", "C#", 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 2, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\WishlistItemResponse.cs", "C#", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Responses\WishlistResponse.cs", "C#", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Services\Auth\EmailService.cs", "C#", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 9, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Services\Auth\JwtTokenService.cs", "C#", 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 3, 12, 70
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Services\Auth\TokenService.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 3, 18
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\AddressValidator.cs", "C#", 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 1, 5, 48
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\CategoryValidator.cs", "C#", 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 16, 81
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\ChatValidator.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\CompanyScheduleValidator.cs", "C#", 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 11, 49
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\CompanyUserValidator.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 6, 31
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\CompanyValidator.cs", "C#", 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\CouponValidator.cs", "C#", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 6, 12, 58
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\FavoriteValidator.cs", "C#", 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 12, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\MessageValidator.cs", "C#", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 10, 44
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\NotificationValidator.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 8, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\OrderCouponValidator.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\OrderItemValidator.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 5, 12, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\OrderValidator.cs", "C#", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 14, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\PaymentValidator.cs", "C#", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 12, 49
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\ProductImageValidator.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 6, 33
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\ProductValidator.cs", "C#", 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 5, 28, 114
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\RatingValidator.cs", "C#", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 13, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\ReviewValidator.cs", "C#", 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 10, 42
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\ShippingMethodValidator.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 5, 9, 42
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\UserValidator.cs", "C#", 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 13, 65
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\WishlistItemValidator.cs", "C#", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 7, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Application\Validators\WishlistValidator.cs", "C#", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 6, 29
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Address.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 3, 39
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Category.cs", "C#", 0, 0, 0, 124, 0, 0, 0, 0, 0, 0, 0, 2, 18, 144
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Chat.cs", "C#", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 17, 6, 71
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Company.cs", "C#", 0, 0, 0, 187, 0, 0, 0, 0, 0, 0, 0, 0, 18, 205
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\CompanySchedule.cs", "C#", 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 13, 105
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\CompanyUser.cs", "C#", 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 6, 45
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Coupon.cs", "C#", 0, 0, 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 12, 114
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Favorite.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 7, 61
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\IEntity.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Message.cs", "C#", 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 0, 9, 67
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Notification.cs", "C#", 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 10, 75
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Order.cs", "C#", 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 16, 121
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\OrderCoupon.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 18, 9, 54
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\OrderItem.cs", "C#", 0, 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 11, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Payment.cs", "C#", 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 13, 96
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Product.cs", "C#", 0, 0, 0, 186, 0, 0, 0, 0, 0, 0, 0, 1, 20, 207
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\ProductImage.cs", "C#", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 10, 51
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Rating.cs", "C#", 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 13, 117
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Review.cs", "C#", 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 8, 71
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\ShippingMethod.cs", "C#", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 14, 89
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\User.cs", "C#", 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 9, 113
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\Wishlist.cs", "C#", 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 6, 56
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Entities\WishlistItem.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 19, 6, 52
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Exceptions\DomainException.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 1, 5, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Marketplace.Domain.csproj", "XML", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 4, 26
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IAddressRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\ICategoryRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 1, 4, 15
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IChatRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\ICompanyRepository.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\ICompanyScheduleRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\ICompanyUserRepository.cs", "C#", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 4, 3, 14
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\ICouponRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IFavoriteRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IMessageRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\INotificationRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IOrderCouponRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IOrderItemRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IOrderRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IPaymentRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IProductImageRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IProductRepository.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IRatingRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IRepository.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 5, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IReviewRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IShippingMethodRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IUserRepository.cs", "C#", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IWishlistItemRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\Repositories\IWishlistRepository.cs", "C#", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 4, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\AddressVO.cs", "C#", 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 1, 6, 46
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Email.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 7, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Meta.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 6, 25
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Money.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 1, 6, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Password.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 5, 33
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Phone.cs", "C#", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Slug.cs", "C#", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 6, 25
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Domain\ValueObjects\Url.cs", "C#", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 5, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\DatabaseSeeder\DatabaseSeeder.cs", "C#", 0, 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 7, 14, 85
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\InfrastructureServiceExtensions.cs", "C#", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 8, 9, 63
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Marketplace.Infrastructure.csproj", "XML", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 6, 33
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250412102541_InitialCreate.Designer.cs", "C#", 0, 0, 0, 1236, 0, 0, 0, 0, 0, 0, 0, 2, 444, 1682
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250412102541_InitialCreate.cs", "C#", 0, 0, 0, 912, 0, 0, 0, 0, 0, 0, 0, 3, 98, 1013
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250420180614_UserUpdate.Designer.cs", "C#", 0, 0, 0, 1244, 0, 0, 0, 0, 0, 0, 0, 2, 448, 1694
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250420180614_UserUpdate.cs", "C#", 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 3, 10, 60
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250506200445_RemovePhoneColumn.Designer.cs", "C#", 0, 0, 0, 1244, 0, 0, 0, 0, 0, 0, 0, 2, 448, 1694
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\20250506200445_RemovePhoneColumn.cs", "C#", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 3, 6, 23
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Migrations\MarketplaceDbContextModelSnapshot.cs", "C#", 0, 0, 0, 1242, 0, 0, 0, 0, 0, 0, 0, 1, 448, 1691
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\AddressConfiguration.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 6, 42
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\CategoryConfiguration.cs", "C#", 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 12, 72
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\ChatConfiguration.cs", "C#", 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 7, 9, 47
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\CompanyConfiguration.cs", "C#", 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 1, 19, 98
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\CompanyScheduleConfiguration.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 7, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\CompanyUserConfiguration.cs", "C#", 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 4, 7, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\CouponConfiguration.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 11, 47
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\FavoriteConfiguration.cs", "C#", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 2, 11, 59
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\MessageConfiguration.cs", "C#", 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 8, 9, 50
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\NotificationConfiguration.cs", "C#", 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 2, 7, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\OrderConfiguration.cs", "C#", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 2, 15, 65
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\OrderCouponConfiguration.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 2, 7, 38
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\OrderItemConfiguration.cs", "C#", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 2, 12, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\PaymentConfiguration.cs", "C#", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 2, 11, 54
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\ProductConfiguration.cs", "C#", 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 2, 22, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\ProductImageConfiguration.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 5, 32
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\RatingConfiguration.cs", "C#", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 12, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\ReviewConfiguration.cs", "C#", 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 12, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\ShippingMethodConfiguration.cs", "C#", 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 6, 38
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\UserConfiguration.cs", "C#", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 12, 48
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\WishlistConfiguration.cs", "C#", 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 7, 37
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Configurations\WishlistItemConfiguration.cs", "C#", 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 5, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\AddressRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\CategoryRepository.cs", "C#", 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 7, 16, 81
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\ChatRepository.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 3, 18
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\CompanyRepository.cs", "C#", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 6, 30
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\CompanyScheduleRepository.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 2, 17
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\CompanyUserRepository.cs", "C#", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 4, 22
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\CouponRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\FavoriteRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\MessageRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\NotificationRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\OrderCouponRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\OrderItemRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\OrderRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\PaymentRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\ProductImageRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\ProductRepository.cs", "C#", 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 6, 39
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\RatingRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\Repository.cs", "C#", 0, 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 14, 90
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\ReviewRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\ShippingMethodRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\UserRepository.cs", "C#", 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 4, 8, 61
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\WishlistItemRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\Implementation\WishlistRepository.cs", "C#", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Infrastructure\Persistence\MarketplaceDbContext.cs", "C#", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 3, 40
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\AddressController.cs", "C#", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 14, 67
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\AdminCategoryController.cs", "C#", 0, 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 10, 33, 201
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\AuthController.cs", "C#", 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 14, 21, 132
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\BasicApiController.cs", "C#", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 3, 18
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\CategoryController.cs", "C#", 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 17, 78
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\ChatController.cs", "C#", 0, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 17, 86
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\CompanyController.cs", "C#", 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 17, 78
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\CompanyScheduleController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\CompanyUserController.cs", "C#", 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 15, 70
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\CouponController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\FavoriteController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\MessageController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\NotificationController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\OrderController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 14, 68
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\OrderCouponController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\OrderItemController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\PaymentController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\ProductController.cs", "C#", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 19, 94
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\ProductImageController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\RatingController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\ReviewController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\ShippingMethodController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\UserController.cs", "C#", 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 19, 87
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\WishlistController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Controllers\WishlistItemController.cs", "C#", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 15, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Marketplace.Presentation.csproj", "XML", 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 5, 29
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Error.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 4, 27
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Error.cshtml.cs", "C#", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 8, 28
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\MockAuthenticationHandler.cs", "C#", 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 2, 7, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Privacy.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 2, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Privacy.cshtml.cs", "C#", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 6, 20
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Shared\_Layout.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 4, 53
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Shared\_Layout.cshtml.css", "CSS", 0, 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 2, 9, 49
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\Shared\_ValidationScriptsPartial.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 3
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\_ViewImports.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 2, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Pages\_ViewStart.cshtml", "ASP.NET Razor", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 1, 4
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Program.cs", "C#", 0, 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 3, 21, 136
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\Properties\launchSettings.json", "JSON", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\appsettings.Development.json", "JSON", 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Marketplace.Presentation\appsettings.json", "JSON", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\README-GOOGLE-AUTH.md", "Markdown", 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 103
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\Routes.md", "Markdown", 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 191
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\.env", "Properties", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 2
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\README.md", "Markdown", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 51
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 2, 26, 180
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\package-lock.json", "JSON", 0, 0, 914, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 915
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\package.json", "JSON", 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 30
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\App.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 329, 0, 1, 58, 388
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\TestComponent.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 4, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\README.md", "Markdown", 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 91
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\api.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 2, 3, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\Header.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 3, 54, 363
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\Sidebar.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 0, 1, 41, 299
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\categories\CategoryFormModal.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 209, 0, 4, 24, 237
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\categories\CategorySkeleton.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 0, 2, 20, 184
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\categories\CategoryTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 86, 0, 0, 6, 92
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\categories\CategoryTreeNode.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 19, 160
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\AdminStatCard.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 0, 0, 13, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\ConfirmDialog.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 0, 16, 118
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\DataTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 403, 0, 5, 56, 464
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\EmptyState.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 7, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\FilterPanel.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 0, 0, 16, 114
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\LoadingIndicator.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 7, 65
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\Pagination.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 0, 5, 22, 161
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\common\StatusBadge.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 171, 0, 0, 25, 196
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\dashboard\OrdersByStatusChart.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 200, 0, 0, 26, 226
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\dashboard\PendingSellerRequests.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 191, 0, 0, 30, 221
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\dashboard\RecentOrders.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 0, 0, 23, 183
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\dashboard\SalesChart.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 23, 228
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\orders\OrderDetailsModal.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 244, 0, 4, 22, 270
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\orders\OrderFilters.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 15, 155
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\orders\OrderTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 110, 0, 0, 8, 118
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\products\ProductFilters.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 0, 0, 18, 165
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\products\ProductFormModal.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 263, 0, 4, 23, 290
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\products\ProductTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 0, 0, 8, 115
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\seller-requests\SellerRequestDetailsModal.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 5, 23, 222
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\seller-requests\SellerRequestFilters.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 113, 0, 0, 14, 127
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\seller-requests\SellerRequestTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 0, 7, 109
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\users\UserFilters.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 109, 0, 0, 14, 123
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\users\UserFormModal.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 223, 0, 4, 20, 247
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\components\users\UserTable.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 0, 0, 12, 155
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\layouts\AdminLayout.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 0, 3, 21, 136
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\categories.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 228, 0, 0, 36, 51, 315
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\dashboard.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 5, 11, 69
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\orders.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 334, 0, 0, 12, 14, 360
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\products.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 240, 0, 0, 10, 12, 262
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\reports.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 210, 0, 0, 6, 7, 223
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\seller-requests.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 189, 0, 0, 5, 6, 200
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\sellerRequests.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 221, 0, 0, 7, 8, 236
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\settings.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 259, 0, 0, 10, 11, 280
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\services\users.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 366, 0, 0, 15, 17, 398
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Categories.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 3, 16, 138
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Dashboard.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 6, 33, 289
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Orders.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 4, 18, 141
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Products.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 151, 0, 5, 21, 177
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Reports.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 332, 0, 6, 34, 372
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\SellerRequests.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 136, 0, 4, 19, 159
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Settings.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 331, 0, 6, 42, 379
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\Users.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 5, 24, 207
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\categories\CategoryDetail.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 535, 0, 6, 65, 606
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\categories\CategoryForm.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 454, 0, 6, 64, 524
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\categories\CategoryList.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 555, 0, 10, 73, 638
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\orders\OrderDetail.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 701, 0, 6, 88, 795
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\orders\OrderList.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 528, 0, 4, 44, 576
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\products\ProductDetail.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 453, 0, 5, 52, 510
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\products\ProductForm.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 661, 0, 7, 70, 738
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\products\ProductList.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 397, 0, 4, 37, 438
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\seller-requests\SellerRequestDetail.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 551, 0, 5, 76, 632
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\seller-requests\SellerRequestList.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 420, 0, 3, 60, 483
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\users\UserDetail.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 803, 0, 8, 97, 908
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\admin\views\users\UserList.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 617, 0, 7, 72, 696
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\assets\css\admin.css", "CSS", 0, 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 12, 38, 234
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\assets\css\style.css", "CSS", 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 1, 10, 57
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\assets\images\apple-icon.svg", "XML", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 1, 4
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\assets\images\google-icon.svg", "XML", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 1, 7
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\assets\images\klondike-logo.svg", "XML", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 1, 9
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\components\ErrorBoundary.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 33, 206
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\components\GlobalLoading.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 10, 82
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\components\admin\common\StatusBadge.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 9, 90
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\config\google-auth.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 110, 0, 0, 23, 16, 149
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\layouts\AdminLayout.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 494, 0, 3, 69, 566
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\main.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 0, 1, 6, 21
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\router\index.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 227, 0, 0, 22, 22, 271
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\api.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 193, 0, 0, 43, 48, 284
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\api.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 10, 12, 70
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\auth.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 103, 0, 0, 21, 32, 156
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\category.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 76, 0, 0, 12, 20, 108
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\dashboard.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 10, 7, 102
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\order.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 7, 9, 41
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\product.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 8, 10, 50
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\request-manager.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 28, 13, 88
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\seller-request.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 6, 8, 36
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\services\user.service.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 7, 9, 41
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\store\index.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 2, 13
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\store\modules\auth.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 124, 0, 0, 20, 29, 173
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\store\modules\categories.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 7, 39, 227
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\store\modules\loading.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 70, 0, 0, 1, 17, 88
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\utils\slugify.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 12, 4, 34
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\AdminDashboard.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 150, 0, 0, 9, 159
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Cart.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 706, 0, 0, 74, 780
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Dashboard.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 71, 0, 0, 10, 81
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Home.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 4, 59
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Login.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 647, 0, 2, 113, 762
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\NotFound.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 3, 22
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Profile.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 20, 159
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\Register.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 707, 0, 2, 119, 828
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\Categories.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 421, 0, 5, 62, 488
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\Dashboard.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 839, 0, 8, 101, 948
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\Orders.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 567, 0, 5, 50, 622
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\Products.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 434, 0, 5, 53, 492
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\SellerRequests.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 469, 0, 5, 49, 523
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\src\views\admin\Users.vue", "vue", 0, 0, 0, 0, 0, 0, 0, 0, 0, 412, 0, 5, 45, 462
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\frontend\vite.config.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 2, 22
"d:\Diplomkla_VS_Code\Nash Git\Marketplace\start-dev.bat", "Batch", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 13
"Total", "-", 9, 356, 1009, 18511, 120, 89, 268, 1, 3533, 20134, 152, 1089, 8073, 53344