﻿﻿using Marketplace.Application.Responses.Admin;
using Marketplace.Domain.Entities;
using Marketplace.Domain.Repositories;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Marketplace.Application.Queries.Admin;

public class GetAdminDashboardDataQueryHandler : IRequestHandler<GetAdminDashboardDataQuery, AdminDashboardResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly IProductRepository _productRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IOrderItemRepository _orderItemRepository;
    private readonly ISellerRequestRepository _sellerRequestRepository;

    public GetAdminDashboardDataQueryHandler(
        IUserRepository userRepository,
        IProductRepository productRepository,
        IOrderRepository orderRepository,
        IOrderItemRepository orderItemRepository,
        ISellerRequestRepository sellerRequestRepository)
    {
        _userRepository = userRepository;
        _productRepository = productRepository;
        _orderRepository = orderRepository;
        _orderItemRepository = orderItemRepository;
        _sellerRequestRepository = sellerRequestRepository;
    }

    public async Task<AdminDashboardResponse> Handle(GetAdminDashboardDataQuery request, CancellationToken cancellationToken)
    {
        // Get dashboard stats
        var stats = await GetDashboardStats(cancellationToken);

        // Get sales data (last 7 days by default)
        var salesData = await GetSalesData("week", cancellationToken);

        // Get orders by status
        var ordersByStatus = await GetOrdersByStatus(cancellationToken);

        // Get recent orders
        var recentOrders = await GetRecentOrders(5, cancellationToken);

        // Get pending seller requests
        var sellerRequests = await GetPendingSellerRequests(5, cancellationToken);

        return new AdminDashboardResponse
        {
            Stats = stats,
            SalesData = salesData,
            OrdersByStatus = ordersByStatus,
            RecentOrders = recentOrders,
            SellerRequests = sellerRequests
        };
    }

    private async Task<AdminDashboardStatsResponse> GetDashboardStats(CancellationToken cancellationToken)
    {
        // Get total products count
        var productsCount = await _productRepository.CountAsync(
            filter: null,
            cancellationToken: cancellationToken);

        // Get total users count
        var usersCount = await _userRepository.CountAsync(
            filter: null,
            cancellationToken: cancellationToken);

        // Get total orders count
        var ordersCount = await _orderRepository.CountAsync(
            filter: null,
            cancellationToken: cancellationToken);

        // Get total revenue - use a more efficient approach with pagination
        decimal totalRevenue = 0;
        int pageSize = 100;
        int page = 1;
        bool hasMoreItems = true;

        // Process order items in batches to avoid loading everything at once
        while (hasMoreItems)
        {
            var orderItemsBatch = await _orderItemRepository.GetAllAsync(
                filter: oi => oi.Order != null &&
                    (oi.Order.Status == Domain.Entities.OrderStatus.Shipped ||
                     oi.Order.Status == Domain.Entities.OrderStatus.Delivered),
                orderBy: null,
                descending: false,
                page: page,
                pageSize: pageSize,
                cancellationToken: cancellationToken);

            var batchList = orderItemsBatch.ToList();
            if (batchList.Count == 0)
            {
                hasMoreItems = false;
            }
            else
            {
                // Calculate revenue from valid order items
                totalRevenue += batchList.Sum(item => item.Price.Amount * item.Quantity);
                page++;
            }
        }

        return new AdminDashboardStatsResponse
        {
            Products = productsCount,
            Users = usersCount,
            Orders = ordersCount,
            Revenue = totalRevenue
        };
    }

    private async Task<List<AdminSalesDataItem>> GetSalesData(string period, CancellationToken cancellationToken)
    {
        // Determine date range based on period
        DateTime startDate;
        string format;
        Func<DateTime, string> groupByFunc;

        switch (period.ToLower())
        {
            case "year":
                startDate = DateTime.UtcNow.AddYears(-1);
                format = "MMM yyyy";
                groupByFunc = d => d.ToString("MMM yyyy");
                break;
            case "month":
                startDate = DateTime.UtcNow.AddMonths(-1);
                format = "dd MMM";
                groupByFunc = d => d.ToString("dd MMM");
                break;
            case "week":
            default:
                startDate = DateTime.UtcNow.AddDays(-7);
                format = "dd MMM";
                groupByFunc = d => d.ToString("dd MMM");
                break;
        }

        // Get orders with date filter applied at the database level
        var filteredOrders = await _orderRepository.GetAllAsync(
            filter: o => o.CreatedAt >= startDate,
            orderBy: "CreatedAt",
            descending: false,
            page: null,
            pageSize: null,
            cancellationToken: cancellationToken,
            includes: new System.Linq.Expressions.Expression<Func<Domain.Entities.Order, object>>[]
            {
                o => o.Customer
            });

        // Get order items for filtered orders - process in batches if there are many orders
        var orderIds = filteredOrders.Select(o => o.Id).ToList();
        List<Domain.Entities.OrderItem> orderItems = new List<Domain.Entities.OrderItem>();

        // If we have a reasonable number of orders, get all items at once
        if (orderIds.Count <= 50)
        {
            orderItems = (await _orderItemRepository.GetAllAsync(
                filter: oi => orderIds.Contains(oi.OrderId),
                cancellationToken: cancellationToken)).ToList();
        }
        else
        {
            // Process in batches of 50 orders at a time
            for (int i = 0; i < orderIds.Count; i += 50)
            {
                var batchIds = orderIds.Skip(i).Take(50).ToList();
                var batchItems = await _orderItemRepository.GetAllAsync(
                    filter: oi => batchIds.Contains(oi.OrderId),
                    cancellationToken: cancellationToken);

                orderItems.AddRange(batchItems);
            }
        }

        // Group by date and calculate total sales
        var salesByDate = filteredOrders
            .GroupBy(o => groupByFunc(o.CreatedAt))
            .Select(g => new
            {
                Date = g.Key,
                Orders = g.ToList()
            })
            .OrderBy(g => g.Orders.Min(o => o.CreatedAt))
            .ToList();

        var result = new List<AdminSalesDataItem>();

        foreach (var group in salesByDate)
        {
            var groupOrderIds = group.Orders.Select(o => o.Id).ToList();
            var groupOrderItems = orderItems.Where(oi => groupOrderIds.Contains(oi.OrderId)).ToList();
            var totalSales = groupOrderItems.Sum(oi => oi.Price.Amount * oi.Quantity);

            result.Add(new AdminSalesDataItem
            {
                Label = group.Date,
                Value = totalSales
            });
        }

        return result;
    }

    private async Task<List<AdminOrdersByStatusItem>> GetOrdersByStatus(CancellationToken cancellationToken)
    {
        // Get all orders
        var orders = await _orderRepository.GetAllAsync(
            filter: null,
            cancellationToken: cancellationToken);

        // Group orders by status
        var ordersByStatus = orders
            .GroupBy(o => o.Status)
            .Select(g => new AdminOrdersByStatusItem
            {
                Status = g.Key.ToString(),
                Count = g.Count()
            })
            .ToList();

        return ordersByStatus;
    }

    private async Task<List<AdminRecentOrderResponse>> GetRecentOrders(int limit, CancellationToken cancellationToken)
    {
        // Get recent orders
        var orders = await _orderRepository.GetAllAsync(
            filter: null,
            orderBy: "CreatedAt",
            descending: true,
            page: 1,
            pageSize: limit,
            cancellationToken: cancellationToken,
            includes: new System.Linq.Expressions.Expression<Func<Domain.Entities.Order, object>>[]
            {
                o => o.Customer
            });

        // Map to response
        var result = orders.Select(o => new AdminRecentOrderResponse
        {
            Id = o.Id,
            CustomerName = o.Customer?.Username ?? "Unknown",
            Total = o.TotalPrice.Amount,
            Status = o.Status.ToString(),
            CreatedAt = o.CreatedAt
        }).ToList();

        return result;
    }

    private async Task<List<AdminSellerRequestResponse>> GetPendingSellerRequests(int limit, CancellationToken cancellationToken)
    {
        // Get pending seller requests
        var requests = await _sellerRequestRepository.GetAllAsync(
            filter: sr => sr.Status == Domain.Entities.RequestStatus.Pending,
            orderBy: "CreatedAt",
            descending: true,
            page: 1,
            pageSize: limit,
            cancellationToken: cancellationToken,
            includes: new System.Linq.Expressions.Expression<Func<Domain.Entities.SellerRequest, object>>[]
            {
                sr => sr.User
            });

        // Map to response
        var result = requests.Select(sr => new AdminSellerRequestResponse
        {
            Id = sr.Id,
            UserName = sr.User?.Username ?? "Unknown",
            CompanyName = GetCompanyNameFromJson(sr.CompanyRequestData),
            AdditionalInformation = sr.AdditionalInformation,
            CreatedAt = sr.CreatedAt
        }).ToList();

        return result;
    }

    private string GetCompanyNameFromJson(Domain.ValueObjects.CompanyRequestData companyRequestData)
    {
        try
        {
            return companyRequestData?.Name ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }
}
