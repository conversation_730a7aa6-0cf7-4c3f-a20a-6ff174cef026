import{_ as re,g as c,z as ce,i as de,c as u,a as e,b as _,A as D,B as W,D as X,C as ue,F as ve,p as pe,t as v,k as I,w as Y,r as me,n as C,d as fe,o as p}from"./index-C1YYMYJd.js";import{s as N}from"./seller-requests-Cz8SuHKM.js";import{S as ge}from"./StatusBadge-7HpbPXqn.js";import{P as _e}from"./Pagination-uai-tBBw.js";import{C as he}from"./ConfirmDialog-hpHY5nH2.js";/* empty css                                                                    */const ye={class:"seller-request-list"},be={class:"level"},Re={class:"level-right"},Ce={class:"level-item"},ke={class:"field has-addons"},Ae={class:"control"},xe={class:"box"},we={class:"columns"},Se={class:"column is-4"},je={class:"field"},Pe={class:"control"},De={class:"select is-fullwidth"},Ie={class:"card"},Ne={class:"card-content"},Ee={key:0,class:"has-text-centered py-6"},Ve={key:1,class:"has-text-centered py-6"},Fe={key:2},Te={class:"columns is-multiline"},qe={class:"card seller-request-card"},Me={class:"card-content"},Ue={class:"media"},Be={class:"media-left"},Le={class:"image is-64x64"},$e=["src","alt"],ze={class:"media-content"},Oe={class:"title is-4"},Qe={class:"subtitle is-6"},Ge=["href"],He={class:"subtitle is-6"},Je={class:"ml-2"},Ke={class:"content"},We={class:"field"},Xe={class:"field"},Ye={class:"field"},Ze={class:"field"},es={key:0,class:"field"},ss={class:"field mt-4"},ts={key:1,class:"field is-grouped mt-4"},as={class:"control"},ls=["onClick"],os={class:"control"},ns=["onClick"],is={key:2,class:"field mt-4"},rs={key:3,class:"field mt-4"},cs={class:"has-text-danger"},ds={key:0,class:"mt-2"},us={class:"modal-card"},vs={class:"modal-card-body"},ps={class:"field mt-4"},ms={class:"control"},fs={class:"modal-card-foot"},gs={__name:"SellerRequestList",setup(_s){const Z=(t,s)=>{let n;return function(...d){const i=()=>{clearTimeout(n),t(...d)};clearTimeout(n),n=setTimeout(i,s)}},l=c([]),k=c(!1),A=c(""),g=c(1),x=c(1),E=c(0),ee=c(10),h=c(!1),y=c(!1),o=c(null),f=c(""),w=c(!1),S=ce({status:""}),j=async(t=1)=>{var s;k.value=!0,g.value=t;try{const n={page:g.value,pageSize:ee.value,filter:A.value,status:S.status};console.log("🔍 Fetching seller requests with params:",n);const m=await N.getSellerRequests(n);console.log("📥 Raw API response:",m);let d=[],i={};if(m&&m.success&&m.data){const r=m.data;console.log("📊 Data structure:",r),r.data&&Array.isArray(r.data)?(d=r.data,i=r):Array.isArray(r)&&(d=r)}console.log("📋 Final requests data:",d),console.log("📊 Final pagination data:",i),l.value=d.map(r=>({...r,processing:!1})),x.value=i.totalPages||Math.ceil((i.totalItems||d.length)/(i.pageSize||10)),E.value=i.totalItems||d.length,g.value=i.currentPage||1,console.log("✅ Final state:",{requestsCount:l.value.length,totalPages:x.value,totalItems:E.value,currentPage:g.value})}catch(n){console.error("❌ Error fetching seller requests:",n),console.error("❌ Error details:",((s=n.response)==null?void 0:s.data)||n.message)}finally{k.value=!1}},P=()=>{g.value=1,j()},V=Z(P,300),se=t=>{j(t)},F=t=>t?new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(t)):"",b=t=>{if(typeof t=="string")return t.toLowerCase();switch(t){case 0:return"pending";case 1:return"approved";case 2:return"rejected";default:return"pending"}},te=t=>{t.target.src="https://via.placeholder.com/64?text=No+Image"},ae=t=>{o.value=t,h.value=!0},le=t=>{o.value=t,f.value="",y.value=!0},R=()=>{h.value=!1,y.value=!1,o.value=null,f.value=""},oe=async()=>{if(!o.value)return;const t=l.value.findIndex(s=>s.id===o.value.id);t!==-1&&(l.value[t].processing=!0);try{await N.approveSellerRequest(o.value.id),t!==-1&&(l.value[t].status=1,l.value[t].updatedAt=new Date,l.value[t].processing=!1),h.value=!1,o.value=null}catch(s){console.error("Error approving seller request:",s),t!==-1&&(l.value[t].processing=!1)}},ne=async()=>{if(!o.value)return;w.value=!0;const t=l.value.findIndex(s=>s.id===o.value.id);t!==-1&&(l.value[t].processing=!0);try{await N.rejectSellerRequest(o.value.id,f.value),t!==-1&&(l.value[t].status=2,l.value[t].updatedAt=new Date,l.value[t].rejectionReason=f.value,l.value[t].processing=!1),y.value=!1,o.value=null,f.value=""}catch(s){console.error("Error rejecting seller request:",s),t!==-1&&(l.value[t].processing=!1)}finally{w.value=!1}};return de(()=>{j()}),(t,s)=>{var m,d,i,r;const n=me("router-link");return p(),u("div",ye,[e("div",be,[s[5]||(s[5]=e("div",{class:"level-left"},[e("div",{class:"level-item"},[e("h1",{class:"title"},"Seller Requests")])],-1)),e("div",Re,[e("div",Ce,[e("div",ke,[e("div",Ae,[D(e("input",{class:"input",type:"text",placeholder:"Search requests...","onUpdate:modelValue":s[0]||(s[0]=a=>A.value=a),onInput:s[1]||(s[1]=(...a)=>X(V)&&X(V)(...a))},null,544),[[W,A.value]])]),e("div",{class:"control"},[e("button",{class:"button is-info",onClick:P},s[4]||(s[4]=[e("span",{class:"icon"},[e("i",{class:"fas fa-search"})],-1)]))])])])])]),e("div",xe,[e("div",we,[e("div",Se,[e("div",je,[s[7]||(s[7]=e("label",{class:"label"},"Status",-1)),e("div",Pe,[e("div",De,[D(e("select",{"onUpdate:modelValue":s[2]||(s[2]=a=>S.status=a),onChange:P},s[6]||(s[6]=[e("option",{value:""},"All Statuses",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"approved"},"Approved",-1),e("option",{value:"rejected"},"Rejected",-1)]),544),[[ue,S.status]])])])])])])]),e("div",Ie,[e("div",Ne,[k.value&&!l.value.length?(p(),u("div",Ee,s[8]||(s[8]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-spinner fa-pulse fa-2x"})],-1),e("p",{class:"mt-2"},"Loading seller requests...",-1)]))):l.value.length?(p(),u("div",Fe,[e("div",Te,[(p(!0),u(ve,null,pe(l.value,a=>{var T,q,M,U,B,L,$,z,O,Q,G,H,J,K;return p(),u("div",{key:a.id,class:"column is-12-tablet is-6-desktop"},[e("div",qe,[e("div",Me,[e("div",Ue,[e("div",Be,[e("figure",Le,[e("img",{src:((T=a.user)==null?void 0:T.avatar)||"https://via.placeholder.com/64",alt:(((q=a.user)==null?void 0:q.firstName)||"")+" "+(((M=a.user)==null?void 0:M.lastName)||""),onError:te},null,40,$e)])]),e("div",ze,[e("p",Oe,v(((U=a.user)==null?void 0:U.username)||"Unknown User"),1),e("p",Qe,[e("a",{href:`mailto:${((L=(B=a.user)==null?void 0:B.email)==null?void 0:L.value)||(($=a.user)==null?void 0:$.email)}`},v(((O=(z=a.user)==null?void 0:z.email)==null?void 0:O.value)||((Q=a.user)==null?void 0:Q.email)),9,Ge)]),e("p",He,[_(ge,{status:b(a.status),type:"default"},null,8,["status"]),e("span",Je,v(F(a.createdAt)),1)])])]),e("div",Ke,[e("div",We,[s[10]||(s[10]=e("label",{class:"label"},"Company Name",-1)),e("p",null,v(((G=a.companyRequestData)==null?void 0:G.name)||"N/A"),1)]),e("div",Xe,[s[11]||(s[11]=e("label",{class:"label"},"Company Description",-1)),e("p",null,v(((H=a.companyRequestData)==null?void 0:H.description)||"N/A"),1)]),e("div",Ye,[s[12]||(s[12]=e("label",{class:"label"},"Contact Email",-1)),e("p",null,v(((J=a.companyRequestData)==null?void 0:J.contactEmail)||"N/A"),1)]),e("div",Ze,[s[13]||(s[13]=e("label",{class:"label"},"Contact Phone",-1)),e("p",null,v(((K=a.companyRequestData)==null?void 0:K.contactPhone)||"N/A"),1)]),a.additionalInformation?(p(),u("div",es,[s[14]||(s[14]=e("label",{class:"label"},"Additional Information",-1)),e("p",null,v(a.additionalInformation),1)])):I("",!0),e("div",ss,[_(n,{to:{name:"AdminSellerRequestDetail",params:{id:a.id}},class:"button is-info is-small"},{default:Y(()=>s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-eye"})],-1),e("span",null,"View Details",-1)])),_:2},1032,["to"])]),b(a.status)==="pending"?(p(),u("div",ts,[e("div",as,[e("button",{class:C(["button is-success",{"is-loading":a.processing}]),onClick:ie=>ae(a)},s[16]||(s[16]=[e("span",{class:"icon"},[e("i",{class:"fas fa-check"})],-1),e("span",null,"Approve",-1)]),10,ls)]),e("div",os,[e("button",{class:C(["button is-danger",{"is-loading":a.processing}]),onClick:ie=>le(a)},s[17]||(s[17]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1),e("span",null,"Reject",-1)]),10,ns)])])):b(a.status)==="approved"?(p(),u("div",is,[_(n,{to:`/admin/users/${a.userId}`,class:"button is-info"},{default:Y(()=>s[18]||(s[18]=[e("span",{class:"icon"},[e("i",{class:"fas fa-user"})],-1),e("span",null,"View Seller Profile",-1)])),_:2},1032,["to"])])):b(a.status)==="rejected"?(p(),u("div",rs,[e("p",cs,[s[19]||(s[19]=e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})],-1)),e("span",null,"Rejected on "+v(F(a.updatedAt)),1)]),a.rejectionReason?(p(),u("p",ds,[s[20]||(s[20]=e("strong",null,"Reason:",-1)),fe(" "+v(a.rejectionReason),1)])):I("",!0)])):I("",!0)])])])])}),128))]),_(_e,{"current-page":g.value,"total-pages":x.value,onPageChanged:se},null,8,["current-page","total-pages"])])):(p(),u("div",Ve,s[9]||(s[9]=[e("span",{class:"icon is-large"},[e("i",{class:"fas fa-store fa-2x"})],-1),e("p",{class:"mt-2"},"No seller requests found",-1),e("p",{class:"mt-2"},"There are currently no seller requests to review",-1)])))])]),_(he,{"is-open":h.value,title:"Approve Seller Request",message:`Are you sure you want to approve ${(d=(m=o.value)==null?void 0:m.user)==null?void 0:d.username}'s seller request?`,"confirm-text":"Approve","cancel-text":"Cancel","confirm-button-class":"is-success",onConfirm:oe,onCancel:R},null,8,["is-open","message"]),e("div",{class:C(["modal",{"is-active":y.value}])},[e("div",{class:"modal-background",onClick:R}),e("div",us,[e("header",{class:"modal-card-head"},[s[21]||(s[21]=e("p",{class:"modal-card-title"},"Reject Seller Request",-1)),e("button",{class:"delete","aria-label":"close",onClick:R})]),e("section",vs,[e("p",null,"Are you sure you want to reject "+v((r=(i=o.value)==null?void 0:i.user)==null?void 0:r.username)+"'s seller request?",1),e("div",ps,[s[22]||(s[22]=e("label",{class:"label"},"Reason for Rejection (Optional)",-1)),e("div",ms,[D(e("textarea",{class:"textarea","onUpdate:modelValue":s[3]||(s[3]=a=>f.value=a),placeholder:"Provide a reason for rejection"},"              ",512),[[W,f.value]])])])]),e("footer",fs,[e("button",{class:C(["button is-danger",{"is-loading":w.value}]),onClick:ne}," Reject ",2),e("button",{class:"button is-light",onClick:R},"Cancel")])])],2)])}}},As=re(gs,[["__scopeId","data-v-8bdacf99"]]);export{As as default};
