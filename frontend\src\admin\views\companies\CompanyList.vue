<template>
  <div class="company-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Companies</h1>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Companies"
      search-placeholder="Search by name or description..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="companies"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading companies...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <button class="delete" @click="error = null"></button>
      {{ error }}
    </div>

    <!-- No Data -->
    <div class="has-text-centered py-6" v-else-if="!loading && !items.length">
      <span class="icon is-large">
        <i class="fas fa-building fa-2x"></i>
      </span>
      <p class="mt-2">No companies found</p>
      <p class="mt-2">Try adjusting your search criteria or filters</p>
    </div>

    <!-- Companies Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>Name</th>
                <th>Contact</th>
                <th>Location</th>
                <th>Featured</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="company in items" :key="company.id">
                <td>
                  <div class="media">
                    <div class="media-left" v-if="company.imageUrl">
                      <figure class="image is-48x48">
                        <img :src="company.imageUrl" :alt="company.name" class="is-rounded">
                      </figure>
                    </div>
                    <div class="media-content">
                      <p class="title is-6">{{ company.name }}</p>
                      <p class="subtitle is-7">{{ company.slug }}</p>
                    </div>
                  </div>
                </td>
                <td>
                  <p>{{ company.contactEmail }}</p>
                  <p class="has-text-grey is-size-7">{{ company.contactPhone }}</p>
                </td>
                <td>
                  <p>{{ company.addressCity }}, {{ company.addressRegion }}</p>
                  <p class="has-text-grey is-size-7">{{ company.addressStreet }}</p>
                </td>
                <td>
                  <span v-if="company.isFeatured" class="tag is-info">Featured</span>
                  <span v-else class="tag">Regular</span>
                </td>
                <td>
                  <div class="buttons">
                    <router-link
                      :to="{ name: 'AdminCompanyDetail', params: { id: company.id } }"
                      class="button is-small is-info">
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                      <span>View</span>
                    </router-link>
                    <router-link
                      :to="{ name: 'AdminCompanyEdit', params: { id: company.id } }"
                      class="button is-small is-warning">
                      <span class="icon">
                        <i class="fas fa-edit"></i>
                      </span>
                      <span>Edit</span>
                    </router-link>
                    <button
                      class="button is-small is-danger"
                      @click="deleteCompany(company.id)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                      <span>Delete</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Loading overlay -->
          <div v-if="loading && !isFirstLoad" class="table-loading-overlay">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-pulse"></i>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <Pagination
          :current-page="currentPage"
          :total-pages="totalPages"
          @page-changed="handlePageChange"
          v-if="totalPages > 1"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { companiesService } from '@/admin/services/companies';
import { useAdminSearch } from '@/composables/useAdminSearch';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import Pagination from '@/admin/components/common/Pagination.vue';

// Action loading state
const actionLoading = ref(false);

// Filter configuration
const filterFields = [
  {
    key: 'featured',
    label: 'Featured',
    type: 'select',
    columnClass: 'is-3',
    allOption: 'All Companies',
    options: [
      { value: 'true', label: 'Featured Only' },
      { value: 'false', label: 'Regular Only' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    type: 'select',
    columnClass: 'is-3',
    allOption: false, // Прибираємо "All" опцію
    options: [
      { value: 'ApprovedAt', label: 'Approved Date' },
      { value: 'Name', label: 'Name' }
    ]
  },
  {
    key: 'sortOrder',
    label: 'Order',
    type: 'select',
    columnClass: 'is-3',
    options: [
      { value: 'desc', label: 'Descending' },
      { value: 'asc', label: 'Ascending' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: companiesService.getCompanies,
  defaultFilters: {
    featured: '',
    sortBy: 'ApprovedAt',
    sortOrder: 'desc'
  },
  debounceTime: 300,
  defaultPageSize: 15,
  clientSideSearch: false
});

// Event handlers
const handleSearchChange = (searchValue) => {
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  filters[filterKey] = filterValue;

  // Handle sorting separately
  if (filterKey === 'sortBy' || filterKey === 'sortOrder') {
    // Update the composable's sort values
    if (filterKey === 'sortBy') {
      // The composable will handle this through the filters watcher
    }
  }
};

const handleResetFilters = () => {
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else if (key === 'sortBy') {
      filters[key] = 'ApprovedAt';
    } else if (key === 'sortOrder') {
      filters[key] = 'desc';
    } else {
      filters[key] = '';
    }
  });
};

// Company actions
const deleteCompany = async (id) => {
  if (!confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.deleteCompany(id);
    await fetchData(); // Refresh data using composable method
  } catch (err) {
    error.value = err.message || 'Failed to delete company';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods (removed unused methods)

// Lifecycle
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.company-list {
  padding: 1.5rem;
}

.title {
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
  position: relative;
}

.table-container.is-loading {
  opacity: 0.7;
  transition: opacity 0.3s;
}

.table-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 10;
}

.loading-spinner {
  font-size: 2rem;
  color: #ff7700;
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}
</style>
