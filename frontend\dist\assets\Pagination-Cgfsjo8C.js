import{_ as N,h as g,c as l,o,a as e,k as P,n as k,F as $,p as w,t as d}from"./index-BtyG65bR.js";const F={class:"pagination is-centered",role:"navigation","aria-label":"pagination"},M=["disabled"],V=["disabled"],B={class:"pagination-list"},E={key:0},L={key:1},q=["onClick"],z={key:2},D={key:3},I={class:"pagination-info"},S={class:"has-text-grey"},T={__name:"Pagination",props:{currentPage:{type:Number,required:!0},totalPages:{type:Number,required:!0},maxVisiblePages:{type:Number,default:5}},emits:["page-changed"],setup(a,{emit:p}){const n=a,x=p,r=g(()=>{const{currentPage:i,totalPages:t,maxVisiblePages:s}=n,v=[];let c=Math.max(1,i-Math.floor(s/2)),m=Math.min(t,c+s-1);m-c+1<s&&(c=Math.max(1,m-s+1));for(let b=c;b<=m;b++)v.push(b);return v}),f=g(()=>n.totalPages>1&&!r.value.includes(1)),h=g(()=>n.totalPages>1&&!r.value.includes(n.totalPages)),y=g(()=>f.value&&r.value[0]>2),C=g(()=>h.value&&r.value[r.value.length-1]<n.totalPages-1),u=i=>{i>=1&&i<=n.totalPages&&i!==n.currentPage&&x("page-changed",i)};return(i,t)=>(o(),l("nav",F,[e("button",{class:"pagination-previous",disabled:a.currentPage<=1,onClick:t[0]||(t[0]=s=>u(a.currentPage-1))},t[4]||(t[4]=[e("span",{class:"icon"},[e("i",{class:"fas fa-chevron-left"})],-1),e("span",null,"Previous",-1)]),8,M),e("button",{class:"pagination-next",disabled:a.currentPage>=a.totalPages,onClick:t[1]||(t[1]=s=>u(a.currentPage+1))},t[5]||(t[5]=[e("span",null,"Next",-1),e("span",{class:"icon"},[e("i",{class:"fas fa-chevron-right"})],-1)]),8,V),e("ul",B,[f.value?(o(),l("li",E,[e("button",{class:k(["pagination-link",{"is-current":a.currentPage===1}]),onClick:t[2]||(t[2]=s=>u(1))}," 1 ",2)])):P("",!0),y.value?(o(),l("li",L,t[6]||(t[6]=[e("span",{class:"pagination-ellipsis"},"…",-1)]))):P("",!0),(o(!0),l($,null,w(r.value,s=>(o(),l("li",{key:s},[e("button",{class:k(["pagination-link",{"is-current":a.currentPage===s}]),onClick:v=>u(s)},d(s),11,q)]))),128)),C.value?(o(),l("li",z,t[7]||(t[7]=[e("span",{class:"pagination-ellipsis"},"…",-1)]))):P("",!0),h.value?(o(),l("li",D,[e("button",{class:k(["pagination-link",{"is-current":a.currentPage===a.totalPages}]),onClick:t[3]||(t[3]=s=>u(a.totalPages))},d(a.totalPages),3)])):P("",!0)]),e("div",I,[e("span",S," Page "+d(a.currentPage)+" of "+d(a.totalPages),1)])]))}},A=N(T,[["__scopeId","data-v-c53b5c65"]]);export{A as P};
