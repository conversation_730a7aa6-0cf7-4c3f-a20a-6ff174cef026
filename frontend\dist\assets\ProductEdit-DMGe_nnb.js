import{_ as H,g as h,h as te,x as le,i as oe,c as n,o as i,a as e,k as $,d as z,t as y,n as P,A as F,B as T,F as R,p as q,m as J,q as L,J as ie,f as de,b as ae,C as re,e as ce}from"./index-BtyG65bR.js";import{p as Z}from"./products-d_ocQBUS.js";import{c as ue}from"./companies-Cx9IcxUK.js";const ve={class:"category-select"},me={class:"label"},ge={key:0,class:"has-text-danger"},pe={class:"control"},fe={class:"dropdown-trigger"},he={class:"field has-addons"},ye={class:"control is-expanded"},_e=["placeholder","readonly"],be={class:"control"},Ie=["disabled"],Ce={class:"icon"},$e={class:"dropdown-menu",role:"menu"},we={class:"dropdown-content"},xe={key:0,class:"dropdown-item"},ke={key:1,class:"dropdown-item"},Ae=["onMousedown"],De={class:"category-item"},Me={class:"category-name"},Pe={class:"category-slug has-text-grey is-size-7"},Se={key:0,class:"help"},Ee={__name:"CategorySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Category"},placeholder:{type:String,default:"Search and select category..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(_,{emit:V}){const C=_,S=V,f=h([]),D=h(!1),v=h(""),u=h(!1),g=h(null),p=te(()=>{var b;const l=Array.isArray(f.value)?f.value:[];if(console.log("🏷️ Filtering categories:",{totalCategories:l.length,searchQuery:v.value,isDropdownOpen:u.value}),!v.value.trim()||v.value===(((b=g.value)==null?void 0:b.name)||""))return console.log("📋 Showing all categories:",l.length),l;const r=v.value.toLowerCase().trim(),d=l.filter(I=>!I||typeof I!="object"?!1:I.name&&I.name.toLowerCase().includes(r)||I.slug&&I.slug.toLowerCase().includes(r)).slice(0,50);return console.log("🎯 Filtered categories:",d.length),d}),M=async()=>{try{D.value=!0,console.log("🏷️ Fetching categories...");const l=await Z.getCategories({pageSize:1e3});if(console.log("📦 Categories response:",l),f.value=Array.isArray(l)?l:[],console.log("✅ Categories loaded:",f.value.length),C.modelValue&&f.value.length>0){const r=f.value.find(d=>d&&d.id===C.modelValue);r?(g.value=r,v.value=r.name||"",console.log("🎯 Selected category found:",r.name)):console.log("❌ Category not found for ID:",C.modelValue)}}catch(l){console.error("❌ Error fetching categories:",l),f.value=[]}finally{D.value=!1}},U=l=>{if(!l||typeof l!="object"||!l.id){console.error("❌ Invalid category object:",l);return}g.value=l,v.value=l.name||"",u.value=!1,console.log("✅ Category selected:",l.name),S("update:modelValue",l.id),S("change",l)},x=()=>{C.readonly||(u.value=!0,console.log("📂 Category dropdown opened"))},A=()=>{u.value=!1,console.log("📁 Category dropdown closed")},t=()=>{C.readonly||(u.value?A():(g.value&&v.value===g.value.name&&(v.value=""),x()))},k=()=>{u.value||(u.value=!0,console.log("📂 Category dropdown opened via search input"))},E=()=>{setTimeout(()=>{A(),g.value?v.value=g.value.name:v.value=""},200)};return le(()=>C.modelValue,l=>{const r=Array.isArray(f.value)?f.value:[];if(l&&r.length>0){const d=r.find(b=>b&&b.id===l);d?(g.value=d,v.value=d.name||"",console.log("🔄 Category updated via watch:",d.name)):console.log("🔍 Category not found in watch for ID:",l)}else l||(g.value=null,v.value="",console.log("🧹 Cleared category selection"))}),oe(()=>{M()}),(l,r)=>(i(),n("div",ve,[e("label",me,[z(y(_.label)+" ",1),_.required?(i(),n("span",ge,"*")):$("",!0)]),e("div",pe,[e("div",{class:P(["dropdown",{"is-active":u.value}])},[e("div",fe,[e("div",he,[e("div",ye,[F(e("input",{class:"input",type:"text",placeholder:_.placeholder,"onUpdate:modelValue":r[0]||(r[0]=d=>v.value=d),onInput:k,onFocus:x,onBlur:E,readonly:_.readonly},null,40,_e),[[T,v.value]])]),e("div",be,[e("button",{class:"button",type:"button",onClick:t,disabled:_.readonly},[e("span",Ce,[e("i",{class:P(["fas fa-chevron-down",{"fa-rotate-180":u.value}])},null,2)])],8,Ie)])])]),e("div",$e,[e("div",we,[D.value?(i(),n("div",xe,r[1]||(r[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),z(" Loading categories... ")],-1)]))):p.value.length===0?(i(),n("div",ke,r[2]||(r[2]=[e("div",{class:"has-text-grey has-text-centered"}," No categories found ",-1)]))):(i(!0),n(R,{key:2},q(p.value,d=>{var b;return i(),n("a",{key:d.id,class:P(["dropdown-item",{"is-active":((b=g.value)==null?void 0:b.id)===d.id}]),onMousedown:J(I=>U(d),["prevent"])},[e("div",De,[e("div",Me,y(d.name),1),e("div",Pe,y(d.slug),1)])],42,Ae)}),128))])])],2)]),g.value?(i(),n("p",Se," Selected: "+y(g.value.name),1)):$("",!0)]))}},Fe=H(Ee,[["__scopeId","data-v-80c00f51"]]),Ue={class:"company-select"},Te={class:"label"},Ve={key:0,class:"has-text-danger"},je={class:"control"},Le={class:"dropdown-trigger"},Oe={class:"field has-addons"},Be={class:"control is-expanded"},Ne=["placeholder","readonly"],ze={class:"control"},Re=["disabled"],qe={class:"icon"},Ge={class:"dropdown-menu",role:"menu"},Qe={class:"dropdown-content"},We={key:0,class:"dropdown-item"},Ze={key:1,class:"dropdown-item"},He=["onMousedown"],Je={class:"company-item"},Ye={class:"company-name"},Xe={class:"company-details has-text-grey is-size-7"},Ke={key:0,class:"help"},es={__name:"CompanySelect",props:{modelValue:{type:[String,null],default:null},label:{type:String,default:"Company"},placeholder:{type:String,default:"Search and select company..."},required:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(_,{emit:V}){const C=_,S=V,f=h([]),D=h(!1),v=h(""),u=h(!1),g=h(null),p=te(()=>{var b;const l=Array.isArray(f.value)?f.value:[];if(console.log("🔍 Filtering companies:",{totalCompanies:l.length,searchQuery:v.value,isDropdownOpen:u.value}),!v.value.trim()||v.value===(((b=g.value)==null?void 0:b.name)||""))return console.log("📋 Showing all companies:",l.length),l.slice(0,50);const r=v.value.toLowerCase().trim(),d=l.filter(I=>!I||typeof I!="object"?!1:I.name&&I.name.toLowerCase().includes(r)||I.contactEmail&&I.contactEmail.toLowerCase().includes(r)||I.addressCity&&I.addressCity.toLowerCase().includes(r)||I.slug&&I.slug.toLowerCase().includes(r)).slice(0,50);return console.log("🎯 Filtered companies:",d.length),d}),M=async()=>{try{D.value=!0,console.log("🏢 Fetching companies...");const l=await ue.getCompanies({pageSize:1e3});console.log("📦 Companies API response:",l);let r=[];if(l&&l.data&&Array.isArray(l.data)?r=l.data:l&&l.companies&&Array.isArray(l.companies)?r=l.companies:Array.isArray(l)?r=l:(console.warn("⚠️ Unexpected API response structure:",l),r=[]),f.value=r,console.log("✅ Companies loaded:",f.value.length),C.modelValue&&Array.isArray(f.value)){const d=f.value.find(b=>b&&b.id===C.modelValue);d?(g.value=d,v.value=d.name||"",console.log("🎯 Selected company found:",d.name)):console.log("❌ Company not found for ID:",C.modelValue)}}catch(l){console.error("❌ Error fetching companies:",l),f.value=[]}finally{D.value=!1}},U=l=>{if(!l||typeof l!="object"||!l.id){console.error("❌ Invalid company object:",l);return}g.value=l,v.value=l.name||"",u.value=!1,console.log("✅ Company selected:",l.name),S("update:modelValue",l.id),S("change",l)},x=()=>{C.readonly||(u.value=!0,console.log("📂 Dropdown opened"))},A=()=>{u.value=!1,console.log("📁 Dropdown closed")},t=()=>{C.readonly||(u.value?A():(g.value&&v.value===g.value.name&&(v.value=""),x()))},k=()=>{u.value||(u.value=!0,console.log("📂 Dropdown opened via search input"))},E=()=>{setTimeout(()=>{A(),g.value?v.value=g.value.name||"":v.value=""},200)};return le(()=>C.modelValue,l=>{const r=Array.isArray(f.value)?f.value:[];if(l&&r.length>0){const d=r.find(b=>b&&b.id===l);d?(g.value=d,v.value=d.name||"",console.log("🔄 Company updated via watch:",d.name)):console.log("🔍 Company not found in watch for ID:",l)}else l||(g.value=null,v.value="",console.log("🧹 Cleared company selection"))}),oe(()=>{M()}),(l,r)=>(i(),n("div",Ue,[e("label",Te,[z(y(_.label)+" ",1),_.required?(i(),n("span",Ve,"*")):$("",!0)]),e("div",je,[e("div",{class:P(["dropdown",{"is-active":u.value}])},[e("div",Le,[e("div",Oe,[e("div",Be,[F(e("input",{class:"input",type:"text",placeholder:_.placeholder,"onUpdate:modelValue":r[0]||(r[0]=d=>v.value=d),onInput:k,onFocus:x,onBlur:E,readonly:_.readonly},null,40,Ne),[[T,v.value]])]),e("div",ze,[e("button",{class:"button",type:"button",onClick:t,disabled:_.readonly},[e("span",qe,[e("i",{class:P(["fas fa-chevron-down",{"fa-rotate-180":u.value}])},null,2)])],8,Re)])])]),e("div",Ge,[e("div",Qe,[D.value?(i(),n("div",We,r[1]||(r[1]=[e("div",{class:"has-text-centered"},[e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})]),z(" Loading companies... ")],-1)]))):p.value.length===0?(i(),n("div",Ze,r[2]||(r[2]=[e("div",{class:"has-text-grey has-text-centered"}," No companies found ",-1)]))):(i(!0),n(R,{key:2},q(p.value,d=>{var b;return i(),n("a",{key:d.id,class:P(["dropdown-item",{"is-active":((b=g.value)==null?void 0:b.id)===d.id}]),onMousedown:J(I=>U(d),["prevent"])},[e("div",Je,[e("div",Ye,y(d.name),1),e("div",Xe,y(d.contactEmail)+" • "+y(d.addressCity),1)])],42,He)}),128))])])],2)]),g.value?(i(),n("p",Ke," Selected: "+y(g.value.name),1)):$("",!0)]))}},ss=H(es,[["__scopeId","data-v-becaac52"]]),as={class:"product-image-manager"},ts={class:"upload-section mb-5"},ls={class:"field"},os={class:"control"},ns=["disabled"],is={class:"drop-zone-content"},ds={class:"icon is-large has-text-grey-light"},rs={key:0,class:"fas fa-cloud-upload-alt fa-3x"},cs={key:1,class:"fas fa-spinner fa-spin fa-3x"},us={class:"has-text-grey mt-3"},vs={key:0},ms={key:1},gs={key:0,class:"help"},ps={key:0,class:"notification is-info"},fs={class:"level"},hs={class:"level-left"},ys={class:"level-item"},_s={class:"icon-text"},bs={class:"level-right"},Is={class:"level-item"},Cs=["value","max"],$s={key:0,class:"current-images mb-5"},ws={class:"label"},xs={class:"columns is-multiline"},ks={class:"card image-card"},As={class:"card-image"},Ds={class:"image is-4by3"},Ms=["src","alt","onClick"],Ps={class:"image-overlay"},Ss={class:"image-actions"},Es=["onClick"],Fs=["onClick"],Us=["onClick"],Ts={class:"card-content p-3"},Vs={class:"level is-mobile"},js={class:"level-left"},Ls={class:"level-item"},Os={class:"level-right"},Bs={class:"level-item"},Ns={key:0,class:"tag is-small is-info"},zs={key:1,class:"pending-images mb-5"},Rs={class:"label"},qs={class:"columns is-multiline"},Gs={class:"card image-card"},Qs={class:"card-image"},Ws={class:"image is-4by3"},Zs=["src","alt"],Hs={class:"card-content p-3"},Js={class:"level is-mobile"},Ys={class:"level-left"},Xs={class:"level-item"},Ks={class:"level-right"},ea={class:"level-item"},sa={class:"buttons are-small"},aa=["onClick"],ta=["onClick"],la={class:"content is-small"},oa={key:2,class:"no-images has-background-light has-text-centered p-6 is-rounded"},na={class:"modal-content"},ia={class:"image"},da=["src"],ra=5*1024*1024,ca={__name:"ProductImageManager",props:{productId:{type:String,default:null},images:{type:Array,default:()=>[]},isCreate:{type:Boolean,default:!1}},emits:["images-updated","main-image-changed","image-uploaded","image-deleted","pending-images-changed"],setup(_,{expose:V,emit:C}){const S=_,f=C,D=h(null),v=h(!1),u=h(!1),g=h([]),p=h([]),M=h(!1),U=h(!1),x=h(null),A=h(null),t=h({current:0,total:0}),k=["image/jpeg","image/jpg","image/png","image/gif","image/webp","image/jfif"],E=()=>{!u.value&&D.value&&D.value.click()},l=a=>{if(!a.target||!a.target.files)return;const s=Array.from(a.target.files);I(s),a.target.value=""},r=a=>{if(a.preventDefault(),v.value=!1,u.value)return;const s=Array.from(a.dataTransfer.files);I(s)},d=a=>{a.preventDefault(),u.value||(v.value=!0)},b=()=>{v.value=!1},I=a=>{if(a.length===0)return;const s=[],o=[];a.forEach(m=>{if(!k.includes(m.type.toLowerCase())){o.push(`${m.name}: Unsupported file type`);return}if(m.size>ra){o.push(`${m.name}: File too large (max 5MB)`);return}s.push(m)}),o.length>0&&alert(`Some files were rejected:
`+o.join(`
`)),s.length!==0&&(g.value=s,s.forEach((m,c)=>{const w=new FileReader;w.onload=async N=>{const j={id:`temp-${Date.now()}-${Math.random()}-${c}`,file:m,name:m.name,preview:N.target.result,size:m.size,isTemp:!0,isMain:p.value.length===0&&S.images.length===0};p.value.push(j),await ie(),f("pending-images-changed",p.value)},w.readAsDataURL(m)}))},G=async a=>{if(S.productId)try{const s=await L.patch(`/api/admin/products/${S.productId}/images/${a}/main`);s.data&&s.data.success&&(f("main-image-changed",a),f("images-updated"))}catch(s){console.error("Error setting main image:",s),alert("Failed to set main image. Please try again.")}},Y=a=>{p.value.forEach(s=>{s.isMain=s.id===a}),f("pending-images-changed",p.value)},X=a=>{const s=p.value.findIndex(o=>o.id===a);s!==-1&&(p.value.splice(s,1),f("pending-images-changed",p.value))},Q=a=>{x.value=a,M.value=!0},O=()=>{M.value=!1,x.value=null},W=a=>{A.value=a,U.value=!0},B=()=>{U.value=!1,A.value=null},K=async()=>{if(A.value)try{await L.delete(`/api/admin/products/${S.productId}/images/${A.value.id}`),f("image-deleted",A.value.id),f("images-updated"),B()}catch(a){console.error("Error deleting image:",a),alert("Failed to delete image. Please try again.")}},ee=a=>{a.target.dataset.errorHandled||(a.target.dataset.errorHandled="true",a.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjhGOUZBIiByeD0iOCIvPgo8cGF0aCBkPSJNMTAwIDExMEMxMTQuMTQyIDExMCAxMjYgOTguMTQyMSAxMjYgODRDMTI2IDY5Ljg1NzkgMTE0LjE0MiA1OCAxMDAgNThDODUuODU3OSA1OCA3NCA2OS44NTc5IDc0IDg0Qzc0IDk4LjE0MjEgODUuODU3OSAxMTAgMTAwIDExMFoiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik04NiA3NEg5MFY3OEg4NlY3NFoiIGZpbGw9IiNEMUQxRDEiLz4KPHA+CjwvcGF0aD4KPC9zdmc+Cg==",a.target.classList.add("image-error"))},se=a=>{if(!a)return"";try{const s=new Date(a);return new Intl.DateTimeFormat("uk-UA",{month:"short",day:"numeric"}).format(s)}catch{return""}};return V({uploadPendingImages:async a=>{if(p.value.length!==0)try{u.value=!0,t.value={current:0,total:p.value.length};for(const s of p.value){const o=new FormData;o.append("image",s.file);try{const m=await L.post(`/api/admin/products/${a}/images/single`,o);if(s.isMain&&m.data&&m.data.success&&m.data.data){const c=m.data.data;await G(c)}t.value.current++,f("image-uploaded",m.data)}catch(m){throw console.error(`Failed to upload image: ${s.name}`,m),new Error(`Failed to upload ${s.name}: ${m.message}`)}}p.value=[],f("pending-images-changed",[]),f("images-updated")}catch(s){throw console.error("Error uploading pending images:",s),s}finally{u.value=!1,t.value={current:0,total:0}}},getPendingImages:()=>p.value,clearPendingImages:()=>{p.value=[],f("pending-images-changed",[])}}),(a,s)=>{var o,m;return i(),n("div",as,[s[12]||(s[12]=e("h2",{class:"title is-5 mb-4"},"🖼️ Product Images",-1)),e("div",ts,[e("div",ls,[s[1]||(s[1]=e("label",{class:"label"},"Upload Images",-1)),e("div",os,[e("div",{class:P(["drop-zone",{"is-dragover":v.value,"is-disabled":u.value}]),onDrop:r,onDragover:J(d,["prevent"]),onDragleave:b,onClick:E},[e("input",{ref_key:"fileInput",ref:D,type:"file",accept:"image/*,.jfif",multiple:"",onChange:l,disabled:u.value,style:{display:"none"}},null,40,ns),e("div",is,[e("span",ds,[u.value?(i(),n("i",cs)):(i(),n("i",rs))]),e("p",us,[u.value?(i(),n("strong",ms,"Uploading images...")):(i(),n("strong",vs,"Drop images here or click to browse"))]),s[0]||(s[0]=e("p",{class:"has-text-grey-light is-size-7"}," Supports: JPG, PNG, GIF, WebP, JFIF (max 5MB each) ",-1))])],34)]),g.value.length>0?(i(),n("p",gs,y(g.value.length)+" file(s) selected ",1)):$("",!0)]),u.value?(i(),n("div",ps,[e("div",fs,[e("div",hs,[e("div",ys,[e("span",_s,[s[2]||(s[2]=e("span",{class:"icon"},[e("i",{class:"fas fa-spinner fa-spin"})],-1)),e("span",null,"Uploading "+y(t.value.current)+" of "+y(t.value.total)+" images...",1)])])]),e("div",bs,[e("div",Is,[e("progress",{class:"progress is-info",value:t.value.current,max:t.value.total},y(Math.round(t.value.current/t.value.total*100))+"% ",9,Cs)])])])])):$("",!0)]),_.images.length>0?(i(),n("div",$s,[e("label",ws,"Current Images ("+y(_.images.length)+")",1),e("div",xs,[(i(!0),n(R,null,q(_.images,c=>(i(),n("div",{key:c.id,class:"column is-6-tablet is-4-desktop"},[e("div",ks,[e("div",As,[e("figure",Ds,[e("img",{src:c.imageUrl||c.url,alt:c.altText||"Product image",onError:ee,onClick:w=>Q(c)},null,40,Ms)]),e("div",Ps,[e("div",Ss,[c.isMain?$("",!0):(i(),n("button",{key:0,class:"button is-small is-info",onClick:w=>G(c.id),title:"Set as main image"},s[3]||(s[3]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-star"})],-1)]),8,Es)),e("button",{class:"button is-small is-primary",onClick:w=>Q(c),title:"View full size"},s[4]||(s[4]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-eye"})],-1)]),8,Fs),e("button",{class:"button is-small is-danger",onClick:w=>W(c),title:"Delete image"},s[5]||(s[5]=[e("span",{class:"icon is-small"},[e("i",{class:"fas fa-trash"})],-1)]),8,Us)])])]),e("div",Ts,[e("div",Vs,[e("div",js,[e("div",Ls,[e("span",{class:P(["tag is-small",c.isMain?"is-primary":"is-light"])},y(c.isMain?"Main":`#${c.order||0}`),3)])]),e("div",Os,[e("div",Bs,[c.createdAt?(i(),n("span",Ns,y(se(c.createdAt)),1)):$("",!0)])])])])])]))),128))])])):$("",!0),p.value.length>0?(i(),n("div",zs,[e("label",Rs,"Images to Upload ("+y(p.value.length)+")",1),s[8]||(s[8]=e("div",{class:"notification is-warning is-light"},[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"These images will be uploaded when you save the product.")])],-1)),e("div",qs,[(i(!0),n(R,null,q(p.value,c=>(i(),n("div",{key:c.id,class:"column is-6-tablet is-4-desktop"},[e("div",Gs,[e("div",Qs,[e("figure",Ws,[e("img",{src:c.preview,alt:c.name},null,8,Zs)])]),e("div",Hs,[e("div",Js,[e("div",Ys,[e("div",Xs,[e("span",{class:P(["tag is-small",c.isMain?"is-warning":"is-light"])},y(c.isMain?"Will be Main":"Pending"),3)])]),e("div",Ks,[e("div",ea,[e("div",sa,[c.isMain?$("",!0):(i(),n("button",{key:0,class:"button is-warning is-small",onClick:w=>Y(c.id),title:"Set as main"},s[6]||(s[6]=[e("span",{class:"icon"},[e("i",{class:"fas fa-star"})],-1)]),8,aa)),e("button",{class:"button is-danger is-small",onClick:w=>X(c.id),title:"Remove"},s[7]||(s[7]=[e("span",{class:"icon"},[e("i",{class:"fas fa-times"})],-1)]),8,ta)])])])]),e("div",la,[e("p",null,[e("strong",null,y(c.name),1)]),e("p",null,y((c.size/1024/1024).toFixed(2))+" MB",1)])])])]))),128))])])):$("",!0),_.images.length===0&&p.value.length===0?(i(),n("div",oa,s[9]||(s[9]=[e("span",{class:"icon is-large has-text-grey"},[e("i",{class:"fas fa-images fa-3x"})],-1),e("p",{class:"has-text-grey mt-2"},"No images uploaded yet",-1),e("p",{class:"has-text-grey-light is-size-7"},"Use the upload area above to add product images",-1)]))):$("",!0),e("div",{class:P(["modal",{"is-active":M.value}])},[e("div",{class:"modal-background",onClick:O}),e("div",na,[e("p",ia,[x.value?(i(),n("img",{key:0,src:((o=x.value)==null?void 0:o.imageUrl)||((m=x.value)==null?void 0:m.url)},null,8,da)):$("",!0)])]),e("button",{class:"modal-close is-large",onClick:O})],2),e("div",{class:P(["modal",{"is-active":U.value}])},[e("div",{class:"modal-background",onClick:B}),e("div",{class:"modal-card"},[e("header",{class:"modal-card-head"},[s[10]||(s[10]=e("p",{class:"modal-card-title"},"Confirm Delete",-1)),e("button",{class:"delete",onClick:B})]),s[11]||(s[11]=e("section",{class:"modal-card-body"},[e("p",null,"Are you sure you want to delete this image?"),e("p",{class:"has-text-danger is-size-7 mt-2"},"This action cannot be undone.")],-1)),e("footer",{class:"modal-card-foot"},[e("button",{class:"button is-danger",onClick:K},"Delete"),e("button",{class:"button",onClick:B},"Cancel")])])],2)])}}},ua=H(ca,[["__scopeId","data-v-68fbc564"]]),va={class:"product-edit"},ma={key:0,class:"has-text-centered py-6"},ga={key:1,class:"notification is-danger"},pa={class:"level mb-5"},fa={class:"level-left"},ha={class:"level-item"},ya={class:"title is-3"},_a={key:0,class:"subtitle is-6 has-text-grey"},ba={class:"level-right"},Ia={class:"level-item"},Ca={class:"buttons"},$a={class:"form-sections"},wa={class:"box mb-5"},xa={class:"columns is-multiline"},ka={class:"column is-6"},Aa={class:"column is-6"},Da={class:"column is-12"},Ma={class:"field"},Pa={class:"control"},Sa={class:"column is-12"},Ea={class:"field"},Fa={class:"control"},Ua={class:"column is-12"},Ta={class:"field"},Va={class:"control"},ja={class:"box mb-5"},La={class:"box mb-5"},Oa={class:"columns"},Ba={class:"column is-4"},Na={class:"field"},za={class:"control has-icons-left"},Ra={class:"column is-4"},qa={class:"field"},Ga={class:"control has-icons-left"},Qa={class:"column is-4"},Wa={class:"field"},Za={class:"control"},Ha={class:"select is-fullwidth"},Ja={class:"box mb-5"},Ya={class:"field is-grouped mb-4"},Xa={class:"control is-expanded"},Ka={class:"control is-expanded"},et={key:0,class:"table-container"},st={class:"table is-fullwidth is-striped is-hoverable"},at={class:"tags"},tt=["onClick"],lt=["onClick"],ot={key:1,class:"notification is-light has-text-centered"},nt={class:"box mb-5"},it={class:"columns is-multiline"},dt={class:"column is-12"},rt={class:"field"},ct={class:"control"},ut={class:"column is-12"},vt={class:"field"},mt={class:"control"},gt={class:"column is-12"},pt={class:"field"},ft={class:"field has-addons"},ht={class:"control is-expanded"},yt={class:"file has-name is-fullwidth"},_t={class:"file-label"},bt=["disabled"],It={key:0,class:"file-icon"},Ct={class:"file-label"},$t={class:"file-name"},wt={key:0,class:"control"},xt=["disabled"],kt={key:0,class:"has-text-centered mt-4"},At={class:"box has-background-light p-4"},Dt={class:"image is-128x128 is-inline-block"},Mt=["src","alt"],Pt={key:0,class:"box mb-5"},St={class:"columns"},Et={class:"column is-6"},Ft={class:"field"},Ut={class:"control"},Tt=["value"],Vt={key:0,class:"column is-6"},jt={class:"field"},Lt={class:"control"},Ot=["value"],Bt={__name:"ProductEdit",props:{productId:{type:String,required:!1},isCreate:{type:Boolean,default:!1}},emits:["save","cancel"],setup(_,{emit:V}){const C=_,S=V,f=de(),D=ce(),v=h(!1),u=h(!1),g=h(!1),p=h(null),M=h(""),U=h([]),x=h(null),A=h(""),t=h({id:"",companyId:"",name:"",slug:"",description:"",priceAmount:0,priceCurrency:0,stock:0,status:"0",categoryId:"",attributes:{},metaTitle:"",metaDescription:"",metaImage:"",createdAt:null,updatedAt:null}),k=h({key:"",value:""}),E=te(()=>C.productId||f.params.id),l=()=>{if(!k.value.key.trim()||!k.value.value.trim())return;const a=k.value.key.trim(),s=k.value.value.trim();t.value.attributes[a]?Array.isArray(t.value.attributes[a])?t.value.attributes[a].push(s):t.value.attributes[a]=[t.value.attributes[a],s]:t.value.attributes[a]=s,k.value.key="",k.value.value=""},r=a=>{delete t.value.attributes[a]},d=(a,s)=>{const o=t.value.attributes[a];Array.isArray(o)?o.length===1?delete t.value.attributes[a]:o.splice(s,1):delete t.value.attributes[a]},b=async a=>{const s=a.target.files[0];if(s){M.value=s.name,g.value=!0;try{const o=new FileReader;o.onload=m=>{t.value.metaImage=m.target.result},o.readAsDataURL(s)}catch(o){console.error("Error processing meta image:",o),p.value="Failed to process meta image. Please try again."}finally{g.value=!1}}},I=()=>{t.value.metaImage="",M.value=""},G=a=>{console.log("Main image changed:",a)},Y=a=>{console.log("Image uploaded:",a),O()},X=a=>{console.log("Image deleted:",a)},Q=a=>{console.log("Pending images changed:",a.length)},O=async()=>{if(!(C.isCreate||!E.value))try{const a=await L.get(`/api/admin/products/${E.value}/with-images`);a.data&&a.data.success&&a.data.data&&(U.value=a.data.data.images||[])}catch(a){console.error("Error loading product images:",a),U.value=[]}},W=a=>{if(!a)return"N/A";try{const s=new Date(a);return new Intl.DateTimeFormat("uk-UA",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(s)}catch(s){return console.error("Error formatting date:",s,a),a}},B=a=>{console.log("Category changed:",a)},K=a=>{console.log("Company changed:",a)},ee=()=>{D.push("/admin/products")},se=async()=>{try{if(u.value=!0,p.value=null,!t.value.companyId){p.value="Company is required",u.value=!1;return}if(!t.value.name||!t.value.name.trim()){p.value="Product name is required",u.value=!1;return}if(!t.value.description||!t.value.description.trim()){p.value="Product description is required",u.value=!1;return}if(!t.value.categoryId){p.value="Category is required",u.value=!1;return}if(!t.value.priceAmount||t.value.priceAmount<=0){p.value="Price must be greater than 0",u.value=!1;return}const a=t.value.slug||t.value.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"),s={companyId:t.value.companyId,name:t.value.name.trim(),slug:a,description:t.value.description.trim(),priceCurrency:0,priceAmount:parseFloat(t.value.priceAmount)||0,stock:parseInt(t.value.stock)||0,categoryId:t.value.categoryId,status:parseInt(t.value.status)||0,attributes:t.value.attributes||null,metaTitle:t.value.metaTitle||t.value.name||"",metaDescription:t.value.metaDescription||t.value.description||"",metaImage:t.value.metaImage&&!t.value.metaImage.startsWith("data:")?t.value.metaImage.trim():"https://via.placeholder.com/300x200.png?text=Product+Image"};console.log("📤 Sending product data:",s);let o;if(C.isCreate){const m=await Z.createProduct(s);if(console.log("✅ Product created:",m),o=m.data||m,t.value.metaImage&&t.value.metaImage.startsWith("data:")&&o)try{const w=await(await fetch(t.value.metaImage)).blob(),N=new File([w],M.value||"meta-image.jpg",{type:w.type}),j=new FormData;j.append("image",N),await L.post(`/api/admin/products/${o}/meta-image`,j,{headers:{"Content-Type":"multipart/form-data"}}),console.log("✅ Meta image uploaded for new product")}catch(c){console.error("❌ Error uploading meta image:",c)}}else{const m=await Z.updateProduct(E.value,s);if(console.log("✅ Product updated:",m),o=E.value,t.value.metaImage&&t.value.metaImage.startsWith("data:"))try{const w=await(await fetch(t.value.metaImage)).blob(),N=new File([w],M.value||"meta-image.jpg",{type:w.type}),j=new FormData;j.append("image",N),await L.post(`/api/admin/products/${o}/meta-image`,j,{headers:{"Content-Type":"multipart/form-data"}}),console.log("✅ Meta image updated")}catch(c){console.error("❌ Error updating meta image:",c)}else if(!t.value.metaImage&&A.value)try{await L.delete(`/api/admin/products/${o}/meta-image`),console.log("✅ Meta image deleted")}catch(c){console.error("❌ Error deleting meta image:",c)}}if(x.value&&o){const m=x.value.getPendingImages();m.length>0&&(console.log(`🔄 Uploading ${m.length} pending images...`),await x.value.uploadPendingImages(o))}S("save",s)}catch(a){console.error("❌ Error saving product:",a);let s="Failed to save product";a.response&&a.response.data?a.response.data.message?s=a.response.data.message:a.response.data.errors?s=Object.values(a.response.data.errors).flat().join(", "):typeof a.response.data=="string"&&(s=a.response.data):a.message&&(s=a.message),p.value=s}finally{u.value=!1}};le(()=>t.value.name,a=>{a&&!t.value.slug&&(t.value.slug=a.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim("-"))});const ne=async()=>{if(!C.isCreate)try{v.value=!0,p.value=null;const a=await Z.getProductById(E.value),s=a.data||a;if(Object.keys(t.value).forEach(o=>{s[o]!==void 0&&(t.value[o]=s[o])}),A.value=s.metaImage||"",s.attributes)try{t.value.attributes=typeof s.attributes=="string"?JSON.parse(s.attributes):s.attributes}catch(o){console.error("Error parsing attributes:",o),t.value.attributes={}}console.log("Product loaded for editing:",t.value),await O()}catch(a){console.error("Error loading product:",a),p.value=a.message||"Failed to load product"}finally{v.value=!1}};return oe(async()=>{await ne()}),(a,s)=>(i(),n("div",va,[v.value?(i(),n("div",ma,s[13]||(s[13]=[e("div",{class:"loader is-loading"},null,-1),e("p",{class:"mt-4"},"Loading product data...",-1)]))):p.value?(i(),n("div",ga,[s[14]||(s[14]=e("strong",null,"Error:",-1)),z(" "+y(p.value),1)])):(i(),n("form",{key:2,onSubmit:J(se,["prevent"]),class:"product-form"},[e("div",pa,[e("div",fa,[e("div",{class:"level-item"},[e("button",{class:"button",onClick:ee},s[15]||(s[15]=[e("span",{class:"icon"},[e("i",{class:"fas fa-arrow-left"})],-1),e("span",null,"Back to Products",-1)]))]),e("div",ha,[e("div",null,[e("h1",ya,y(_.isCreate?"Create Product":"Edit Product"),1),_.isCreate?$("",!0):(i(),n("p",_a,y(t.value.id),1))])])]),e("div",ba,[e("div",Ia,[e("div",Ca,[e("button",{type:"button",class:"button",onClick:s[0]||(s[0]=o=>a.$emit("cancel"))}," Cancel "),e("button",{type:"submit",class:P(["button is-primary",{"is-loading":u.value}])},y(_.isCreate?"Create Product":"Save Changes"),3)])])])]),e("div",$a,[e("div",wa,[s[20]||(s[20]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-primary"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"Basic Information")])],-1)),e("div",xa,[e("div",ka,[ae(ss,{modelValue:t.value.companyId,"onUpdate:modelValue":s[1]||(s[1]=o=>t.value.companyId=o),label:"Company",placeholder:"Search and select company...",required:!0,onChange:K},null,8,["modelValue"])]),e("div",Aa,[ae(Fe,{modelValue:t.value.categoryId,"onUpdate:modelValue":s[2]||(s[2]=o=>t.value.categoryId=o),label:"Category",placeholder:"Search and select category...",required:!0,onChange:B},null,8,["modelValue"])]),e("div",Da,[e("div",Ma,[s[16]||(s[16]=e("label",{class:"label"},"Product Name *",-1)),e("div",Pa,[F(e("input",{"onUpdate:modelValue":s[3]||(s[3]=o=>t.value.name=o),class:"input",type:"text",placeholder:"Enter product name",required:""},null,512),[[T,t.value.name]])])])]),e("div",Sa,[e("div",Ea,[s[17]||(s[17]=e("label",{class:"label"},"Slug",-1)),e("div",Fa,[F(e("input",{"onUpdate:modelValue":s[4]||(s[4]=o=>t.value.slug=o),class:"input",type:"text",placeholder:"Auto-generated from name"},null,512),[[T,t.value.slug]])]),s[18]||(s[18]=e("p",{class:"help"},"Leave empty to auto-generate from product name",-1))])]),e("div",Ua,[e("div",Ta,[s[19]||(s[19]=e("label",{class:"label"},"Description",-1)),e("div",Va,[F(e("textarea",{"onUpdate:modelValue":s[5]||(s[5]=o=>t.value.description=o),class:"textarea",placeholder:"Enter product description",rows:"4"},null,512),[[T,t.value.description]])])])])])]),e("div",ja,[s[21]||(s[21]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-link"},[e("i",{class:"fas fa-images"})]),e("span",null,"Product Images")])],-1)),ae(ua,{ref_key:"imageManager",ref:x,"product-id":E.value,images:U.value,"is-create":C.isCreate,onImagesUpdated:O,onMainImageChanged:G,onImageUploaded:Y,onImageDeleted:X,onPendingImagesChanged:Q},null,8,["product-id","images","is-create"])]),e("div",La,[s[28]||(s[28]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-success"},[e("i",{class:"fas fa-dollar-sign"})]),e("span",null,"Pricing & Inventory")])],-1)),e("div",Oa,[e("div",Ba,[e("div",Na,[s[23]||(s[23]=e("label",{class:"label"},"Price *",-1)),e("div",za,[F(e("input",{"onUpdate:modelValue":s[6]||(s[6]=o=>t.value.priceAmount=o),class:"input",type:"number",step:"0.01",placeholder:"0.00",required:""},null,512),[[T,t.value.priceAmount,void 0,{number:!0}]]),s[22]||(s[22]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-hryvnia-sign"})],-1))])])]),e("div",Ra,[e("div",qa,[s[25]||(s[25]=e("label",{class:"label"},"Stock *",-1)),e("div",Ga,[F(e("input",{"onUpdate:modelValue":s[7]||(s[7]=o=>t.value.stock=o),class:"input",type:"number",min:"0",placeholder:"0",required:""},null,512),[[T,t.value.stock,void 0,{number:!0}]]),s[24]||(s[24]=e("span",{class:"icon is-small is-left"},[e("i",{class:"fas fa-boxes"})],-1))])])]),e("div",Qa,[e("div",Wa,[s[27]||(s[27]=e("label",{class:"label"},"Status",-1)),e("div",Za,[e("div",Ha,[F(e("select",{"onUpdate:modelValue":s[8]||(s[8]=o=>t.value.status=o)},s[26]||(s[26]=[e("option",{value:"0"},"Pending",-1),e("option",{value:"1"},"Approved",-1),e("option",{value:"2"},"Rejected",-1)]),512),[[re,t.value.status]])])])])])])]),e("div",Ja,[s[33]||(s[33]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-info"},[e("i",{class:"fas fa-tags"})]),e("span",null,"Product Attributes")])],-1)),e("div",Ya,[e("div",Xa,[F(e("input",{"onUpdate:modelValue":s[9]||(s[9]=o=>k.value.key=o),class:"input",type:"text",placeholder:"Attribute name (e.g., Color, Size)"},null,512),[[T,k.value.key]])]),e("div",Ka,[F(e("input",{"onUpdate:modelValue":s[10]||(s[10]=o=>k.value.value=o),class:"input",type:"text",placeholder:"Attribute value (e.g., Red, Large)"},null,512),[[T,k.value.value]])]),e("div",{class:"control"},[e("button",{type:"button",class:"button is-primary",onClick:l},s[29]||(s[29]=[e("span",{class:"icon"},[e("i",{class:"fas fa-plus"})],-1),e("span",null,"Add",-1)]))])]),Object.keys(t.value.attributes).length>0?(i(),n("div",et,[e("table",st,[s[31]||(s[31]=e("thead",null,[e("tr",null,[e("th",null,"Attribute"),e("th",null,"Values"),e("th",{width:"100"},"Actions")])],-1)),e("tbody",null,[(i(!0),n(R,null,q(t.value.attributes,(o,m)=>(i(),n("tr",{key:m},[e("td",null,[e("strong",null,y(m),1)]),e("td",null,[e("span",at,[(i(!0),n(R,null,q(Array.isArray(o)?o:[o],(c,w)=>(i(),n("span",{key:w,class:"tag is-light"},[z(y(c)+" ",1),e("button",{type:"button",class:"delete is-small",onClick:N=>d(m,w)},null,8,tt)]))),128))])]),e("td",null,[e("button",{type:"button",class:"button is-small is-danger",onClick:c=>r(m)},s[30]||(s[30]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1)]),8,lt)])]))),128))])])])):(i(),n("div",ot,s[32]||(s[32]=[e("span",{class:"icon-text"},[e("span",{class:"icon"},[e("i",{class:"fas fa-info-circle"})]),e("span",null,"No attributes added yet. Use the form above to add product attributes.")],-1)])))]),e("div",nt,[s[40]||(s[40]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-warning"},[e("i",{class:"fas fa-search"})]),e("span",null,"SEO Meta Information")])],-1)),e("div",it,[e("div",dt,[e("div",rt,[s[34]||(s[34]=e("label",{class:"label"},"Meta Title",-1)),e("div",ct,[F(e("input",{"onUpdate:modelValue":s[11]||(s[11]=o=>t.value.metaTitle=o),class:"input",type:"text",placeholder:"SEO title for search engines"},null,512),[[T,t.value.metaTitle]])])])]),e("div",ut,[e("div",vt,[s[35]||(s[35]=e("label",{class:"label"},"Meta Description",-1)),e("div",mt,[F(e("textarea",{"onUpdate:modelValue":s[12]||(s[12]=o=>t.value.metaDescription=o),class:"textarea",placeholder:"SEO description for search engines",rows:"3"},null,512),[[T,t.value.metaDescription]])])])]),e("div",gt,[e("div",pt,[s[38]||(s[38]=e("label",{class:"label"},"Meta Image",-1)),e("div",ft,[e("div",ht,[e("div",yt,[e("label",_t,[e("input",{class:"file-input",type:"file",accept:"image/*",onChange:b,disabled:g.value},null,40,bt),e("span",{class:P(["file-cta",{"is-loading":g.value}])},[g.value?$("",!0):(i(),n("span",It,s[36]||(s[36]=[e("i",{class:"fas fa-upload"},null,-1)]))),e("span",Ct,y(g.value?"Uploading...":"Choose image..."),1)],2),e("span",$t,y(M.value||"No file selected"),1)])])]),t.value.metaImage?(i(),n("div",wt,[e("button",{type:"button",class:"button is-danger",onClick:I,disabled:g.value},s[37]||(s[37]=[e("span",{class:"icon"},[e("i",{class:"fas fa-trash"})],-1),e("span",null,"Clear",-1)]),8,xt)])):$("",!0)])])])]),t.value.metaImage?(i(),n("div",kt,[e("div",At,[e("figure",Dt,[e("img",{src:t.value.metaImage,alt:t.value.name,class:"is-rounded"},null,8,Mt)]),s[39]||(s[39]=e("p",{class:"has-text-grey mt-2"},[e("small",null,"Meta Image Preview")],-1))])])):$("",!0)]),_.isCreate?$("",!0):(i(),n("div",Pt,[s[43]||(s[43]=e("h2",{class:"title is-4 mb-5"},[e("span",{class:"icon-text"},[e("span",{class:"icon has-text-grey"},[e("i",{class:"fas fa-clock"})]),e("span",null,"Timestamps")])],-1)),e("div",St,[e("div",Et,[e("div",Ft,[s[41]||(s[41]=e("label",{class:"label"},"Created At",-1)),e("div",Ut,[e("input",{class:"input",type:"text",value:W(t.value.createdAt),readonly:""},null,8,Tt)])])]),t.value.updatedAt?(i(),n("div",Vt,[e("div",jt,[s[42]||(s[42]=e("label",{class:"label"},"Updated At",-1)),e("div",Lt,[e("input",{class:"input",type:"text",value:W(t.value.updatedAt),readonly:""},null,8,Ot)])])])):$("",!0)])]))])],32))]))}},qt=H(Bt,[["__scopeId","data-v-ae5b1a1c"]]);export{qt as default};
