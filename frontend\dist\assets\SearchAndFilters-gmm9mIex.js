import{g as f,z as H,h as x,x as L,_ as J,c as u,o as h,a as r,k as P,n as U,t as y,A as V,B as A,F as E,p as N,C as W,d as z}from"./index-C1YYMYJd.js";function ke(v={}){const{fetchFunction:j,defaultFilters:_={},debounceTime:C=300,defaultPageSize:c=15,clientSideSearch:S=!1}=v,k=f([]),I=f(!1),b=f(null),B=f(!0),m=f(1),F=f(1),a=f(0),n=f(c),e=H({search:"",..._}),s=f("createdAt"),w=f("desc"),M=x(()=>Object.values(e).some(l=>l!==""&&l!==null&&l!==void 0)),D=x(()=>Object.entries(e).filter(([l,o])=>o!==""&&o!==null&&o!==void 0).reduce((l,[o,t])=>(l[o]=t,l),{})),O=async(l=1)=>{(B.value||m.value!==l)&&(I.value=!0),m.value=l,b.value=null;try{const o={page:m.value,pageSize:n.value,orderBy:s.value,descending:w.value==="desc"};S?Object.entries(D.value).forEach(([p,g])=>{p!=="search"&&(p==="sortBy"?o.orderBy=g:p==="sortOrder"?o.descending=g==="desc":o[p]=g)}):Object.entries(D.value).forEach(([p,g])=>{p==="sortBy"?o.orderBy=g:p==="sortOrder"?o.descending=g==="desc":o[p]=g}),console.log("Fetching data with params:",o);const t=await j(o);let i=[],d={};if(t&&(t.success&&t.data?t.data.items?(i=t.data.items,d=t.data):t.data.data&&(i=t.data.data,d=t.data):t.data?Array.isArray(t.data)?i=t.data:t.data.data?(i=t.data.data,d=t.data):t.data.Data&&(i=t.data.Data,d=t.data):Array.isArray(t)?i=t:t.users&&(i=t.users,d=t.pagination||{})),S&&e.search){const p=e.search.toLowerCase();i=i.filter(g=>R(g,p))}k.value=i,S&&e.search?(a.value=i.length,F.value=Math.ceil(a.value/n.value)||1):(a.value=d.total||d.totalItems||d.Total||i.length,F.value=d.totalPages||d.lastPage||d.LastPage||1,m.value=d.page||d.currentPage||d.CurrentPage||1),console.log("Data fetched successfully:",{itemsCount:k.value.length,totalItems:a.value,totalPages:F.value,currentPage:m.value})}catch(o){console.error("Error fetching data:",o),b.value=o.message||"Failed to load data",k.value=[],F.value=1,a.value=0}finally{I.value=!1,B.value&&(B.value=!1)}},R=(l,o)=>["name","username","email","title","description","contactEmail","contactPhone","slug"].some(i=>{const d=q(l,i);return d&&d.toString().toLowerCase().includes(o)}),q=(l,o)=>o.split(".").reduce((t,i)=>t&&t[i]!==void 0?t[i]:null,l),Q=()=>{Object.keys(e).forEach(l=>{l==="search"?e[l]="":e[l]=_[l]||""}),m.value=1,O(1)},G=l=>{O(l)};let T=null;const $=()=>{T&&clearTimeout(T),T=setTimeout(()=>{m.value=1,O(1)},C)};return L(()=>e.search,()=>{console.log("Search changed:",e.search),$()}),Object.keys(_).forEach(l=>{l!=="search"&&L(()=>e[l],(o,t)=>{o!==t&&(console.log(`Filter ${l} changed:`,e[l]),m.value=1,O(1))})}),{items:k,loading:I,error:b,isFirstLoad:B,currentPage:m,totalPages:F,totalItems:a,pageSize:n,filters:e,hasFilters:M,activeFilters:D,sortBy:s,sortOrder:w,fetchData:O,resetFilters:Q,handlePageChange:G,debouncedSearch:$}}const X={class:"search-and-filters"},Y={class:"card mb-4"},Z={class:"card-content"},K={class:"columns is-multiline"},ee={class:"field"},te={class:"label"},ae={class:"control has-icons-left"},se=["placeholder"],le={class:"field"},ne={class:"label"},oe={class:"control"},ce={key:0,class:"select is-fullwidth"},re=["onUpdate:modelValue","onChange"],ie={key:0,value:""},de=["value"],ue=["placeholder","onUpdate:modelValue","onInput"],he=["onUpdate:modelValue","onChange"],ve=["placeholder","onUpdate:modelValue","onInput"],me={key:0,class:"column is-2"},fe={class:"field",style:{"margin-top":"1.9rem"}},pe={class:"buttons is-right"},ge={key:0,class:"level mb-4"},ye={class:"level-left"},_e={class:"level-item"},be={key:0},Fe={__name:"SearchAndFilters",props:{filters:{type:Object,required:!0},filterFields:{type:Array,default:()=>[]},searchLabel:{type:String,default:"Search"},searchPlaceholder:{type:String,default:"Search..."},searchColumnClass:{type:String,default:"is-5"},showResetButton:{type:Boolean,default:!0},resetButtonText:{type:String,default:"Reset Filters"},showStatusBar:{type:Boolean,default:!0},totalItems:{type:Number,default:0},itemName:{type:String,default:"items"},loading:{type:Boolean,default:!1}},emits:["search-changed","filter-changed","reset-filters"],setup(v,{emit:j}){const _=v,C=j,c=f({..._.filters}),S=x(()=>Object.values(c.value).some(a=>a!==""&&a!==null&&a!==void 0)),k=x(()=>Object.entries(c.value).filter(([a,n])=>n!==""&&n!==null&&n!==void 0).reduce((a,[n,e])=>(a[n]=e,a),{})),I=()=>{C("search-changed",c.value.search)},b=(a,n)=>{C("filter-changed",a,n)},B=()=>{Object.keys(c.value).forEach(a=>{c.value[a]=""}),C("reset-filters")},m=a=>{if(a==="search")return"Search";const n=_.filterFields.find(e=>e.key===a);return n?n.label:a},F=(a,n)=>{const e=_.filterFields.find(s=>s.key===a);if(e&&e.type==="select"&&e.options){const s=e.options.find(w=>w.value===n);return s?s.label:n}return n};return L(()=>_.filters,a=>{c.value={...a}},{deep:!0,immediate:!0}),(a,n)=>(h(),u("div",X,[r("div",Y,[r("div",Z,[r("div",K,[r("div",{class:U(["column",v.searchColumnClass])},[r("div",ee,[r("label",te,y(v.searchLabel),1),r("div",ae,[V(r("input",{class:"input",type:"text",placeholder:v.searchPlaceholder,"onUpdate:modelValue":n[0]||(n[0]=e=>c.value.search=e),onInput:I},null,40,se),[[A,c.value.search]]),n[1]||(n[1]=r("span",{class:"icon is-small is-left"},[r("i",{class:"fas fa-search"})],-1))])])],2),(h(!0),u(E,null,N(v.filterFields,e=>(h(),u("div",{key:e.key,class:U(["column",e.columnClass||"is-3"])},[r("div",le,[r("label",ne,y(e.label),1),r("div",oe,[e.type==="select"?(h(),u("div",ce,[V(r("select",{"onUpdate:modelValue":s=>c.value[e.key]=s,onChange:s=>b(e.key,c.value[e.key])},[e.allOption!==!1?(h(),u("option",ie,y(e.allOption||`All ${e.label}`),1)):P("",!0),(h(!0),u(E,null,N(e.options,s=>(h(),u("option",{key:s.value,value:s.value},y(s.label),9,de))),128))],40,re),[[W,c.value[e.key]]])])):e.type==="text"?V((h(),u("input",{key:1,class:"input",type:"text",placeholder:e.placeholder,"onUpdate:modelValue":s=>c.value[e.key]=s,onInput:s=>b(e.key,c.value[e.key])},null,40,ue)),[[A,c.value[e.key]]]):e.type==="date"?V((h(),u("input",{key:2,class:"input",type:"date","onUpdate:modelValue":s=>c.value[e.key]=s,onChange:s=>b(e.key,c.value[e.key])},null,40,he)),[[A,c.value[e.key]]]):e.type==="number"?V((h(),u("input",{key:3,class:"input",type:"number",placeholder:e.placeholder,"onUpdate:modelValue":s=>c.value[e.key]=s,onInput:s=>b(e.key,c.value[e.key])},null,40,ve)),[[A,c.value[e.key]]]):P("",!0)])])],2))),128)),v.showResetButton?(h(),u("div",me,[r("div",fe,[r("div",pe,[r("button",{class:U(["button is-light",{"is-loading":v.loading}]),onClick:B},y(v.resetButtonText),3)])])])):P("",!0)])])]),v.showStatusBar&&(v.totalItems>0||S.value)?(h(),u("div",ge,[r("div",ye,[r("div",_e,[r("p",null,[r("strong",null,y(v.totalItems),1),z(" "+y(v.itemName)+" found ",1),S.value?(h(),u("span",be,[n[2]||(n[2]=z(" with filters: ")),(h(!0),u(E,null,N(k.value,(e,s)=>(h(),u("span",{key:s,class:"tag is-info is-light mr-1"},y(m(s))+": "+y(F(s,e)),1))),128))])):P("",!0)])])])])):P("",!0)]))}},Be=J(Fe,[["__scopeId","data-v-3d4815cd"]]);export{Be as S,ke as u};
